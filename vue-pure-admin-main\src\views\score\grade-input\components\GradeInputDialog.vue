<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="500px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      label-position="right"
    >
      <el-form-item label="学号" prop="studentId">
        <el-input v-model="form.studentId" disabled />
      </el-form-item>
      <el-form-item label="学生姓名">
        <el-input v-model="form.studentName" disabled />
      </el-form-item>
      <el-form-item label="课程代码">
        <el-input v-model="form.courseCode" disabled />
      </el-form-item>
      <el-form-item label="课程名称">
        <el-input v-model="form.courseName" disabled />
      </el-form-item>
      <el-form-item label="期末成绩" prop="finalScore">
        <el-input-number
          v-model="form.finalScore"
          :min="0"
          :max="100"
          :precision="2"
          :step="0.1"
          placeholder="请输入期末成绩"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="绩点">
        <el-input
          v-model="form.gradePoint"
          placeholder="后端自动计算"
          disabled
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="是否重修">
        <el-switch v-model="form.isRetake" />
      </el-form-item>
      <el-form-item label="备注">
        <el-input
          v-model="form.remarks"
          type="textarea"
          :rows="3"
          placeholder="请输入备注"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from "vue";
import { message } from "@/utils/message";
import { inputGrade, updateGrade } from "@/api/score/grade-input";
import type { GradeInputVO, GradeInputDTO } from "@/api/score/grade-input";
import type { FormInstance, FormRules } from "element-plus";

defineOptions({
  name: "GradeInputDialog"
});

// Props
interface Props {
  modelValue: boolean;
  gradeData?: GradeInputVO | null;
  courseCode: string;
  courseName: string;
  semesterId?: number | null;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  gradeData: null,
  courseCode: "",
  courseName: "",
  semesterId: null
});

// Emits
const emit = defineEmits<{
  "update:modelValue": [value: boolean];
  success: [];
}>();

// 响应式数据
const loading = ref(false);
const formRef = ref<FormInstance>();

// 表单数据
const form = reactive<GradeInputDTO & { studentName?: string; courseName?: string }>({
  id: undefined,
  studentId: "",
  studentName: "",
  courseCode: "",
  courseName: "",
  semesterId: 0,
  finalScore: 0,
  gradePoint: 0,
  isRetake: false,
  remarks: ""
});

// 表单验证规则
const rules: FormRules = {
  studentId: [
    { required: true, message: "学号不能为空", trigger: "blur" }
  ],
  finalScore: [
    { required: true, message: "期末成绩不能为空", trigger: "blur" },
    { type: "number", min: 0, max: 100, message: "成绩必须在0-100之间", trigger: "blur" }
  ],
  gradePoint: [
    { type: "number", min: 0, max: 5, message: "绩点必须在0-5之间", trigger: "blur" }
  ]
};

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value)
});

const dialogTitle = computed(() => {
  return props.gradeData?.hasGrade ? "编辑成绩" : "录入成绩";
});

// 监听弹窗显示
watch(visible, (newVal) => {
  if (newVal) {
    initForm();
  }
});

// 初始化表单
const initForm = () => {
  if (props.gradeData) {
    form.id = props.gradeData.id;
    form.studentId = props.gradeData.studentId;
    form.studentName = props.gradeData.studentName || "";
    form.courseCode = props.courseCode;
    form.courseName = props.courseName;
    form.semesterId = props.semesterId || 0;
    form.finalScore = props.gradeData.finalScore || 0;
    form.gradePoint = props.gradeData.gradePoint || 0;
    form.isRetake = props.gradeData.isRetake || false;
    form.remarks = props.gradeData.remarks || "";
  }
};

// 绩点由后端自动计算，前端不再处理

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    loading.value = true;

    const submitData: GradeInputDTO = {
      id: form.id,
      studentId: form.studentId,
      courseCode: form.courseCode,
      semesterId: form.semesterId,
      finalScore: form.finalScore,
      gradePoint: form.gradePoint,
      isRetake: form.isRetake,
      remarks: form.remarks
    };

    if (props.gradeData?.hasGrade && form.id) {
      // 更新成绩
      await updateGrade(form.id, submitData);
    } else {
      // 录入成绩
      await inputGrade(submitData);
    }

    emit("success");
    handleClose();
  } catch (error) {
    console.error("提交失败:", error);
    message("提交失败", { type: "error" });
  } finally {
    loading.value = false;
  }
};

// 关闭弹窗
const handleClose = () => {
  formRef.value?.resetFields();
  visible.value = false;
};
</script>

