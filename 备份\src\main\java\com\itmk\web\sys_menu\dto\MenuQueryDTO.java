package com.itmk.web.sys_menu.dto;

import lombok.Data;

import javax.validation.constraints.Pattern;

/**
 * 菜单查询参数DTO
 * 基于Spring Boot最佳实践设计
 */
@Data
public class MenuQueryDTO {
    
    /**
     * 菜单标题（模糊查询）
     */
    private String title;
    
    /**
     * 菜单类型 (0:目录 1:菜单 2:按钮)
     */
    @Pattern(regexp = "^[012]$", message = "菜单类型只能是0、1或2")
    private String type;
    
    /**
     * 父级菜单ID
     */
    private Long parentId;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 用户ID（用于权限过滤）
     */
    private Long userId;
    
    /**
     * 角色ID（用于权限过滤）
     */
    private Long roleId;
    
    /**
     * 用户类型（0:学生 1:教师 2:管理员）
     */
    @Pattern(regexp = "^[012]$", message = "用户类型只能是0、1或2")
    private String userType;
    
    /**
     * 是否包含按钮权限
     */
    private Boolean includeButtons = false;
    
    /**
     * 是否构建树形结构
     */
    private Boolean buildTree = true;
    
    /**
     * 排序字段
     */
    private String orderBy = "orderNum";
    
    /**
     * 排序方向 (ASC/DESC)
     */
    @Pattern(regexp = "^(ASC|DESC)$", message = "排序方向只能是ASC或DESC")
    private String orderDirection = "ASC";
}
