package com.example.utils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * 文件名处理工具类
 * 用于统一处理文件下载时的文件名编码问题
 */
public class FileNameUtils {

    /**
     * 对文件名进行正确的URL编码，符合RFC 5987标准
     *
     * @param fileName 原始文件名
     * @return 编码后的文件名，可直接用于Content-Disposition头
     */
    public static String encodeFileName(String fileName) {
        try {
            // 使用UTF-8编码，并将+号替换为%20（空格的正确编码）
            return URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString())
                    .replaceAll("\\+", "%20");
        } catch (UnsupportedEncodingException e) {
            // 这种情况理论上不会发生，因为UTF-8是标准编码
            throw new RuntimeException("UTF-8编码不支持", e);
        }
    }

    /**
     * 生成符合RFC 5987标准的Content-Disposition头值
     *
     * @param fileName 原始文件名
     * @return 完整的Content-Disposition头值
     */
    public static String generateContentDisposition(String fileName) {
        String encodedFileName = encodeFileName(fileName);
        return "attachment; filename*=UTF-8''" + encodedFileName;
    }

    /**
     * 生成带时间戳的文件名
     *
     * @param baseName 基础文件名（不含扩展名）
     * @param extension 文件扩展名（如.xlsx）
     * @return 带时间戳的完整文件名
     */
    public static String generateTimestampFileName(String baseName, String extension) {
        String timestamp = java.time.LocalDateTime.now()
                .format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        return baseName + "_" + timestamp + extension;
    }

    /**
     * 生成模板文件名
     *
     * @param templateType 模板类型（如"成绩导入模板"）
     * @param params 额外参数（可选）
     * @return 完整的模板文件名
     */
    public static String generateTemplateFileName(String templateType, String... params) {
        StringBuilder fileName = new StringBuilder(templateType);

        // 添加额外参数
        for (String param : params) {
            if (param != null && !param.trim().isEmpty()) {
                fileName.append("_").append(param.trim());
            }
        }

        // 添加时间戳
        String timestamp = java.time.LocalDateTime.now()
                .format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        fileName.append("_").append(timestamp);

        // 添加扩展名
        fileName.append(".xlsx");

        return fileName.toString();
    }
}
