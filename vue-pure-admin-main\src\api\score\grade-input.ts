import { http } from "@/utils/http";
import type { PageResult } from "@/api/types";

// 成绩录入查询参数
export interface GradeInputQueryParams {
  current?: number;
  size?: number;
  classCode?: string;
  courseCode?: string;
  semesterId?: number;
  studentId?: string;
  studentName?: string;
  hasGrade?: boolean;
}

// 成绩录入DTO
export interface GradeInputDTO {
  id?: number;
  studentId: string;
  courseCode: string;
  semesterId: number;
  finalScore: number;
  gradePoint?: number;
  isRetake?: boolean;
  remarks?: string;
}



// 成绩录入VO
export interface GradeInputVO {
  id?: number;
  studentId: string;
  studentName?: string;
  classCode?: string;
  className?: string;
  courseCode: string;
  courseName?: string;
  courseType?: string;
  credits?: number;
  semesterId: number;
  semesterName?: string;
  finalScore?: number;
  gradePoint?: number;
  isRetake?: boolean;
  remarks?: string;
  hasGrade?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

// 成绩导入结果DTO
export interface GradeImportResultDTO {
  success: boolean;
  totalRows: number;
  successRows: number;
  failedRows: number;
  errorMessages: string[];
  errorDetails: {
    rowNumber: number;
    studentId: string;
    errorMessage: string;
  }[];
}

/**
 * 分页查询成绩录入列表
 */
export const getGradeInputPage = (params: GradeInputQueryParams) => {
  return http.request<PageResult<GradeInputVO>>("post", "/api/score/input/page", { data: params });
};

/**
 * 录入单个成绩
 */
export const inputGrade = (data: GradeInputDTO) => {
  return http.request<boolean>("post", "/api/score/input", { data });
};

/**
 * 更新成绩
 */
export const updateGrade = (id: number, data: GradeInputDTO) => {
  return http.request<boolean>("put", `/api/score/input/${id}`, { data });
};

/**
 * 根据班级和课程获取学生成绩录入列表
 */
export const getStudentGradeInputList = (classCode: string, courseCode: string, semesterId: number) => {
  return http.request<GradeInputVO[]>("get", "/api/score/input/students", {
    params: { classCode, courseCode, semesterId }
  });
};

/**
 * 根据ID获取成绩详情
 */
export const getGradeById = (id: number) => {
  return http.request<GradeInputVO>("get", `/api/score/input/${id}`);
};

/**
 * 检查成绩是否已存在
 */
export const checkGradeExists = (studentId: string, courseCode: string, semesterId: number) => {
  return http.request<boolean>("get", "/api/score/input/check", {
    params: { studentId, courseCode, semesterId }
  });
};

/**
 * 导入成绩
 */
export const importGrades = (file: File, classCode: string, courseCode: string, semesterId: number) => {
  const formData = new FormData();
  formData.append("file", file);
  formData.append("classCode", classCode);
  formData.append("courseCode", courseCode);
  formData.append("semesterId", semesterId.toString());

  return http.request<GradeImportResultDTO>("post", "/api/score/input/import", {
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};

/**
 * 下载多课程成绩导入模板
 */
export const downloadMultiCourseTemplate = (classCode: string, courseCodes: string[], semesterId?: number) => {
  const params: any = {
    classCode,
    courseCodes: courseCodes.join(',')
  };

  if (semesterId) {
    params.semesterId = semesterId;
  }

  return http.request<ArrayBuffer>("get", "/api/score/input/multi-template", {
    params,
    responseType: "arraybuffer"
  });
};

/**
 * 导入多课程成绩
 */
export const importMultiCourseGrades = (file: File, classCode: string, courseCodes: string[], semesterId?: number) => {
  const formData = new FormData();
  formData.append("file", file);
  formData.append("classCode", classCode);
  formData.append("courseCodes", courseCodes.join(','));

  if (semesterId) {
    formData.append("semesterId", semesterId.toString());
  }

  return http.request<GradeImportResultDTO>("post", "/api/score/input/multi-import", {
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};

/**
 * 下载成绩导入模板
 */
export const downloadImportTemplate = (classCode: string, courseCode: string, semesterId: number) => {
  return new Promise((resolve, reject) => {
    http.request<Blob>("get", "/api/score/input/template", {
      params: { classCode, courseCode, semesterId },
      responseType: "blob"
    }, {
      beforeResponseCallback: (response) => {
        // 返回完整的响应对象，包含data和headers
        resolve({
          data: response.data,
          headers: response.headers
        });
      }
    }).catch(reject);
  });
};
