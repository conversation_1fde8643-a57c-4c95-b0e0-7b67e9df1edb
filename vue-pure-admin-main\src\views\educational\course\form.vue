<template>
  <el-form
    ref="ruleFormRef"
    :model="newFormInline"
    :rules="formRules"
    label-width="82px"
  >
    <el-form-item label="课程代码" prop="courseCode">
      <el-input
        v-model="newFormInline.courseCode"
        clearable
        placeholder="请输入课程代码"
      />
    </el-form-item>

    <el-form-item label="课程名称" prop="courseName">
      <el-input
        v-model="newFormInline.courseName"
        clearable
        placeholder="请输入课程名称"
      />
    </el-form-item>

    <el-form-item label="学分" prop="credits">
      <el-input-number
        v-model="newFormInline.credits"
        :min="0.5"
        :max="10"
        :step="0.5"
        :precision="1"
        class="w-full"
        placeholder="请输入学分"
      />
    </el-form-item>

    <el-form-item label="课程类型" prop="courseType">
      <el-select
        v-model="newFormInline.courseType"
        placeholder="请选择课程类型"
        clearable
        class="w-full"
      >
        <el-option
          v-for="item in courseTypeOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>

    <el-form-item label="所属学院" prop="collegeCode">
      <el-select
        v-model="newFormInline.collegeCode"
        placeholder="请选择所属学院（通识课可不选）"
        clearable
        filterable
        class="w-full"
        @change="onCollegeChange"
      >
        <el-option
          v-for="item in collegeOptions"
          :key="item.collegeCode"
          :label="item.collegeName"
          :value="item.collegeCode"
        />
      </el-select>
    </el-form-item>



    <el-form-item label="课程描述" prop="description">
      <el-input
        v-model="newFormInline.description"
        :autosize="{ minRows: 3, maxRows: 5 }"
        type="textarea"
        placeholder="请输入课程描述"
      />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { formRules } from "./utils/rule";
import { FormProps } from "./utils/types";
import { getAllColleges } from "@/api/basic/college";

const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({
    courseCode: "",
    courseName: "",
    credits: 0,
    courseType: "",
    collegeCode: "",
    description: ""
  })
});

const ruleFormRef = ref();
const newFormInline = ref(props.formInline);

// 选项数据
const collegeOptions = ref([]);
const courseTypeOptions = ref([
  { label: "必修", value: "必修" },
  { label: "选修", value: "选修" },
  { label: "实践", value: "实践" },
  { label: "通识", value: "通识" }
]);

// 加载学院选项
async function loadCollegeOptions() {
  try {
    const response = await getAllColleges();
    if (response.success && response.data) {
      collegeOptions.value = response.data;
    }
  } catch (error) {
    // 静默处理错误
  }
}

// 学院变化处理
function onCollegeChange(collegeCode: string) {
  // 学院变化时的处理逻辑（如果需要的话）
}

// 获取表单引用
function getRef() {
  return ruleFormRef.value;
}

// 组件挂载时加载数据
onMounted(async () => {
  await loadCollegeOptions();
});

defineExpose({ getRef });
</script>
