<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.basic.MajorMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.entity.basic.Major">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="major_code" property="majorCode" jdbcType="VARCHAR"/>
        <result column="major_name" property="majorName" jdbcType="VARCHAR"/>
        <result column="college_code" property="collegeCode" jdbcType="VARCHAR"/>
        <result column="duration" property="duration" jdbcType="TINYINT"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, major_code, major_name, college_code, duration, description, created_at, updated_at
    </sql>

    <!-- 分页查询专业列表 -->
    <select id="selectMajorPage" resultMap="BaseResultMap">
        SELECT
        m.id, m.major_code, m.major_name, m.college_code, m.duration, m.description,
        m.created_at, m.updated_at, c.college_name
        FROM majors m
        LEFT JOIN colleges c ON m.college_code = c.college_code
        <where>
            <if test="query.majorCode != null and query.majorCode != ''">
                AND m.major_code LIKE CONCAT('%', #{query.majorCode}, '%')
            </if>
            <if test="query.majorName != null and query.majorName != ''">
                AND m.major_name LIKE CONCAT('%', #{query.majorName}, '%')
            </if>
            <if test="query.collegeCode != null and query.collegeCode != ''">
                AND m.college_code = #{query.collegeCode}
            </if>
            <if test="query.duration != null">
                AND m.duration = #{query.duration}
            </if>
        </where>
        ORDER BY
        <if test="query.sortField == 'collegeName'">CONVERT(c.college_name USING gbk)</if>
        <if test="query.sortField == 'majorName'">CONVERT(m.major_name USING gbk)</if>
        <if test="query.sortField == 'majorCode'">m.major_code</if>
        <if test="query.sortField == 'createdAt'">m.created_at</if>
        <if test="query.sortField == null or query.sortField == ''">CONVERT(c.college_name USING gbk)</if>
        <choose>
            <when test="query.sortOrder == 'desc'">DESC</when>
            <otherwise>ASC</otherwise>
        </choose>
        <if test="query.sortField != 'majorName'">, CONVERT(m.major_name USING gbk) ASC</if>
    </select>

    <!-- 查询所有专业 -->
    <select id="selectAllMajors" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM majors
        ORDER BY college_code, major_code
    </select>

    <!-- 根据学院代码查询专业列表 -->
    <select id="selectMajorsByCollegeCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM majors
        WHERE college_code = #{collegeCode}
        ORDER BY major_code
    </select>

    <!-- 根据专业代码查询专业 -->
    <select id="selectByMajorCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM majors
        WHERE major_code = #{majorCode}
    </select>

    <!-- 检查专业代码是否存在 -->
    <select id="existsByMajorCode" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM majors
        WHERE major_code = #{majorCode}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查专业名称是否存在 -->
    <select id="existsByMajorName" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM majors
        WHERE major_name = #{majorName}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 根据学院代码统计专业数量 -->
    <select id="countByCollegeCode" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM majors
        WHERE college_code = #{collegeCode}
    </select>

</mapper>
