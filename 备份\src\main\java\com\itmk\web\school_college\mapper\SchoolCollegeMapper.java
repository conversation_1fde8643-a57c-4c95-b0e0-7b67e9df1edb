package com.itmk.web.school_college.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.itmk.web.school_college.entity.ListParm;
import com.itmk.web.school_college.entity.SchoolCollege;
import org.apache.ibatis.annotations.Param;

public interface SchoolCollegeMapper extends BaseMapper<SchoolCollege> {
    IPage<SchoolCollege> getList(IPage<SchoolCollege> page, @Param("parm") ListParm listParm);
}