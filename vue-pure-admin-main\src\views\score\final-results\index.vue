<template >
  <div class="main-content">
    <!-- 专业列表 -->
    <MajorList
      v-if="currentView === 'majors'"
      @select-major="handleSelectMajor"
    />

    <!-- 班级列表 -->
    <ClassList
      v-if="currentView === 'classes'"
      :major-code="selectedMajor?.majorCode"
      :major-name="selectedMajor?.majorName"
      @select-class="handleSelectClass"
      @back="handleBackToMajors"
    />

    <!-- 期末成绩列表 -->
    <FinalGradesList
      v-if="currentView === 'grades'"
      :class-code="selectedClass?.classCode"
      :class-name="selectedClass?.className"
      :major-name="selectedMajor?.majorName"
      @back="handleBackToClasses"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import MajorList from "../components/MajorList.vue";
import ClassList from "../components/ClassList.vue";
import FinalGradesList from "./components/FinalGradesList.vue";

defineOptions({
  name: "FinalGrades"
});

// 当前视图
const currentView = ref<'majors' | 'classes' | 'grades'>('majors');

// 选中的专业和班级
const selectedMajor = ref<{ majorCode: string; majorName: string } | null>(null);
const selectedClass = ref<{ classCode: string; className: string } | null>(null);

// 选择专业
const handleSelectMajor = (major: { majorCode: string; majorName: string }) => {
  selectedMajor.value = major;
  currentView.value = 'classes';
};

// 选择班级
const handleSelectClass = (classInfo: { classCode: string; className: string }) => {
  selectedClass.value = classInfo;
  currentView.value = 'grades';
};

// 返回到专业列表
const handleBackToMajors = () => {
  currentView.value = 'majors';
  selectedMajor.value = null;
  selectedClass.value = null;
};

// 返回到班级列表
const handleBackToClasses = () => {
  currentView.value = 'classes';
  selectedClass.value = null;
};
</script>


