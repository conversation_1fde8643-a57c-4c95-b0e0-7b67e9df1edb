<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.system.SysRoleMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.entity.system.SysRole">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="role_name" property="roleName" jdbcType="VARCHAR"/>
        <result column="role_code" property="roleCode" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 通用SQL片段 -->
    <sql id="Base_Column_List">
        id, role_name, role_code, description, sort, status, remark, create_time, update_time
    </sql>

    <!-- 查询角色列表 -->
    <select id="selectRoleList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_role
        <where>
            status != 0
            <if test="query.roleName != null and query.roleName != ''">
                AND role_name LIKE CONCAT('%', #{query.roleName}, '%')
            </if>
            <if test="query.roleCode != null and query.roleCode != ''">
                AND role_code LIKE CONCAT('%', #{query.roleCode}, '%')
            </if>
            <if test="query.status != null">
                AND status = #{query.status}
            </if>
        </where>
        ORDER BY sort ASC, create_time DESC
    </select>

    <!-- 查询所有启用的角色 -->
    <select id="selectAllActive" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_role
        WHERE status = 1
        ORDER BY sort ASC, create_time DESC
    </select>

    <!-- 根据角色编码查询角色 -->
    <select id="selectByRoleCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_role
        WHERE role_code = #{roleCode}
          AND status = 1
    </select>

    <!-- 根据用户ID查询角色列表 -->
    <select id="selectByUserId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT r.id, r.role_name, r.role_code, r.description, r.sort,
               r.status, r.remark, r.create_time, r.update_time
        FROM sys_role r
        INNER JOIN sys_user_role ur ON r.id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND r.status = 1
        ORDER BY r.sort ASC
    </select>

    <!-- 检查角色名称是否存在 -->
    <select id="existsByRoleName" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM sys_role
        WHERE role_name = #{roleName}
          AND status = 1
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查角色编码是否存在 -->
    <select id="existsByRoleCode" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM sys_role
        WHERE role_code = #{roleCode}
          AND status = 1
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 获取最大排序值 -->
    <select id="getMaxSort" resultType="java.lang.Integer">
        SELECT COALESCE(MAX(sort), 0)
        FROM sys_role
        WHERE status = 1
    </select>

    <!-- 根据角色ID查询用户数量 -->
    <select id="countUsersByRoleId" parameterType="java.lang.Integer" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM sys_user_role ur
        INNER JOIN sys_user u ON ur.user_id = u.id
        WHERE ur.role_id = #{roleId}
          AND u.status = 1
    </select>

    <!-- 批量更新角色状态 -->
    <update id="batchUpdateStatus">
        UPDATE sys_role
        SET status = #{status},
            update_time = NOW()
        WHERE id IN
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </update>

    <!-- 查询角色权限（菜单ID列表） -->
    <select id="selectRoleMenuIds" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT rm.menu_id
        FROM sys_role_menu rm
        INNER JOIN sys_menu m ON rm.menu_id = m.id
        WHERE rm.role_id = #{roleId}
          AND m.status = 1
        ORDER BY m.`rank` ASC
    </select>

    <!-- 查询角色的菜单权限详情 -->
    <select id="selectRoleMenus" parameterType="java.lang.Integer" resultType="com.example.entity.system.SysMenu">
        SELECT m.id, m.parent_id, m.title, m.`name`, m.`path`, m.component, m.`redirect`,
               m.menu_type, m.icon, m.`rank`, m.auths, m.show_link, m.show_parent,
               m.keep_alive, m.fixed_tag, m.frame_src, m.status, m.create_time, m.update_time
        FROM sys_menu m
        INNER JOIN sys_role_menu rm ON m.id = rm.menu_id
        WHERE rm.role_id = #{roleId}
          AND m.status = 1
        ORDER BY m.parent_id ASC, m.`rank` ASC
    </select>

    <!-- 统计角色数量 -->
    <select id="countRoles" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM sys_role
        WHERE status = 1
    </select>

    <!-- 查询角色详情（包含菜单权限） -->
    <select id="selectRoleDetail" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_role
        WHERE id = #{roleId}
          AND status = 1
    </select>

</mapper>
