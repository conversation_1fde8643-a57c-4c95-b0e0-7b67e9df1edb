import { $t } from "@/plugins/i18n";
import { home } from "@/router/enums";
const Layout = () => import("@/layout/index.vue");
const { VITE_HIDE_HOME } = import.meta.env;

export default [
  // 首页路由
  {
    path: "/",
    name: "Home",
    component: Layout,
    redirect: "/welcome",
    meta: {
      icon: "ep/home-filled",
      title: $t("menus.pureHome"),
      rank: home,
      showLink: false
    },
    children: [
      {
        path: "/welcome",
        name: "Welcome",
        component: () => import("@/views/welcome/index.vue"),
        meta: {
          title: $t("menus.pureHome"),
          showLink: VITE_HIDE_HOME === "true" ? false : true
        }
      }
    ]
  },
  // 登录路由
  {
    path: "/login",
    name: "Login",
    component: () => import("@/views/login/index.vue"),
    meta: {
      title: $t("menus.pureLogin"),
      showLink: false,
      rank: 101
    }
  },
  // 重定向路由
  {
    path: "/redirect",
    component: Layout,
    meta: {
      title: $t("status.pureLoad"),
      showLink: false,
      rank: 102
    },
    children: [
      {
        path: "/redirect/:path(.*)",
        name: "Redirect",
        component: () => import("@/layout/redirect.vue")
      }
    ]
  },
  // 账户设置路由
  {
    path: "/account-settings",
    name: "AccountSettings",
    component: () => import("@/views/account-settings/index.vue"),
    meta: {
      title: $t("buttons.pureAccountSettings"),
      showLink: false,
      rank: 104
    }
  },
  // 错误页面路由
  {
    path: "/error",
    component: Layout,
    redirect: "/error/403",
    meta: {
      title: $t("menus.pureAbnormal"),
      showLink: false,
      rank: 105
    },
    children: [
      {
        path: "/error/403",
        name: "403",
        component: () => import("@/views/error/403.vue"),
        meta: {
          title: $t("menus.pureFourZeroOne"),
          showLink: false
        }
      },
      {
        path: "/error/404",
        name: "404",
        component: () => import("@/views/error/404.vue"),
        meta: {
          title: $t("menus.pureFourZeroFour"),
          showLink: false
        }
      },
      {
        path: "/error/500",
        name: "500",
        component: () => import("@/views/error/500.vue"),
        meta: {
          title: $t("menus.pureFive"),
          showLink: false
        }
      }
    ]
  }
] satisfies Array<RouteConfigsTable>;
