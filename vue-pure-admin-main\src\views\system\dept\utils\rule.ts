import { reactive } from "vue";
import type { FormRules } from "element-plus";
import { isPhone, isEmail } from "@pureadmin/utils";

/**
 * 验证手机号或座机号
 * @param value 输入的号码
 * @returns 是否为有效的手机号或座机号
 */
function isPhoneOrTelephone(value: string): boolean {
  // 手机号验证（使用原有的 isPhone 函数）
  if (isPhone(value)) {
    return true;
  }

  // 座机号验证规则：区号-号码格式
  // 支持格式：010-12345678, 0931-1234567, 021-12345678
  const telephoneRegex = /^0\d{2,3}-\d{7,8}$/;

  return telephoneRegex.test(value);
}

/** 自定义表单规则校验 */
export const formRules = reactive(<FormRules>{
  deptName: [{ required: true, message: "部门名称为必填项", trigger: "blur" }],
  phone: [
    {
      validator: (rule, value, callback) => {
        if (value === "") {
          callback();
        } else if (!isPhoneOrTelephone(value)) {
          callback(new Error("请输入正确的手机号或座机号格式（如：13812345678 或 0931-1234567）"));
        } else {
          callback();
        }
      },
      trigger: "blur"
      // trigger: "click" // 如果想在点击确定按钮时触发这个校验，trigger 设置成 click 即可
    }
  ],
  email: [
    {
      validator: (_rule, value, callback) => {
        if (value === "") {
          callback();
        } else if (!isEmail(value)) {
          callback(new Error("请输入正确的邮箱格式"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ]
});
