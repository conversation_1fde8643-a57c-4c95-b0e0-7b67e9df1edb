package com.example.dto.system;

import lombok.Data;

/**
 * 用户查询DTO
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
public class UserQueryDTO {

    /**
     * 页码
     */
    private Integer page = 1;

    /**
     * 每页大小
     */
    private Integer size = 10;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 状态：0禁用、1启用
     */
    private Integer status;

    /**
     * 部门ID
     */
    private Integer deptId;

    /**
     * 角色ID
     */
    private Integer roleId;

    /**
     * 创建时间开始
     */
    private String createTimeStart;

    /**
     * 创建时间结束
     */
    private String createTimeEnd;

    /**
     * 排序字段
     */
    private String orderBy = "create_time";

    /**
     * 排序方向：asc、desc
     */
    private String orderDirection = "desc";
}
