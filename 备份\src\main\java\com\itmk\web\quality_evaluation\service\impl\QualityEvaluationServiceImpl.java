package com.itmk.web.quality_evaluation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itmk.utils.PageUtils;
import com.itmk.web.quality_evaluation.entity.QualityEvaluation;
import com.itmk.web.quality_evaluation.entity.QualityEvaluationParm;
import com.itmk.web.quality_evaluation.mapper.QualityEvaluationMapper;
import com.itmk.web.quality_evaluation.service.QualityEvaluationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 基本素质测评成绩Service实现类
 */
@Service
public class QualityEvaluationServiceImpl extends ServiceImpl<QualityEvaluationMapper, QualityEvaluation> implements QualityEvaluationService {

    private static final Logger logger = LoggerFactory.getLogger(QualityEvaluationServiceImpl.class);

    @Override
    public IPage<QualityEvaluation> getList(QualityEvaluationParm parm) {
        // 参数检查，防止空指针异常
        if (parm == null) {
            parm = new QualityEvaluationParm();
        }

        // 添加默认分页参数
        Long currentPage = parm.getCurrentPage() != null ? parm.getCurrentPage() : 1L;
        Long pageSize = parm.getPageSize() != null ? parm.getPageSize() : 10L;

        // 构造分页对象
        IPage<QualityEvaluation> page = PageUtils.createPage(currentPage, pageSize);
        IPage<QualityEvaluation> result = this.baseMapper.getList(page, parm);

        // 处理查询结果，确保第二学期的基础分正确设置
        if (result != null && result.getRecords() != null) {
            for (QualityEvaluation record : result.getRecords()) {
                // 检查是否是第二学期且有上一学期分数
                if (!isFirstSemester(record.getEvaluationPeriod()) &&
                    record.getPrevPeriodScore() != null &&
                    record.getPrevPeriodScore() > 0) {

                    // 如果periodScore不等于prevPeriodScore，则更新periodScore
                    if (record.getPeriodScore() == null ||
                        !record.getPeriodScore().equals(record.getPrevPeriodScore())) {
                        record.setPeriodScore(record.getPrevPeriodScore());
                    }
                }
            }
        }

        return result;
    }
    
    @Override
    public QualityEvaluation getByStudentIdAndPeriod(String studentId, String evaluationPeriod) {
        if (studentId == null || evaluationPeriod == null) {
            return null;
        }
        
        try {
            LambdaQueryWrapper<QualityEvaluation> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(QualityEvaluation::getStudentId, studentId)
                        .eq(QualityEvaluation::getEvaluationPeriod, evaluationPeriod)
                        .orderByDesc(QualityEvaluation::getCreateTime); // 按创建时间降序排列
            
            // 使用selectList而不是selectOne，以避免TooManyResultsException
            List<QualityEvaluation> records = this.baseMapper.selectList(queryWrapper);
            
            // 如果列表为空，返回null
            if (records == null || records.isEmpty()) {
                return null;
            }
            
            // 返回最新的记录（列表中的第一条）
            return records.get(0);
        } catch (Exception e) {
            return null;
        }
    }
    
    @Override
    public boolean isFirstSemester(String evaluationPeriod) {
        if (evaluationPeriod == null || evaluationPeriod.isEmpty()) {
            return false;
        }
        
        // 检查学期ID是否以-1结尾或包含"第一学期"/"上学期"字样
        boolean isFirst = evaluationPeriod.endsWith("-1") || 
               evaluationPeriod.contains("第一学期") || 
               evaluationPeriod.contains("上学期");
               
        // 检查是否明确是第二学期
        boolean isSecond = evaluationPeriod.endsWith("-2") || 
                evaluationPeriod.contains("第二学期") || 
                evaluationPeriod.contains("下学期");
                
        // 如果有明确标识，直接返回结果
        if (isFirst) {
            return true;
        }
        if (isSecond) {
            return false;
        }
        
        // 处理纯数字的学期ID
        try {
            int numId = Integer.parseInt(evaluationPeriod);
            // 奇数ID为第一学期，偶数ID为第二学期
            return numId % 2 == 1;
        } catch (NumberFormatException e) {
            // 不是纯数字ID，继续后续判断
        }
        
        // 尝试从学期ID中提取学期信息
        String[] parts = evaluationPeriod.split("-");
        if (parts.length >= 3) {
            try {
                int lastPart = Integer.parseInt(parts[parts.length - 1]);
                if (lastPart == 1) {
                    return true;
                }
                if (lastPart == 2) {
                    return false;
                }
            } catch (NumberFormatException e) {
                // 忽略解析错误
            }
        }
        
        // 检查是否以数字1结尾，但不是以01、11等结尾
        if (evaluationPeriod.endsWith("1") && 
            !evaluationPeriod.endsWith("01") && 
            !evaluationPeriod.endsWith("11") && 
            !evaluationPeriod.endsWith("21") && 
            !evaluationPeriod.endsWith("31") && 
            !evaluationPeriod.endsWith("41") && 
            !evaluationPeriod.endsWith("51") && 
            !evaluationPeriod.endsWith("61") && 
            !evaluationPeriod.endsWith("71") && 
            !evaluationPeriod.endsWith("81") && 
            !evaluationPeriod.endsWith("91")) {
            return true;
        }
        
        // 检查是否以数字2结尾，但不是以02、12等结尾
        if (evaluationPeriod.endsWith("2") && 
            !evaluationPeriod.endsWith("02") && 
            !evaluationPeriod.endsWith("12") && 
            !evaluationPeriod.endsWith("22") && 
            !evaluationPeriod.endsWith("32") && 
            !evaluationPeriod.endsWith("42") && 
            !evaluationPeriod.endsWith("52") && 
            !evaluationPeriod.endsWith("62") && 
            !evaluationPeriod.endsWith("72") && 
            !evaluationPeriod.endsWith("82") && 
            !evaluationPeriod.endsWith("92")) {
            return false;
        }
        
        // 默认情况下，如果无法确定，假设是第一学期
        return true;
    }
    
    @Override
    public double calculateAddScore(String addScoreRemark) {
        if (addScoreRemark == null || addScoreRemark.isEmpty()) {
            return 0;
        }
        
        List<ScoreItem> items = parseRemarkItems(addScoreRemark);
        double total = 0;
        
        for (ScoreItem item : items) {
            if ("+".equals(item.getSign())) {
                total += item.getScore();
            }
        }
        
        // 保留两位小数
        return new BigDecimal(total).setScale(2, RoundingMode.HALF_UP).doubleValue();
    }
    
    @Override
    public double calculateReduceScore(String reduceScoreRemark) {
        if (reduceScoreRemark == null || reduceScoreRemark.isEmpty()) {
            return 0;
        }
        
        List<ScoreItem> items = parseRemarkItems(reduceScoreRemark);
        double total = 0;
        
        for (ScoreItem item : items) {
            if ("-".equals(item.getSign())) {
                total += item.getScore();
            }
        }
        
        // 保留两位小数
        return new BigDecimal(total).setScale(2, RoundingMode.HALF_UP).doubleValue();
    }
    
    @Override
    public void calculateTotalScore(QualityEvaluation qualityEvaluation) {
        if (qualityEvaluation == null) {
            return;
        }
        
        // 计算加分和扣分
        if (qualityEvaluation.getAddScoreRemark() != null) {
            qualityEvaluation.setAddScore(calculateAddScore(qualityEvaluation.getAddScoreRemark()));
        }
        
        if (qualityEvaluation.getReduceScoreRemark() != null) {
            qualityEvaluation.setReduceScore(calculateReduceScore(qualityEvaluation.getReduceScoreRemark()));
        }
        
        // 获取基础分
        double baseScore = 0;
        
        // 判断是否是第一学期
        boolean isFirstSemester = isFirstSemester(qualityEvaluation.getEvaluationPeriod());
        
        if (isFirstSemester) {
            // 第一学期基础分为60
            baseScore = 60;
            qualityEvaluation.setPeriodScore(baseScore);
            qualityEvaluation.setPrevPeriodScore(null); // 第一学期没有上一学期记录
        } else if (qualityEvaluation.getPrevPeriodScore() != null && qualityEvaluation.getPrevPeriodScore() > 0) {
            // 第二学期基础分为上一学期的总分
            baseScore = qualityEvaluation.getPrevPeriodScore();
            qualityEvaluation.setPeriodScore(baseScore);
        } else {
            // 如果没有上一学期分数，使用当前设置的周期得分，如果没有则默认为60
            baseScore = qualityEvaluation.getPeriodScore() != null ? qualityEvaluation.getPeriodScore() : 60;
        }
        
        // 计算总分 = 基础分 + 加分 - 扣分
        double addScore = qualityEvaluation.getAddScore() != null ? qualityEvaluation.getAddScore() : 0;
        double reduceScore = qualityEvaluation.getReduceScore() != null ? qualityEvaluation.getReduceScore() : 0;
        double totalScore = baseScore + addScore - reduceScore;
        
        // 确保总分不为负数
        if (totalScore < 0) {
            totalScore = 0;
        }
        
        // 保留两位小数
        totalScore = new BigDecimal(totalScore).setScale(2, RoundingMode.HALF_UP).doubleValue();
        
        // 设置总分
        qualityEvaluation.setTotalScore(totalScore);
    }
    
    @Override
    public String getPreviousSemesterId(String currentSemesterId) {
        if (currentSemesterId == null || currentSemesterId.isEmpty()) {
            return null;
        }
        
        // 检查当前学期是否是第一学期
        if (isFirstSemester(currentSemesterId)) {
            return null;
        }
        
        // 处理形如 "2023-2024-2" 的学期ID，转换为 "2023-2024-1"
        if (currentSemesterId.matches(".*-\\d+$")) {
            // 将学期ID按"-"分割
            String[] parts = currentSemesterId.split("-");
            if (parts.length >= 3) {
                try {
                    int lastPart = Integer.parseInt(parts[parts.length - 1]);
                    if (lastPart == 2) {
                        // 替换最后一部分为1
                        StringBuilder sb = new StringBuilder();
                        for (int i = 0; i < parts.length - 1; i++) {
                            sb.append(parts[i]).append("-");
                        }
                        sb.append("1");
                        return sb.toString();
                    }
                } catch (NumberFormatException e) {
                    // 忽略解析错误
                }
            }
        }
        
        // 处理包含关键字的学期ID
        if (currentSemesterId.contains("第二学期")) {
            return currentSemesterId.replace("第二学期", "第一学期");
        } else if (currentSemesterId.contains("下学期")) {
            return currentSemesterId.replace("下学期", "上学期");
        }
        
        // 处理纯数字学期ID
        try {
            int semesterId = Integer.parseInt(currentSemesterId);
            if (semesterId % 2 == 0) {  // 偶数为第二学期
                return String.valueOf(semesterId - 1);
            }
        } catch (NumberFormatException e) {
            // 不是纯数字ID，继续后续判断
        }
        
        // 处理末尾是单独数字"2"的学期ID
        if (currentSemesterId.endsWith("2") && 
            !currentSemesterId.endsWith("02") && 
            !currentSemesterId.endsWith("12") && 
            !currentSemesterId.endsWith("22") && 
            !currentSemesterId.endsWith("32") && 
            !currentSemesterId.endsWith("42") && 
            !currentSemesterId.endsWith("52") && 
            !currentSemesterId.endsWith("62") && 
            !currentSemesterId.endsWith("72") && 
            !currentSemesterId.endsWith("82") && 
            !currentSemesterId.endsWith("92")) {
            return currentSemesterId.substring(0, currentSemesterId.length() - 1) + "1";
        }
        
        // 如果无法确定上一学期ID，返回null
        return null;
    }
    
    /**
     * 解析加分说明和扣分说明
     * @param remark 说明文本
     * @return 解析后的加减分项列表
     */
    private List<ScoreItem> parseRemarkItems(String remark) {
        List<ScoreItem> items = new ArrayList<>();
        if (remark == null || remark.isEmpty()) {
            return items;
        }
        
        // 按行分割
        String[] lines = remark.split("\n");
        
        for (String line : lines) {
            // 去除空行
            line = line.trim();
            if (line.isEmpty()) {
                continue;
            }
            
            // 移除可能的序号前缀，如 "1. " 或 "1、"
            Pattern prefixPattern = Pattern.compile("^(\\d+[\\.、])\\s*");
            Matcher prefixMatcher = prefixPattern.matcher(line);
            if (prefixMatcher.find()) {
                line = line.substring(prefixMatcher.end());
            }
            
            // 尝试多种模式匹配加减分
            boolean matched = false;
            
            // 模式0：带有乘法表达式的模式，如 "-2*2" 或 "+3*5"
            Pattern multiplyPattern = Pattern.compile("(.+?)\\s*(\\+|\\-)(\\d+(\\.\\d+)?)\\*(\\d+)\\s*$");
            Matcher multiplyMatcher = multiplyPattern.matcher(line);
            
            if (multiplyMatcher.find()) {
                String text = multiplyMatcher.group(1).trim();
                String sign = multiplyMatcher.group(2);
                double baseScore = Double.parseDouble(multiplyMatcher.group(3));
                int multiplier = Integer.parseInt(multiplyMatcher.group(5));
                double score = baseScore * multiplier;
                
                items.add(new ScoreItem(text, score, sign));
                matched = true;
            }
            
            // 模式1：末尾有明确的加减分数值 "+5", "-2"等
            if (!matched) {
            Pattern endScorePattern = Pattern.compile("(.+?)\\s*(\\+|\\-)(\\d+(\\.\\d+)?)\\s*$");
            Matcher endScoreMatcher = endScorePattern.matcher(line);
            
            if (endScoreMatcher.find()) {
                String text = endScoreMatcher.group(1).trim();
                String sign = endScoreMatcher.group(2);
                double score = Double.parseDouble(endScoreMatcher.group(3));
                
                items.add(new ScoreItem(text, score, sign));
                    matched = true;
                }
            }
            
            // 模式2：字符串中包含带乘法的 "+数字*数字" 或 "-数字*数字" 格式
            if (!matched) {
                Pattern inlineMultiplyPattern = Pattern.compile("(.+)(\\+|\\-)(\\d+(\\.\\d+)?)\\*(\\d+)(.*)");
                Matcher inlineMultiplyMatcher = inlineMultiplyPattern.matcher(line);
                
                if (inlineMultiplyMatcher.find()) {
                    String prefix = inlineMultiplyMatcher.group(1).trim();
                    String sign = inlineMultiplyMatcher.group(2);
                    double baseScore = Double.parseDouble(inlineMultiplyMatcher.group(3));
                    int multiplier = Integer.parseInt(inlineMultiplyMatcher.group(5));
                    String suffix = inlineMultiplyMatcher.group(6).trim();
                    
                    double score = baseScore * multiplier;
                    
                    String text = prefix;
                    if (!suffix.isEmpty()) {
                        text += " " + suffix;
                    }
                    
                    items.add(new ScoreItem(text, score, sign));
                matched = true;
                }
            }
            
            // 模式3：字符串中包含 "+数字" 或 "-数字" 格式
            if (!matched) {
                Pattern inlineScorePattern = Pattern.compile("(.+)(\\+|\\-)(\\d+(\\.\\d+)?)(.*)");
                Matcher inlineScoreMatcher = inlineScorePattern.matcher(line);
                
                if (inlineScoreMatcher.find()) {
                    String prefix = inlineScoreMatcher.group(1).trim();
                    String sign = inlineScoreMatcher.group(2);
                    double score = Double.parseDouble(inlineScoreMatcher.group(3));
                    String suffix = inlineScoreMatcher.group(5).trim();
                    
                    String text = prefix;
                    if (!suffix.isEmpty()) {
                        text += " " + suffix;
                    }
                    
                    items.add(new ScoreItem(text, score, sign));
                    matched = true;
                }
            }
            
            // 模式4：特殊格式，如"校级先进班集体 +5" 或 "管理部个人卫生差 -2"
            if (!matched) {
                // 先尝试匹配带乘法的特殊模式
                Pattern specialMultiplyPattern = Pattern.compile("(\\+|\\-)(\\d+(\\.\\d+)?)\\*(\\d+)");
                Matcher specialMultiplyMatcher = specialMultiplyPattern.matcher(line);
                
                if (specialMultiplyMatcher.find()) {
                    String sign = specialMultiplyMatcher.group(1);
                    double baseScore = Double.parseDouble(specialMultiplyMatcher.group(2));
                    int multiplier = Integer.parseInt(specialMultiplyMatcher.group(4));
                    double score = baseScore * multiplier;
                    String text = line.replace(sign + specialMultiplyMatcher.group(2) + "*" + multiplier, "").trim();
                    
                    items.add(new ScoreItem(text, score, sign));
                    matched = true;
                } else {
                    // 常规特殊模式
                Pattern specialPattern = Pattern.compile("(\\+|\\-)(\\d+(\\.\\d+)?)");
                Matcher specialMatcher = specialPattern.matcher(line);
                
                while (specialMatcher.find()) {
                    String sign = specialMatcher.group(1);
                    double score = Double.parseDouble(specialMatcher.group(2));
                    String text = line.replace(sign + specialMatcher.group(2), "").trim();
                    
                    items.add(new ScoreItem(text, score, sign));
                    matched = true;
                    break;  // 只取第一个匹配项
                    }
                }
            }
            
            // 模式5：分隔形式，如"XXX（XXX） +5"
            if (!matched) {
                // 先尝试匹配带乘法的分隔模式
                Pattern separatedMultiplyPattern = Pattern.compile("(.+?)\\s*(\\+|\\-)\\s*(\\d+(\\.\\d+)?)\\*(\\d+)");
                Matcher separatedMultiplyMatcher = separatedMultiplyPattern.matcher(line);
                
                if (separatedMultiplyMatcher.find()) {
                    String text = separatedMultiplyMatcher.group(1).trim();
                    String sign = separatedMultiplyMatcher.group(2);
                    double baseScore = Double.parseDouble(separatedMultiplyMatcher.group(3));
                    int multiplier = Integer.parseInt(separatedMultiplyMatcher.group(5));
                    double score = baseScore * multiplier;
                    
                    items.add(new ScoreItem(text, score, sign));
                    matched = true;
                } else {
                    // 常规分隔模式
                Pattern separatedPattern = Pattern.compile("(.+?)\\s*(\\+|\\-)\\s*(\\d+(\\.\\d+)?)");
                Matcher separatedMatcher = separatedPattern.matcher(line);
                
                if (separatedMatcher.find()) {
                    String text = separatedMatcher.group(1).trim();
                    String sign = separatedMatcher.group(2);
                    double score = Double.parseDouble(separatedMatcher.group(3));
                    
                    items.add(new ScoreItem(text, score, sign));
                    matched = true;
                    }
                }
            }
            
            // 如果所有模式都没匹配成功
            if (!matched) {
                // 尝试匹配带乘法的兜底模式
                Pattern fallbackMultiplyPattern = Pattern.compile(".*(\\d+(\\.\\d+)?)\\*(\\d+)$");
                Matcher fallbackMultiplyMatcher = fallbackMultiplyPattern.matcher(line);
                
                if (fallbackMultiplyMatcher.find()) {
                    double baseScore = Double.parseDouble(fallbackMultiplyMatcher.group(1));
                    int multiplier = Integer.parseInt(fallbackMultiplyMatcher.group(3));
                    double score = baseScore * multiplier;
                    String text = line.substring(0, fallbackMultiplyMatcher.start()).trim();
                    
                    // 根据上下文判断是加分还是扣分
                    String sign = "+";  // 默认为加分
                    if (text.contains("扣") || text.contains("减") || text.contains("违") || 
                        text.contains("差") || text.contains("降") || text.contains("罚")) {
                        sign = "-";
                    }
                    
                    items.add(new ScoreItem(text, score, sign));
                } else {
                    // 常规兜底模式
                Pattern fallbackPattern = Pattern.compile(".*(\\d+(\\.\\d+)?)$");
                Matcher fallbackMatcher = fallbackPattern.matcher(line);
                
                if (fallbackMatcher.find()) {
                    double score = Double.parseDouble(fallbackMatcher.group(1));
                    String text = line.substring(0, fallbackMatcher.start()).trim();
                    
                    // 根据上下文判断是加分还是扣分
                    String sign = "+";  // 默认为加分
                    if (text.contains("扣") || text.contains("减") || text.contains("违") || 
                        text.contains("差") || text.contains("降") || text.contains("罚")) {
                        sign = "-";
                    }
                    
                    items.add(new ScoreItem(text, score, sign));
                } else {
                    // 完全无法解析，作为0分项添加
                    items.add(new ScoreItem(line, 0, "+"));
                    }
                }
            }
        }
        
        return items;
    }
    
    /**
     * 加减分项内部类
     */
    private static class ScoreItem {
        private String text;
        private double score;
        private String sign;
        
        public ScoreItem(String text, double score, String sign) {
            this.text = text;
            this.score = score;
            this.sign = sign;
        }
        
        public String getText() {
            return text;
        }
        
        public double getScore() {
            return score;
        }
        
        public String getSign() {
            return sign;
        }
    }
    
    /**
     * 更新第二学期的prev_period_score字段为上一学期的总分
     */
    @Override
    public int updatePrevPeriodScore() {
        return this.baseMapper.updatePrevPeriodScore();
    }
    
    /**
     * 更新第二学期的period_score字段为prev_period_score
     */
    @Override
    public int updatePeriodScoreFromPrev() {
        return this.baseMapper.updatePeriodScoreFromPrev();
    }
    
    @Override
    public void prepareScores(QualityEvaluation qualityEvaluation) {
        if (qualityEvaluation == null) {
            return;
        }

        try {
            // 计算加分和扣分
            double addScore = calculateAddScore(qualityEvaluation.getAddScoreRemark());
            double reduceScore = calculateReduceScore(qualityEvaluation.getReduceScoreRemark());

            qualityEvaluation.setAddScore(addScore);
            qualityEvaluation.setReduceScore(reduceScore);

            // 获取基础分
            double baseScore = getBaseScore(qualityEvaluation);

            // 计算总分 = 基础分 + 加分 - 扣分
            double totalScore = baseScore + addScore - reduceScore;

            // 确保总分不为负数
            if (totalScore < 0) {
                totalScore = 0;
            }

            // 保留两位小数
            totalScore = new BigDecimal(totalScore).setScale(2, RoundingMode.HALF_UP).doubleValue();

            // 设置总分
            qualityEvaluation.setTotalScore(totalScore);

            // 记录日志用于调试
            logger.info("分数计算完成 - 学生ID: {}, 学期: {}, 基础分: {}, 加分: {}, 扣分: {}, 总分: {}",
                qualityEvaluation.getStudentId(),
                qualityEvaluation.getEvaluationPeriod(),
                baseScore, addScore, reduceScore, totalScore);

        } catch (Exception e) {
            logger.error("计算分数时发生错误: {}", e.getMessage(), e);
            throw new RuntimeException("分数计算失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public double getBaseScore(QualityEvaluation qualityEvaluation) {
        if (qualityEvaluation == null) {
            return 60.0;
        }

        try {
            // 判断是否是第一学期
            boolean isFirstSemester = isFirstSemester(qualityEvaluation.getEvaluationPeriod());

            if (isFirstSemester) {
                // 第一学期基础分为60
                qualityEvaluation.setPeriodScore(60.0);
                qualityEvaluation.setPrevPeriodScore(null); // 第一学期没有上一学期记录
                return 60.0;
            } else {
                // 第二学期，优先使用已设置的上一学期总分
                if (qualityEvaluation.getPrevPeriodScore() != null && qualityEvaluation.getPrevPeriodScore() > 0) {
                    double prevScore = qualityEvaluation.getPrevPeriodScore();
                    qualityEvaluation.setPeriodScore(prevScore);
                    return prevScore;
                }

                // 如果没有设置上一学期分数，尝试查询数据库
                String prevSemesterId = getPreviousSemesterId(qualityEvaluation.getEvaluationPeriod());
                if (prevSemesterId != null && qualityEvaluation.getStudentId() != null) {
                    QualityEvaluation prevRecord = getByStudentIdAndPeriod(
                        qualityEvaluation.getStudentId(), prevSemesterId);
                    if (prevRecord != null && prevRecord.getTotalScore() != null && prevRecord.getTotalScore() > 0) {
                        // 设置上一学期的总分作为本学期的基础分
                        double prevScore = prevRecord.getTotalScore();
                        qualityEvaluation.setPrevPeriodScore(prevScore);
                        qualityEvaluation.setPeriodScore(prevScore);
                        return prevScore;
                    }
                }

                // 如果找不到上一学期的记录，检查是否已有周期得分设置
                if (qualityEvaluation.getPeriodScore() != null && qualityEvaluation.getPeriodScore() > 0) {
                    return qualityEvaluation.getPeriodScore();
                }

                // 最后使用默认值60，但给出警告
                logger.warn("学生ID {} 的第二学期（{}）未找到上一学期记录，使用默认基础分60",
                    qualityEvaluation.getStudentId(), qualityEvaluation.getEvaluationPeriod());
                qualityEvaluation.setPeriodScore(60.0);
                return 60.0;
            }
        } catch (Exception e) {
            logger.error("获取基础分时发生错误: {}", e.getMessage(), e);
            // 发生异常时返回默认值
            qualityEvaluation.setPeriodScore(60.0);
            return 60.0;
        }
    }

    /**
     * 确保第二学期数据的一致性
     * @param qualityEvaluation 基本素质测评成绩
     */
    public void ensureSecondSemesterConsistency(QualityEvaluation qualityEvaluation) {
        if (qualityEvaluation == null || isFirstSemester(qualityEvaluation.getEvaluationPeriod())) {
            return;
        }

        try {
            // 获取上一学期记录
            String prevSemesterId = getPreviousSemesterId(qualityEvaluation.getEvaluationPeriod());
            if (prevSemesterId != null && qualityEvaluation.getStudentId() != null) {
                QualityEvaluation prevRecord = getByStudentIdAndPeriod(
                    qualityEvaluation.getStudentId(), prevSemesterId);

                if (prevRecord != null && prevRecord.getTotalScore() != null) {
                    // 确保prevPeriodScore和periodScore一致
                    double prevTotalScore = prevRecord.getTotalScore();
                    qualityEvaluation.setPrevPeriodScore(prevTotalScore);
                    qualityEvaluation.setPeriodScore(prevTotalScore);

                    logger.info("数据一致性检查：学生ID {}，第二学期基础分已设置为上一学期总分 {}",
                        qualityEvaluation.getStudentId(), prevTotalScore);
                }
            }
        } catch (Exception e) {
            logger.error("确保第二学期数据一致性时发生错误: {}", e.getMessage(), e);
        }
    }

    @Override
    public int recalculateSecondSemesterTotalScores() {
        try {
            long startTime = System.currentTimeMillis();
            int result = this.baseMapper.recalculateSecondSemesterTotalScores();
            long endTime = System.currentTimeMillis();
            logger.info("批量重新计算第二学期总分完成，更新了{}条记录，耗时{}ms", result, (endTime - startTime));
            return result;
        } catch (Exception e) {
            logger.error("批量重新计算第二学期总分时发生错误: {}", e.getMessage(), e);
            return 0;
        }
    }
    
    @Override
    public String resolveSemesterId(String semesterName, Map<String, String> semesterMap) {
        if (semesterName == null || semesterName.isEmpty()) {
            return null;
        }
        
        // 直接匹配
        if (semesterMap.containsKey(semesterName)) {
            return semesterMap.get(semesterName);
        }
        
        // 尝试模糊匹配
        for (Map.Entry<String, String> entry : semesterMap.entrySet()) {
            if (entry.getKey().contains(semesterName) || 
                semesterName.contains(entry.getKey()) ||
                semesterName.replace(" ", "").contains(entry.getKey()) ||
                entry.getKey().replace(" ", "").contains(semesterName.replace(" ", ""))) {
                return entry.getValue();
            }
        }
        
        // 尝试提取学年和学期信息进行匹配
        // 提取类似 "2023-2024-1" 的格式
        String pattern = "(\\d{4})[-\\s]*(\\d{4})[-\\s]*(\\d+|一|二|三|四|上|下|第一|第二|第三|第四)";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(semesterName);
        
        if (m.find()) {
            String startYear = m.group(1);
            String endYear = m.group(2);
            String semester = m.group(3);
            
            // 转换中文学期序号为数字
            String semesterNum;
            if (semester.equals("一") || semester.equals("上") || semester.equals("第一")) {
                semesterNum = "1";
            } else if (semester.equals("二") || semester.equals("下") || semester.equals("第二")) {
                semesterNum = "2";
            } else {
                semesterNum = semester;
            }
            
            String formattedSemester = startYear + "-" + endYear + "-" + semesterNum;
            
            // 检查是否匹配任何学期
            if (semesterMap.containsKey(formattedSemester)) {
                return semesterMap.get(formattedSemester);
            }
        }
        
        // 处理特殊情况：根据关键字尝试匹配第一学期或第二学期
        if (semesterName.contains("第一") || semesterName.contains("一") || semesterName.contains("1")) {
            // 尝试查找以"-1"结尾的学期ID
            for (Map.Entry<String, String> entry : semesterMap.entrySet()) {
                if (entry.getKey().endsWith("-1")) {
                    return entry.getValue();
                }
            }
        } else if (semesterName.contains("第二") || semesterName.contains("二") || semesterName.contains("2")) {
            // 尝试查找以"-2"结尾的学期ID
            for (Map.Entry<String, String> entry : semesterMap.entrySet()) {
                if (entry.getKey().endsWith("-2")) {
                    return entry.getValue();
                }
            }
        }
        
        // 无法匹配
        return null;
    }
} 