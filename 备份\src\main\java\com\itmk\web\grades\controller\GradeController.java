package com.itmk.web.grades.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.itmk.config.jwt.UserContext;
import com.itmk.utils.*;
import com.itmk.utils.excel.ExcelUtils;
import com.itmk.service.UniversalExcelService;
import com.itmk.web.common.entity.Course;
import com.itmk.web.common.entity.Semester;
import com.itmk.web.common.service.CourseService;
import com.itmk.web.common.service.SemesterService;
import com.itmk.web.grades.entity.Grade;
import com.itmk.web.grades.entity.GradeParm;
import com.itmk.web.grades.service.GradeService;
import com.itmk.web.school_student.entity.SchoolStudent;
import com.itmk.web.school_student.service.SchoolStudentService;
import com.itmk.web.quality_evaluation.entity.QualityEvaluation;
import com.itmk.web.quality_evaluation.service.QualityEvaluationService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import org.springframework.web.multipart.MultipartFile;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/grade")
public class GradeController {

    private static final Logger logger = LoggerFactory.getLogger(GradeController.class);

    @Autowired
    private GradeService gradeService;

    @Autowired
    private SchoolStudentService schoolStudentService;

    @Autowired
    private CourseService courseService;

    @Autowired
    private SemesterService semesterService;

    @Autowired
    private QualityEvaluationService qualityEvaluationService;

    /**
     * 解析学期ID参数的工具方法
     */
    private List<Integer> parseSemesterIds(Integer semesterId, Integer paramsSemesterId,
                                         List<Integer> semesterIds, List<Integer> paramsSemesterIds,
                                         String academicYear, String paramsAcademicYear) {
        List<Integer> finalSemesterIds = new ArrayList<>();
        String finalAcademicYear = paramsAcademicYear != null ? paramsAcademicYear : academicYear;

        // 优先级：semesterIds > semesterId > 学年查询
        if (semesterIds != null && !semesterIds.isEmpty()) {
            finalSemesterIds = semesterIds;
        } else if (paramsSemesterIds != null && !paramsSemesterIds.isEmpty()) {
            finalSemesterIds = paramsSemesterIds;
        } else if (semesterId != null) {
            finalSemesterIds.add(semesterId);
        } else if (paramsSemesterId != null) {
            finalSemesterIds.add(paramsSemesterId);
        } else if (StringUtils.isNotBlank(finalAcademicYear)) {
            QueryWrapper<Semester> semesterQuery = new QueryWrapper<>();
            semesterQuery.lambda().eq(Semester::getAcademicYear, finalAcademicYear);
            List<Semester> semesters = semesterService.list(semesterQuery);

            if (semesters.isEmpty()) {
                semesterQuery = new QueryWrapper<>();
                semesterQuery.lambda().like(Semester::getSemesterName, finalAcademicYear);
                semesters = semesterService.list(semesterQuery);
            }

            if (!semesters.isEmpty()) {
                finalSemesterIds = semesters.stream()
                        .map(Semester::getSemesterId)
                        .collect(Collectors.toList());
            }
        }

        return finalSemesterIds;
    }

    /**
     * 获取成绩详情
     */
    @GetMapping("/{gradeId}")
    public ResultVo getGradeById(@PathVariable("gradeId") Integer gradeId) {
        Grade grade = gradeService.getById(gradeId);
        return ResultUtils.success("查询成功", grade);
    }

    /**
     * 新增
     */
    @PostMapping
    public ResultVo add(@RequestBody Grade grade){
        boolean save = gradeService.save(grade);
        if(save){
            return ResultUtils.success("新增成功!");
        }
        return ResultUtils.error("新增失败!");
    }

    /**
     * 编辑
     */
    @PutMapping
    public ResultVo edit(@RequestBody Grade grade){
        // 参数验证
        if (grade == null || grade.getStudentId() == null || grade.getCourseCode() == null) {
            return ResultUtils.error("缺少必要参数!");
        }

        try {
            // 如果没有提供gradeId，则根据学生ID、课程代码和学期ID查找成绩记录
            if (grade.getGradeId() == null) {
                // 先构造基础查询条件：学生ID + 课程代码
                QueryWrapper<Grade> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda()
                        .eq(Grade::getStudentId, grade.getStudentId())
                        .eq(Grade::getCourseCode, grade.getCourseCode());

                // 如果提供了学期ID，则添加到查询条件
                if (grade.getSemesterId() != null) {
                    queryWrapper.lambda().eq(Grade::getSemesterId, grade.getSemesterId());
                }

                Grade existingGrade = gradeService.getOne(queryWrapper);

                if (existingGrade != null) {
                    // 更新已有记录
                    existingGrade.setGrade(grade.getGrade()); // 可以是null
                    if (grade.getRemarks() != null) {
                        existingGrade.setRemarks(grade.getRemarks());
                    }
                    // 手动构建更新参数，确保包含grade字段
                    boolean updated = false;
                    try {
                        // 直接使用SQL更新语句，确保包含grade字段
                        updated = gradeService.lambdaUpdate()
                                .eq(Grade::getGradeId, existingGrade.getGradeId())
                                .set(Grade::getStudentId, existingGrade.getStudentId())
                                .set(Grade::getCourseCode, existingGrade.getCourseCode())
                                .set(Grade::getSemesterId, existingGrade.getSemesterId())
                                .set(Grade::getGrade, existingGrade.getGrade())
                                .set(Grade::getRemarks, existingGrade.getRemarks())
                                .update();
                    } catch (Exception e) {
                        // 如果上面的方法失败，尝试使用updateById
                        updated = gradeService.updateById(existingGrade);
                    }
                    if (updated) {
                        return ResultUtils.success("编辑成功!");
                    } else {
                        return ResultUtils.error("编辑失败!");
                    }
                } else {
                    // 如果记录不存在且提供了学期ID，则创建新记录
                    if (grade.getSemesterId() != null) {
                        boolean saved = gradeService.save(grade);
                        if (saved) {
                            return ResultUtils.success("新增成功!");
                        } else {
                            return ResultUtils.error("新增失败!");
                        }
                    } else {
                        return ResultUtils.error("成绩记录不存在且未提供学期ID!");
                    }
                }
            } else {
                // 直接通过ID更新
                boolean updated = gradeService.updateById(grade);
                if (updated) {
                    return ResultUtils.success("编辑成功!");
                } else {
                    return ResultUtils.error("编辑失败!");
                }
            }
        } catch (Exception e) {
            return ResultUtils.error("编辑失败: " + e.getMessage());
        }
    }

    /**
     * 删除
     */
    @DeleteMapping("/{gradeId}")
    public ResultVo delete(@PathVariable("gradeId") Integer gradeId){
        boolean save = gradeService.removeById(gradeId);
        if(save){
            return ResultUtils.success("删除成功!");
        }
        return ResultUtils.error("删除失败!");
    }

    /**
     * 列表
     */
    @GetMapping("/list")
    public ResultVo list(GradeParm parm){
        try {
            // 数据权限控制：如果是学生登录，只能查看自己的成绩
            if (UserContext.isStudent()) {
                String currentUsername = UserContext.getCurrentUsername();
                if (currentUsername != null) {
                    // 设置查询条件为当前学生的学号
                    if (parm == null) {
                        parm = new GradeParm();
                    }
                    parm.setStudentId(currentUsername);
                } else {
                    return ResultUtils.error("无法获取当前用户信息");
                }
            }

            // 检查是否是根据学生ID和课程代码查询特定成绩
            if (parm != null && StringUtils.isNotEmpty(parm.getStudentId()) && StringUtils.isNotEmpty(parm.getCourseCode())) {
                // 构造查询条件
                QueryWrapper<Grade> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda()
                        .eq(Grade::getStudentId, parm.getStudentId())
                        .eq(Grade::getCourseCode, parm.getCourseCode());

                // 直接查询符合条件的成绩记录
                List<Grade> grades = gradeService.list(queryWrapper);

                // 构建分页结果
                IPage<Grade> page = PageUtils.createPage(parm.getCurrentPage(), parm.getPageSize());
                page.setRecords(grades);
                page.setTotal(grades.size());

                return ResultUtils.success("查询成功", page);
            } else {
                // 正常分页查询
                IPage<Grade> list = gradeService.getList(parm);
                return ResultUtils.success("查询成功", list);
            }
        } catch (Exception e) {
            return ResultUtils.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 按学号姓名横向输出成绩
     */

    @GetMapping("/exportHorizontal")
    public ResultVo exportHorizontalGrades(
            @RequestParam(required = false) Integer semesterId,
            @RequestParam(value = "semesterIds[]", required = false) List<Integer> semesterIds,
            @RequestParam(required = false) String academicYear,
            @RequestParam(required = false) String studentId,
            @RequestParam(required = false, defaultValue = "1") Long currentPage,
            @RequestParam(required = false, defaultValue = "10") Long pageSize) {
        try {
            // 数据权限控制：如果是学生登录，只能查看自己的成绩
            String finalStudentId = studentId;
            if (UserContext.isStudent()) {
                String currentUsername = UserContext.getCurrentUsername();
                if (currentUsername != null) {
                    finalStudentId = currentUsername;
                } else {
                    return ResultUtils.error("无法获取当前用户信息");
                }
            }

            // 解析学期ID参数
            List<Integer> finalSemesterIds = parseSemesterIds(semesterId, null,
                                                            semesterIds, null,
                                                            academicYear, null);

            if (finalSemesterIds.isEmpty()) {
                return ResultUtils.error("未找到有效的学期数据");
            }

            // 设置学期名称
            String semesterName = "";
            if (StringUtils.isNotBlank(academicYear)) {
                semesterName = academicYear + "学年";
            }

            // 如果有学期ID但没有学期名称，查询学期名称
            if (!finalSemesterIds.isEmpty() && StringUtils.isBlank(semesterName)) {
                List<Semester> semesters = semesterService.listByIds(finalSemesterIds);
                if (!semesters.isEmpty()) {
                    semesterName = semesters.stream()
                            .map(Semester::getSemesterName)
                            .collect(Collectors.joining(", "));
                }
            }

            // 先查询选定学期的成绩
            QueryWrapper<Grade> gradeQueryWrapper = new QueryWrapper<>();
            if (!finalSemesterIds.isEmpty()) {
                gradeQueryWrapper.lambda().in(Grade::getSemesterId, finalSemesterIds);
            }
            // 如果指定了学生ID，添加学生ID过滤
            if (StringUtils.isNotBlank(finalStudentId)) {
                gradeQueryWrapper.lambda().eq(Grade::getStudentId, finalStudentId);
            }
            List<Grade> semesterGrades = gradeService.list(gradeQueryWrapper);

            if (semesterGrades.isEmpty()) {
                // 返回空数据结构而不是错误信息
                Map<String, Object> emptyResult = new HashMap<>();
                emptyResult.put("courses", new ArrayList<>());
                emptyResult.put("courseInfos", new ArrayList<>());
                emptyResult.put("students", new ArrayList<>());
                emptyResult.put("total", 0);
                emptyResult.put("currentPage", currentPage);
                emptyResult.put("pageSize", pageSize);

                return ResultUtils.success("查询成功", emptyResult);
            }

            // 获取有成绩的学生ID列表
            List<String> studentIdsWithGrades = semesterGrades.stream()
                    .map(Grade::getStudentId)
                    .distinct()
                    .collect(Collectors.toList());

            // 只查询有成绩的学生
            QueryWrapper<SchoolStudent> studentQueryWrapper = new QueryWrapper<>();
            studentQueryWrapper.lambda().in(SchoolStudent::getStudentId, studentIdsWithGrades);

            // 计算总数
            int total = schoolStudentService.count(studentQueryWrapper);

            // 分页查询学生
            IPage<SchoolStudent> studentPage = PageUtils.createPage(currentPage, pageSize);
            IPage<SchoolStudent> studentIPage = schoolStudentService.page(studentPage, studentQueryWrapper);
            List<SchoolStudent> students = studentIPage.getRecords();

            if (students == null || students.isEmpty()) {
                return ResultUtils.error("没有找到学生信息");
            }

            // 查询指定学期的课程
            List<Course> courses;
            QueryWrapper<Course> courseQuery = new QueryWrapper<>();

            // 优先使用学期ID直接查询课程
            if (finalSemesterIds != null && !finalSemesterIds.isEmpty()) {
                courseQuery.lambda().in(Course::getSemesterId, finalSemesterIds);
            }
            // 如果没有学期ID但有学期名称，尝试通过名称查询
            else if (semesterName != null && StringUtils.isNotEmpty(semesterName)) {
                try {
                    // 先查询semesterName对应的semesterId
                    Integer foundSemesterId = courseService.getSemesterIdByName(semesterName);
                    if (foundSemesterId != null) {
                        courseQuery.lambda().eq(Course::getSemesterId, foundSemesterId);
                        // 更新finalSemesterIds，用于后续查询
                        finalSemesterIds.add(foundSemesterId);
                    }
                } catch (Exception e) {
                    // 忽略课程查询错误
                }
            }

            // 按照课程ID排序
            courseQuery.lambda().orderByAsc(Course::getId);
            courses = courseService.list(courseQuery);

            // 使用有序的课程列表
            List<String> orderedCourseList = courses.stream()
                    .map(Course::getCourseCode)
                    .collect(Collectors.toList());

            // 查询所有成绩，如果指定了学期，则按学期筛选
            QueryWrapper<Grade> queryWrapper = new QueryWrapper<>();
            // 只选择数据库中存在的字段
            queryWrapper.select("grade_id", "student_id", "course_code", "semester_id", "grade", "remarks");
            if (!finalSemesterIds.isEmpty()) {
                queryWrapper.lambda().in(Grade::getSemesterId, finalSemesterIds);
            }
            List<Grade> allGrades = gradeService.list(queryWrapper);

            // 获取学业成绩数据
            List<Map<String, Object>> gpaList;
            if (finalSemesterIds.isEmpty()) {
                gpaList = gradeService.getStudentGpaById(null);
            } else {
                gpaList = gradeService.getStudentGpaByIds(finalSemesterIds);
            }

            // 转换为studentId -> calculateGpa和academicScore的映射
            Map<String, Double> studentGpaMap = gpaList.stream()
                    .collect(Collectors.toMap(
                            map -> map.get("studentId").toString(),
                            map -> map.get("calculateGpa") == null ? 0.0 : Double.parseDouble(map.get("calculateGpa").toString()),
                            (v1, v2) -> v1
                    ));

            // 学业成绩映射
            Map<String, Double> academicScoreMap = gpaList.stream()
                    .collect(Collectors.toMap(
                            map -> map.get("studentId").toString(),
                            map -> map.get("academicScore") == null ? 0.0 : Double.parseDouble(map.get("academicScore").toString()),
                            (v1, v2) -> v1
                    ));

            // 准备课程信息列表，用于前端显示
            List<Map<String, String>> courseInfoList = new ArrayList<>();
            for (Course course : courses) {
                Map<String, String> courseInfo = new HashMap<>();
                courseInfo.put("code", course.getCourseCode());
                courseInfo.put("name", course.getCourseName());
                courseInfoList.add(courseInfo);
            }

            // 构建结果数据：学号、姓名、各科成绩
            List<Map<String, Object>> resultList = new ArrayList<>();

            for (SchoolStudent student : students) {
                Map<String, Object> studentGrades = new HashMap<>();
                studentGrades.put("studentId", student.getStudentId());
                studentGrades.put("studentName", student.getStuName());

                // 添加每门课程的成绩
                for (String courseCode : orderedCourseList) {
                    // 查找该学生该课程的成绩
                    Optional<Grade> gradeOpt = allGrades.stream()
                            .filter(g -> g.getStudentId().equals(student.getStudentId())
                                    && g.getCourseCode().equals(courseCode))
                            .findFirst();

                    // 课程代码作为key，成绩作为value
                    Double grade = gradeOpt.map(Grade::getGrade).orElse(null);
                    studentGrades.put(courseCode, grade);
                }

                // 计算平均分
                double sum = 0;
                int count = 0;
                for (String courseCode : orderedCourseList) {
                    Optional<Grade> gradeOpt = allGrades.stream()
                            .filter(g -> g.getStudentId().equals(student.getStudentId())
                                    && g.getCourseCode().equals(courseCode))
                            .findFirst();
                    if (gradeOpt.isPresent() && gradeOpt.get().getGrade() != null) {
                        sum += gradeOpt.get().getGrade();
                        count++;
                    }
                }
                double average = count > 0 ? sum / count : 0;
                studentGrades.put("average", average);

                // 添加学业成绩(GPA)
                double calculateGpa = studentGpaMap.getOrDefault(student.getStudentId(), 0.0);
                studentGrades.put("calculateGpa", calculateGpa);

                // 添加平均学业成绩
                double academicScore = academicScoreMap.getOrDefault(student.getStudentId(), 0.0);
                studentGrades.put("academicScore", academicScore);

                resultList.add(studentGrades);
            }

            // 返回结果，包含课程列表、学生成绩数据和分页信息
            Map<String, Object> result = new HashMap<>();
            result.put("courses", orderedCourseList);
            result.put("courseInfos", courseInfoList);
            result.put("students", resultList);
            result.put("total", total);
            result.put("currentPage", currentPage);
            result.put("pageSize", pageSize);

            return ResultUtils.success("查询成功", result);
        } catch (Exception e) {
            return ResultUtils.error("导出横向成绩失败: " + e.getMessage());
        }
    }

    /**
     * 导出成绩数据为Excel
     */

    @GetMapping("/export")
    public ResponseEntity<byte[]> exportGrades(
            @RequestParam(required = false) Integer semesterId,
            @RequestParam(value = "semesterIds[]", required = false) List<Integer> semesterIds,
            @RequestParam(required = false) String academicYear) {

        LogUtils.logOperationStart(logger, "导出成绩", "学期ID: " + semesterId + ", 学年: " + academicYear);

        try {
            // 解析学期ID参数
            List<Integer> finalSemesterIds = parseSemesterIds(semesterId, null,
                                                            semesterIds, null,
                                                            academicYear, null);

            if (finalSemesterIds.isEmpty()) {
                LogUtils.logOperationError(logger, "导出成绩", "学期参数解析", "未找到有效的学期数据", null);
                return ResponseEntity.badRequest()
                    .body("未找到有效的学期数据".getBytes(StandardCharsets.UTF_8));
            }

            // 查询所有成绩，如果指定了学期，则按学期筛选
            QueryWrapper<Grade> queryWrapper = new QueryWrapper<>();
            if (finalSemesterIds != null && !finalSemesterIds.isEmpty()) {
                queryWrapper.lambda().in(Grade::getSemesterId, finalSemesterIds);
            }
            List<Grade> allGrades = gradeService.list(queryWrapper);

            if (allGrades == null || allGrades.isEmpty()) {
                allGrades = new ArrayList<>(); // 创建空列表，继续执行
            }

            // 获取学生列表
            List<SchoolStudent> students;
            if (allGrades.isEmpty()) {
                // 如果没有成绩数据，获取所有学生
                students = schoolStudentService.list();
            } else {
                // 获取有成绩的学生学号列表
                List<String> studentIds = allGrades.stream()
                        .map(Grade::getStudentId)
                        .distinct()
                        .collect(Collectors.toList());

                // 只查询有成绩的学生
                QueryWrapper<SchoolStudent> studentQueryWrapper = new QueryWrapper<>();
                studentQueryWrapper.lambda().in(SchoolStudent::getStudentId, studentIds);
                students = schoolStudentService.list(studentQueryWrapper);
            }

            if (students == null || students.isEmpty()) {
                students = new ArrayList<>(); // 创建空列表，继续执行
            }

            // 查询指定学期的课程
            List<Course> courses;
            QueryWrapper<Course> courseQuery = new QueryWrapper<>();

            // 使用semesterId筛选课程
            if (finalSemesterIds != null && !finalSemesterIds.isEmpty()) {
                courseQuery.lambda().in(Course::getSemesterId, finalSemesterIds);
            }

            // 按照课程ID排序
            courseQuery.lambda().orderByAsc(Course::getId);
            courses = courseService.list(courseQuery);

            // 如果没有找到课程，给出提示
            if (courses.isEmpty()) {
                courses = new ArrayList<>(); // 创建空列表，继续执行
            }

            // 获取学业成绩和绩点数据
            Map<String, Double> studentGpaMap = new HashMap<>();
            Map<String, Double> academicScoreMap = new HashMap<>();

            try {
                // 获取学生GPA和学业成绩数据（一次查询同时获取两个数据）
                List<Map<String, Object>> gpaData = gradeService.getStudentGpaByIds(finalSemesterIds);

                // 如果指定学期没有GPA数据，尝试获取所有学期的数据
                if (gpaData == null || gpaData.isEmpty()) {
                    gpaData = gradeService.getStudentGpaByIds(null); // 传入null获取所有学期数据
                }

                if (gpaData != null) {
                    for (Map<String, Object> data : gpaData) {
                        String studentId = (String) data.get("studentId");

                        // 处理BigDecimal到Double的转换
                        Double calculateGpa = null;
                        Double academicScore = null;

                        Object gpaObj = data.get("calculateGpa");
                        if (gpaObj != null) {
                            if (gpaObj instanceof BigDecimal) {
                                calculateGpa = ((BigDecimal) gpaObj).doubleValue();
                            } else if (gpaObj instanceof Double) {
                                calculateGpa = (Double) gpaObj;
                            }
                        }

                        Object academicObj = data.get("academicScore");
                        if (academicObj != null) {
                            if (academicObj instanceof BigDecimal) {
                                academicScore = ((BigDecimal) academicObj).doubleValue();
                            } else if (academicObj instanceof Double) {
                                academicScore = (Double) academicObj;
                            }
                        }

                        if (studentId != null) {
                            if (calculateGpa != null) {
                                studentGpaMap.put(studentId, calculateGpa);
                            }
                            if (academicScore != null) {
                                academicScoreMap.put(studentId, academicScore);
                            }
                        }
                    }
                }
            } catch (Exception e) {
                // 继续执行，只是没有这些统计数据
            }

            // 查询学期信息
            String semesterName = "全部学期";
            if (finalSemesterIds != null && !finalSemesterIds.isEmpty()) {
                List<Semester> semesters = semesterService.listByIds(finalSemesterIds);
                if (!semesters.isEmpty()) {
                    semesterName = semesters.stream()
                            .map(Semester::getSemesterName)
                            .collect(Collectors.joining(", "));
                }
            }

            // 创建工作簿和工作表
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("成绩表");

            // 创建标题行样式
            CellStyle titleStyle = workbook.createCellStyle();
            Font titleFont = workbook.createFont();
            titleFont.setBold(true);
            titleFont.setFontHeightInPoints((short) 14);
            titleStyle.setFont(titleFont);
            titleStyle.setAlignment(HorizontalAlignment.CENTER);

            // 创建表头样式
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);

            // 创建单元格样式
            CellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setBorderTop(BorderStyle.THIN);
            cellStyle.setBorderBottom(BorderStyle.THIN);
            cellStyle.setBorderLeft(BorderStyle.THIN);
            cellStyle.setBorderRight(BorderStyle.THIN);

            // 创建标题行
            Row titleRow = sheet.createRow(0);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue(semesterName + " 学生成绩表");
            titleCell.setCellStyle(titleStyle);
            // 合并标题行 (增加3列：平均分、学业成绩、绩点)
            int totalColumns = Math.max(courses.size() + 4, 4); // 至少4列（学号、姓名、平均分、学业成绩、绩点）
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, totalColumns));

            // 创建表头行
            Row headerRow = sheet.createRow(1);
            headerRow.createCell(0).setCellValue("学号");
            headerRow.createCell(1).setCellValue("姓名");

            // 设置表头样式 (增加3列)
            for (int i = 0; i <= totalColumns; i++) {
                Cell cell = headerRow.getCell(i, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                cell.setCellStyle(headerStyle);
            }

            // 添加课程名称到表头
            for (int i = 0; i < courses.size(); i++) {
                Course course = courses.get(i);
                Cell cell = headerRow.createCell(i + 2);
                cell.setCellValue(course.getCourseName() + "(" + course.getCourseCode() + ")");
                cell.setCellStyle(headerStyle);
            }

            // 添加统计列到表头
            int avgColumnIndex = courses.size() + 2;
            int academicColumnIndex = courses.size() + 3;
            int gpaColumnIndex = courses.size() + 4;

            Cell avgCell = headerRow.createCell(avgColumnIndex);
            avgCell.setCellValue("平均分");
            avgCell.setCellStyle(headerStyle);

            Cell academicCell = headerRow.createCell(academicColumnIndex);
            academicCell.setCellValue("学业成绩");
            academicCell.setCellStyle(headerStyle);

            Cell gpaCell = headerRow.createCell(gpaColumnIndex);
            gpaCell.setCellValue("绩点");
            gpaCell.setCellStyle(headerStyle);

            // 填充学生成绩数据
            int rowNum = 2;
            if (students.isEmpty()) {
                // 如果没有学生数据，添加一行提示信息
                Row emptyRow = sheet.createRow(rowNum++);
                Cell emptyCell = emptyRow.createCell(0);
                emptyCell.setCellValue("暂无学生数据");
                emptyCell.setCellStyle(cellStyle);

                // 合并单元格显示提示信息
                sheet.addMergedRegion(new CellRangeAddress(rowNum - 1, rowNum - 1, 0, totalColumns));
            }

            for (SchoolStudent student : students) {
                Row row = sheet.createRow(rowNum++);

                // 学号和姓名
                Cell idCell = row.createCell(0);
                idCell.setCellValue(student.getStudentId());
                idCell.setCellStyle(cellStyle);

                Cell nameCell = row.createCell(1);
                nameCell.setCellValue(student.getStuName());
                nameCell.setCellStyle(cellStyle);

                // 各科成绩和平均分计算
                double sum = 0;
                int count = 0;
                for (int i = 0; i < courses.size(); i++) {
                    Course course = courses.get(i);
                    Cell gradeCell = row.createCell(i + 2);

                    // 查找该学生该课程的成绩
                    Optional<Grade> gradeOpt = allGrades.stream()
                            .filter(g -> g.getStudentId().equals(student.getStudentId())
                                    && g.getCourseCode().equals(course.getCourseCode()))
                            .findFirst();

                    if (gradeOpt.isPresent() && gradeOpt.get().getGrade() != null) {
                        Double grade = gradeOpt.get().getGrade();
                        gradeCell.setCellValue(grade);
                        sum += grade;
                        count++;
                    } else {
                        gradeCell.setCellValue("");
                    }
                    gradeCell.setCellStyle(cellStyle);
                }

                // 计算并填充平均分
                double average = count > 0 ? sum / count : 0;
                Cell avgDataCell = row.createCell(avgColumnIndex);
                if (average > 0) {
                    avgDataCell.setCellValue(Math.round(average * 100.0) / 100.0); // 保留两位小数
                } else {
                    avgDataCell.setCellValue("");
                }
                avgDataCell.setCellStyle(cellStyle);

                // 填充学业成绩
                Cell academicDataCell = row.createCell(academicColumnIndex);
                Double academicScore = academicScoreMap.get(student.getStudentId());
                if (academicScore != null && academicScore > 0) {
                    academicDataCell.setCellValue(Math.round(academicScore * 100.0) / 100.0); // 保留两位小数
                } else {
                    academicDataCell.setCellValue("");
                }
                academicDataCell.setCellStyle(cellStyle);

                // 填充绩点
                Cell gpaDataCell = row.createCell(gpaColumnIndex);
                Double gpa = studentGpaMap.get(student.getStudentId());
                if (gpa != null && gpa > 0) {
                    gpaDataCell.setCellValue(Math.round(gpa * 100.0) / 100.0); // 保留两位小数
                } else {
                    gpaDataCell.setCellValue("");
                }
                gpaDataCell.setCellStyle(cellStyle);
            }

            // 自动调整列宽 (增加3列：平均分、学业成绩、绩点)
            for (int i = 0; i <= totalColumns; i++) {
                sheet.autoSizeColumn(i);
            }

            // 生成文件名
            String fileName = URLEncoder.encode(semesterName + "_学生成绩表_" +
                    DateTimeUtils.getCurrentTimeCompactString() + ".xlsx",
                    StandardCharsets.UTF_8.toString()).replaceAll("\\+", "%20");

            // 将工作簿写入字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            workbook.close();

            // 设置HTTP响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);

            // 返回Excel文件
            LogUtils.logOperationSuccess(logger, "导出成绩", "Excel文件", "文件大小: " + outputStream.size() + " bytes");
            return new ResponseEntity<>(outputStream.toByteArray(), headers, HttpStatus.OK);
        } catch (Exception e) {
            LogUtils.logOperationError(logger, "导出成绩", "Excel生成", e.getMessage(), e);
            return new ResponseEntity<>(("导出失败: " + e.getMessage()).getBytes(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 下载成绩导入模板
     */

    @GetMapping("/template")
    public ResponseEntity<byte[]> downloadTemplate(
            @RequestParam(required = false) Integer semesterId,
            @RequestParam(value = "semesterIds[]", required = false) List<Integer> semesterIds,
            @RequestParam(required = false) String academicYear) {
        try {
            // 解析学期ID参数
            List<Integer> finalSemesterIds = parseSemesterIds(semesterId, null,
                                                            semesterIds, null,
                                                            academicYear, null);

            if (finalSemesterIds.isEmpty()) {
                LogUtils.logOperationError(logger, "下载模板", "学期参数解析", "未找到有效的学期数据", null);
                return ResponseEntity.badRequest()
                    .body("未找到有效的学期数据".getBytes(StandardCharsets.UTF_8));
            }

            // 设置学期名称
            String semesterName = "";
            if (StringUtils.isNotBlank(academicYear)) {
                semesterName = academicYear + "学年";
            }



            // 如果有学期ID但没有学期名称，查询学期名称
            if (!finalSemesterIds.isEmpty() && StringUtils.isBlank(semesterName)) {
                List<Semester> semesters = semesterService.listByIds(finalSemesterIds);
                if (!semesters.isEmpty()) {
                    semesterName = semesters.stream()
                            .map(Semester::getSemesterName)
                            .collect(Collectors.joining(", "));
                }
            }

            // 创建工作簿和工作表
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("成绩导入模板");

            // 创建表头样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);

            // 创建必填项样式
            CellStyle requiredStyle = workbook.createCellStyle();
            requiredStyle.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex());
            requiredStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            Font requiredFont = workbook.createFont();
            requiredFont.setBold(true);
            requiredStyle.setFont(requiredFont);

            // 查询指定学期的课程
            List<Course> courses = new ArrayList<>();

            // 构建查询
            QueryWrapper<Course> courseQuery = new QueryWrapper<>();

            // 优先使用学期ID直接查询课程
            if (finalSemesterIds != null && !finalSemesterIds.isEmpty()) {
                courseQuery.lambda().in(Course::getSemesterId, finalSemesterIds);
            }
            // 如果没有学期ID但有学期名称，尝试通过名称查询
            else if (semesterName != null && StringUtils.isNotEmpty(semesterName)) {
                try {
                    // 先查询semesterName对应的semesterId
                    Integer foundSemesterId = courseService.getSemesterIdByName(semesterName);
                    if (foundSemesterId != null) {
                        courseQuery.lambda().eq(Course::getSemesterId, foundSemesterId);
                        // 更新finalSemesterIds，用于后续查询
                        finalSemesterIds.add(foundSemesterId);
                    }
                } catch (Exception e) {
                    // 捕获查询异常，但不中断流程
                }
            }

            // 按照课程ID排序
            courseQuery.lambda().orderByAsc(Course::getId);
            courses = courseService.list(courseQuery);

            // 如果没有找到课程，给出提示或使用空列表
            if (courses.isEmpty()) {
                // 可以在模板中添加提示信息
                courses = new ArrayList<>(); // 确保有一个空列表而不是null
            }

            // 创建表头
            Row headerRow = sheet.createRow(0);
            int cellIndex = 0;

            // 添加学号和姓名列
            Cell studentIdCell = headerRow.createCell(cellIndex++);
            studentIdCell.setCellValue("学号*");
            studentIdCell.setCellStyle(requiredStyle);

            Cell nameCell = headerRow.createCell(cellIndex++);
            nameCell.setCellValue("姓名*");
            nameCell.setCellStyle(requiredStyle);

            // 学期列
            Cell semesterCell = headerRow.createCell(cellIndex++);
            semesterCell.setCellValue("学期*");
            semesterCell.setCellStyle(requiredStyle);

            // 添加课程列
            for (Course course : courses) {
                Cell cell = headerRow.createCell(cellIndex++);
                cell.setCellValue(course.getCourseName() + "(" + course.getCourseCode() + ")");
                cell.setCellStyle(headerStyle);
            }

            // 添加示例数据行
            Row exampleRow = sheet.createRow(1);
            cellIndex = 0;
            exampleRow.createCell(cellIndex++).setCellValue("20230001"); // 学号示例
            exampleRow.createCell(cellIndex++).setCellValue("张三"); // 姓名示例

            // 使用多学期格式作为示例
            String semesterExample = StringUtils.isNotEmpty(semesterName) ? semesterName : "2023-2024-1, 2023-2024-2, 2024-2025-1";
            exampleRow.createCell(cellIndex++).setCellValue(semesterExample); // 学期示例，使用逗号分隔的多学期格式

            // 添加示例成绩
            for (int i = 0; i < courses.size(); i++) {
                exampleRow.createCell(cellIndex++).setCellValue(80 + Math.random() * 20); // 示例成绩80-100之间
            }

            // 添加说明行
            Row noteRow = sheet.createRow(3);
            Cell noteCell = noteRow.createCell(0);
            noteCell.setCellValue("说明：\n1. 标记*的字段为必填项\n" +
                    "2. 学号、姓名必须与系统中的学生信息匹配\n" +
                    "3. 学期格式如：2023-2024-1，表示2023-2024学年第1学期\n" +
                    "4. 可以在学期列中填写多个学期（用逗号分隔），系统会自动为每门课程匹配正确的学期\n" +
                    "   例如：2023-2024-1, 2023-2024-2, 2024-2025-1\n" +
                    "5. 成绩为0-100的数值，可以包含小数\n" +
                    "6. 如果某门课程没有成绩，可以留空\n" +
                    "7. 如果系统中已有该学生该学期该课程的成绩，将会更新替换");

            // 合并单元格作为说明
            sheet.addMergedRegion(new CellRangeAddress(3, 8, 0, Math.max(3, courses.size() + 2)));

            // 自动调整列宽
            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                sheet.autoSizeColumn(i);
            }

            // 写入到字节流
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            workbook.close();

            // 设置响应头
            String fileName = "成绩导入模板" + (StringUtils.isNotEmpty(semesterName) ? "_" + semesterName : "") + ".xlsx";
            // 正确编码文件名，使用RFC 5987标准格式
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString())
                    .replaceAll("\\+", "%20"); // 将+号替换为%20
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            // 使用RFC 5987标准格式设置Content-Disposition
            headers.set("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(outputStream.toByteArray());

        } catch (Exception e) {
            throw new RuntimeException("下载成绩导入模板失败: " + e.getMessage());
        }
    }

    /**
     * 导入成绩
     */

    @PostMapping("/import")
    @Transactional
    public ResultVo importGrades(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "semesterId", required = false) Integer semesterId,
            @RequestParam(value = "semesterIds[]", required = false) List<Integer> semesterIds,
            @RequestParam(value = "academicYear", required = false) String academicYear) {

        LogUtils.logOperationStart(logger, "导入成绩", "文件: " + file.getOriginalFilename() + ", 大小: " + file.getSize() + " bytes");

        if (file.isEmpty()) {
            LogUtils.logOperationError(logger, "导入成绩", "文件验证", "文件为空", null);
            return ResultUtils.error("请选择一个Excel文件");
        }

        // 检查文件类型
        String fileName = file.getOriginalFilename();
        if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
            return ResultUtils.error("请上传Excel文件(.xlsx或.xls)");
        }

        // 解析学期ID参数
        List<Integer> allSemesterIds = parseSemesterIds(semesterId, null,
                                                       semesterIds, null,
                                                       academicYear, null);

        if (allSemesterIds.isEmpty()) {
            return ResultUtils.error("未找到有效的学期数据");
        }



        // 如果前端传递了有效的学期ID，记录下来供后续使用
        Integer defaultSemesterId = null;
        if (!allSemesterIds.isEmpty()) {
            defaultSemesterId = allSemesterIds.get(0);
        }



        // 获取所有学期信息，用于名称到ID的映射
        Map<String, Integer> semesterNameToIdMap = new HashMap<>();
        try {
            List<Semester> allSemesters = semesterService.list();
            for (Semester semester : allSemesters) {
                if (semester.getSemesterName() != null && semester.getSemesterId() != null) {
                    semesterNameToIdMap.put(semester.getSemesterName(), semester.getSemesterId());
                }
            }
        } catch (Exception e) {
            // 忽略学期查询错误
        }

        try (InputStream is = file.getInputStream(); Workbook workbook = WorkbookFactory.create(is)) {
            Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表
            List<Grade> newGrades = new ArrayList<>();  // 新增的成绩
            List<Grade> updateGrades = new ArrayList<>(); // 更新的成绩

            // 记录成功和失败的数量
            int newCount = 0;   // 新增数量
            int updateCount = 0; // 更新数量
            int failureCount = 0;
            List<String> errorMessages = new ArrayList<>();

            // 从第二行开始读取数据（跳过表头）
            for (int rowNum = 1; rowNum <= sheet.getLastRowNum(); rowNum++) {
                Row row = sheet.getRow(rowNum);
                if (row == null) {
                    continue;
                }

                try {
                    // 读取前三列：学号、姓名、学期
                    String studentId = getCellValueAsString(row.getCell(0));
                    String studentName = getCellValueAsString(row.getCell(1));
                    String excelSemesterName = getCellValueAsString(row.getCell(2));

                    // 检查必填字段
                    if (studentId.isEmpty()) {
                        errorMessages.add("第 " + (rowNum + 1) + " 行：学号为必填项");
                        failureCount++;
                        continue;
                    }

                    // 查询学生是否存在
                    QueryWrapper<SchoolStudent> studentQuery = new QueryWrapper<>();
                    studentQuery.lambda().eq(SchoolStudent::getStudentId, studentId);
                    SchoolStudent student = schoolStudentService.getOne(studentQuery);

                    if (student == null) {
                        errorMessages.add("第 " + (rowNum + 1) + " 行：学号 " + studentId + " 不存在");
                        failureCount++;
                        continue;
                    }

                    // 检查姓名是否匹配
                    if (!student.getStuName().equals(studentName)) {
                        errorMessages.add("第 " + (rowNum + 1) + " 行：学生姓名不匹配，系统中为 " + student.getStuName());
                        failureCount++;
                        continue;
                    }

                    // 确定学期ID - 优先使用Excel中指定的学期，如果为空则使用前端传递的学期ID
                    Integer effectiveSemesterId = null;
                    // 存储Excel中可能包含的多个学期名称
                    List<String> excelSemesterNames = new ArrayList<>();

                    // 处理Excel中的学期信息
                    if (!excelSemesterName.isEmpty()) {
                        // 如果Excel中指定了学期，优先使用
                        try {
                            // 处理可能包含多个学期名称的情况（用逗号分隔）
                            if (excelSemesterName.contains(",")) {
                                // 如果包含逗号，拆分为多个学期名称
                                String[] semesterNames = excelSemesterName.split(",");
                                for (String name : semesterNames) {
                                    String trimmedName = name.trim();
                                    if (!trimmedName.isEmpty()) {
                                        excelSemesterNames.add(trimmedName);
                                    }
                                }
                            } else {
                                // 单个学期名称
                                excelSemesterNames.add(excelSemesterName.trim());
                            }

                            // 尝试从第一个学期名称获取学期ID
                            if (!excelSemesterNames.isEmpty()) {
                                String firstSemesterName = excelSemesterNames.get(0);

                                // 先从映射中查找
                                effectiveSemesterId = semesterNameToIdMap.get(firstSemesterName);

                                // 如果映射中没有，尝试通过服务查询
                                if (effectiveSemesterId == null) {
                                    effectiveSemesterId = courseService.getSemesterIdByName(firstSemesterName);
                                }
                            }
                        } catch (Exception e) {
                            // 忽略学期ID获取错误
                        }
                    }

                    // 读取表头信息，获取每列对应的课程代码
                    Row headerRow = sheet.getRow(0);
                    Map<Integer, String> columnToCourseCode = new HashMap<>();

                    // 从第4列开始是课程成绩
                    for (int colIndex = 3; colIndex < headerRow.getLastCellNum(); colIndex++) {
                        Cell headerCell = headerRow.getCell(colIndex);
                        if (headerCell != null) {
                            String headerValue = headerCell.getStringCellValue();
                            // 提取课程代码
                            int startIdx = headerValue.lastIndexOf('(');
                            int endIdx = headerValue.lastIndexOf(')');
                            if (startIdx > 0 && endIdx > startIdx) {
                                String courseCode = headerValue.substring(startIdx + 1, endIdx);
                                columnToCourseCode.put(colIndex, courseCode);
                            }
                        }
                    }

                    // 读取每门课程的成绩
                    for (Map.Entry<Integer, String> entry : columnToCourseCode.entrySet()) {
                        int colIndex = entry.getKey();
                        String courseCode = entry.getValue();
                        Cell gradeCell = row.getCell(colIndex);

                        // 如果单元格为空，跳过该课程
                        if (gradeCell == null) {
                            continue;
                        }

                        // 获取成绩
                        String gradeStr = getCellValueAsString(gradeCell);
                        if (gradeStr.isEmpty()) {
                            continue;
                        }

                        Double gradeValue;
                        try {
                            gradeValue = Double.parseDouble(gradeStr);

                            // 检查成绩范围
                            if (gradeValue < 0 || gradeValue > 100) {
                                errorMessages.add("第 " + (rowNum + 1) + " 行：课程 " + courseCode + " 的成绩应在 0-100 之间");
                                failureCount++;
                                continue;
                            }

                        } catch (NumberFormatException e) {
                            errorMessages.add("第 " + (rowNum + 1) + " 行：课程 " + courseCode + " 的成绩格式不正确: " + gradeStr);
                            failureCount++;
                            continue;
                        }

                        // 检查课程是否存在
                        QueryWrapper<Course> courseQuery = new QueryWrapper<>();
                        courseQuery.lambda().eq(Course::getCourseCode, courseCode);
                        Course course = courseService.getOne(courseQuery);

                        if (course == null) {
                            errorMessages.add("第 " + (rowNum + 1) + " 行：课程代码 " + courseCode + " 不存在");
                            failureCount++;
                            continue;
                        }

                        // 为每个课程选择合适的学期ID
                        Integer courseSemesterId = null;

                        // 优先级：
                        // 1. 尝试根据课程代码从Excel中的多个学期名称中找到匹配的学期
                        // 2. Excel中指定的第一个学期ID
                        // 3. 课程自带的学期ID
                        // 4. 前端传递的多个学期ID中匹配的
                        // 5. 默认学期ID

                        // 1. 尝试从Excel中的多个学期名称中找到与课程匹配的学期
                        if (!excelSemesterNames.isEmpty() && excelSemesterNames.size() > 1) {
                            // 查询该课程在哪个学期开设
                            try {
                                boolean found = false;
                                for (String semName : excelSemesterNames) {
                                    // 先从映射中查找学期ID
                                    Integer semId = semesterNameToIdMap.get(semName);
                                    if (semId == null) {
                                        // 如果映射中没有，尝试通过服务查询
                                        semId = courseService.getSemesterIdByName(semName);
                                    }

                                    if (semId != null) {
                                        // 查询该课程是否在此学期开设
                                        QueryWrapper<Course> semesterCourseQuery = new QueryWrapper<>();
                                        semesterCourseQuery.lambda()
                                            .eq(Course::getCourseCode, courseCode)
                                            .eq(Course::getSemesterId, semId);

                                        if (courseService.count(semesterCourseQuery) > 0) {
                                            courseSemesterId = semId;
                                            found = true;
                                            break;
                                        }
                                    }
                                }

                                if (found) {
                                    // 已找到匹配的学期，继续处理
                                }
                            } catch (Exception e) {
                                // 忽略学期匹配错误
                            }
                        }

                        // 2. 如果上面没找到，使用Excel中指定的第一个学期ID
                        if (courseSemesterId == null && effectiveSemesterId != null) {
                            courseSemesterId = effectiveSemesterId;
                        }
                        // 3. 使用课程自带的学期ID
                        else if (courseSemesterId == null && course.getSemesterId() != null) {
                            courseSemesterId = course.getSemesterId();
                        }
                        // 4. 如果有多个学期ID，尝试找到与课程匹配的学期ID
                        else if (courseSemesterId == null && !allSemesterIds.isEmpty()) {
                            boolean found = false;

                            // 先查询该课程在各个学期中是否存在
                            for (Integer sid : allSemesterIds) {
                                QueryWrapper<Course> semesterCourseQuery = new QueryWrapper<>();
                                semesterCourseQuery.lambda()
                                    .eq(Course::getCourseCode, courseCode)
                                    .eq(Course::getSemesterId, sid);

                                if (courseService.count(semesterCourseQuery) > 0) {
                                    courseSemesterId = sid;
                                    found = true;
                                    break;
                                }
                            }

                            // 如果没有找到匹配的学期，使用第一个学期ID
                            if (!found) {
                                courseSemesterId = allSemesterIds.get(0);
                            }
                        }
                        // 5. 使用默认学期ID
                        else if (courseSemesterId == null && defaultSemesterId != null) {
                            courseSemesterId = defaultSemesterId;
                        }
                        // 无法确定学期ID
                        else if (courseSemesterId == null) {
                            errorMessages.add("第 " + (rowNum + 1) + " 行：课程 " + courseCode + " 无法确定学期ID");
                            failureCount++;
                            continue;
                        }

                        // 检查是否已有该学生该课程该学期的成绩
                        QueryWrapper<Grade> gradeQuery = new QueryWrapper<>();
                        gradeQuery.lambda()
                                .eq(Grade::getStudentId, studentId)
                                .eq(Grade::getCourseCode, courseCode)
                                .eq(Grade::getSemesterId, courseSemesterId);
                        Grade existingGrade = gradeService.getOne(gradeQuery);

                        if (existingGrade != null) {
                            // 更新成绩
                            existingGrade.setGrade(gradeValue);
                            updateGrades.add(existingGrade);
                            updateCount++;
                        } else {
                            // 新增成绩
                            Grade newGrade = new Grade();
                            newGrade.setStudentId(studentId);
                            newGrade.setCourseCode(courseCode);
                            newGrade.setSemesterId(courseSemesterId);
                            newGrade.setGrade(gradeValue);
                            newGrades.add(newGrade);
                            newCount++;
                        }
                    }
                } catch (Exception e) {
                    errorMessages.add("第 " + (rowNum + 1) + " 行处理失败: " + e.getMessage());
                    failureCount++;
                }
            }

            // 批量保存新增成绩
            if (!newGrades.isEmpty()) {
                gradeService.saveBatch(newGrades);
            }

            // 批量更新已有成绩
            if (!updateGrades.isEmpty()) {
                gradeService.updateBatchById(updateGrades);
            }

            // 返回导入结果
            String resultMessage = String.format("成绩导入完成：新增 %d 条，更新 %d 条，失败 %d 条", newCount, updateCount, failureCount);
            if (!errorMessages.isEmpty()) {
                // 最多返回10条错误信息
                int maxErrors = Math.min(errorMessages.size(), 10);
                resultMessage += "。错误信息：" + String.join("；", errorMessages.subList(0, maxErrors));
                if (errorMessages.size() > 10) {
                    resultMessage += "...等";
                }
            }

            return ResultUtils.success(resultMessage);

        } catch (IOException e) {
            return ResultUtils.error("导入失败：" + e.getMessage());
        }
    }

    /**
     * 获取单元格值为字符串
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        String cellValue = "";
        switch (cell.getCellType()) {
            case STRING:
                cellValue = cell.getStringCellValue();
                break;
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    cellValue = cell.getLocalDateTimeCellValue().toString();
                } else {
                    // 使用DataFormatter来格式化数值，避免科学计数法
                    DataFormatter formatter = new DataFormatter();
                    cellValue = formatter.formatCellValue(cell);
                }
                break;
            case BOOLEAN:
                cellValue = String.valueOf(cell.getBooleanCellValue());
                break;
            case FORMULA:
                try {
                    cellValue = String.valueOf(cell.getNumericCellValue());
                } catch (IllegalStateException e) {
                    cellValue = String.valueOf(cell.getRichStringCellValue());
                }
                break;
            default:
                cellValue = "";
        }
        return cellValue.trim();
    }

    /**
     * 更新成绩表中的学期ID
     */
    @GetMapping("/updateSemesterId")
    @Transactional
    public ResultVo updateSemesterId() {
        try {
            // 获取所有学期信息
            List<Semester> semesters = semesterService.list();
            if (semesters == null || semesters.isEmpty()) {
                return ResultUtils.error("没有找到任何学期数据");
            }

            // 创建学期名称到ID的映射
            Map<String, Integer> semesterNameToIdMap = new HashMap<>();
            for (Semester semester : semesters) {
                if (semester.getSemesterName() != null && semester.getSemesterId() != null) {
                    semesterNameToIdMap.put(semester.getSemesterName(), semester.getSemesterId());
                }
            }

            // 获取默认学期ID (使用第一个学期)
            Integer defaultSemesterId = semesters.get(0).getSemesterId();

            // 查询所有缺少学期ID的成绩记录
            QueryWrapper<Grade> queryNullSemester = new QueryWrapper<>();
            queryNullSemester.lambda().isNull(Grade::getSemesterId).or().eq(Grade::getSemesterId, 0);
            List<Grade> gradesWithoutSemester = gradeService.list(queryNullSemester);

            if (gradesWithoutSemester.isEmpty()) {
                return ResultUtils.success("没有需要更新的成绩记录");
            }

            // 按学期名称分组的统计
            Map<String, Integer> updateCountByName = new HashMap<>();
            int updatedByName = 0;
            int updatedByDefault = 0;

            // 遍历需要更新的成绩记录
            for (Grade grade : gradesWithoutSemester) {
                // 如果有学期名称，尝试根据名称设置学期ID
                if (grade.getSemesterName() != null && !grade.getSemesterName().isEmpty()) {
                    Integer semesterId = semesterNameToIdMap.get(grade.getSemesterName());
                    if (semesterId != null) {
                        grade.setSemesterId(semesterId);
                        updatedByName++;

                        // 更新统计
                        updateCountByName.put(grade.getSemesterName(),
                            updateCountByName.getOrDefault(grade.getSemesterName(), 0) + 1);
                    } else {
                        // 如果找不到对应的学期ID，使用默认值
                        grade.setSemesterId(defaultSemesterId);
                        updatedByDefault++;
                    }
                } else {
                    // 如果没有学期名称，直接使用默认值
                    grade.setSemesterId(defaultSemesterId);
                    updatedByDefault++;
                }
            }

            // 批量更新
            boolean updateResult = gradeService.updateBatchById(gradesWithoutSemester);



            // 构建返回结果
            Map<String, Object> resultData = new HashMap<>();
            resultData.put("totalUpdated", gradesWithoutSemester.size());
            resultData.put("updatedByName", updatedByName);
            resultData.put("updatedByDefault", updatedByDefault);
            resultData.put("updateByNameDetails", updateCountByName);
            resultData.put("defaultSemesterId", defaultSemesterId);

            return ResultUtils.success("成绩表学期ID更新完成", resultData);
        } catch (Exception e) {
            return ResultUtils.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有学期列表
     */
    @GetMapping("/semesters")
    public ResultVo getAllSemesters() {
        try {
            List<Semester> semesters = semesterService.list();
            return ResultUtils.success("获取学期列表成功", semesters);
        } catch (Exception e) {
            return ResultUtils.error("获取学期列表失败: " + e.getMessage());
        }
    }

    /**
     * 按学年获取综合素质测评成绩列表
     */
    @GetMapping("/comprehensiveByAcademicYear")
    public ResultVo getComprehensiveByAcademicYear(
            @RequestParam(required = false) String academicYear,
            @RequestParam(required = false) String studentId,
            @RequestParam(required = false) String studentName,
            @RequestParam(defaultValue = "1") Integer currentPage,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            // 数据权限控制：如果是学生登录，只能查看自己的数据
            if (UserContext.isStudent()) {
                String currentUsername = UserContext.getCurrentUsername();
                if (currentUsername != null) {
                    studentId = currentUsername;
                    studentName = null; // 清除其他查询条件
                } else {
                    return ResultUtils.error("无法获取当前用户信息");
                }
            }

            // 检查学年参数
            if (StringUtils.isBlank(academicYear)) {
                return ResultUtils.error("学年参数不能为空");
            }

            // 1. 根据学年获取对应的学期列表
            QueryWrapper<Semester> semesterQuery = new QueryWrapper<>();
            // 先尝试通过academicYear字段查询
            semesterQuery.lambda().eq(Semester::getAcademicYear, academicYear);
            List<Semester> semesters = semesterService.list(semesterQuery);

            // 如果通过academicYear字段没有找到，则通过semesterName模糊查询
            if (semesters.isEmpty()) {
                semesterQuery = new QueryWrapper<>();
                semesterQuery.lambda().like(Semester::getSemesterName, academicYear);
                semesters = semesterService.list(semesterQuery);
            }

            if (semesters.isEmpty()) {
                return ResultUtils.error("未找到指定学年的学期数据: " + academicYear);
            }

            List<Integer> semesterIds = semesters.stream()
                    .map(Semester::getSemesterId)
                    .collect(Collectors.toList());

            // 2. 获取学生列表
            QueryWrapper<SchoolStudent> studentQuery = new QueryWrapper<>();
            if (StringUtils.isNotBlank(studentId)) {
                studentQuery.lambda().like(SchoolStudent::getStudentId, studentId);
            }
            if (StringUtils.isNotBlank(studentName)) {
                studentQuery.lambda().like(SchoolStudent::getStuName, studentName);
            }
            List<SchoolStudent> students = schoolStudentService.list(studentQuery);

            if (students.isEmpty()) {
                return ResultUtils.error("未找到符合条件的学生信息");
            }

            // 3. 获取学业成绩数据
            Map<String, Double> academicScoreMap = getAcademicScoresByAcademicYear(semesterIds);

            // 4. 获取基本素质测评数据（优先选择第二学期）
            Map<String, Double> qualityScoreMap = getQualityScoresByAcademicYear(semesterIds, studentId, studentName);

            // 5. 组装综合素质测评数据
            List<Map<String, Object>> comprehensiveList = new ArrayList<>();
            for (SchoolStudent student : students) {
                Map<String, Object> record = new HashMap<>();
                record.put("studentId", student.getStudentId());
                record.put("studentName", student.getStuName());
                record.put("academicYear", academicYear);

                // 获取学业成绩
                Double academicScore = academicScoreMap.getOrDefault(student.getStudentId(), 0.0);
                record.put("academicScore", academicScore);

                // 获取基本素质测评成绩
                Double qualityScore = qualityScoreMap.getOrDefault(student.getStudentId(), 0.0);
                record.put("score1", qualityScore);

                // 计算综合素质测评成绩 = 学业成绩 * 70% + 基本素质测评成绩 * 30%
                Double finalScore = academicScore * 0.7 + qualityScore * 0.3;
                record.put("score2", Math.round(finalScore * 100) / 100.0);

                comprehensiveList.add(record);
            }

            // 6. 分页处理
            int total = comprehensiveList.size();
            int startIndex = (currentPage - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, total);
            List<Map<String, Object>> pagedList = comprehensiveList.subList(startIndex, endIndex);

            // 7. 构造分页结果
            Map<String, Object> result = new HashMap<>();
            result.put("records", pagedList);
            result.put("total", total);
            result.put("current", currentPage);
            result.put("size", pageSize);
            result.put("pages", (int) Math.ceil((double) total / pageSize));

            return ResultUtils.success("查询成功", result);
        } catch (Exception e) {

            return ResultUtils.error("获取综合素质测评列表失败: " + e.getMessage());
        }
    }

    /**
     * 导出综合素质测评成绩
     */
    @GetMapping("/exportComprehensive")
    public ResponseEntity<byte[]> exportComprehensive(
            @RequestParam(required = false) String academicYear,
            @RequestParam(required = false) String studentId,
            @RequestParam(required = false) String studentName) {
        try {
            // 检查参数：学年不能为空
            if (StringUtils.isBlank(academicYear)) {
                throw new RuntimeException("学年不能为空");
            }

            List<Integer> semesterIds = new ArrayList<>();
            String semesterNameStr = ""; // 用于显示的学期名称

            // 按学年查询学期
            QueryWrapper<Semester> semesterQuery = new QueryWrapper<>();
            semesterQuery.lambda().eq(Semester::getAcademicYear, academicYear);
            List<Semester> semesters = semesterService.list(semesterQuery);

            // 如果通过academicYear字段没有找到，则通过semesterName模糊查询
            if (semesters.isEmpty()) {
                semesterQuery = new QueryWrapper<>();
                semesterQuery.lambda().like(Semester::getSemesterName, academicYear);
                semesters = semesterService.list(semesterQuery);
            }

            if (semesters.isEmpty()) {
                throw new RuntimeException("未找到指定学年的学期信息: " + academicYear);
            }

            semesterIds = semesters.stream().map(Semester::getSemesterId).collect(Collectors.toList());
            semesterNameStr = academicYear + "学年";

            // 1. 查询学生信息
            QueryWrapper<SchoolStudent> studentQueryWrapper = new QueryWrapper<>();
            if (StringUtils.isNotBlank(studentId)) {
                studentQueryWrapper.lambda().like(SchoolStudent::getStudentId, studentId);
            }
            if (StringUtils.isNotBlank(studentName)) {
                studentQueryWrapper.lambda().like(SchoolStudent::getStuName, studentName);
            }
            List<SchoolStudent> students = schoolStudentService.list(studentQueryWrapper);

            if (students == null || students.isEmpty()) {
                throw new RuntimeException("未找到符合条件的学生信息");
            }

            // 2. 获取学业成绩数据 - 支持多个学期
            List<Map<String, Object>> academicScores = new ArrayList<>();

            // 调用Service方法获取所有选中学期的学生学业成绩
            List<Map<String, Object>> allStudentGpaList = gradeService.getStudentGpaByIds(semesterIds);

            // 为每个学生查找成绩
            for (SchoolStudent student : students) {
                // 查找当前学生的成绩
                for (Map<String, Object> scoreMap : allStudentGpaList) {
                    if (student.getStudentId().equals(scoreMap.get("studentId"))) {
                        Map<String, Object> studentScore = new HashMap<>();
                        studentScore.put("studentId", student.getStudentId());
                        studentScore.put("academicScore", scoreMap.get("academicScore"));
                        academicScores.add(studentScore);
                        break;
                    }
                }
            }

            // 3. 获取基本素质测评数据 - 支持多个学期
            QueryWrapper<QualityEvaluation> qualityQueryWrapper = new QueryWrapper<>();
            qualityQueryWrapper.lambda().in(QualityEvaluation::getEvaluationPeriod, semesterIds);
            if (StringUtils.isNotBlank(studentId)) {
                qualityQueryWrapper.lambda().like(QualityEvaluation::getStudentId, studentId);
            }
            if (StringUtils.isNotBlank(studentName)) {
                qualityQueryWrapper.lambda().like(QualityEvaluation::getStudentName, studentName);
            }

            List<QualityEvaluation> qualityScores = qualityEvaluationService.list(qualityQueryWrapper);

            // 4. 组装综合素质测评成绩数据
            List<Map<String, Object>> comprehensiveScores = new ArrayList<>();
            for (SchoolStudent student : students) {
                Map<String, Object> comprehensiveScore = new HashMap<>();
                comprehensiveScore.put("studentId", student.getStudentId());
                comprehensiveScore.put("studentName", student.getStuName());
                comprehensiveScore.put("semesterName", semesterNameStr); // 使用多个学期名称的组合

                // 获取学业成绩
                Double academicScore = 0.0;
                for (Map<String, Object> score : academicScores) {
                    if (student.getStudentId().equals(score.get("studentId"))) {
                        Object scoreValue = score.get("academicScore");
                        if (scoreValue != null) {
                            academicScore = Double.parseDouble(scoreValue.toString());
                        }
                        break;
                    }
                }
                comprehensiveScore.put("academicScore", academicScore);

                // 获取基本素质测评成绩 - 查找该学生在选定学期中的最新一条基本素质测评成绩
                Double qualityScore = 0.0;
                QualityEvaluation latestQuality = null;
                for (QualityEvaluation quality : qualityScores) {
                    if (student.getStudentId().equals(quality.getStudentId())) {
                        if (latestQuality == null ||
                            quality.getCreateTime() != null && latestQuality.getCreateTime() != null &&
                            quality.getCreateTime().after(latestQuality.getCreateTime())) {
                            latestQuality = quality;
                        }
                    }
                }
                if (latestQuality != null) {
                    qualityScore = latestQuality.getTotalScore();
                }
                comprehensiveScore.put("score1", qualityScore);

                // 计算综合素质测评成绩 = 学业成绩 * 70% + 基本素质测评成绩 * 30%
                Double finalScore = academicScore * 0.7 + qualityScore * 0.3;
                comprehensiveScore.put("score2", Math.round(finalScore * 100) / 100.0);

                comprehensiveScores.add(comprehensiveScore);
            }

            // 5. 创建Excel工作簿导出
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("综合素质测评成绩");

            // 创建标题行样式
            CellStyle titleStyle = workbook.createCellStyle();
            Font titleFont = workbook.createFont();
            titleFont.setBold(true);
            titleFont.setFontHeightInPoints((short) 14);
            titleStyle.setFont(titleFont);
            titleStyle.setAlignment(HorizontalAlignment.CENTER);

            // 创建表头样式
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);

            // 创建数据单元格样式
            CellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setBorderTop(BorderStyle.THIN);
            cellStyle.setBorderBottom(BorderStyle.THIN);
            cellStyle.setBorderLeft(BorderStyle.THIN);
            cellStyle.setBorderRight(BorderStyle.THIN);

            // 创建标题行
            Row titleRow = sheet.createRow(0);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue(semesterNameStr + " 综合素质测评成绩表");
            titleCell.setCellStyle(titleStyle);
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 5));

            // 创建表头行
            Row headerRow = sheet.createRow(1);
            String[] headerNames = {"学号", "姓名", "学期", "学业成绩", "基本素质测评成绩", "综合素质测评成绩"};
            for (int i = 0; i < headerNames.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headerNames[i]);
                cell.setCellStyle(headerStyle);
            }

            // 填充数据
            int rowNum = 2;
            for (Map<String, Object> score : comprehensiveScores) {
                Row row = sheet.createRow(rowNum++);

                // 学号
                Cell idCell = row.createCell(0);
                idCell.setCellValue(score.get("studentId").toString());
                idCell.setCellStyle(cellStyle);

                // 姓名
                Cell nameCell = row.createCell(1);
                nameCell.setCellValue(score.get("studentName").toString());
                nameCell.setCellStyle(cellStyle);

                // 学期
                Cell semesterCell = row.createCell(2);
                semesterCell.setCellValue(score.get("semesterName").toString());
                semesterCell.setCellStyle(cellStyle);

                // 学业成绩
                Cell academicCell = row.createCell(3);
                academicCell.setCellValue(Double.parseDouble(score.get("academicScore").toString()));
                academicCell.setCellStyle(cellStyle);

                // 基本素质测评成绩
                Cell qualityCell = row.createCell(4);
                qualityCell.setCellValue(Double.parseDouble(score.get("score1").toString()));
                qualityCell.setCellStyle(cellStyle);

                // 综合素质测评成绩
                Cell finalCell = row.createCell(5);
                finalCell.setCellValue(Double.parseDouble(score.get("score2").toString()));
                finalCell.setCellStyle(cellStyle);
            }

            // 自动调整列宽
            for (int i = 0; i < headerNames.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // 生成文件名
            String fileName = URLEncoder.encode(semesterNameStr + "_综合素质测评成绩表_" +
                    DateTimeUtils.getCurrentTimeCompactString() + ".xlsx",
                    StandardCharsets.UTF_8.toString()).replaceAll("\\+", "%20");

            // 将工作簿写入字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            workbook.close();

            // 设置HTTP响应头
            HttpHeaders responseHeaders = new HttpHeaders();
            responseHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            responseHeaders.setContentDispositionFormData("attachment", fileName);

            // 返回Excel文件
            return new ResponseEntity<byte[]>(outputStream.toByteArray(), responseHeaders, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<byte[]>(("导出失败: " + e.getMessage()).getBytes(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 根据学年获取学业成绩数据
     */
    private Map<String, Double> getAcademicScoresByAcademicYear(List<Integer> semesterIds) {
        Map<String, Double> academicScoreMap = new HashMap<>();

        if (semesterIds.isEmpty()) {
            return academicScoreMap;
        }

        try {
            // 获取该学年所有学期的学业成绩
            List<Map<String, Object>> academicScores = gradeService.getStudentGpaByIds(semesterIds);

            // 按学生分组，计算平均学业成绩
            Map<String, List<Double>> studentScoresMap = new HashMap<>();
            for (Map<String, Object> scoreMap : academicScores) {
                String studentId = scoreMap.get("studentId").toString();
                Object scoreValue = scoreMap.get("academicScore");
                if (scoreValue != null) {
                    Double score = Double.parseDouble(scoreValue.toString());
                    studentScoresMap.computeIfAbsent(studentId, k -> new ArrayList<>()).add(score);
                }
            }

            // 计算每个学生的平均学业成绩
            for (Map.Entry<String, List<Double>> entry : studentScoresMap.entrySet()) {
                String studentId = entry.getKey();
                List<Double> scores = entry.getValue();
                Double averageScore = scores.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                academicScoreMap.put(studentId, Math.round(averageScore * 100) / 100.0);
            }
        } catch (Exception e) {

        }

        return academicScoreMap;
    }

    /**
     * 根据学年获取基本素质测评数据（优先选择第二学期）
     */
    private Map<String, Double> getQualityScoresByAcademicYear(List<Integer> semesterIds, String studentId, String studentName) {
        Map<String, Double> qualityScoreMap = new HashMap<>();

        if (semesterIds.isEmpty()) {
            return qualityScoreMap;
        }

        try {
            // 查询基本素质测评数据
            QueryWrapper<QualityEvaluation> qualityQuery = new QueryWrapper<>();
            qualityQuery.lambda().in(QualityEvaluation::getEvaluationPeriod,
                    semesterIds.stream().map(String::valueOf).collect(Collectors.toList()));
            if (StringUtils.isNotBlank(studentId)) {
                qualityQuery.lambda().like(QualityEvaluation::getStudentId, studentId);
            }
            if (StringUtils.isNotBlank(studentName)) {
                qualityQuery.lambda().like(QualityEvaluation::getStudentName, studentName);
            }
            List<QualityEvaluation> qualityList = qualityEvaluationService.list(qualityQuery);

            // 按学生分组
            Map<String, List<QualityEvaluation>> studentQualityMap = qualityList.stream()
                    .collect(Collectors.groupingBy(QualityEvaluation::getStudentId));

            // 为每个学生选择最优的基本素质测评成绩（优先选择第二学期）
            for (Map.Entry<String, List<QualityEvaluation>> entry : studentQualityMap.entrySet()) {
                String stuId = entry.getKey();
                List<QualityEvaluation> studentQualities = entry.getValue();

                QualityEvaluation selectedQuality = null;
                QualityEvaluation secondSemesterQuality = null;
                QualityEvaluation firstSemesterQuality = null;

                for (QualityEvaluation quality : studentQualities) {
                    boolean isFirstSemester = qualityEvaluationService.isFirstSemester(quality.getEvaluationPeriod());
                    if (isFirstSemester) {
                        if (firstSemesterQuality == null ||
                            (quality.getUpdateTime() != null && firstSemesterQuality.getUpdateTime() != null &&
                             quality.getUpdateTime().after(firstSemesterQuality.getUpdateTime()))) {
                            firstSemesterQuality = quality;
                        }
                    } else {
                        if (secondSemesterQuality == null ||
                            (quality.getUpdateTime() != null && secondSemesterQuality.getUpdateTime() != null &&
                             quality.getUpdateTime().after(secondSemesterQuality.getUpdateTime()))) {
                            secondSemesterQuality = quality;
                        }
                    }
                }

                // 优先选择第二学期的成绩，如果没有则选择第一学期的
                selectedQuality = secondSemesterQuality != null ? secondSemesterQuality : firstSemesterQuality;

                if (selectedQuality != null) {
                    qualityScoreMap.put(stuId, selectedQuality.getTotalScore());
                }
            }
        } catch (Exception e) {

        }

        return qualityScoreMap;
    }
}