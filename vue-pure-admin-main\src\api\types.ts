// 通用API类型定义

// 分页结果
export interface PageResult<T> {
  list: T[];
  total: number;
  pageSize: number;
  pageNum: number;
  totalPages: number;
}

// 通用响应结果
export interface ApiResult<T = any> {
  success: boolean;
  code: number;
  message: string;
  data?: T;
  timestamp: number;
}

// 分页查询参数
export interface PageQuery {
  current?: number;
  size?: number;
  sortField?: string;
  sortOrder?: string;
}
