package com.example.entity.system;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 角色菜单关联实体
 * 注意：此表使用复合主键(role_id, menu_id)，不使用单独的自增ID
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("sys_role_menu")
public class SysRoleMenu {

    /**
     * 角色ID（复合主键之一）
     */
    @TableField("role_id")
    private Integer roleId;

    /**
     * 菜单ID（复合主键之一）
     */
    @TableField("menu_id")
    private Integer menuId;
}
