package com.itmk.web.quality_evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 基本素质测评成绩实体类
 */
@Data
@TableName("quality_evaluation")
public class QualityEvaluation implements Serializable {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer evaluationId;
    
    /**
     * 学生ID
     */
    private String studentId;
    
    /**
     * 学生姓名（冗余字段，便于查询）
     */
    @TableField(exist = false)
    private String studentName;
    
    /**
     * 宿舍号
     */
    @TableField(exist = false)
    private String dormitoryNo;
    
    /**
     * 加分项
     */
    private Double addScore;
    
    /**
     * 扣分项
     */
    private Double reduceScore;
    
    /**
     * 加分说明
     */
    private String addScoreRemark;
    
    /**
     * 扣分说明
     */
    private String reduceScoreRemark;
    
    /**
     * 评分学期ID
     */
    private String evaluationPeriod;
    
    /**
     * 周期得分
     */
    private Double periodScore;
    
    /**
     * 总得分
     */
    private Double totalScore;
    
    /**
     * 上一学期分数（仅在当前为第二学期时有值）
     */
    private Double prevPeriodScore;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 