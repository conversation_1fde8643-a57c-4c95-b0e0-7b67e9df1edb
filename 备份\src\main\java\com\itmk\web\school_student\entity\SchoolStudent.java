package com.itmk.web.school_student.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 学校学生实体类
 *
 * 用于日常学校管理的学生信息，包括：
 * - 学生登录系统
 * - 成绩管理
 * - 质量评估
 * - 权限管理
 *
 * 与学院、专业、班级有关联关系
 * 对应数据库表：school_student
 */
@Data
@TableName("school_student")
public class SchoolStudent {
    @TableId(type = IdType.AUTO)
    private Long stuId;
    @TableField(exist = false)
    private Long roleId;
    @TableField(exist = false)
    private Long collegeId;
    @TableField(exist = false)
    private Long majorId;
    private Long classId;
    private String stuName;
    private String sex;
    private String phone;
    private String intoTime;
    private String studentId;
    private String password;
    //帐户是否过期(1 未过期，0已过期)
    private boolean isAccountNonExpired = true;
    //帐户是否被锁定(1 未锁定，0已锁定)
    private boolean isAccountNonLocked = true;
    //密码是否过期(1 未过期，0已过期)
    private boolean isCredentialsNonExpired = true;
    //帐户是否可用(1 可用，0 删除用户)
    private boolean isEnabled = true;

    @TableField(exist =false)
    private String collegeName;
    @TableField(exist =false)
    private String majorName;
    @TableField(exist =false)
    private String className;
}
