package com.itmk.web.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.sql.Timestamp;

@Data
@TableName("courses") // 指定表名
public class Course {

    @TableId(type = IdType.AUTO) // 主键，自增
    private Integer id;

    private String courseName; // 课程名称
    private String courseCode; // 课程代码
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "0.00")
    private Double credits;   // 课程学分
    private Integer semesterId; // 学期ID，关联学期表

    @TableField(exist = false)
    private String semesterName; // 学期名称
    
    @TableField(exist = false)
    private String academicYear; // 学年

    private Timestamp createdAt; // 创建时间
    private Timestamp updatedAt; // 更新时间
}