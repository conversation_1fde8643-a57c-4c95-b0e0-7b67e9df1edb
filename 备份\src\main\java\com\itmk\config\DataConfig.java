package com.itmk.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.BlockAttackInnerInterceptor;
import com.itmk.utils.LogUtils;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 数据配置类
 * 统一管理MyBatis Plus配置和数据相关配置
 *
 * <AUTHOR>
 * @since 2025-07-19
 */
@Configuration
@MapperScan("com.itmk.*.*.mapper")
public class DataConfig {

    private static final Logger logger = LoggerFactory.getLogger(DataConfig.class);

    // ==================== MyBatis Plus 配置 ====================

    /**
     * MyBatis Plus 拦截器配置
     * 包含分页、乐观锁、防全表更新删除等插件
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        // 分页插件配置
        PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor(DbType.MYSQL);
        // 设置请求的页面大于最大页后操作，true调回到首页，false继续请求
        paginationInterceptor.setOverflow(false);
        // 单页分页条数限制，默认无限制
        paginationInterceptor.setMaxLimit(1000L);
        // count sql，只在必要时进行count查询
        paginationInterceptor.setOptimizeJoin(true);
        interceptor.addInnerInterceptor(paginationInterceptor);

        // 乐观锁插件
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());

        // 防全表更新与删除插件
        interceptor.addInnerInterceptor(new BlockAttackInnerInterceptor());

        LogUtils.logSystemStart(logger, "MyBatis Plus拦截器");

        return interceptor;
    }

    // ==================== 数据源相关配置 ====================

    /**
     * 数据源配置信息记录
     * 在应用启动时记录数据源配置信息
     */
    @Bean
    public DataSourceConfigLogger dataSourceConfigLogger() {
        return new DataSourceConfigLogger();
    }

    /**
     * 数据源配置日志记录器
     */
    public static class DataSourceConfigLogger {

        private static final Logger logger = LoggerFactory.getLogger(DataSourceConfigLogger.class);

        public DataSourceConfigLogger() {
            LogUtils.logSystemStart(logger, "数据源配置");

            // 记录一些关键配置信息（不包含敏感信息）
            LogUtils.logConfigLoaded(logger, "数据库类型", "MySQL");
            LogUtils.logConfigLoaded(logger, "连接池类型", "Druid");
            LogUtils.logConfigLoaded(logger, "ORM框架", "MyBatis Plus");

            logger.info("数据配置初始化完成");
        }
    }

    // ==================== 性能监控配置 ====================

    /**
     * 数据库性能监控配置
     */
    @Bean
    public DatabasePerformanceMonitor databasePerformanceMonitor() {
        return new DatabasePerformanceMonitor();
    }

    /**
     * 数据库性能监控器
     */
    public static class DatabasePerformanceMonitor {

        private static final Logger logger = LoggerFactory.getLogger(DatabasePerformanceMonitor.class);

        public DatabasePerformanceMonitor() {
            LogUtils.logSystemStart(logger, "数据库性能监控");

            // 这里可以添加性能监控相关的初始化逻辑
            // 比如：慢查询监控、连接池监控等

            logger.info("数据库性能监控初始化完成");
        }

        /**
         * 记录慢查询
         *
         * @param sql SQL语句
         * @param executionTime 执行时间（毫秒）
         */
        public void logSlowQuery(String sql, long executionTime) {
            if (executionTime > 2000) { // 超过2秒的查询
                logger.warn("慢查询检测 - 执行时间: {}ms, SQL: {}", executionTime, sql);
            }
        }

        /**
         * 记录数据库连接信息
         *
         * @param activeConnections 活跃连接数
         * @param totalConnections 总连接数
         */
        public void logConnectionInfo(int activeConnections, int totalConnections) {
            if (activeConnections > totalConnections * 0.8) { // 连接使用率超过80%
                logger.warn("数据库连接使用率较高 - 活跃连接: {}, 总连接: {}",
                    activeConnections, totalConnections);
            }
        }
    }

    // ==================== 数据验证配置 ====================

    /**
     * 数据验证配置
     */
    @Bean
    public DataValidationConfig dataValidationConfig() {
        return new DataValidationConfig();
    }

    /**
     * 数据验证配置类
     */
    public static class DataValidationConfig {

        private static final Logger logger = LoggerFactory.getLogger(DataValidationConfig.class);

        public DataValidationConfig() {
            LogUtils.logSystemStart(logger, "数据验证配置");

            // 数据验证相关配置
            LogUtils.logConfigLoaded(logger, "字段策略", "NOT_EMPTY");
            LogUtils.logConfigLoaded(logger, "逻辑删除", "启用");
            LogUtils.logConfigLoaded(logger, "主键策略", "ASSIGN_ID");

            logger.info("数据验证配置初始化完成");
        }
    }

    // ==================== 缓存配置 ====================

    /**
     * 数据缓存配置
     */
    @Bean
    public DataCacheConfig dataCacheConfig() {
        return new DataCacheConfig();
    }

    /**
     * 数据缓存配置类
     */
    public static class DataCacheConfig {

        private static final Logger logger = LoggerFactory.getLogger(DataCacheConfig.class);

        public DataCacheConfig() {
            LogUtils.logSystemStart(logger, "数据缓存配置");

            // 缓存相关配置
            LogUtils.logConfigLoaded(logger, "二级缓存", "启用");
            LogUtils.logConfigLoaded(logger, "本地缓存作用域", "STATEMENT");
            LogUtils.logConfigLoaded(logger, "SQL解析缓存", "启用");

            logger.info("数据缓存配置初始化完成");
        }
    }
}
