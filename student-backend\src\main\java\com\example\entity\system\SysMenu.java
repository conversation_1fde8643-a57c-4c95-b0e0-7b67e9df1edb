package com.example.entity.system;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 系统菜单实体类
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_menu")
public class SysMenu {

    /**
     * 菜单ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 父菜单ID
     */
    @TableField("parent_id")
    private Integer parentId;

    /**
     * 菜单名称
     */
    @TableField("title")
    private String title;

    /**
     * 路由名称
     */
    @TableField("name")
    private String name;

    /**
     * 路由路径
     */
    @TableField("path")
    private String path;

    /**
     * 组件路径
     */
    @TableField("component")
    private String component;

    /**
     * 重定向路径
     */
    @TableField("redirect")
    private String redirect;

    /**
     * 菜单类型：0目录、1菜单、2按钮
     */
    @TableField("menu_type")
    private Integer menuType;

    /**
     * 菜单图标
     */
    @TableField("icon")
    private String icon;

    /**
     * 排序
     */
    @TableField("`rank`")
    private Integer rank;

    /**
     * 是否显示链接：0否、1是
     */
    @TableField("show_link")
    private Integer showLink;

    /**
     * 是否显示父级：0否、1是
     */
    @TableField("show_parent")
    private Integer showParent;

    /**
     * 是否缓存：0否、1是
     */
    @TableField("keep_alive")
    private Integer keepAlive;

    /**
     * 是否固定标签：0否、1是
     */
    @TableField("fixed_tag")
    private Integer fixedTag;

    /**
     * 是否内嵌：0否、1是
     */
    @TableField("frame_src")
    private String frameSrc;

    /**
     * iframe加载状态：0否、1是
     */
    @TableField("frame_loading")
    private Integer frameLoading;

    /**
     * 是否隐藏标签：0否、1是
     */
    @TableField("hidden_tag")
    private Integer hiddenTag;



    /**
     * 是否隐藏：0否、1是 (计算字段，基于show_link的反向值)
     */
    @TableField(exist = false)
    private Integer hideMenu;

    /**
     * 权限标识
     */
    @TableField("auths")
    private String auths;

    /**
     * 状态：0禁用、1启用
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
