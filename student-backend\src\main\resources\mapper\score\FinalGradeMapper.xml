<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.score.FinalGradeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.example.entity.score.Grade">
        <id column="id" property="id" />
        <result column="student_id" property="studentId" />
        <result column="course_code" property="courseCode" />
        <result column="semester_id" property="semesterId" />
        <result column="final_score" property="finalScore" />
        <result column="grade_point" property="gradePoint" />
        <result column="is_retake" property="isRetake" />
        <result column="remarks" property="remarks" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- GradeVO查询映射结果 -->
    <resultMap id="GradeVOResultMap" type="com.example.vo.score.GradeVO">
        <id column="id" property="id" />
        <result column="student_id" property="studentId" />
        <result column="student_name" property="studentName" />
        <result column="class_code" property="classCode" />
        <result column="class_name" property="className" />
        <result column="major_code" property="majorCode" />
        <result column="major_name" property="majorName" />
        <result column="college_code" property="collegeCode" />
        <result column="college_name" property="collegeName" />
        <result column="course_code" property="courseCode" />
        <result column="course_name" property="courseName" />
        <result column="course_type" property="courseType" />
        <result column="credits" property="credits" />
        <result column="semester_id" property="semesterId" />
        <result column="semester_name" property="semesterName" />
        <result column="final_score" property="finalScore" />
        <result column="grade_point" property="gradePoint" />
        <result column="is_retake" property="isRetake" />
        <result column="remarks" property="remarks" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, student_id, course_code, semester_id, final_score, grade_point, is_retake, remarks, created_at, updated_at
    </sql>

    <!-- 基础查询字段 -->
    <sql id="selectGradeVo">
        SELECT
            g.id, g.student_id, g.course_code, g.semester_id, g.final_score,
            g.grade_point, g.is_retake, g.remarks, g.created_at, g.updated_at,
            s.name as student_name, s.class_code,
            c.class_name, c.major_code,
            m.major_name, m.college_code,
            col.college_name,
            co.course_name, co.course_type, co.credits,
            sem.semester_name
        FROM grades g
        LEFT JOIN students s ON g.student_id = s.student_id
        LEFT JOIN classes c ON s.class_code = c.class_code
        LEFT JOIN majors m ON c.major_code = m.major_code
        LEFT JOIN colleges col ON m.college_code = col.college_code
        LEFT JOIN courses co ON g.course_code = co.course_code
        LEFT JOIN semesters sem ON g.semester_id = sem.id
    </sql>

    <!-- 分页查询期末成绩列表 -->
    <select id="selectGradePage" resultMap="GradeVOResultMap">
        <include refid="selectGradeVo"/>
        <where>
            <if test="query.studentId != null and query.studentId != ''">
                AND g.student_id LIKE CONCAT('%', #{query.studentId}, '%')
            </if>
            <if test="query.studentName != null and query.studentName != ''">
                AND s.name LIKE CONCAT('%', #{query.studentName}, '%')
            </if>
            <if test="query.classCode != null and query.classCode != ''">
                AND s.class_code = #{query.classCode}
            </if>
            <if test="query.majorCode != null and query.majorCode != ''">
                AND c.major_code = #{query.majorCode}
            </if>
            <if test="query.collegeCode != null and query.collegeCode != ''">
                AND m.college_code = #{query.collegeCode}
            </if>
            <if test="query.courseCode != null and query.courseCode != ''">
                AND g.course_code LIKE CONCAT('%', #{query.courseCode}, '%')
            </if>
            <if test="query.courseName != null and query.courseName != ''">
                AND co.course_name LIKE CONCAT('%', #{query.courseName}, '%')
            </if>
            <if test="query.courseType != null and query.courseType != ''">
                AND co.course_type = #{query.courseType}
            </if>
            <if test="query.semesterId != null">
                AND g.semester_id = #{query.semesterId}
            </if>
            <if test="query.minScore != null">
                AND g.final_score >= #{query.minScore}
            </if>
            <if test="query.maxScore != null">
                AND g.final_score &lt;= #{query.maxScore}
            </if>
            <if test="query.isRetake != null">
                AND g.is_retake = #{query.isRetake}
            </if>
            <if test="query.grade != null and query.grade != ''">
                AND s.grade = #{query.grade}
            </if>
        </where>
        ORDER BY g.student_id, g.semester_id, g.course_code
    </select>

    <!-- 根据班级查询期末成绩列表 -->
    <select id="selectGradesByClass" resultMap="GradeVOResultMap">
        <include refid="selectGradeVo"/>
        WHERE s.class_code = #{classCode}
        <if test="semesterId != null">
            AND g.semester_id = #{semesterId}
        </if>
        ORDER BY g.student_id, g.course_code
    </select>

    <!-- 根据学生查询期末成绩列表 -->
    <select id="selectGradesByStudent" resultMap="GradeVOResultMap">
        <include refid="selectGradeVo"/>
        WHERE g.student_id = #{studentId}
        <if test="semesterId != null">
            AND g.semester_id = #{semesterId}
        </if>
        ORDER BY g.semester_id, g.course_code
    </select>

    <!-- 按学号姓名横向输出期末成绩 - 获取课程列表 -->
    <select id="selectCoursesByClassAndSemester" resultType="com.example.vo.educational.CourseVO">
        SELECT DISTINCT
            co.id,
            co.course_code as courseCode,
            co.course_name as courseName,
            co.credits,
            co.course_type as courseType,
            co.description,
            cc.semester_id as semesterId,
            s.semester_name as semesterName,
            s.academic_year as academicYear,
            s.semester_number as semesterNumber,
            cc.sort_order as sortOrder
        FROM courses co
        INNER JOIN class_courses cc ON co.course_code = cc.course_code
        LEFT JOIN semesters s ON cc.semester_id = s.id
        WHERE cc.class_code = #{classCode}
        <if test="semesterId != null">
            AND cc.semester_id = #{semesterId}
        </if>
        ORDER BY s.academic_year, s.semester_number, cc.sort_order
    </select>

    <!-- 按学号姓名横向输出期末成绩 - 获取学生成绩数据 -->
    <select id="selectStudentsWithGrades" resultType="java.util.Map">
        SELECT
            s.student_id as studentId,
            s.name as studentName,
            ROUND(AVG(g.final_score), 2) as avgScore,
            SUM(co.credits) as totalCredits
        FROM students s
        LEFT JOIN grades g ON s.student_id = g.student_id
        LEFT JOIN courses co ON g.course_code = co.course_code
        WHERE s.class_code = #{classCode}
        <if test="semesterId != null">
            AND (g.semester_id = #{semesterId} OR g.semester_id IS NULL)
        </if>
        GROUP BY s.student_id, s.name
        ORDER BY s.student_id
        LIMIT #{pageSize} OFFSET #{offset}
    </select>

    <!-- 按学号姓名横向输出期末成绩 - 获取具体成绩 -->
    <select id="selectGradesByStudentAndClass" resultType="java.util.Map">
        SELECT
            g.student_id as studentId,
            g.course_code as courseCode,
            g.final_score as finalScore,
            CASE
                WHEN g.grade_point IS NOT NULL THEN g.grade_point
                WHEN g.final_score IS NOT NULL THEN GREATEST(0.0, g.final_score / 10.0 - 5.0)
                ELSE NULL
            END as gradePoint,
            g.is_retake as isRetake
        FROM grades g
        INNER JOIN students s ON g.student_id = s.student_id
        WHERE s.class_code = #{classCode}
        <if test="semesterId != null">
            AND g.semester_id = #{semesterId}
        </if>
        ORDER BY g.student_id, g.course_code
    </select>

    <!-- 获取学生总数 -->
    <select id="selectStudentCountByClass" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM students s
        WHERE s.class_code = #{classCode}
    </select>

    <!-- 及格分数线常量 -->
    <sql id="PASS_SCORE">60</sql>

    <!-- 计算学业成绩 -->
    <select id="calculateGpa" resultType="com.example.entity.score.Grade">
        SELECT
            g.student_id,
            SUM(
                CASE
                    WHEN g.final_score IS NULL OR g.final_score &lt; <include refid="PASS_SCORE"/> THEN 0
                    ELSE g.final_score * c.credits
                END
            ) / NULLIF(SUM(
                CASE
                    WHEN g.final_score IS NULL THEN 0
                    ELSE c.credits
                END
            ), 0) AS calculateGpa
        FROM grades g
        LEFT JOIN courses c ON g.course_code = c.course_code
        <where>
            <if test="parm.semesterId != null">
                g.semester_id = #{parm.semesterId}
            </if>
        </where>
        GROUP BY g.student_id
    </select>

    <!-- 获取学生成绩绩点，用于横向展示 -->
    <select id="getStudentGpa" resultType="java.util.Map">
        SELECT
            g.student_id AS studentId,
            SUM(
                CASE
                    WHEN g.final_score IS NULL OR g.final_score &lt; <include refid="PASS_SCORE"/> THEN 0
                    ELSE ((g.final_score / 10) - 5) * c.credits
                END
            ) / NULLIF(SUM(
                CASE
                    WHEN g.final_score IS NULL THEN 0
                    ELSE c.credits
                END
            ), 0) AS calculateGpa,
            SUM(
                CASE
                    WHEN g.final_score IS NULL OR g.final_score &lt; <include refid="PASS_SCORE"/> THEN 0
                    ELSE g.final_score * c.credits
                END
            ) / NULLIF(SUM(
                CASE
                    WHEN g.final_score IS NULL THEN 0
                    ELSE c.credits
                END
            ), 0) AS academicScore
        FROM
            grades g
        LEFT JOIN
            courses c ON g.course_code = c.course_code
        <where>
            <if test="semesterId != null">
                g.semester_id = #{semesterId}
            </if>
        </where>
        GROUP BY
            g.student_id
    </select>

    <!-- 获取多个学期的学生成绩绩点 -->
    <select id="getStudentGpaByIds" resultType="java.util.Map">
        SELECT
            g.student_id AS studentId,
            SUM(
                CASE
                    WHEN g.final_score IS NULL OR g.final_score &lt; <include refid="PASS_SCORE"/> THEN 0
                    ELSE ((g.final_score / 10) - 5) * c.credits
                END
            ) / NULLIF(SUM(
                CASE
                    WHEN g.final_score IS NULL THEN 0
                    ELSE c.credits
                END
            ), 0) AS calculateGpa,
            SUM(
                CASE
                    WHEN g.final_score IS NULL OR g.final_score &lt; <include refid="PASS_SCORE"/> THEN 0
                    ELSE g.final_score * c.credits
                END
            ) / NULLIF(SUM(
                CASE
                    WHEN g.final_score IS NULL THEN 0
                    ELSE c.credits
                END
            ), 0) AS academicScore
        FROM
            grades g
        LEFT JOIN
            courses c ON g.course_code = c.course_code
        <where>
            <if test="semesterIds != null and semesterIds.size() > 0">
                g.semester_id IN
                <foreach collection="semesterIds" item="semesterId" open="(" separator="," close=")">
                    #{semesterId}
                </foreach>
            </if>
        </where>
        GROUP BY
            g.student_id
    </select>

    <!-- 获取学生平均分 -->
    <select id="getStudentAverageScore" resultType="java.util.Map">
        SELECT
            g.student_id AS studentId,
            s.name AS studentName,
            ROUND(AVG(g.final_score), 2) AS avgScore
        FROM
            grades g
        LEFT JOIN
            students s ON g.student_id = s.student_id
        <where>
            <if test="classCode != null">
                AND s.class_code = #{classCode}
            </if>
            <if test="semesterId != null">
                AND g.semester_id = #{semesterId}
            </if>
        </where>
        GROUP BY
            g.student_id, s.name

    </select>

    <!-- 获取班级学生的学业成绩和绩点 -->
    <select id="getStudentAcademicScoreAndGpa" resultType="java.util.Map">
        SELECT
            g.student_id AS studentId,
            s.name AS studentName,
            ROUND(AVG(
                CASE
                    WHEN g.final_score IS NOT NULL THEN g.final_score
                    ELSE NULL
                END
            ), 2) AS avgScore,
            SUM(
                CASE
                    WHEN g.final_score IS NULL OR g.final_score &lt; <include refid="PASS_SCORE"/> THEN 0
                    ELSE g.final_score * c.credits
                END
            ) / NULLIF(SUM(
                CASE
                    WHEN g.final_score IS NULL THEN 0
                    ELSE c.credits
                END
            ), 0) AS academicScore,
            SUM(
                CASE
                    WHEN g.final_score IS NULL OR g.final_score &lt; <include refid="PASS_SCORE"/> THEN 0
                    ELSE ((g.final_score / 10) - 5) * c.credits
                END
            ) / NULLIF(SUM(
                CASE
                    WHEN g.final_score IS NULL THEN 0
                    ELSE c.credits
                END
            ), 0) AS calculateGpa
        FROM
            grades g
        LEFT JOIN
            courses c ON g.course_code = c.course_code
        LEFT JOIN
            students s ON g.student_id = s.student_id
        WHERE
            s.class_code = #{classCode}
            <if test="semesterId != null">
                AND g.semester_id = #{semesterId}
            </if>
        GROUP BY
            g.student_id, s.name
    </select>

    <!-- 根据班级代码获取班级名称 -->
    <select id="getClassNameByCode" resultType="java.lang.String">
        SELECT c.class_name
        FROM classes c
        WHERE c.class_code = #{classCode}
    </select>

    <!-- 根据学期ID获取学期名称 -->
    <select id="getSemesterNameById" resultType="java.lang.String">
        SELECT s.semester_name
        FROM semesters s
        WHERE s.id = #{semesterId}
    </select>

</mapper>
