<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.itmk.web.grades.mapper.GradeMapper">
    <!-- 定义常量 -->
    <sql id="PASS_SCORE">60</sql>
    
    <!-- 查询成绩 -->
    <select id="getList" resultType="com.itmk.web.grades.entity.Grade">
        select
        g.*,
        c.course_name,
        c.credits,
        s.semester_name as semesterName,
        CASE
        <!-- 转译 < 应该使用 &lt;  > 应该使用 &gt;-->
        WHEN g.grade IS NULL THEN NULL
        WHEN g.grade &lt; <include refid="PASS_SCORE"/> THEN 0.0
        ELSE ((g.grade / 10) - 5)
        END AS gpa
        from grades g
        left join courses c on g.course_code = c.course_code
        left join semesters s on g.semester_id = s.semester_id
        where 1=1
        <if test="parm.studentId != null and parm.studentId != ''">
            and g.student_id like concat('%', #{parm.studentId}, '%')
        </if>
        <if test="parm.courseCode != null and parm.courseCode != ''">
            and g.course_code like concat('%', #{parm.courseCode}, '%')
        </if>
        <if test="parm.semesterId != null">
            and g.semester_id = #{parm.semesterId}
        </if>
    </select>

    <!-- 计算学业成绩 -->
    <select id="calculategpa" resultType="com.itmk.web.grades.entity.Grade">
        SELECT 
            g.student_id,
            SUM(
                CASE
                    WHEN g.grade IS NULL OR g.grade &lt; <include refid="PASS_SCORE"/> THEN 0
                    ELSE g.grade * c.credits
                END
            ) / NULLIF(SUM(
                CASE
                    WHEN g.grade IS NULL THEN 0
                    ELSE c.credits
                END
            ), 0) AS calculateGpa
        FROM grades g
        LEFT JOIN courses c ON g.course_code = c.course_code
        <where>
            <if test="parm.semesterId != null">
                g.semester_id = #{parm.semesterId}
            </if>
        </where>
        GROUP BY g.student_id
    </select>
    
    <!-- 获取学生成绩绩点，用于横向展示 -->
    <select id="getStudentGpa" resultType="java.util.Map">
        SELECT 
            g.student_id AS studentId,
            SUM(
                CASE
                    WHEN g.grade IS NULL OR g.grade &lt; <include refid="PASS_SCORE"/> THEN 0
                    ELSE ((g.grade / 10) - 5) * c.credits
                END
            ) / NULLIF(SUM(
                CASE
                    WHEN g.grade IS NULL THEN 0
                    ELSE c.credits
                END
            ), 0) AS calculateGpa,
            SUM(
                CASE
                    WHEN g.grade IS NULL OR g.grade &lt; <include refid="PASS_SCORE"/> THEN 0
                    ELSE g.grade * c.credits
                END
            ) / NULLIF(SUM(
                CASE
                    WHEN g.grade IS NULL THEN 0
                    ELSE c.credits
                END
            ), 0) AS academicScore
        FROM 
            grades g
        LEFT JOIN 
            courses c ON g.course_code = c.course_code
        <where>
            <if test="semesterId != null">
                g.semester_id = #{semesterId}
            </if>
        </where>
        GROUP BY 
            g.student_id
    </select>
    
    <!-- 获取多个学期的学生成绩绩点 -->
    <select id="getStudentGpaByIds" resultType="java.util.Map">
        SELECT 
            g.student_id AS studentId,
            SUM(
                CASE
                    WHEN g.grade IS NULL OR g.grade &lt; <include refid="PASS_SCORE"/> THEN 0
                    ELSE ((g.grade / 10) - 5) * c.credits
                END
            ) / NULLIF(SUM(
                CASE
                    WHEN g.grade IS NULL THEN 0
                    ELSE c.credits
                END
            ), 0) AS calculateGpa,
            SUM(
                CASE
                    WHEN g.grade IS NULL OR g.grade &lt; <include refid="PASS_SCORE"/> THEN 0
                    ELSE g.grade * c.credits
                END
            ) / NULLIF(SUM(
                CASE
                    WHEN g.grade IS NULL THEN 0
                    ELSE c.credits
                END
            ), 0) AS academicScore
        FROM 
            grades g
        LEFT JOIN 
            courses c ON g.course_code = c.course_code
        <where>
            <if test="semesterIds != null and semesterIds.size() > 0">
                g.semester_id IN 
                <foreach collection="semesterIds" item="semesterId" open="(" separator="," close=")">
                    #{semesterId}
                </foreach>
            </if>
        </where>
        GROUP BY 
            g.student_id
    </select>
</mapper>