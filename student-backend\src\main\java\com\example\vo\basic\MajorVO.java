package com.example.vo.basic;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.time.LocalDateTime;

/**
 * 专业VO
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Data
@Schema(description = "专业信息")
public class MajorVO {

    @Schema(description = "专业ID")
    private Integer id;

    @Schema(description = "专业代码", required = true)
    @NotBlank(message = "专业代码不能为空")
    @Size(max = 20, message = "专业代码长度不能超过20个字符")
    private String majorCode;

    @Schema(description = "专业名称", required = true)
    @NotBlank(message = "专业名称不能为空")
    @Size(max = 100, message = "专业名称长度不能超过100个字符")
    private String majorName;

    @Schema(description = "所属学院代码", required = true)
    @NotBlank(message = "所属学院代码不能为空")
    @Size(max = 20, message = "学院代码长度不能超过20个字符")
    private String collegeCode;

    @Schema(description = "学制年限", required = true)
    @NotNull(message = "学制年限不能为空")
    @Min(value = 1, message = "学制年限不能少于1年")
    @Max(value = 4, message = "学制年限不能超过4年")
    private Integer duration;

    @Schema(description = "专业描述")
    @Size(max = 500, message = "专业描述长度不能超过500个字符")
    private String description;

    @Schema(description = "学院名称")
    private String collegeName;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
}
