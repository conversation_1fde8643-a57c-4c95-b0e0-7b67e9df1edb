package com.example.config;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * 性能监控配置
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Aspect
@Component
public class PerformanceConfig {

    /**
     * 监控Service层方法执行时间
     */
    @Around("execution(* com.example.service.impl.*.*(..))")
    public Object monitorServicePerformance(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        String methodName = joinPoint.getSignature().toShortString();
        
        try {
            Object result = joinPoint.proceed();
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            
            // 只记录执行时间超过100ms的方法
            if (executionTime > 100) {
                log.warn("慢方法执行: {} 耗时: {}ms", methodName, executionTime);
            } else if (log.isDebugEnabled()) {
                log.debug("方法执行: {} 耗时: {}ms", methodName, executionTime);
            }
            
            return result;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            log.error("方法执行异常: {} 耗时: {}ms", methodName, executionTime, e);
            throw e;
        }
    }

    /**
     * 监控Controller层方法执行时间
     */
    @Around("execution(* com.example.controller.*.*(..))")
    public Object monitorControllerPerformance(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        String methodName = joinPoint.getSignature().toShortString();
        
        try {
            Object result = joinPoint.proceed();
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            
            // 只记录执行时间超过200ms的接口
            if (executionTime > 200) {
                log.warn("慢接口: {} 耗时: {}ms", methodName, executionTime);
            } else if (log.isDebugEnabled()) {
                log.debug("接口执行: {} 耗时: {}ms", methodName, executionTime);
            }
            
            return result;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            log.error("接口执行异常: {} 耗时: {}ms", methodName, executionTime, e);
            throw e;
        }
    }
}
