# 基本素质测评成绩登记功能

## 功能概述

基本素质测评成绩登记功能是一个完整的学生素质测评管理系统，支持成绩录入、批量导入、数据导出等功能。

## 功能特性

### 🎯 核心功能
- **成绩录入**：支持单条记录的新增和编辑
- **批量导入**：支持Excel文件批量导入成绩数据
- **数据导出**：支持将成绩数据导出为Excel文件
- **智能计算**：自动计算总分（基础分 + 加分 - 扣分）
- **分数解析**：自动解析加分扣分说明中的分数

### 📊 数据管理
- **多维度查询**：支持按专业、班级、学期查询
- **搜索过滤**：支持按学生姓名、学号、宿舍号搜索
- **分页显示**：支持大数据量的分页展示
- **实时统计**：显示班级成绩统计信息

### 🎨 用户界面
- **响应式设计**：适配不同屏幕尺寸
- **直观操作**：简洁明了的操作界面
- **状态反馈**：完善的加载状态和错误提示
- **数据可视化**：成绩数据的图表展示

## 技术架构

### 前端技术栈
- **Vue 3** + **TypeScript**：现代化的前端框架
- **Element Plus**：UI组件库
- **Composition API**：组合式API设计
- **Pinia**：状态管理
- **Vite**：构建工具

### 后端技术栈
- **Spring Boot**：Java后端框架
- **MyBatis Plus**：ORM框架
- **MySQL**：数据库
- **Apache POI**：Excel文件处理

## 目录结构

```
src/views/score/quality-assessment/
├── index.vue                 # 主页面
├── form/
│   ├── index.vue            # 编辑表单
│   └── import.vue           # 导入表单
├── utils/
│   ├── hook.tsx             # 业务逻辑钩子
│   ├── rule.ts              # 表单验证规则
│   └── types.ts             # TypeScript类型定义
└── README.md                # 功能说明文档

src/api/score/
└── quality-assessment.ts    # API接口定义

backend-example/
├── QualityAssessmentController.java  # 控制器示例
├── QualityAssessment.java           # 实体类示例
└── quality_assessment.sql           # 数据库表结构
```

## 数据库设计

### 主表：quality_assessment
| 字段名 | 类型 | 说明 |
|--------|------|------|
| evaluation_id | bigint | 评价ID（主键）|
| student_id | varchar(20) | 学生学号 |
| student_name | varchar(50) | 学生姓名 |
| dormitory_no | varchar(20) | 宿舍号 |
| add_score | decimal(5,2) | 加分 |
| reduce_score | decimal(5,2) | 扣分 |
| add_score_remark | text | 加分说明 |
| reduce_score_remark | text | 扣分说明 |
| evaluation_period | varchar(20) | 评分学期 |
| period_score | decimal(5,2) | 基础分 |
| total_score | decimal(5,2) | 总得分 |
| class_id | varchar(20) | 班级ID |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |

### 索引设计
- 主键索引：evaluation_id
- 唯一索引：student_id + evaluation_period
- 复合索引：class_id + evaluation_period
- 单列索引：student_id, dormitory_no, total_score

## API接口

### 查询接口
- `POST /quality-assessment/list` - 分页查询成绩列表
- `GET /quality-assessment/{id}` - 根据ID查询详情

### 操作接口
- `POST /quality-assessment/add` - 新增成绩记录
- `POST /quality-assessment/update` - 修改成绩记录
- `DELETE /quality-assessment/delete/{id}` - 删除成绩记录

### 导入导出接口
- `POST /quality-assessment/export` - 导出成绩数据
- `POST /quality-assessment/download-template` - 下载导入模板
- `POST /quality-assessment/import` - 导入成绩数据

## 使用说明

### 1. 成绩录入
1. 选择专业、班级、学期
2. 点击"新增记录"按钮
3. 填写学生信息和成绩详情
4. 系统自动计算总分
5. 保存记录

### 2. 批量导入
1. 选择班级和学期
2. 点击"成绩导入"按钮
3. 下载导入模板
4. 按模板格式填写数据
5. 上传Excel文件
6. 查看导入结果

### 3. 数据导出
1. 设置查询条件
2. 点击"导出数据"按钮
3. 系统生成Excel文件
4. 下载到本地

## 特色功能

### 智能分数计算
- 自动解析加分说明中的 `+数字` 格式
- 自动解析扣分说明中的 `-数字` 格式
- 实时计算总分：基础分 + 加分 - 扣分

### 数据验证
- 学号唯一性验证
- 分数范围验证（0-200分）
- 必填字段验证
- 文件格式验证

### 用户体验
- 响应式布局设计
- 加载状态提示
- 操作结果反馈
- 错误信息展示

## 部署说明

### 前端部署
1. 安装依赖：`npm install`
2. 构建项目：`npm run build`
3. 部署到Web服务器

### 后端部署
1. 配置数据库连接
2. 执行SQL脚本创建表结构
3. 启动Spring Boot应用
4. 配置跨域和文件上传

### 数据库配置
1. 创建数据库
2. 执行 `quality_assessment.sql` 脚本
3. 配置数据库连接参数
4. 设置字符集为 utf8mb4

## 注意事项

1. **数据安全**：确保学生信息的隐私保护
2. **权限控制**：实现适当的用户权限管理
3. **数据备份**：定期备份重要数据
4. **性能优化**：大数据量时注意查询性能
5. **文件大小**：限制上传文件的大小和格式

## 扩展功能

### 可扩展的功能点
- 成绩统计分析
- 图表数据展示
- 成绩排名功能
- 历史记录查询
- 批量操作功能
- 数据审核流程

### 技术优化
- 缓存机制
- 异步处理
- 数据压缩
- CDN加速
- 负载均衡
