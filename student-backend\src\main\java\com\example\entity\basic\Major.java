package com.example.entity.basic;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 专业实体类
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("majors")
public class Major {

    /**
     * 专业ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 专业代码
     */
    @TableField("major_code")
    private String majorCode;

    /**
     * 专业名称
     */
    @TableField("major_name")
    private String majorName;

    /**
     * 所属学院代码
     */
    @TableField("college_code")
    private String collegeCode;

    /**
     * 学制年限
     */
    @TableField("duration")
    private Integer duration;

    /**
     * 专业描述
     */
    @TableField("description")
    private String description;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
