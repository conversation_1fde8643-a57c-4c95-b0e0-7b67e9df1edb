package com.example.controller.system;

import com.example.common.Result;
import com.example.dto.system.DeptQueryDTO;
import com.example.service.system.DeptService;
import com.example.vo.system.DeptVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 系统部门管理控制器
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@RestController
@RequestMapping("/api/system/dept")
@RequiredArgsConstructor
@Validated
@Tag(name = "系统部门管理", description = "系统部门管理相关接口")
public class SystemDeptController {

    private final DeptService deptService;

    /**
     * 获取部门列表（树形结构）
     */
    @PostMapping("/list")
    @Operation(summary = "获取部门列表", description = "获取部门树形列表")
    public Result<List<DeptVO>> getDeptList(@RequestBody(required = false) DeptQueryDTO query) {
        List<DeptVO> deptList = deptService.getDeptList(query);
        return Result.success("获取所有部门成功", deptList);
    }

    /**
     * 获取所有部门列表（用于下拉选择）
     */
    @GetMapping("/all")
    @Operation(summary = "获取所有部门", description = "获取所有可用部门列表")
    public Result<List<DeptVO>> getAllDepts() {
        List<DeptVO> deptList = deptService.getAllDepts();
        return Result.success("获取所有部门成功", deptList);
    }

    /**
     * 根据ID获取部门详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取部门详情", description = "根据ID获取部门详情")
    public Result<DeptVO> getDeptById(@PathVariable Integer id) {
        if (id == null) {
            return Result.badRequest("部门ID不能为空");
        }
        DeptVO dept = deptService.getDeptById(id);
        return Result.success("查询成功", dept);
    }

    /**
     * 新增部门
     */
    @PostMapping("/save")
    @Operation(summary = "新增部门", description = "新增系统部门")
    public Result<Void> saveDept(@Valid @RequestBody DeptVO deptVO) {
        deptService.saveDept(deptVO);
        return Result.success("新增部门成功");
    }

    /**
     * 更新部门
     */
    @PostMapping("/update")
    @Operation(summary = "更新部门", description = "更新系统部门")
    public Result<Void> updateDept(@Valid @RequestBody DeptVO deptVO) {
        if (deptVO.getId() == null) {
            return Result.badRequest("部门ID不能为空");
        }
        deptService.updateDept(deptVO);
        return Result.success("修改部门成功");
    }

    /**
     * 删除部门
     */
    @PostMapping("/delete")
    @Operation(summary = "删除部门", description = "删除系统部门")
    public Result<Void> deleteDept(@RequestBody Map<String, Object> params) {
        if (params == null || params.get("id") == null) {
            return Result.badRequest("部门ID不能为空");
        }

        try {
            Integer deptId = Integer.valueOf(params.get("id").toString());
            deptService.deleteDept(deptId);
            return Result.success("删除部门成功");
        } catch (NumberFormatException e) {
            return Result.badRequest("部门ID格式错误");
        }
    }

    /**
     * 切换部门状态
     */
    @PostMapping("/toggle-status")
    @Operation(summary = "切换部门状态", description = "启用或禁用部门")
    public Result<Void> toggleDeptStatus(@RequestBody Map<String, Object> params) {
        if (params == null || params.get("deptId") == null || params.get("status") == null) {
            return Result.badRequest("部门ID和状态不能为空");
        }

        try {
            Integer deptId = Integer.valueOf(params.get("deptId").toString());
            Integer status = Integer.valueOf(params.get("status").toString());
            deptService.toggleDeptStatus(deptId, status);
            String statusText = status == 1 ? "启用" : "禁用";
            return Result.success(statusText + "部门成功");
        } catch (NumberFormatException e) {
            return Result.badRequest("参数格式错误");
        }
    }
}
