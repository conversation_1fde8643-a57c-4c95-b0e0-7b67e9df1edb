package com.example.controller.system;

import com.example.common.PageResult;
import com.example.common.Result;
import com.example.dto.system.UserQueryDTO;
import com.example.service.system.UserService;
import com.example.vo.system.UserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 系统用户管理控制器
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@RestController
@RequestMapping("/api/system/user")
@RequiredArgsConstructor
@Validated
@Tag(name = "系统用户管理", description = "系统用户管理相关接口")
public class SystemUserController {

    private final UserService userService;

    /**
     * 获取用户列表
     */
    @PostMapping("/list")
    @Operation(summary = "获取用户列表", description = "分页查询用户列表")
    public Result<PageResult<UserVO>> getUserList(@RequestBody(required = false) UserQueryDTO query) {
        PageResult<UserVO> userList = userService.getUserList(query);
        return Result.success("查询成功", userList);
    }

    /**
     * 获取所有用户列表（用于下拉选择）
     */
    @GetMapping("/all")
    @Operation(summary = "获取所有用户", description = "获取所有可用用户列表")
    public Result<List<UserVO>> getAllUsers() {
        List<UserVO> userList = userService.getAllUsers();
        return Result.success("查询成功", userList);
    }

    /**
     * 根据ID获取用户详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取用户详情", description = "根据ID获取用户详情")
    public Result<UserVO> getUserById(@PathVariable Integer id) {
        if (id == null) {
            return Result.badRequest("用户ID不能为空");
        }
        UserVO user = userService.getUserById(id);
        return Result.success("查询成功", user);
    }

    /**
     * 新增用户
     */
    @PostMapping("/save")
    @Operation(summary = "新增用户", description = "新增系统用户")
    public Result<Void> saveUser(@Valid @RequestBody UserVO userVO) {
        userService.saveUser(userVO);
        return Result.success("新增用户成功");
    }

    /**
     * 更新用户
     */
    @PostMapping("/update")
    @Operation(summary = "更新用户", description = "更新系统用户")
    public Result<Void> updateUser(@Valid @RequestBody UserVO userVO) {
        if (userVO.getId() == null) {
            return Result.badRequest("用户ID不能为空");
        }
        userService.updateUser(userVO);
        return Result.success("修改用户成功");
    }

    /**
     * 删除用户
     */
    @PostMapping("/delete")
    @Operation(summary = "删除用户", description = "删除系统用户")
    public Result<Void> deleteUser(@RequestBody Map<String, Object> params) {
        if (params == null || params.get("id") == null) {
            return Result.badRequest("用户ID不能为空");
        }

        try {
            Integer userId = Integer.valueOf(params.get("id").toString());
            userService.deleteUser(userId);
            return Result.success("删除用户成功");
        } catch (NumberFormatException e) {
            return Result.badRequest("用户ID格式错误");
        }
    }

    /**
     * 重置用户密码
     */
    @PostMapping("/reset-password")
    @Operation(summary = "重置密码", description = "重置用户密码")
    public Result<Void> resetPassword(@RequestBody Map<String, Object> params) {
        if (params == null || params.get("userId") == null) {
            return Result.badRequest("用户ID不能为空");
        }

        try {
            Integer userId = Integer.valueOf(params.get("userId").toString());
            String newPassword = params.get("newPassword") != null ?
                params.get("newPassword").toString() : "123456";
            userService.resetPassword(userId, newPassword);
            return Result.success("重置密码成功");
        } catch (NumberFormatException e) {
            return Result.badRequest("用户ID格式错误");
        }
    }

    /**
     * 分配用户角色
     */
    @PostMapping("/assign-roles")
    @Operation(summary = "分配角色", description = "为用户分配角色")
    public Result<Void> assignRoles(@RequestBody Map<String, Object> params) {
        if (params == null || params.get("userId") == null || params.get("roleIds") == null) {
            return Result.badRequest("用户ID和角色ID不能为空");
        }

        try {
            Integer userId = Integer.valueOf(params.get("userId").toString());
            @SuppressWarnings("unchecked")
            List<Integer> roleIds = (List<Integer>) params.get("roleIds");
            userService.assignRoles(userId, roleIds);
            return Result.success("分配角色成功");
        } catch (Exception e) {
            return Result.badRequest("参数格式错误");
        }
    }

    /**
     * 获取用户角色ID列表
     */
    @PostMapping("/role-ids")
    @Operation(summary = "获取用户角色", description = "获取指定用户的角色ID列表")
    public Result<List<Integer>> getUserRoleIds(@RequestBody Map<String, Object> params) {
        if (params == null || params.get("userId") == null) {
            return Result.badRequest("用户ID不能为空");
        }

        try {
            Integer userId = Integer.valueOf(params.get("userId").toString());
            List<Integer> roleIds = userService.getUserRoleIds(userId);
            return Result.success("查询成功", roleIds);
        } catch (NumberFormatException e) {
            return Result.badRequest("用户ID格式错误");
        }
    }

    /**
     * 启用/禁用用户
     */
    @PostMapping("/toggle-status")
    @Operation(summary = "切换用户状态", description = "启用或禁用用户")
    public Result<Void> toggleUserStatus(@RequestBody Map<String, Object> params) {
        if (params == null || params.get("userId") == null || params.get("status") == null) {
            return Result.badRequest("用户ID和状态不能为空");
        }

        try {
            Integer userId = Integer.valueOf(params.get("userId").toString());
            Integer status = Integer.valueOf(params.get("status").toString());
            userService.toggleUserStatus(userId, status);
            String statusText = status == 1 ? "启用" : "禁用";
            return Result.success(statusText + "用户成功");
        } catch (NumberFormatException e) {
            return Result.badRequest("参数格式错误");
        }
    }
}
