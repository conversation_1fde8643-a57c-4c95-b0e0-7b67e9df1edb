<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import ReCol from "@/components/ReCol";
import { formRules } from "../utils/rule";

// API导入
import { getStudentsByClass } from "@/api/basic/student";

// Props
interface Props {
  formInline?: {
    title: string;
    evaluationId: number | null;
    studentId: string;
    studentName: string;
    dormitoryNo: string;
    addScore: number;
    reduceScore: number;
    addScoreRemark: string;
    reduceScoreRemark: string;
    evaluationPeriod: string;
    evaluationPeriodName: string;
    periodScore: number;
    totalScore: number;
    classId: string;
    className: string;
  };
}

const props = withDefaults(defineProps<Props>(), {
  formInline: () => ({
    title: "基本素质测评记录",
    evaluationId: null,
    studentId: "",
    studentName: "",
    dormitoryNo: "",
    addScore: 0,
    reduceScore: 0,
    addScoreRemark: "",
    reduceScoreRemark: "",
    evaluationPeriod: "",
    evaluationPeriodName: "",
    periodScore: 60,
    totalScore: 60,
    classId: "",
    className: ""
  })
});

// Emits
const emit = defineEmits<{
  success: [];
}>();

// 表单数据
const formInline = computed(() => props.formInline);

const ruleFormRef = ref();
const studentsLoading = ref(false);
const studentOptions = ref([]);

// 加载班级学生
const loadStudents = async () => {
  if (!formInline.value?.classId) {
    return;
  }

  studentsLoading.value = true;
  try {
    const response = await getStudentsByClass(formInline.value.classId);
    studentOptions.value = response.data || [];
  } catch (error) {
    console.error("加载学生数据失败:", error);
    ElMessage.error("加载学生数据失败");
  } finally {
    studentsLoading.value = false;
  }
};

// 学生选择变化处理
const handleStudentChange = (studentId: string) => {
  const student = studentOptions.value.find((s: any) => s.studentId === studentId);
  if (student && formInline.value) {
    formInline.value.studentName = student.studentName || "";
    formInline.value.dormitoryNo = student.dormitoryNo || "";
  }
};

// 计算总分
const calculateTotalScore = () => {
  if (formInline.value) {
    const periodScore = formInline.value.periodScore || 0;
    const addScore = formInline.value.addScore || 0;
    const reduceScore = formInline.value.reduceScore || 0;
    formInline.value.totalScore = periodScore + addScore - reduceScore;
  }
};

// 解析加分说明并计算加分
const parseAddScoreRemark = () => {
  if (!formInline.value?.addScoreRemark) {
    formInline.value.addScore = 0;
    calculateTotalScore();
    return;
  }

  const remark = formInline.value.addScoreRemark;
  const regex = /\+(\d+(?:\.\d+)?)/g;
  let totalAdd = 0;
  let match;

  while ((match = regex.exec(remark)) !== null) {
    totalAdd += parseFloat(match[1]);
  }

  formInline.value.addScore = totalAdd;
  calculateTotalScore();
};

// 解析扣分说明并计算扣分
const parseReduceScoreRemark = () => {
  if (!formInline.value?.reduceScoreRemark) {
    formInline.value.reduceScore = 0;
    calculateTotalScore();
    return;
  }

  const remark = formInline.value.reduceScoreRemark;
  const regex = /-(\d+(?:\.\d+)?)/g;
  let totalReduce = 0;
  let match;

  while ((match = regex.exec(remark)) !== null) {
    totalReduce += parseFloat(match[1]);
  }

  formInline.value.reduceScore = totalReduce;
  calculateTotalScore();
};

// 监听表单数据变化
watch(() => formInline.value?.classId, (newClassId) => {
  if (newClassId) {
    loadStudents();
  }
}, { immediate: true });

watch(() => formInline.value?.addScoreRemark, () => {
  parseAddScoreRemark();
});

watch(() => formInline.value?.reduceScoreRemark, () => {
  parseReduceScoreRemark();
});

watch(() => formInline.value?.periodScore, () => {
  calculateTotalScore();
});

function getRef() {
  return ruleFormRef.value;
}

defineExpose({ getRef });
</script>

<template>
  <el-form
    ref="ruleFormRef"
    :model="formInline"
    :rules="formRules"
    label-width="100px"
  >
    <el-row :gutter="30">
      <!-- 左侧：学生信息和评分信息 -->
      <re-col :value="10" :xs="24" :sm="24">
        <!-- 学生基本信息 -->
        <div class="form-section-title">学生基本信息</div>

        <el-form-item label="学号" prop="studentId">
          <el-select
            v-model="formInline.studentId"
            placeholder="请选择学生学号"
            filterable
            style="width: 100%"
            :loading="studentsLoading"
            @change="handleStudentChange"
          >
            <el-option
              v-for="student in studentOptions"
              :key="student.studentId"
              :label="student.studentId"
              :value="student.studentId"
            >
              <span>{{ student.studentId }}</span>
              <span style="float: right; color: #8492a6; font-size: 12px">{{ student.studentName }}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="姓名">
          <el-input v-model="formInline.studentName" placeholder="自动获取" :disabled="true" />
        </el-form-item>

        <el-form-item label="宿舍号">
          <el-input v-model="formInline.dormitoryNo" placeholder="自动获取" :disabled="true" />
        </el-form-item>

        <el-form-item label="评分学期">
          <el-input v-model="formInline.evaluationPeriodName" placeholder="当前选择学期" :disabled="true" />
        </el-form-item>

        <!-- 得分信息 -->
        <div class="form-section-title">得分信息</div>
        <div class="score-calculation-info">
          <div class="calculation-formula">
            所有分数由系统根据说明内容自动计算，无需手动输入
          </div>
        </div>

        <el-form-item label="基础分">
          <el-input-number 
            v-model="formInline.periodScore" 
            :precision="2" 
            :step="1" 
            :min="0" 
            :max="200" 
            style="width: 100%" 
          />
          <div class="remark-hint">系统自动设置：第一学期60分，第二学期为上学期总分</div>
        </el-form-item>

        <el-form-item label="加分">
          <el-input-number 
            v-model="formInline.addScore" 
            :precision="2" 
            :step="1" 
            :min="0" 
            :max="100" 
            style="width: 100%" 
            disabled 
          />
          <div class="remark-hint">系统根据加分说明自动计算</div>
        </el-form-item>

        <el-form-item label="扣分">
          <el-input-number 
            v-model="formInline.reduceScore" 
            :precision="2" 
            :step="1" 
            :min="0" 
            :max="100" 
            style="width: 100%" 
            disabled 
          />
          <div class="remark-hint">系统根据扣分说明自动计算</div>
        </el-form-item>

        <el-form-item label="总得分">
          <el-input-number 
            v-model="formInline.totalScore" 
            :precision="2" 
            :step="1" 
            :min="0" 
            :max="200" 
            style="width: 100%" 
            disabled 
          />
          <div class="remark-hint">系统自动计算：基础分 + 加分 - 扣分</div>
        </el-form-item>
      </re-col>

      <!-- 右侧：加分扣分说明 -->
      <re-col :value="14" :xs="24" :sm="24">
        <div class="form-section-title">加分扣分详情</div>
        <el-form-item label="加分说明" prop="addScoreRemark">
          <el-input 
            v-model="formInline.addScoreRemark" 
            type="textarea" 
            :rows="4" 
            placeholder="请输入加分说明，格式如：参加志愿活动 +1" 
          />
        </el-form-item>
        <el-form-item label="扣分说明" prop="reduceScoreRemark">
          <el-input 
            v-model="formInline.reduceScoreRemark" 
            type="textarea" 
            :rows="4" 
            placeholder="请输入扣分说明，格式如：迟到 -1" 
          />
        </el-form-item>
      </re-col>
    </el-row>
  </el-form>
</template>

<style scoped>
.form-section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.score-calculation-info {
  margin-bottom: 16px;
  padding: 12px;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
}

.calculation-formula {
  font-size: 12px;
  color: var(--el-text-color-regular);
  text-align: center;
}

.remark-hint {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
}
</style>
