package com.itmk.web.quality_evaluation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.itmk.web.quality_evaluation.entity.QualityEvaluation;
import com.itmk.web.quality_evaluation.entity.QualityEvaluationParm;

import java.util.Map;

/**
 * 基本素质测评成绩Service接口
 */
public interface QualityEvaluationService extends IService<QualityEvaluation> {

    /**
     * 分页查询基本素质测评成绩
     * @param parm 查询参数
     * @return 分页结果
     */
    IPage<QualityEvaluation> getList(QualityEvaluationParm parm);
    
    /**
     * 根据学生ID和评分学期查询基本素质测评成绩
     * @param studentId 学生ID
     * @param evaluationPeriod 评分学期
     * @return 基本素质测评成绩
     */
    QualityEvaluation getByStudentIdAndPeriod(String studentId, String evaluationPeriod);
    
    /**
     * 判断是否是第一学期
     * @param evaluationPeriod 评分学期
     * @return 是否是第一学期
     */
    boolean isFirstSemester(String evaluationPeriod);
    
    /**
     * 计算加分
     * @param addScoreRemark 加分说明
     * @return 加分值
     */
    double calculateAddScore(String addScoreRemark);
    
    /**
     * 计算扣分
     * @param reduceScoreRemark 扣分说明
     * @return 扣分值
     */
    double calculateReduceScore(String reduceScoreRemark);
    
    /**
     * 计算总分
     * @param qualityEvaluation 基本素质测评成绩
     */
    void calculateTotalScore(QualityEvaluation qualityEvaluation);
    
    /**
     * 获取上一学期ID
     * @param currentSemesterId 当前学期ID
     * @return 上一学期ID
     */
    String getPreviousSemesterId(String currentSemesterId);
    
    /**
     * 更新第二学期的prev_period_score字段为上一学期的总分
     * @return 更新的记录数
     */
    int updatePrevPeriodScore();
    
    /**
     * 更新第二学期的period_score字段为prev_period_score
     * @return 更新的记录数
     */
    int updatePeriodScoreFromPrev();
    
    /**
     * 准备评分数据，包括计算加分、扣分、总分等
     * @param qualityEvaluation 基本素质测评成绩
     */
    void prepareScores(QualityEvaluation qualityEvaluation);
    
    /**
     * 获取基础分
     * @param qualityEvaluation 基本素质测评成绩
     * @return 基础分
     */
    double getBaseScore(QualityEvaluation qualityEvaluation);

    /**
     * 确保第二学期数据的一致性
     * @param qualityEvaluation 基本素质测评成绩
     */
    void ensureSecondSemesterConsistency(QualityEvaluation qualityEvaluation);

    /**
     * 批量重新计算第二学期的总分
     * @return 更新的记录数
     */
    int recalculateSecondSemesterTotalScores();
    
    /**
     * 解析学期名称为学期ID
     * @param semesterName 学期名称
     * @param semesterMap 学期映射表
     * @return 学期ID
     */
    String resolveSemesterId(String semesterName, Map<String, String> semesterMap);
} 