package com.itmk.web.sys_menu.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 菜单数据传输对象
 * 基于Spring Boot最佳实践设计
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MenuDTO {

    /**
     * 菜单ID
     */
    private Long menuId;

    /**
     * 父级菜单ID
     */
    @NotNull(message = "父级菜单ID不能为空")
    private Long parentId;

    /**
     * 菜单标题
     */
    @NotBlank(message = "菜单标题不能为空")
    private String title;



    /**
     * 路由名称
     */
    @NotBlank(message = "路由名称不能为空")
    private String name;

    /**
     * 路由路径
     */
    @NotBlank(message = "路由路径不能为空")
    private String path;

    /**
     * 组件路径
     */
    private String url;

    /**
     * 菜单类型 (0:目录 1:菜单 2:按钮)
     */
    @NotBlank(message = "菜单类型不能为空")
    @Pattern(regexp = "^[012]$", message = "菜单类型只能是0、1或2")
    private String type;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 父级菜单名称
     */
    private String parentName;

    /**
     * 排序号
     */
    private Long orderNum;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 子菜单列表
     */
    private List<MenuDTO> children = new ArrayList<>();

    /**
     * 是否有子菜单
     */
    public boolean hasChildren() {
        return children != null && !children.isEmpty();
    }

    /**
     * 添加子菜单
     */
    public void addChild(MenuDTO child) {
        if (children == null) {
            children = new ArrayList<>();
        }
        children.add(child);
    }

    // Getter and Setter methods for compatibility
    public Long getMenuId() {
        return menuId;
    }

    public void setMenuId(Long menuId) {
        this.menuId = menuId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Long getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Long orderNum) {
        this.orderNum = orderNum;
    }

    public List<MenuDTO> getChildren() {
        return children;
    }

    public void setChildren(List<MenuDTO> children) {
        this.children = children;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 菜单类型枚举
     */
    public enum MenuType {
        DIRECTORY("0", "目录"),
        MENU("1", "菜单"),
        BUTTON("2", "按钮");

        private final String code;
        private final String description;

        MenuType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static MenuType fromCode(String code) {
            for (MenuType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("未知的菜单类型: " + code);
        }
    }
}
