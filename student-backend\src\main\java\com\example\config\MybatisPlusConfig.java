package com.example.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;

/**
 * MyBatis Plus配置
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Configuration
public class MybatisPlusConfig {

    /**
     * 分页插件
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }

    /**
     * 自动填充处理器
     * 支持多种字段名格式的自动填充
     */
    @Bean
    public MetaObjectHandler metaObjectHandler() {
        return new MetaObjectHandler() {
            @Override
            public void insertFill(MetaObject metaObject) {
                // 创建时间字段
                this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
                this.strictInsertFill(metaObject, "createdAt", LocalDateTime.class, LocalDateTime.now());

                // 更新时间字段
                this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
                this.strictInsertFill(metaObject, "updatedAt", LocalDateTime.class, LocalDateTime.now());

                // 创建人字段（暂时使用system，后续可以从SecurityContext获取）
                this.strictInsertFill(metaObject, "createBy", String.class, getCurrentUser());
                this.strictInsertFill(metaObject, "createdBy", String.class, getCurrentUser());
            }

            @Override
            public void updateFill(MetaObject metaObject) {
                // 更新时间字段
                this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
                this.strictUpdateFill(metaObject, "updatedAt", LocalDateTime.class, LocalDateTime.now());

                // 更新人字段（暂时使用system，后续可以从SecurityContext获取）
                this.strictUpdateFill(metaObject, "updateBy", String.class, getCurrentUser());
                this.strictUpdateFill(metaObject, "updatedBy", String.class, getCurrentUser());
            }

            /**
             * 获取当前用户
             * TODO: 后续可以从Spring Security的SecurityContext中获取当前登录用户
             */
            private String getCurrentUser() {
                // 这里可以从SecurityContextHolder获取当前用户
                // SecurityContext context = SecurityContextHolder.getContext();
                // Authentication authentication = context.getAuthentication();
                // if (authentication != null && authentication.getPrincipal() instanceof UserDetails) {
                //     return ((UserDetails) authentication.getPrincipal()).getUsername();
                // }
                return "system"; // 暂时返回system
            }
        };
    }
}
