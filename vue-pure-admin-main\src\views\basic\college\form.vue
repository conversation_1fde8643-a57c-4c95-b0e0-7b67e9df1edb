<template>
  <el-form
    ref="ruleFormRef"
    :model="formInline"
    :rules="formRules"
    label-width="100px"
  >
    <el-form-item label="学院代码" prop="collegeCode">
      <el-input
        v-model="formInline.collegeCode"
        clearable
        placeholder="请输入学院代码"
      />
    </el-form-item>
    <el-form-item label="学院名称" prop="collegeName">
      <el-input
        v-model="formInline.collegeName"
        clearable
        placeholder="请输入学院名称"
      />
    </el-form-item>
    <el-form-item label="学院描述" prop="description">
      <el-input
        v-model="formInline.description"
        :autosize="{ minRows: 3, maxRows: 5 }"
        type="textarea"
        placeholder="请输入学院描述"
      />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import type { FormInstance } from "element-plus";
import { formRules } from "./utils/rule";
import { FormProps } from "./utils/types";

const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({
    id: undefined,
    collegeCode: "",
    collegeName: "",
    description: ""
  })
});

const ruleFormRef = ref<FormInstance>();
const formInline = reactive(props.formInline);

function getRef() {
  return ruleFormRef.value;
}

defineExpose({ getRef });
</script>
