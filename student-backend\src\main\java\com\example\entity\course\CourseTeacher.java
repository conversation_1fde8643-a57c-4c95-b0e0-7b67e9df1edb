package com.example.entity.course;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 课程-教师关联实体类
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("course_teachers")
public class CourseTeacher {

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 课程代码
     */
    @TableField("course_code")
    private String courseCode;

    /**
     * 教师工号
     */
    @TableField("teacher_code")
    private String teacherCode;

    /**
     * 学期ID
     */
    @TableField("semester_id")
    private Integer semesterId;

    /**
     * 班级代码
     */
    @TableField("class_code")
    private String classCode;

    /**
     * 是否主讲教师
     */
    @TableField("is_primary")
    private Boolean isPrimary;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
