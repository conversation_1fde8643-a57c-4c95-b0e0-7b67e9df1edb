<template>
  <div class="main-content">
    <PureTableBar
      :title="`${majorName} - ${className} - 课程选择`"
      :columns="columns"
      @refresh="onSearch"
    >
      <template #buttons>
        <el-space>
          <el-select
            v-model="selectedSemesterId"
            placeholder="选择学期"
            clearable
            @change="handleSemesterChange"
            style="width: 150px"
          >
            <el-option label="全部学期" value="" />
            <el-option
              v-for="semester in semesters"
              :key="semester.id"
              :label="semester.semesterName"
              :value="semester.id"
            />
          </el-select>
          <el-button type="primary" @click="onSearch">
            搜索
          </el-button>
          <el-button @click="onReset">
            重置
          </el-button>
          <el-button type="success" @click="showMultiCourseImport">
            多课程导入
          </el-button>
          <el-button type="info" @click="handleBack">
            返回
          </el-button>
        </el-space>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="courseData"
          :columns="dynamicColumns"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
        >
          <template #operation="{ row }">
            <el-button
              type="primary"
              link
              size="small"
              @click="handleSelectCourse(row)"
            >
              成绩录入
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <!-- 多课程导入弹窗 -->
    <MultiCourseImportDialog
      v-model="multiCourseImportVisible"
      :class-code="classCode"
      :class-name="className"
      @success="handleMultiCourseImportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { PureTableBar } from "@/components/RePureTableBar";
import { getCoursesByClass, getCoursesByClassAndSemester } from "@/api/educational/classCourse";
import { getAllSemesters } from "@/api/basic/semester";
import MultiCourseImportDialog from "./MultiCourseImportDialog.vue";
import { message } from "@/utils/message";

// Props
interface Props {
  classCode: string;
  className: string;
  majorName: string;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  back: [];
  selectCourse: [course: any];
}>();

// 响应式数据
const loading = ref(false);
const courseData = ref([]);
const semesters = ref([]);
const selectedSemesterId = ref<number | string>("");
const multiCourseImportVisible = ref(false);

// 表格列定义
const columns = ref([
  {
    label: "课程名称",
    prop: "courseName",
    minWidth: 120
  },
  {
    label: "课程代码",
    prop: "courseCode",
    minWidth: 100
  },
  {
    label: "学期",
    prop: "semesterName",
    minWidth: 100
  },
  {
    label: "课程类型",
    prop: "courseType",
    minWidth: 100
  },
  {
    label: "学分",
    prop: "credits",
    minWidth: 80
  },
  {
    label: "操作",
    slot: "operation",
    fixed: "right",
    width: 100
  }
]);

// 加载学期数据
const loadSemesters = async () => {
  try {
    const response = await getAllSemesters();
    semesters.value = response.data || [];
  } catch (error) {
    console.error("加载学期失败:", error);
  }
};

// 加载课程数据
const loadCourses = async (semesterId: number | string = "") => {
  loading.value = true;
  try {
    let response;
    if (semesterId && semesterId !== "") {
      response = await getCoursesByClassAndSemester(props.classCode, Number(semesterId));
    } else {
      response = await getCoursesByClass(props.classCode);
    }
    courseData.value = response.data || [];
  } catch (error) {
    console.error("加载课程失败:", error);
    courseData.value = [];
  } finally {
    loading.value = false;
  }
};

// 学期变化处理
const handleSemesterChange = (semesterId: number | string) => {
  loadCourses(semesterId);
};

// 搜索
const onSearch = () => {
  loadCourses(selectedSemesterId.value);
};

// 重置
const onReset = () => {
  selectedSemesterId.value = "";
  loadCourses();
};

// 返回
const handleBack = () => {
  emit("back");
};

// 选择课程
const handleSelectCourse = (course: any) => {
  emit("selectCourse", course);
};

// 显示多课程导入弹窗
const showMultiCourseImport = () => {
  multiCourseImportVisible.value = true;
};

// 多课程导入成功回调
const handleMultiCourseImportSuccess = () => {
  message("多课程成绩导入成功", { type: "success" });
  multiCourseImportVisible.value = false;
};

// 组件挂载时加载数据
onMounted(() => {
  loadSemesters();
  loadCourses();
});
</script>

<style scoped>
.main-content {
  padding: 20px;
}
</style>
