import dayjs from "dayjs";
import editForm from "../form.vue";
import { message } from "@/utils/message";
import { addDialog } from "@/components/ReDialog";
import { type PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h, toRaw } from "vue";
import {
  getTeachersPage,
  saveTeachers,
  updateTeachers,
  deleteTeachers
} from "@/api/teacher/teachers";
import type {
  TeacherItem,
  TeacherQueryParams
} from "./types";
import {
  TITLE_TAG_TYPE_MAP,
  STATUS_TAG_TYPE_MAP
} from "./types";

export function useTeacher() {
  const form = reactive<TeacherQueryParams>({
    teacherCode: "",
    name: "",
    gender: "",
    collegeCode: "",
    title: "",
    status: ""
  });

  const dataList = ref([]);
  const loading = ref(true);
  const selectedNum = ref(0);

  const columns: TableColumnList = [
    {
      label: "勾选列",
      type: "selection",
      width: 55,
      align: "left"
    },
    {
      label: "序号",
      type: "index",
      width: 70
    },
    {
      label: "教师工号",
      prop: "teacherCode",
      minWidth: 120
    },
    {
      label: "姓名",
      prop: "name",
      minWidth: 100
    },
    {
      label: "性别",
      prop: "gender",
      minWidth: 80,
      cellRenderer: ({ row, props }) => (
        <el-tag size={props.size} type={row.gender === "男" ? "primary" : "danger"}>
          {row.gender}
        </el-tag>
      )
    },
    {
      label: "出生日期",
      prop: "birthDate",
      minWidth: 120,
      formatter: ({ birthDate }) => dayjs(birthDate).format("YYYY-MM-DD")
    },
    {
      label: "电话号码",
      prop: "phone",
      minWidth: 130
    },
    {
      label: "邮箱",
      prop: "email",
      minWidth: 180
    },
    {
      label: "所属学院",
      prop: "collegeName",
      minWidth: 150
    },
    {
      label: "职称",
      prop: "title",
      minWidth: 100,
      cellRenderer: ({ row, props }) => {
        return (
          <el-tag size={props.size} type={TITLE_TAG_TYPE_MAP[row.title] || "info"}>
            {row.title || "未设置"}
          </el-tag>
        );
      }
    },
    {
      label: "入职日期",
      prop: "hireDate",
      minWidth: 120,
      formatter: ({ hireDate }) => hireDate ? dayjs(hireDate).format("YYYY-MM-DD") : ""
    },
    {
      label: "状态",
      prop: "status",
      minWidth: 100,
      cellRenderer: ({ row, props }) => {
        return (
          <el-tag size={props.size} type={STATUS_TAG_TYPE_MAP[row.status] || "info"}>
            {row.status || "未设置"}
          </el-tag>
        );
      }
    },
    {
      label: "创建时间",
      prop: "createdAt",
      minWidth: 180,
      formatter: ({ createdAt }) => dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "操作",
      fixed: "right",
      width: 240,
      slot: "operation"
    }
  ];

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  });

  function handleDelete(row: TeacherItem) {
    deleteTeachers(row.id).then(() => {
      message("删除成功", { type: "success" });
      onSearch();
    }).catch(() => {
      message("网络错误，请稍后重试", { type: "error" });
    });
  }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  function handleSelectionChange(val: TeacherItem[]) {
    selectedNum.value = val.length;
  }

  async function onSearch() {
    loading.value = true;
    const { teacherCode, name, gender, collegeCode, title, status } = toRaw(form);

    try {
      const res = await getTeachersPage({
        current: pagination.currentPage,
        size: pagination.pageSize,
        teacherId: teacherCode || undefined,
        teacherName: name || undefined,
        gender: gender || undefined,
        collegeCode: collegeCode || undefined,
        title: title || undefined,
        status: status || undefined
      });

      // 直接使用返回的数据，因为 HTTP 拦截器已经处理了响应格式
      if (res) {
        dataList.value = res.records || [];
        pagination.total = res.total || 0;
      } else {
        dataList.value = [];
        pagination.total = 0;
      }
    } catch (error) {
      console.error("查询教师列表失败:", error);
      message("网络错误，请检查后端服务是否正常运行", { type: "error" });
      dataList.value = [];
      pagination.total = 0;
    } finally {
      loading.value = false;
    }
  }

  const resetForm = (formEl: any) => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  function openDialog(title = "新增", row?: TeacherItem) {
    const formRef = ref();

    addDialog({
      title: `${title}教师`,
      props: {
        formInline: {
          id: row?.id ?? null,
          teacherCode: row?.teacherCode ?? "",
          name: row?.name ?? "",
          gender: row?.gender ?? "",
          birthDate: row?.birthDate ?? "",
          phone: row?.phone ?? "",
          email: row?.email ?? "",
          collegeCode: row?.collegeCode ?? "",
          title: row?.title ?? "",
          hireDate: row?.hireDate ?? "",
          status: row?.status ?? "在职"
        }
      },
      width: "60%",
      draggable: true,
      fullscreenIcon: true,
      closeOnClickModal: false,
      contentRenderer: () => h(editForm, { ref: formRef }),
      beforeSure: (done, { options }) => {
        const FormRef = formRef.value?.getRef();
        const curData = options.props.formInline as TeacherItem;

        function chores(successMessage: string) {
          message(successMessage, { type: "success" });
          done(); // 关闭弹框
          onSearch(); // 刷新表格数据
        }

        FormRef?.validate((valid: boolean) => {
          if (valid) {
            // 表单规则校验通过
            // 转换数据格式
            const teacherData = {
              id: curData.id,
              teacherId: curData.teacherCode,
              teacherName: curData.name,
              gender: curData.gender,
              birthDate: curData.birthDate,
              phone: curData.phone,
              email: curData.email,
              address: undefined,
              collegeCode: curData.collegeCode,
              collegeName: curData.collegeName,
              title: curData.title,
              degree: undefined,
              status: curData.status
            };

            if (title === "新增") {
              saveTeachers(teacherData).then(() => {
                chores("保存成功");
              }).catch(() => {
                message("网络错误，请稍后重试", { type: "error" });
              });
            } else {
              updateTeachers(teacherData).then(() => {
                chores("更新成功");
              }).catch(() => {
                message("网络错误，请稍后重试", { type: "error" });
              });
            }
          }
        });
      }
    });
  }

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    selectedNum,
    onSearch,
    resetForm,
    openDialog,
    handleDelete,
    handleSizeChange,
    handleCurrentChange,
    handleSelectionChange
  };
}
