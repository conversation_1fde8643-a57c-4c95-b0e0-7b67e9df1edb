package com.itmk.config;

import com.itmk.config.jwt.JwtInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.oas.annotations.EnableOpenApi;
import springfox.documentation.service.*;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.spring.web.plugins.Docket;

import java.util.Arrays;
import java.util.List;

/**
 * Web配置类
 * 统一管理CORS配置、拦截器配置、Swagger配置等Web相关配置
 *
 * <AUTHOR>
 * @since 2025-07-19
 */
@Configuration
@EnableOpenApi
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private JwtInterceptor jwtInterceptor;

    // ==================== CORS 配置 ====================

    /**
     * CORS跨域配置 - 优化版本
     * 减少OPTIONS预检请求，提升性能
     */
    @Bean
    public CorsFilter corsFilter() {
        // 创建CORS配置
        CorsConfiguration corsConfiguration = new CorsConfiguration();

        // 明确指定允许的前端域名（生产环境建议具体指定）
        corsConfiguration.addAllowedOriginPattern("*"); // 开发环境允许所有源
        // 生产环境建议使用具体域名：
        // corsConfiguration.addAllowedOrigin("http://localhost:9528");
        // corsConfiguration.addAllowedOrigin("https://yourdomain.com");

        // 允许凭证(cookies等)
        corsConfiguration.setAllowCredentials(true);

        // 明确指定允许的请求头（减少预检请求）
        corsConfiguration.addAllowedHeader("Content-Type");
        corsConfiguration.addAllowedHeader("Authorization");
        corsConfiguration.addAllowedHeader("token");
        corsConfiguration.addAllowedHeader("userType");
        corsConfiguration.addAllowedHeader("userId");
        corsConfiguration.addAllowedHeader("X-Timestamp");
        corsConfiguration.addAllowedHeader("X-Requested-With");
        corsConfiguration.addAllowedHeader("Accept");
        corsConfiguration.addAllowedHeader("Origin");

        // 明确指定允许的HTTP方法
        corsConfiguration.addAllowedMethod("GET");
        corsConfiguration.addAllowedMethod("POST");
        corsConfiguration.addAllowedMethod("PUT");
        corsConfiguration.addAllowedMethod("DELETE");
        corsConfiguration.addAllowedMethod("OPTIONS");
        corsConfiguration.addAllowedMethod("HEAD");

        // 设置预检请求的有效期，单位秒（1小时，平衡性能和安全）
        corsConfiguration.setMaxAge(3600L);

        // 添加响应头，允许客户端访问返回的自定义响应头
        corsConfiguration.addExposedHeader("content-disposition");
        corsConfiguration.addExposedHeader("Content-Length");
        corsConfiguration.addExposedHeader("Access-Control-Allow-Origin");
        corsConfiguration.addExposedHeader("Access-Control-Allow-Headers");

        // 添加映射路径，拦截一切请求
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", corsConfiguration);

        return new CorsFilter(source);
    }

    // ==================== 拦截器配置 ====================

    /**
     * 添加拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(jwtInterceptor)
                .addPathPatterns("/api/**") // 拦截所有API请求
                .excludePathPatterns(
                    "/api/login/**",           // 排除原登录接口
                    "/api/auth/**",            // 排除现代化认证接口
                    "/doc.html",               // 排除Knife4j文档
                    "/webjars/**",             // 排除webjars资源
                    "/swagger-resources/**",   // 排除swagger资源
                    "/v3/api-docs/**",         // 排除OpenAPI文档
                    "/favicon.ico",            // 排除图标
                    "/actuator/**"             // 排除健康检查等监控接口
                );
    }

    // ==================== 资源处理器配置 ====================

    /**
     * 资源处理器配置
     * 禁用原生Swagger UI，只保留Knife4j
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 不添加swagger-ui的资源映射，确保原生Swagger UI不可访问
        // 只保留Knife4j的资源映射（由Knife4j自动处理）

        // 如果需要，可以显式禁用swagger-ui路径
        // 这里我们通过不添加资源映射来实现禁用
    }

    // ==================== Knife4j API文档配置 ====================

    /**
     * 创建API文档
     * 访问地址: http://localhost:8089/doc.html
     * 注意: 已完全移除原生Swagger UI依赖，/swagger-ui/ 路径不再可用
     */
    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.OAS_30)
                .apiInfo(apiInfo())
                .select()
                // 扫描所有有注解的api，用这种方式更灵活
                .apis(RequestHandlerSelectors.basePackage("com.itmk.web"))
                .paths(PathSelectors.any())
                .build()
                .securitySchemes(securitySchemes())
                .securityContexts(securityContexts());
    }

    /**
     * API信息
     */
    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("学生信息管理系统 API文档")
                .description("基于Spring Boot + MyBatis Plus的学生信息管理系统后端API接口文档\n" +
                           "使用Knife4j提供美观的API文档界面\n" +
                           "访问地址: http://localhost:8089/doc.html")
                .version("v1.0.0")
                .contact(new Contact("开发团队", "", ""))
                .license("MIT License")
                .licenseUrl("https://opensource.org/licenses/MIT")
                .build();
    }

    /**
     * 安全模式，这里指定token通过Authorization头请求头传递
     */
    private List<SecurityScheme> securitySchemes() {
        ApiKey apiKey = new ApiKey("Authorization", "Authorization", "header");
        return Arrays.asList(apiKey);
    }

    /**
     * 安全上下文
     */
    private List<SecurityContext> securityContexts() {
        SecurityContext securityContext = SecurityContext.builder()
                .securityReferences(defaultAuth())
                .operationSelector(o -> o.requestMappingPattern().matches("/api/.*"))
                .build();
        return Arrays.asList(securityContext);
    }

    /**
     * 默认的安全上下文
     */
    private List<SecurityReference> defaultAuth() {
        AuthorizationScope authorizationScope = new AuthorizationScope("global", "accessEverything");
        AuthorizationScope[] authorizationScopes = new AuthorizationScope[1];
        authorizationScopes[0] = authorizationScope;
        SecurityReference securityReference = new SecurityReference("Authorization", authorizationScopes);
        return Arrays.asList(securityReference);
    }
}
