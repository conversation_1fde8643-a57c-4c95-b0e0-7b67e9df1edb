package com.example.controller.basic;

import com.example.common.PageResult;
import com.example.common.Result;
import com.example.dto.basic.SemesterQueryDTO;
import com.example.service.basic.SemesterService;

import com.example.vo.basic.SemesterVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 学年学期管理控制器
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Slf4j
@RestController
@RequestMapping("/api/basic/semester")
@RequiredArgsConstructor
@Validated
@Tag(name = "学年学期管理", description = "学年学期管理相关接口")
public class SemesterController {

    private final SemesterService semesterService;

    /**
     * 分页查询学年学期列表
     */
    @PostMapping("/list")
    @Operation(summary = "获取学年学期列表", description = "分页查询学年学期列表")
    public Result<PageResult<SemesterVO>> getSemesterList(@RequestBody(required = false) SemesterQueryDTO query) {
        PageResult<SemesterVO> semesterList = semesterService.getSemesterList(query);
        return Result.success("查询成功", semesterList);
    }

    /**
     * 获取所有学年学期列表
     */
    @GetMapping("/all")
    @Operation(summary = "获取所有学年学期", description = "获取所有学年学期列表，不分页")
    public Result<List<SemesterVO>> getAllSemesters() {
        List<SemesterVO> semesterList = semesterService.getAllSemesters();
        return Result.success("查询成功", semesterList);
    }

    /**
     * 根据学年获取学期列表
     */
    @GetMapping("/year/{academicYear}")
    @Operation(summary = "根据学年获取学期", description = "根据学年获取学期列表")
    public Result<List<SemesterVO>> getSemestersByAcademicYear(
            @Parameter(description = "学年") @PathVariable String academicYear) {
        List<SemesterVO> semesterList = semesterService.getSemestersByAcademicYear(academicYear);
        return Result.success("查询成功", semesterList);
    }

    /**
     * 获取当前学期
     */
    @GetMapping("/current")
    @Operation(summary = "获取当前学期", description = "获取当前学期信息")
    public Result<SemesterVO> getCurrentSemester() {
        SemesterVO semester = semesterService.getCurrentSemester();
        return Result.success("查询成功", semester);
    }

    /**
     * 根据ID获取学年学期详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取学年学期详情", description = "根据ID获取学年学期详情")
    public Result<SemesterVO> getSemesterById(@Parameter(description = "学期ID") @PathVariable Integer id) {
        SemesterVO semester = semesterService.getSemesterById(id);
        return Result.success("查询成功", semester);
    }



}
