package com.example.service.system.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.example.dto.system.DeptQueryDTO;
import com.example.entity.system.SysDept;
import com.example.common.exception.BusinessException;
import com.example.mapper.system.SysDeptMapper;
import com.example.service.system.DeptService;
import com.example.vo.system.DeptVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 部门服务实现
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class DeptServiceImpl implements DeptService {

    private final SysDeptMapper deptMapper;

    @Override
    @Cacheable(value = "deptList", key = "#query != null ? #query.hashCode() : 'null'")
    public List<DeptVO> getDeptList(DeptQueryDTO query) {
        if (query == null) {
            query = new DeptQueryDTO();
        }

        try {
            // 构建查询条件
            LambdaQueryWrapper<SysDept> queryWrapper = new LambdaQueryWrapper<>();

            if (StringUtils.hasText(query.getDeptName())) {
                queryWrapper.like(SysDept::getDeptName, query.getDeptName());
            }
            if (StringUtils.hasText(query.getDeptCode())) {
                queryWrapper.like(SysDept::getDeptCode, query.getDeptCode());
            }
            if (query.getStatus() != null) {
                queryWrapper.eq(SysDept::getStatus, query.getStatus());
            }
            if (StringUtils.hasText(query.getLeader())) {
                queryWrapper.like(SysDept::getLeader, query.getLeader());
            }

            // 排序
            if ("asc".equalsIgnoreCase(query.getOrderDirection())) {
                queryWrapper.orderByAsc(SysDept::getSort).orderByAsc(SysDept::getCreateTime);
            } else {
                queryWrapper.orderByAsc(SysDept::getSort).orderByDesc(SysDept::getCreateTime);
            }

            List<SysDept> deptList = deptMapper.selectList(queryWrapper);
            List<DeptVO> deptVOList = convertToDeptVOList(deptList);

            // 返回一维数组，前端自行构建树形结构
            return deptVOList;

        } catch (Exception e) {
            log.error("查询部门列表失败", e);
            throw new BusinessException("查询部门列表失败");
        }
    }

    @Override
    @Cacheable(value = "allDepts", key = "'all'")
    public List<DeptVO> getAllDepts() {
        try {
            LambdaQueryWrapper<SysDept> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysDept::getStatus, 1)
                       .orderByAsc(SysDept::getSort)
                       .orderByDesc(SysDept::getCreateTime);

            List<SysDept> deptList = deptMapper.selectList(queryWrapper);
            List<DeptVO> deptVOList = convertToDeptVOList(deptList);

            return deptVOList;
        } catch (Exception e) {
            log.error("查询所有部门失败", e);
            throw new BusinessException("查询所有部门失败");
        }
    }

    @Override
    @Cacheable(value = "deptDetail", key = "#id")
    public DeptVO getDeptById(Integer id) {
        if (id == null) {
            throw new BusinessException("部门ID不能为空");
        }

        try {
            SysDept dept = deptMapper.selectById(id);
            if (dept == null) {
                throw new BusinessException("部门不存在");
            }

            return convertToDeptVO(dept);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询部门详情失败: {}", id, e);
            throw new BusinessException("查询部门详情失败");
        }
    }

    @Override
    @CacheEvict(value = {"deptList", "allDepts", "deptDetail"}, allEntries = true)
    public void saveDept(DeptVO deptVO) {
        if (deptVO == null) {
            throw new BusinessException("部门信息不能为空");
        }

        // 验证部门信息
        validateDeptInfo(deptVO, null);

        try {
            SysDept dept = new SysDept();
            copyDeptVOToEntity(deptVO, dept);

            // 设置默认值
            dept.setStatus(1);
            dept.setCreateTime(LocalDateTime.now());
            dept.setUpdateTime(LocalDateTime.now());

            if (dept.getSort() == null) {
                dept.setSort(0);
            }

            deptMapper.insert(dept);

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("新增部门失败: {}", deptVO.getDeptName(), e);
            throw new BusinessException("新增部门失败");
        }
    }

    @Override
    @CacheEvict(value = {"deptList", "allDepts", "deptDetail"}, allEntries = true)
    public void updateDept(DeptVO deptVO) {
        if (deptVO == null || deptVO.getId() == null) {
            throw new BusinessException("部门ID不能为空");
        }

        // 检查部门是否存在
        SysDept existingDept = deptMapper.selectById(deptVO.getId());
        if (existingDept == null) {
            throw new BusinessException("部门不存在");
        }

        // 验证部门信息
        validateDeptInfo(deptVO, deptVO.getId());

        try {
            SysDept dept = new SysDept();
            copyDeptVOToEntity(deptVO, dept);
            dept.setUpdateTime(LocalDateTime.now());

            int updated = deptMapper.updateById(dept);
            if (updated == 0) {
                throw new BusinessException("更新部门失败，部门可能已被删除");
            }

            // 处理父子部门状态联动
            if (deptVO.getStatus() != null) {
                handleDeptStatusLinkage(deptVO.getId(), deptVO.getStatus(), existingDept.getStatus());
            }

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新部门失败: {}", deptVO.getId(), e);
            throw new BusinessException("更新部门失败");
        }
    }

    @Override
    @CacheEvict(value = {"deptList", "allDepts", "deptDetail"}, allEntries = true)
    public void deleteDept(Integer deptId) {
        if (deptId == null) {
            throw new BusinessException("部门ID不能为空");
        }

        // 检查部门是否存在
        SysDept existingDept = deptMapper.selectById(deptId);
        if (existingDept == null) {
            throw new BusinessException("部门不存在");
        }

        try {
            // 检查部门状态，只有停用状态的部门才能删除
            if (existingDept.getStatus() == 1) {
                throw new BusinessException("只有停用状态的部门才能删除，请先停用该部门");
            }

            // 检查是否有禁用状态的子部门
            LambdaQueryWrapper<SysDept> childWrapper = new LambdaQueryWrapper<>();
            childWrapper.eq(SysDept::getParentId, deptId).eq(SysDept::getStatus, 0);
            List<SysDept> children = deptMapper.selectList(childWrapper);

            if (!children.isEmpty()) {
                // 递归删除子部门
                for (SysDept child : children) {
                    deleteDept(child.getId());
                }
            }

            // 物理删除部门
            int deleted = deptMapper.deleteById(deptId);
            if (deleted == 0) {
                throw new BusinessException("删除部门失败");
            }

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除部门失败: {}", deptId, e);
            throw new BusinessException("删除部门失败");
        }
    }

    @Override
    public boolean isDeptNameExists(String deptName, Integer excludeId) {
        if (!StringUtils.hasText(deptName)) {
            return false;
        }

        LambdaQueryWrapper<SysDept> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysDept::getDeptName, deptName)
               .eq(SysDept::getStatus, 1);

        if (excludeId != null) {
            wrapper.ne(SysDept::getId, excludeId);
        }

        return deptMapper.selectCount(wrapper) > 0;
    }

    @Override
    public boolean isDeptCodeExists(String deptCode, Integer excludeId) {
        if (!StringUtils.hasText(deptCode)) {
            return false;
        }

        LambdaQueryWrapper<SysDept> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysDept::getDeptCode, deptCode)
               .eq(SysDept::getStatus, 1);

        if (excludeId != null) {
            wrapper.ne(SysDept::getId, excludeId);
        }

        return deptMapper.selectCount(wrapper) > 0;
    }

    @Override
    @CacheEvict(value = {"deptList", "allDepts", "deptDetail"}, allEntries = true)
    public void toggleDeptStatus(Integer deptId, Integer status) {
        if (deptId == null || status == null) {
            throw new BusinessException("部门ID和状态不能为空");
        }

        if (status != 0 && status != 1) {
            throw new BusinessException("状态值无效");
        }

        // 获取当前部门状态
        SysDept existingDept = deptMapper.selectById(deptId);
        if (existingDept == null) {
            throw new BusinessException("部门不存在");
        }

        try {
            SysDept dept = new SysDept();
            dept.setId(deptId);
            dept.setStatus(status);
            dept.setUpdateTime(LocalDateTime.now());

            int updated = deptMapper.updateById(dept);
            if (updated == 0) {
                throw new BusinessException("更新部门状态失败，部门可能不存在");
            }

            // 处理父子部门状态联动
            handleDeptStatusLinkage(deptId, status, existingDept.getStatus());

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("切换部门状态失败: deptId={}, status={}", deptId, status, e);
            throw new BusinessException("切换部门状态失败");
        }
    }



    /**
     * 验证部门信息
     */
    private void validateDeptInfo(DeptVO deptVO, Integer excludeId) {
        // 检查部门名称是否重复
        if (isDeptNameExists(deptVO.getDeptName(), excludeId)) {
            throw new BusinessException("部门名称已存在");
        }

        // 检查部门编码是否重复
        if (StringUtils.hasText(deptVO.getDeptCode()) && isDeptCodeExists(deptVO.getDeptCode(), excludeId)) {
            throw new BusinessException("部门编码已存在");
        }
    }

    /**
     * 转换为DeptVO列表
     */
    private List<DeptVO> convertToDeptVOList(List<SysDept> deptList) {
        if (deptList == null || deptList.isEmpty()) {
            return new ArrayList<>();
        }

        return deptList.stream()
                .map(this::convertToDeptVO)
                .collect(Collectors.toList());
    }

    /**
     * 转换为DeptVO
     */
    private DeptVO convertToDeptVO(SysDept dept) {
        DeptVO deptVO = new DeptVO();
        BeanUtils.copyProperties(dept, deptVO);
        return deptVO;
    }

    /**
     * 复制DeptVO到实体
     */
    private void copyDeptVOToEntity(DeptVO deptVO, SysDept dept) {
        BeanUtils.copyProperties(deptVO, dept);
    }

    /**
     * 处理部门状态联动
     */
    private void handleDeptStatusLinkage(Integer deptId, Integer newStatus, Integer oldStatus) {
        if (Objects.equals(newStatus, oldStatus)) return;

        log.info("处理部门{}状态联动，从{}变为{}", deptId, oldStatus, newStatus);

        if (newStatus == 0) {
            updateChildrenDeptStatus(deptId, 0);
        } else {
            updateParentDeptChain(deptId);
            updateChildrenDeptStatus(deptId, 1);
        }
    }

    /**
     * 递归更新子部门状态
     */
    private void updateChildrenDeptStatus(Integer parentId, Integer status) {
        LambdaQueryWrapper<SysDept> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysDept::getParentId, parentId).eq(SysDept::getStatus, 1 - status);

        deptMapper.selectList(wrapper).forEach(child -> {
            SysDept updateDept = new SysDept();
            updateDept.setId(child.getId());
            updateDept.setStatus(status);
            updateDept.setUpdateTime(LocalDateTime.now());
            deptMapper.updateById(updateDept);
            updateChildrenDeptStatus(child.getId(), status);
        });
    }

    /**
     * 递归更新父部门状态
     */
    private void updateParentDeptChain(Integer childId) {
        SysDept current = deptMapper.selectById(childId);
        if (current == null || current.getParentId() == null || current.getParentId() == 0) return;

        SysDept parent = deptMapper.selectById(current.getParentId());
        if (parent != null && parent.getStatus() == 0) {
            SysDept updateDept = new SysDept();
            updateDept.setId(parent.getId());
            updateDept.setStatus(1);
            updateDept.setUpdateTime(LocalDateTime.now());
            deptMapper.updateById(updateDept);
            updateParentDeptChain(parent.getId());
        }
    }
}
