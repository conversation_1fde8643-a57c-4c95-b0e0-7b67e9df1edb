package com.itmk.web.school_student.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.itmk.web.school_student.entity.SchoolStudent;
import com.itmk.web.school_student.entity.StuParm;
import org.apache.ibatis.annotations.Param;


public interface SchoolStudentMapper extends BaseMapper<SchoolStudent> {
    //根据用户id查询用户信息
    SchoolStudent getStuById(@Param("stuId") Long stuId);
    //列表
    IPage<SchoolStudent> getList( IPage<SchoolStudent> page,@Param("parm") StuParm parm);
}