package com.example.dto.educational;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 班级课程分配查询DTO
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@Schema(description = "班级课程分配查询参数")
public class ClassCourseQueryDTO {

    @Schema(description = "班级代码")
    private String classCode;

    @Schema(description = "班级名称")
    private String className;

    @Schema(description = "课程代码")
    private String courseCode;

    @Schema(description = "课程名称")
    private String courseName;

    @Schema(description = "学期ID")
    private Integer semesterId;



    @Schema(description = "专业代码")
    private String majorCode;

    @Schema(description = "学院代码")
    private String collegeCode;

    @Schema(description = "是否启用")
    private Boolean isActive;

    @Schema(description = "当前页码")
    private Integer current;

    @Schema(description = "每页大小")
    private Integer size;
}
