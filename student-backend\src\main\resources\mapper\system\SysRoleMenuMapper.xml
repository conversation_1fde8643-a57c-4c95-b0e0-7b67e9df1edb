<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.system.SysRoleMenuMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.entity.system.SysRoleMenu">
        <result column="role_id" property="roleId" jdbcType="INTEGER"/>
        <result column="menu_id" property="menuId" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 通用SQL片段 -->
    <sql id="Base_Column_List">
        role_id, menu_id
    </sql>

    <!-- 根据角色ID查询菜单ID列表 -->
    <select id="selectMenuIdsByRoleId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT menu_id
        FROM sys_role_menu
        WHERE role_id = #{roleId}
        ORDER BY menu_id ASC
    </select>

    <!-- 根据菜单ID查询角色ID列表 -->
    <select id="selectRoleIdsByMenuId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT role_id
        FROM sys_role_menu
        WHERE menu_id = #{menuId}
        ORDER BY role_id ASC
    </select>

    <!-- 检查角色菜单关系是否存在 -->
    <select id="existsRoleMenu" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM sys_role_menu
        WHERE role_id = #{roleId}
          AND menu_id = #{menuId}
    </select>

    <!-- 根据角色ID删除角色菜单关系 -->
    <delete id="deleteByRoleId" parameterType="java.lang.Integer">
        DELETE FROM sys_role_menu
        WHERE role_id = #{roleId}
    </delete>

    <!-- 根据菜单ID删除角色菜单关系 -->
    <delete id="deleteByMenuId" parameterType="java.lang.Integer">
        DELETE FROM sys_role_menu
        WHERE menu_id = #{menuId}
    </delete>

    <!-- 删除指定角色菜单关系 -->
    <delete id="deleteRoleMenu">
        DELETE FROM sys_role_menu
        WHERE role_id = #{roleId}
          AND menu_id = #{menuId}
    </delete>

    <!-- 批量插入角色菜单关系 -->
    <insert id="batchInsert">
        INSERT INTO sys_role_menu (role_id, menu_id)
        VALUES
        <foreach collection="roleMenus" item="roleMenu" separator=",">
            (#{roleMenu.roleId}, #{roleMenu.menuId})
        </foreach>
    </insert>

    <!-- 批量删除角色菜单关系 -->
    <delete id="batchDelete">
        DELETE FROM sys_role_menu
        WHERE (role_id, menu_id) IN
        <foreach collection="roleMenus" item="roleMenu" open="(" separator="," close=")">
            (#{roleMenu.roleId}, #{roleMenu.menuId})
        </foreach>
    </delete>

    <!-- 根据角色ID列表删除角色菜单关系 -->
    <delete id="deleteByRoleIds">
        DELETE FROM sys_role_menu
        WHERE role_id IN
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </delete>

    <!-- 根据菜单ID列表删除角色菜单关系 -->
    <delete id="deleteByMenuIds">
        DELETE FROM sys_role_menu
        WHERE menu_id IN
        <foreach collection="menuIds" item="menuId" open="(" separator="," close=")">
            #{menuId}
        </foreach>
    </delete>

    <!-- 查询角色菜单关系列表 -->
    <select id="selectRoleMenuList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_role_menu
        <where>
            <if test="roleId != null">
                AND role_id = #{roleId}
            </if>
            <if test="menuId != null">
                AND menu_id = #{menuId}
            </if>
        </where>
        ORDER BY role_id ASC, menu_id ASC
    </select>

    <!-- 统计角色菜单关系数量 -->
    <select id="countRoleMenus" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM sys_role_menu
        <where>
            <if test="roleId != null">
                AND role_id = #{roleId}
            </if>
            <if test="menuId != null">
                AND menu_id = #{menuId}
            </if>
        </where>
    </select>

    <!-- 查询角色拥有的菜单详情 -->
    <select id="selectMenusByRoleId" parameterType="java.lang.Integer" resultType="com.example.entity.system.SysMenu">
        SELECT m.id, m.parent_id, m.title, m.name, m.path, m.component, m.redirect,
               m.menu_type, m.icon, m.rank, m.auths, m.show_link, m.show_parent,
               m.keep_alive, m.fixed_tag, m.frame_src, m.status, m.create_time, m.update_time
        FROM sys_menu m
        INNER JOIN sys_role_menu rm ON m.id = rm.menu_id
        WHERE rm.role_id = #{roleId}
          AND m.status = 1
        ORDER BY m.parent_id ASC, m.rank ASC
    </select>

    <!-- 查询菜单被分配给的角色详情 -->
    <select id="selectRolesByMenuId" parameterType="java.lang.Integer" resultType="com.example.entity.system.SysRole">
        SELECT r.id, r.role_name, r.role_code, r.description, r.sort,
               r.status, r.remark, r.create_time, r.update_time
        FROM sys_role r
        INNER JOIN sys_role_menu rm ON r.id = rm.role_id
        WHERE rm.menu_id = #{menuId}
          AND r.status = 1
        ORDER BY r.sort ASC
    </select>

    <!-- 根据用户ID查询菜单权限 -->
    <select id="selectMenusByUserId" parameterType="java.lang.Integer" resultType="com.example.entity.system.SysMenu">
        SELECT DISTINCT m.id, m.parent_id, m.title, m.name, m.path, m.component, m.redirect,
               m.menu_type, m.icon, m.rank, m.auths, m.show_link, m.show_parent,
               m.keep_alive, m.fixed_tag, m.frame_src, m.status, m.create_time, m.update_time
        FROM sys_menu m
        INNER JOIN sys_role_menu rm ON m.id = rm.menu_id
        INNER JOIN sys_user_role ur ON rm.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND m.status = 1
        ORDER BY m.parent_id ASC, m.rank ASC
    </select>

    <!-- 批量保存角色菜单权限 -->
    <insert id="saveRoleMenus">
        INSERT INTO sys_role_menu (role_id, menu_id)
        VALUES
        <foreach collection="menuIds" item="menuId" separator=",">
            (#{roleId}, #{menuId})
        </foreach>
    </insert>

</mapper>
