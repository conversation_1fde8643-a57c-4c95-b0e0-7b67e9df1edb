package com.itmk.config.jwt;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * JWT安全增强器
 */
@Component
public class JwtSecurityEnhancer {

    private static final Logger logger = LoggerFactory.getLogger(JwtSecurityEnhancer.class);

    @Autowired
    private JwtUtils jwtUtils;

    // 用于存储已使用的token（防重放攻击）
    private final ConcurrentHashMap<String, Long> usedTokens = new ConcurrentHashMap<>();

    // 用于存储失败尝试次数（防暴力破解）
    private final ConcurrentHashMap<String, Integer> failedAttempts = new ConcurrentHashMap<>();

    /**
     * 验证token并检查安全性
     */
    public boolean validateTokenSecurity(String token, HttpServletRequest request) {
        if (token == null || token.isEmpty()) {
            return false;
        }

        // 1. 检查token是否已被使用（防重放攻击）
        String tokenHash = hashToken(token);
        if (isTokenUsed(tokenHash)) {
            recordFailedAttempt(getClientIP(request));
            return false;
        }

        // 2. 检查客户端失败次数
        String clientIP = getClientIP(request);
        if (isClientBlocked(clientIP)) {
            return false;
        }

        // 3. 验证JWT token
        try {
            boolean isValid = jwtUtils.verify(token);
            if (isValid) {
                // 标记token为已使用（可选，根据业务需求）
                // markTokenAsUsed(tokenHash);
                resetFailedAttempts(clientIP);
                return true;
            } else {
                recordFailedAttempt(clientIP);
                return false;
            }
        } catch (Exception e) {
            recordFailedAttempt(clientIP);
            return false;
        }
    }

    /**
     * 生成token哈希值
     */
    private String hashToken(String token) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(token.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256 algorithm not available", e);
        }
    }

    /**
     * 检查token是否已被使用
     */
    private boolean isTokenUsed(String tokenHash) {
        Long timestamp = usedTokens.get(tokenHash);
        if (timestamp == null) {
            return false;
        }

        // 清理过期的token记录（1小时后清理）
        if (System.currentTimeMillis() - timestamp > TimeUnit.HOURS.toMillis(1)) {
            usedTokens.remove(tokenHash);
            return false;
        }

        return true;
    }

    /**
     * 标记token为已使用
     */
    private void markTokenAsUsed(String tokenHash) {
        usedTokens.put(tokenHash, System.currentTimeMillis());
    }

    /**
     * 获取客户端IP
     */
    private String getClientIP(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIP = request.getHeader("X-Real-IP");
        if (xRealIP != null && !xRealIP.isEmpty()) {
            return xRealIP;
        }

        return request.getRemoteAddr();
    }

    /**
     * 记录失败尝试
     */
    private void recordFailedAttempt(String clientIP) {
        failedAttempts.merge(clientIP, 1, Integer::sum);
    }

    /**
     * 重置失败尝试次数
     */
    private void resetFailedAttempts(String clientIP) {
        failedAttempts.remove(clientIP);
    }

    /**
     * 检查客户端是否被阻止
     */
    private boolean isClientBlocked(String clientIP) {
        Integer attempts = failedAttempts.get(clientIP);
        return attempts != null && attempts >= 5; // 5次失败后阻止
    }

    /**
     * 定期清理过期数据
     */
    public void cleanupExpiredData() {
        long currentTime = System.currentTimeMillis();

        // 清理过期的token记录
        usedTokens.entrySet().removeIf(entry ->
            currentTime - entry.getValue() > TimeUnit.HOURS.toMillis(1));

        // 清理失败尝试记录（1小时后重置）
        // 这里可以根据需要实现更复杂的清理逻辑
    }
}
