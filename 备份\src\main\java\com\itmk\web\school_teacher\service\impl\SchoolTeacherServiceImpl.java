package com.itmk.web.school_teacher.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itmk.web.school_teacher.entity.SchoolTeacher;
import com.itmk.web.school_teacher.entity.TeacherListParm;
import com.itmk.web.school_teacher.mapper.SchoolTeacherMapper;
import com.itmk.web.school_teacher.service.SchoolTeacherService;
import org.springframework.stereotype.Service;

@Service
public class SchoolTeacherServiceImpl extends ServiceImpl<SchoolTeacherMapper, SchoolTeacher> implements SchoolTeacherService {
    @Override
    public IPage<SchoolTeacher> getList(TeacherListParm teacherListParm) {
        //构造查询条件
        QueryWrapper<SchoolTeacher> query = new QueryWrapper<>();
        if (teacherListParm.getTeacherName() != null && !teacherListParm.getTeacherName().isEmpty()) {
            query.lambda().like(SchoolTeacher::getTeacherName, teacherListParm.getTeacherName());
        }
        query.lambda().orderByAsc(SchoolTeacher::getTeacherId);
        //构造分页对象
        IPage<SchoolTeacher> page = new Page<>(teacherListParm.getCurrentPage(), teacherListParm.getPageSize());
        //查询
        return this.page(page, query);
    }
}