package com.example.entity.basic;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 班级实体类
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("classes")
public class Classes {

    /**
     * 班级ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 班级代码
     */
    @TableField("class_code")
    private String classCode;

    /**
     * 班级名称
     */
    @TableField("class_name")
    private String className;

    /**
     * 所属专业代码
     */
    @TableField("major_code")
    private String majorCode;

    /**
     * 入学年份
     */
    @TableField("grade_year")
    private Integer gradeYear;

    /**
     * 学生人数
     */
    @TableField("student_count")
    private Integer studentCount;

    /**
     * 班主任工号
     */
    @TableField("head_teacher_code")
    private String headTeacherCode;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    // 关联字段（非数据库字段）
    
    /**
     * 专业名称
     */
    @TableField(exist = false)
    private String majorName;

    /**
     * 班主任姓名
     */
    @TableField(exist = false)
    private String headTeacherName;
}
