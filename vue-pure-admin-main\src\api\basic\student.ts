import { http } from "@/utils/http";

type Result = {
  success: boolean;
  data?: any;
};

/** 根据班级ID获取学生列表 */
export const getStudentsByClass = (classId: string) => {
  return http.request<Result>("get", `/student/list-by-class/${classId}`);
};

/** 获取所有学生列表 */
export const getAllStudents = (params?: any) => {
  return http.request<Result>("get", "/student/list", { params });
};

/** 根据学号获取学生信息 */
export const getStudentById = (studentId: string) => {
  return http.request<Result>("get", `/student/info/${studentId}`);
};
