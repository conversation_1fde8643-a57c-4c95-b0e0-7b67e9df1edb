<template>
  <div class="main-content">
    <el-form
      ref="formRef"
      :inline="true"
      :model="form"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
    >
      <el-form-item label="学年：" prop="academicYear">
        <el-select
          v-model="form.academicYear"
          placeholder="请选择学年"
          clearable
          class="!w-[180px]"
        >
          <el-option
            v-for="year in academicYearOptions"
            :key="year"
            :label="year"
            :value="year"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="学期号：" prop="semesterNumber">
        <el-select
          v-model="form.semesterNumber"
          placeholder="请选择学期号"
          clearable
          class="!w-[120px]"
        >
          <el-option label="1" :value="1" />
          <el-option label="2" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="学期名称：" prop="semesterName">
        <el-input
          v-model="form.semesterName"
          placeholder="请输入学期名称"
          clearable
          class="!w-[180px]"
        />
      </el-form-item>
      <el-form-item label="当前学期：" prop="isCurrent">
        <el-select
          v-model="form.isCurrent"
          placeholder="请选择"
          clearable
          class="!w-[120px]"
        >
          <el-option label="是" :value="true" />
          <el-option label="否" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:search')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon('ep:refresh')" @click="resetForm(formRef)">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <PureTableBar title="学年学期管理" :columns="columns" @refresh="onSearch">
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          align-whole="center"
          showOverflowTooltip
          table-layout="auto"
          :loading="loading"
          :size="size"
          adaptive
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small' ? true : false"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >

        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useSemester } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

defineOptions({
  name: "Semester"
});

const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  onSearch,
  resetForm,
  handleSizeChange,
  handleCurrentChange
} = useSemester();

const formRef = ref();

// 生成学年选项（当前年份前3年和后3年）
const academicYearOptions = computed(() => {
  const currentYear = new Date().getFullYear();
  const years = [];
  for (let i = currentYear - 3; i <= currentYear + 3; i++) {
    years.push(`${i}-${i + 1}`);
  }
  return years;
});
</script>

<style lang="scss" scoped>
:deep(.el-table__inner-wrapper::before) {
  height: 0;
}



.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
