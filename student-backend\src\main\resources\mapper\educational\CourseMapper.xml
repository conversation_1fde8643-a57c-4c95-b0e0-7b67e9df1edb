<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.educational.CourseMapper">

    <!-- 通用结果映射 -->
    <resultMap id="CourseVOResultMap" type="com.example.vo.educational.CourseVO">
        <id column="id" property="id"/>
        <result column="course_code" property="courseCode"/>
        <result column="course_name" property="courseName"/>
        <result column="credits" property="credits"/>
        <result column="course_type" property="courseType"/>
        <result column="college_code" property="collegeCode"/>
        <result column="college_name" property="collegeName"/>
        <result column="description" property="description"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- 通用SQL片段 -->
    <sql id="Base_Column_List">
        c.id, c.course_code, c.course_name, c.credits, c.course_type,
        c.college_code, c.description, c.created_at, c.updated_at,
        col.college_name
    </sql>

    <!-- 通用连接查询 -->
    <sql id="Base_Join">
        FROM courses c
        LEFT JOIN colleges col ON c.college_code = col.college_code
    </sql>

    <!-- 分页查询课程列表 -->
    <select id="selectCoursePage" resultMap="CourseVOResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Base_Join"/>
        <where>
            <if test="query.courseCode != null and query.courseCode != ''">
                AND c.course_code LIKE CONCAT('%', #{query.courseCode}, '%')
            </if>
            <if test="query.courseName != null and query.courseName != ''">
                AND c.course_name LIKE CONCAT('%', #{query.courseName}, '%')
            </if>
            <if test="query.courseType != null and query.courseType != ''">
                AND c.course_type = #{query.courseType}
            </if>
            <if test="query.collegeCode != null and query.collegeCode != ''">
                AND c.college_code = #{query.collegeCode}
            </if>
        </where>
        ORDER BY c.created_at DESC
    </select>

    <!-- 根据课程ID获取课程详情 -->
    <select id="selectCourseById" resultMap="CourseVOResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Base_Join"/>
        WHERE c.id = #{id}
    </select>

    <!-- 根据课程代码获取课程信息 -->
    <select id="selectCourseByCode" resultMap="CourseVOResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Base_Join"/>
        WHERE c.course_code = #{courseCode}
    </select>

    <!-- 根据学院代码获取课程列表 -->
    <select id="selectCoursesByCollege" resultMap="CourseVOResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Base_Join"/>
        WHERE c.college_code = #{collegeCode}
        ORDER BY c.course_code
    </select>



    <!-- 根据课程类型获取课程列表 -->
    <select id="selectCoursesByType" resultMap="CourseVOResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Base_Join"/>
        WHERE c.course_type = #{courseType}
        ORDER BY c.course_code
    </select>

    <!-- 获取所有课程列表 -->
    <select id="selectAllCourses" resultMap="CourseVOResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Base_Join"/>
        ORDER BY c.course_code
    </select>

    <!-- 检查课程代码是否存在 -->
    <select id="checkCourseCodeExists" resultType="int">
        SELECT COUNT(1)
        FROM courses
        WHERE course_code = #{courseCode}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 批量删除课程 -->
    <delete id="batchDeleteCourses">
        DELETE FROM courses
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据学院代码统计课程数量 -->
    <select id="countCoursesByCollege" resultType="int">
        SELECT COUNT(1)
        FROM courses c
        WHERE c.college_code = #{collegeCode}
    </select>

</mapper>
