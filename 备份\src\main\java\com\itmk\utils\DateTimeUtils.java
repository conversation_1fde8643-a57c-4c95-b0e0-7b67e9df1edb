package com.itmk.utils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * 时间工具类
 * 提供统一的时间格式化和处理方法
 * 
 * <AUTHOR>
 * @since 2025-07-19
 */
public class DateTimeUtils {
    
    /**
     * 默认日期时间格式
     */
    public static final String DEFAULT_DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    
    /**
     * 默认日期格式
     */
    public static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";
    
    /**
     * 默认时间格式
     */
    public static final String DEFAULT_TIME_FORMAT = "HH:mm:ss";
    
    /**
     * 紧凑日期时间格式（用于文件名等）
     */
    public static final String COMPACT_DATETIME_FORMAT = "yyyyMMddHHmmss";
    
    /**
     * 默认日期时间格式化器
     */
    private static final DateTimeFormatter DEFAULT_DATETIME_FORMATTER = 
        DateTimeFormatter.ofPattern(DEFAULT_DATETIME_FORMAT);
    
    /**
     * 默认日期格式化器
     */
    private static final DateTimeFormatter DEFAULT_DATE_FORMATTER = 
        DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT);
    
    /**
     * 默认时间格式化器
     */
    private static final DateTimeFormatter DEFAULT_TIME_FORMATTER = 
        DateTimeFormatter.ofPattern(DEFAULT_TIME_FORMAT);
    
    /**
     * 紧凑日期时间格式化器
     */
    private static final DateTimeFormatter COMPACT_DATETIME_FORMATTER = 
        DateTimeFormatter.ofPattern(COMPACT_DATETIME_FORMAT);
    
    /**
     * 获取当前时间的字符串表示（默认格式）
     * 
     * @return 当前时间字符串，格式：yyyy-MM-dd HH:mm:ss
     */
    public static String getCurrentTimeString() {
        return LocalDateTime.now().format(DEFAULT_DATETIME_FORMATTER);
    }
    
    /**
     * 获取当前日期的字符串表示
     * 
     * @return 当前日期字符串，格式：yyyy-MM-dd
     */
    public static String getCurrentDateString() {
        return LocalDateTime.now().format(DEFAULT_DATE_FORMATTER);
    }
    
    /**
     * 获取当前时间的字符串表示（紧凑格式）
     * 
     * @return 当前时间字符串，格式：yyyyMMddHHmmss
     */
    public static String getCurrentTimeCompactString() {
        return LocalDateTime.now().format(COMPACT_DATETIME_FORMATTER);
    }
    
    /**
     * 格式化日期时间为默认格式
     * 
     * @param dateTime 要格式化的日期时间
     * @return 格式化后的字符串，格式：yyyy-MM-dd HH:mm:ss
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(DEFAULT_DATETIME_FORMATTER);
    }
    
    /**
     * 格式化日期时间为指定格式
     * 
     * @param dateTime 要格式化的日期时间
     * @param pattern 格式模式
     * @return 格式化后的字符串
     */
    public static String formatDateTime(LocalDateTime dateTime, String pattern) {
        if (dateTime == null || pattern == null || pattern.trim().isEmpty()) {
            return null;
        }
        try {
            return dateTime.format(DateTimeFormatter.ofPattern(pattern));
        } catch (Exception e) {
            throw new IllegalArgumentException("无效的日期格式模式: " + pattern, e);
        }
    }
    
    /**
     * 格式化日期为默认格式
     * 
     * @param dateTime 要格式化的日期时间
     * @return 格式化后的日期字符串，格式：yyyy-MM-dd
     */
    public static String formatDate(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(DEFAULT_DATE_FORMATTER);
    }
    
    /**
     * 格式化时间为默认格式
     * 
     * @param dateTime 要格式化的日期时间
     * @return 格式化后的时间字符串，格式：HH:mm:ss
     */
    public static String formatTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(DEFAULT_TIME_FORMATTER);
    }
    
    /**
     * 解析日期时间字符串（默认格式）
     * 
     * @param dateTimeString 日期时间字符串，格式：yyyy-MM-dd HH:mm:ss
     * @return 解析后的LocalDateTime对象
     * @throws IllegalArgumentException 如果字符串格式不正确
     */
    public static LocalDateTime parseDateTime(String dateTimeString) {
        if (dateTimeString == null || dateTimeString.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalDateTime.parse(dateTimeString, DEFAULT_DATETIME_FORMATTER);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("无效的日期时间格式: " + dateTimeString + 
                ", 期望格式: " + DEFAULT_DATETIME_FORMAT, e);
        }
    }
    
    /**
     * 解析日期时间字符串（指定格式）
     * 
     * @param dateTimeString 日期时间字符串
     * @param pattern 格式模式
     * @return 解析后的LocalDateTime对象
     * @throws IllegalArgumentException 如果字符串格式不正确
     */
    public static LocalDateTime parseDateTime(String dateTimeString, String pattern) {
        if (dateTimeString == null || dateTimeString.trim().isEmpty()) {
            return null;
        }
        if (pattern == null || pattern.trim().isEmpty()) {
            throw new IllegalArgumentException("日期格式模式不能为空");
        }
        try {
            return LocalDateTime.parse(dateTimeString, DateTimeFormatter.ofPattern(pattern));
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("无效的日期时间格式: " + dateTimeString + 
                ", 期望格式: " + pattern, e);
        }
    }
    
    /**
     * 检查字符串是否为有效的日期时间格式（默认格式）
     * 
     * @param dateTimeString 要检查的字符串
     * @return 如果格式有效返回true，否则返回false
     */
    public static boolean isValidDateTime(String dateTimeString) {
        try {
            parseDateTime(dateTimeString);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 检查字符串是否为有效的日期时间格式（指定格式）
     * 
     * @param dateTimeString 要检查的字符串
     * @param pattern 格式模式
     * @return 如果格式有效返回true，否则返回false
     */
    public static boolean isValidDateTime(String dateTimeString, String pattern) {
        try {
            parseDateTime(dateTimeString, pattern);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取时间戳（毫秒）
     * 
     * @return 当前时间戳
     */
    public static long getCurrentTimestamp() {
        return System.currentTimeMillis();
    }
    
    /**
     * 获取当前时间用于日志记录的格式化字符串
     * 包含方括号，便于日志识别
     * 
     * @return 格式化的时间字符串，如：[2025-07-19 14:30:25]
     */
    public static String getLogTimeString() {
        return "[" + getCurrentTimeString() + "]";
    }
}
