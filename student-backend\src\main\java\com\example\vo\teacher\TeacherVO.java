package com.example.vo.teacher;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 教师VO
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Data
@Schema(description = "教师信息")
public class TeacherVO {

    @Schema(description = "教师ID")
    private Integer id;

    @Schema(description = "教师工号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "教师工号不能为空")
    private String teacherCode;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "姓名不能为空")
    private String name;

    @Schema(description = "性别", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "性别不能为空")
    @Pattern(regexp = "^(男|女)$", message = "性别只能是男或女")
    private String gender;

    @Schema(description = "出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthDate;

    @Schema(description = "电话号码")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "电话号码格式不正确")
    private String phone;

    @Schema(description = "邮箱")
    @Email(message = "邮箱格式不正确")
    private String email;

    @Schema(description = "所属学院代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "所属学院不能为空")
    private String collegeCode;

    @Schema(description = "所属学院名称")
    private String collegeName;

    @Schema(description = "职称")
    private String title;

    @Schema(description = "入职日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate hireDate;

    @Schema(description = "状态")
    @Pattern(regexp = "^(在职|离职|退休)$", message = "状态只能是在职、离职或退休")
    private String status;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
}
