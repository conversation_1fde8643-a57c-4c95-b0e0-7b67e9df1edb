package com.example.service.system.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.common.PageResult;
import com.example.dto.system.RoleQueryDTO;
import com.example.entity.system.SysRole;
import com.example.entity.system.SysRoleMenu;
import com.example.entity.system.SysUserRole;
import com.example.common.exception.BusinessException;
import com.example.mapper.system.SysRoleMapper;
import com.example.mapper.system.SysRoleMenuMapper;
import com.example.mapper.system.SysUserRoleMapper;
import com.example.service.system.RoleService;
import com.example.vo.system.RoleVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色服务实现
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class RoleServiceImpl implements RoleService {

    private final SysRoleMapper roleMapper;
    private final SysRoleMenuMapper roleMenuMapper;
    private final SysUserRoleMapper userRoleMapper;

    @Override
    @Cacheable(value = "roleList", key = "#query != null ? #query.hashCode() : 'null'")
    public PageResult<RoleVO> getRoleList(RoleQueryDTO query) {
        if (query == null) {
            query = new RoleQueryDTO();
        }

        try {
            // 构建查询条件
            LambdaQueryWrapper<SysRole> queryWrapper = new LambdaQueryWrapper<>();
            // 查询所有角色（包括启用和禁用的）

            if (StringUtils.hasText(query.getRoleName())) {
                queryWrapper.like(SysRole::getRoleName, query.getRoleName());
            }
            if (StringUtils.hasText(query.getRoleCode())) {
                queryWrapper.like(SysRole::getRoleCode, query.getRoleCode());
            }
            if (query.getStatus() != null) {
                queryWrapper.eq(SysRole::getStatus, query.getStatus());
            }

            // 排序
            if ("asc".equalsIgnoreCase(query.getOrderDirection())) {
                queryWrapper.orderByAsc(SysRole::getSort);
            } else {
                queryWrapper.orderByDesc(SysRole::getSort);
            }

            // 分页查询
            Page<SysRole> page = new Page<>(query.getPage(), query.getSize());
            Page<SysRole> result = roleMapper.selectPage(page, queryWrapper);

            // 转换为VO
            List<RoleVO> roleVOList = result.getRecords().stream()
                    .map(this::convertToRoleVO)
                    .collect(Collectors.toList());

            // 返回分页结果
            return PageResult.of(result, roleVOList);

        } catch (Exception e) {
            log.error("查询角色列表失败", e);
            throw new BusinessException("查询角色列表失败");
        }
    }

    @Override
    @Cacheable(value = "allRoles", key = "'all'")
    public List<RoleVO> getAllRoles() {
        try {
            LambdaQueryWrapper<SysRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysRole::getStatus, 1)
                       .orderByAsc(SysRole::getSort)
                       .orderByDesc(SysRole::getCreateTime);

            List<SysRole> roles = roleMapper.selectList(queryWrapper);
            return roles.stream()
                    .map(this::convertToRoleVO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询所有角色失败", e);
            throw new BusinessException("查询所有角色失败");
        }
    }

    @Override
    @Cacheable(value = "roleDetail", key = "#id")
    public RoleVO getRoleById(Integer id) {
        if (id == null) {
            throw new BusinessException("角色ID不能为空");
        }

        try {
            SysRole role = roleMapper.selectById(id);
            if (role == null || role.getStatus() == 0) {
                throw new BusinessException("角色不存在或已被删除");
            }

            RoleVO roleVO = convertToRoleVO(role);
            // 获取角色菜单权限
            roleVO.setMenuIds(getRoleMenuPermissions(id));
            return roleVO;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询角色详情失败: {}", id, e);
            throw new BusinessException("查询角色详情失败");
        }
    }

    @Override
    @CacheEvict(value = {"roleList", "allRoles", "roleDetail", "userMenuList", "asyncRoutes", "userRoles"}, allEntries = true)
    public void saveRole(RoleVO roleVO) {
        if (roleVO == null) {
            throw new BusinessException("角色信息不能为空");
        }

        // 验证角色信息
        validateRoleInfo(roleVO, null);

        try {
            SysRole role = new SysRole();
            copyRoleVOToEntity(roleVO, role);

            // 设置默认值
            role.setStatus(1);
            role.setCreateTime(LocalDateTime.now());
            role.setUpdateTime(LocalDateTime.now());

            if (role.getSort() == null) {
                role.setSort(0);
            }

            roleMapper.insert(role);

            // 分配菜单权限
            if (roleVO.getMenuIds() != null && !roleVO.getMenuIds().isEmpty()) {
                assignMenuPermissions(role.getId().intValue(), roleVO.getMenuIds());
            }

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("新增角色失败: {}", roleVO.getRoleName(), e);
            throw new BusinessException("新增角色失败");
        }
    }

    @Override
    @CacheEvict(value = {"roleList", "allRoles", "roleDetail", "userMenuList", "asyncRoutes", "userRoles"}, allEntries = true)
    public void updateRole(RoleVO roleVO) {
        if (roleVO == null || roleVO.getId() == null) {
            throw new BusinessException("角色ID不能为空");
        }

        // 检查角色是否存在
        SysRole existingRole = roleMapper.selectById(roleVO.getId());
        if (existingRole == null || existingRole.getStatus() == 0) {
            throw new BusinessException("角色不存在或已被删除");
        }

        // 验证角色信息
        validateRoleInfo(roleVO, roleVO.getId());

        try {
            SysRole role = new SysRole();
            copyRoleVOToEntity(roleVO, role);
            role.setUpdateTime(LocalDateTime.now());

            int updated = roleMapper.updateById(role);
            if (updated == 0) {
                throw new BusinessException("更新角色失败，角色可能已被删除");
            }

            // 更新菜单权限
            if (roleVO.getMenuIds() != null) {
                assignMenuPermissions(roleVO.getId(), roleVO.getMenuIds());
            }

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新角色失败: {}", roleVO.getId(), e);
            throw new BusinessException("更新角色失败");
        }
    }

    @Override
    @Caching(evict = {
        @CacheEvict(value = {"roleList", "allRoles", "roleDetail", "userMenuList", "asyncRoutes", "userRoles"}, allEntries = true),
        @CacheEvict(value = "userRoleIds", allEntries = true)
    })
    public void deleteRole(Integer roleId) {
        if (roleId == null) {
            throw new BusinessException("角色ID不能为空");
        }

        // 检查角色是否存在
        SysRole existingRole = roleMapper.selectById(roleId);
        if (existingRole == null) {
            throw new BusinessException("角色不存在");
        }

        // 检查角色状态，只有禁用状态的角色才能删除
        if (existingRole.getStatus() == 1) {
            throw new BusinessException("只有禁用状态的角色才能删除，请先禁用该角色");
        }

        // 检查是否为系统角色
        if ("admin".equals(existingRole.getRoleCode()) || "super_admin".equals(existingRole.getRoleCode())) {
            throw new BusinessException("不能删除系统角色");
        }

        try {
            // 物理删除角色（因为已经是禁用状态）
            int deleted = roleMapper.deleteById(roleId);
            if (deleted == 0) {
                throw new BusinessException("删除角色失败");
            }

            // 删除角色菜单关联
            LambdaQueryWrapper<SysRoleMenu> menuWrapper = new LambdaQueryWrapper<>();
            menuWrapper.eq(SysRoleMenu::getRoleId, roleId);
            roleMenuMapper.delete(menuWrapper);

            // 删除用户角色关联
            LambdaQueryWrapper<SysUserRole> userRoleWrapper = new LambdaQueryWrapper<>();
            userRoleWrapper.eq(SysUserRole::getRoleId, roleId);
            userRoleMapper.delete(userRoleWrapper);

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除角色失败: {}", roleId, e);
            throw new BusinessException("删除角色失败");
        }
    }

    @Override
    @Cacheable(value = "roleMenuPermissions", key = "#roleId")
    public List<Integer> getRoleMenuPermissions(Integer roleId) {
        if (roleId == null) {
            return new ArrayList<>();
        }

        try {
            LambdaQueryWrapper<SysRoleMenu> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SysRoleMenu::getRoleId, roleId);

            List<SysRoleMenu> roleMenus = roleMenuMapper.selectList(wrapper);
            return roleMenus.stream()
                    .map(SysRoleMenu::getMenuId)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询角色菜单权限失败: {}", roleId, e);
            return new ArrayList<>();
        }
    }

    @Override
    @CacheEvict(value = {"roleDetail", "roleMenuPermissions", "userMenuList", "asyncRoutes", "userRoles"}, allEntries = true)
    public void assignMenuPermissions(Integer roleId, List<Integer> menuIds) {
        if (roleId == null) {
            throw new BusinessException("角色ID不能为空");
        }

        try {
            // 删除原有菜单权限关联
            LambdaQueryWrapper<SysRoleMenu> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(SysRoleMenu::getRoleId, roleId);
            roleMenuMapper.delete(deleteWrapper);

            // 添加新的菜单权限关联
            if (menuIds != null && !menuIds.isEmpty()) {
                List<SysRoleMenu> roleMenus = menuIds.stream()
                        .map(menuId -> {
                            SysRoleMenu roleMenu = new SysRoleMenu();
                            roleMenu.setRoleId(roleId);
                            roleMenu.setMenuId(menuId);
                            return roleMenu;
                        })
                        .collect(Collectors.toList());

                for (SysRoleMenu roleMenu : roleMenus) {
                    roleMenuMapper.insert(roleMenu);
                }
            }

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("分配角色菜单权限失败: roleId={}, menuIds={}", roleId, menuIds, e);
            throw new BusinessException("分配角色菜单权限失败");
        }
    }

    @Override
    public boolean isRoleNameExists(String roleName, Integer excludeId) {
        if (!StringUtils.hasText(roleName)) {
            return false;
        }

        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRole::getRoleName, roleName)
               .eq(SysRole::getStatus, 1);

        if (excludeId != null) {
            wrapper.ne(SysRole::getId, excludeId);
        }

        return roleMapper.selectCount(wrapper) > 0;
    }

    @Override
    public boolean isRoleCodeExists(String roleCode, Integer excludeId) {
        if (!StringUtils.hasText(roleCode)) {
            return false;
        }

        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRole::getRoleCode, roleCode)
               .eq(SysRole::getStatus, 1);

        if (excludeId != null) {
            wrapper.ne(SysRole::getId, excludeId);
        }

        return roleMapper.selectCount(wrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"roleList", "allRoles", "roleDetail", "userMenuList", "asyncRoutes", "userRoles"}, allEntries = true)
    public void toggleRoleStatus(Integer roleId, Integer status) {
        if (roleId == null || status == null) {
            throw new BusinessException("角色ID和状态不能为空");
        }

        if (status != 0 && status != 1) {
            throw new BusinessException("状态值无效");
        }

        // 获取当前角色状态
        SysRole existingRole = roleMapper.selectById(roleId);
        if (existingRole == null) {
            throw new BusinessException("角色不存在");
        }

        try {
            SysRole role = new SysRole();
            role.setId(roleId);
            role.setStatus(status);
            role.setUpdateTime(LocalDateTime.now());

            int updated = roleMapper.updateById(role);
            if (updated == 0) {
                throw new BusinessException("更新角色状态失败，角色可能不存在");
            }

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("切换角色状态失败: roleId={}, status={}", roleId, status, e);
            throw new BusinessException("切换角色状态失败");
        }
    }

    /**
     * 验证角色信息
     */
    private void validateRoleInfo(RoleVO roleVO, Integer excludeId) {
        // 检查角色名称是否重复
        if (isRoleNameExists(roleVO.getRoleName(), excludeId)) {
            throw new BusinessException("角色名称已存在");
        }

        // 检查角色编码是否重复
        if (StringUtils.hasText(roleVO.getRoleCode()) && isRoleCodeExists(roleVO.getRoleCode(), excludeId)) {
            throw new BusinessException("角色编码已存在");
        }
    }

    /**
     * 转换为RoleVO
     */
    private RoleVO convertToRoleVO(SysRole role) {
        RoleVO roleVO = new RoleVO();
        BeanUtils.copyProperties(role, roleVO);
        return roleVO;
    }

    /**
     * 复制RoleVO到实体
     */
    private void copyRoleVOToEntity(RoleVO roleVO, SysRole role) {
        BeanUtils.copyProperties(roleVO, role);
    }
}
