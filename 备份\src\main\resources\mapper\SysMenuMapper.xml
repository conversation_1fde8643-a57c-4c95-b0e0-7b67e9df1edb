<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.itmk.web.sys_menu.mapper.SysMenuMapper">
    <select id="getMenuByUserId" resultType="com.itmk.web.sys_menu.entity.SysMenu">
        select m.* from sys_user_role as ur
                            left join sys_role as r on ur.role_id = r.role_id
                            left join sys_role_menu as rm on r.role_id = rm.role_id
                            left join sys_menu as m on rm.menu_id = m.menu_id
        where ur.user_id =#{userId}
        order by m.order_num asc
    </select>
    <select id="getMenuByStuId" resultType="com.itmk.web.sys_menu.entity.SysMenu">
        select m.* from stu_role as ur
                            left join sys_role as r on ur.role_id = r.role_id
                            left join sys_role_menu as rm on r.role_id = rm.role_id
                            left join sys_menu as m on rm.menu_id = m.menu_id
        where ur.stu_id =#{userId}
        order by m.order_num asc
    </select>
    <select id="getMenuByTeacherId" resultType="com.itmk.web.sys_menu.entity.SysMenu">
        select m.* from teacher_role as ur
                            left join sys_role as r on ur.role_id = r.role_id
                            left join sys_role_menu as rm on r.role_id = rm.role_id
                            left join sys_menu as m on rm.menu_id = m.menu_id
        where ur.teacher_id =#{userId}
        order by m.order_num asc
    </select>
    <select id="getMenuByRoleId" resultType="com.itmk.web.sys_menu.entity.SysMenu">
        select m.* from sys_role_menu as rm ,sys_menu as m
        where rm.menu_id = m.menu_id
          and rm.role_id =#{roleId}
    </select>
</mapper>