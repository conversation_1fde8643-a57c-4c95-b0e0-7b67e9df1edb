interface FormItemProps {
  /** 专业ID */
  id?: number;
  /** 所属学院代码 */
  collegeCode: string;
  /** 专业代码 */
  majorCode: string;
  /** 专业名称 */
  majorName: string;
  /** 学制年限 */
  duration: number;
  /** 专业描述 */
  description?: string;
  /** 学院选项 */
  collegeOptions?: any[];
}

interface FormProps {
  formInline: FormItemProps;
}

interface QueryFormProps {
  /** 所属学院代码 */
  collegeCode: string;
  /** 专业代码 */
  majorCode: string;
  /** 专业名称 */
  majorName: string;
  /** 学制年限 */
  duration: number | null;
  /** 当前页码 */
  current?: number;
  /** 每页大小 */
  size?: number;
}

export type { FormItemProps, FormProps, QueryFormProps };
