package com.example.controller.educational;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.common.Result;
import com.example.dto.educational.ClassCourseQueryDTO;
import com.example.service.educational.ClassCourseService;
import com.example.vo.educational.ClassCourseVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 班级课程分配管理控制器
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@RestController
@RequestMapping("/api/educational/class-course")
@RequiredArgsConstructor
@Validated
@Tag(name = "班级课程分配管理", description = "班级课程分配相关接口")
public class ClassCourseController {

    private final ClassCourseService classCourseService;

    /**
     * 分页查询班级课程分配列表
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询班级课程分配", description = "根据条件分页查询班级课程分配列表")
    public Result<IPage<ClassCourseVO>> getClassCoursePage(@RequestBody ClassCourseQueryDTO queryDTO) {
        try {
            IPage<ClassCourseVO> result = classCourseService.getClassCoursePage(queryDTO);
            return Result.success("查询成功", result);
        } catch (Exception e) {
            log.error("分页查询班级课程分配失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID获取班级课程分配详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取班级课程分配详情", description = "根据ID获取班级课程分配详情")
    @Parameter(name = "id", description = "分配ID", required = true)
    public Result<ClassCourseVO> getClassCourseById(@PathVariable Integer id) {
        try {
            ClassCourseVO classCourse = classCourseService.getClassCourseById(id);
            return Result.success("查询成功", classCourse);
        } catch (Exception e) {
            log.error("获取班级课程分配详情失败，ID: {}", id, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 新增班级课程分配
     */
    @PostMapping
    @Operation(summary = "新增班级课程分配", description = "新增班级课程分配")
    public Result<Void> saveClassCourse(@Valid @RequestBody ClassCourseVO classCourseVO) {
        try {
            classCourseService.saveClassCourse(classCourseVO);
            return Result.success("新增成功");
        } catch (Exception e) {
            log.error("新增班级课程分配失败", e);
            return Result.error("新增失败：" + e.getMessage());
        }
    }

    /**
     * 更新班级课程分配
     */
    @PutMapping
    @Operation(summary = "更新班级课程分配", description = "更新班级课程分配")
    public Result<Void> updateClassCourse(@Valid @RequestBody ClassCourseVO classCourseVO) {
        try {
            classCourseService.updateClassCourse(classCourseVO);
            return Result.success("更新成功");
        } catch (Exception e) {
            log.error("更新班级课程分配失败", e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除班级课程分配
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除班级课程分配", description = "根据ID删除班级课程分配")
    @Parameter(name = "id", description = "分配ID", required = true)
    public Result<Void> deleteClassCourse(@PathVariable Integer id) {
        try {
            classCourseService.deleteClassCourse(id);
            return Result.success("删除成功");
        } catch (Exception e) {
            log.error("删除班级课程分配失败，ID: {}", id, e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除班级课程分配
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除班级课程分配", description = "批量删除班级课程分配")
    public Result<Void> batchDeleteClassCourses(@RequestBody List<Integer> ids) {
        try {
            classCourseService.batchDeleteClassCourses(ids);
            return Result.success("批量删除成功");
        } catch (Exception e) {
            log.error("批量删除班级课程分配失败", e);
            return Result.error("批量删除失败：" + e.getMessage());
        }
    }

    /**
     * 根据班级代码获取课程分配列表
     */
    @GetMapping("/class/{classCode}")
    @Operation(summary = "根据班级获取课程分配", description = "根据班级代码获取课程分配列表")
    @Parameter(name = "classCode", description = "班级代码", required = true)
    public Result<List<ClassCourseVO>> getCoursesByClass(@PathVariable String classCode) {
        try {
            List<ClassCourseVO> courses = classCourseService.getCoursesByClass(classCode);
            return Result.success("查询成功", courses);
        } catch (Exception e) {
            log.error("根据班级获取课程分配失败，班级代码: {}", classCode, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据班级代码和学期ID获取课程分配列表
     */
    @GetMapping("/class/{classCode}/semester/{semesterId}")
    @Operation(summary = "根据班级和学期获取课程分配", description = "根据班级代码和学期ID获取课程分配列表")
    public Result<List<ClassCourseVO>> getCoursesByClassAndSemester(
            @PathVariable String classCode,
            @PathVariable Integer semesterId) {
        try {
            List<ClassCourseVO> courses = classCourseService.getCoursesByClassAndSemester(classCode, semesterId);
            return Result.success("查询成功", courses);
        } catch (Exception e) {
            log.error("根据班级和学期获取课程分配失败，班级代码: {}, 学期ID: {}", classCode, semesterId, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据课程代码获取分配的班级列表
     */
    @GetMapping("/course/{courseCode}")
    @Operation(summary = "根据课程获取班级分配", description = "根据课程代码获取分配的班级列表")
    @Parameter(name = "courseCode", description = "课程代码", required = true)
    public Result<List<ClassCourseVO>> getClassesByCourse(@PathVariable String courseCode) {
        try {
            List<ClassCourseVO> classes = classCourseService.getClassesByCourse(courseCode);
            return Result.success("查询成功", classes);
        } catch (Exception e) {
            log.error("根据课程获取班级分配失败，课程代码: {}", courseCode, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 批量分配课程到班级
     */
    @PostMapping("/batch-assign")
    @Operation(summary = "批量分配课程", description = "批量分配课程到班级")
    public Result<Void> batchAssignCourses(@RequestBody Map<String, Object> params) {
        try {
            String classCode = (String) params.get("classCode");
            @SuppressWarnings("unchecked")
            List<String> courseCodes = (List<String>) params.get("courseCodes");
            Integer semesterId = (Integer) params.get("semesterId");

            classCourseService.batchAssignCourses(classCode, courseCodes, semesterId);
            return Result.success("批量分配成功");
        } catch (Exception e) {
            log.error("批量分配课程失败", e);
            return Result.error("批量分配失败：" + e.getMessage());
        }
    }

    /**
     * 复制学期课程分配
     */
    @PostMapping("/copy-semester")
    @Operation(summary = "复制学期课程分配", description = "复制学期课程分配")
    public Result<Void> copySemesterCourses(@RequestBody Map<String, Object> params) {
        try {
            String sourceClassCode = (String) params.get("sourceClassCode");
            Integer sourceSemesterId = (Integer) params.get("sourceSemesterId");
            String targetClassCode = (String) params.get("targetClassCode");
            Integer targetSemesterId = (Integer) params.get("targetSemesterId");

            classCourseService.copySemesterCourses(sourceClassCode, sourceSemesterId, targetClassCode, targetSemesterId);
            return Result.success("复制成功");
        } catch (Exception e) {
            log.error("复制学期课程分配失败", e);
            return Result.error("复制失败：" + e.getMessage());
        }
    }

    /**
     * 根据班级代码统计课程数量
     */
    @GetMapping("/count/class/{classCode}")
    @Operation(summary = "统计班级课程数量", description = "根据班级代码统计课程数量")
    @Parameter(name = "classCode", description = "班级代码", required = true)
    public Result<Integer> countCoursesByClass(@PathVariable String classCode) {
        try {
            int count = classCourseService.countCoursesByClass(classCode);
            return Result.success("查询成功", count);
        } catch (Exception e) {
            log.error("统计班级课程数量失败，班级代码: {}", classCode, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据课程代码统计分配的班级数量
     */
    @GetMapping("/count/course/{courseCode}")
    @Operation(summary = "统计课程分配班级数量", description = "根据课程代码统计分配的班级数量")
    @Parameter(name = "courseCode", description = "课程代码", required = true)
    public Result<Integer> countClassesByCourse(@PathVariable String courseCode) {
        try {
            int count = classCourseService.countClassesByCourse(courseCode);
            return Result.success("查询成功", count);
        } catch (Exception e) {
            log.error("统计课程分配班级数量失败，课程代码: {}", courseCode, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
}
