package com.example.service.system.impl;

import com.example.entity.system.SysMenu;
import com.example.common.exception.BusinessException;
import com.example.mapper.system.SysMenuMapper;
import com.example.mapper.system.SysRoleMenuMapper;
import com.example.mapper.system.SysUserMapper;
import com.example.service.system.MenuService;
import com.example.vo.system.MenuVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 菜单服务实现
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class MenuServiceImpl implements MenuService {

    private final SysMenuMapper menuMapper;
    private final SysRoleMenuMapper roleMenuMapper;
    private final SysUserMapper userMapper;

    @Override
    @Cacheable(value = "menuList", key = "'all'")
    public List<MenuVO> getMenuList() {
        // 查询所有菜单（管理员权限）
        List<SysMenu> menuList = menuMapper.selectAllMenus();
        return convertToMenuVOList(menuList);
    }

    @Override
    @Cacheable(value = "userMenuList", key = "#userId")
    public List<MenuVO> getUserMenuList(Integer userId) {
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        // 查询用户有权限的菜单
        List<SysMenu> menuList = menuMapper.selectMenusByUserId(userId);
        return convertToMenuVOList(menuList);
    }

    @Override
    @Cacheable(value = "asyncRoutes", key = "#userId")
    public List<MenuVO> getAsyncRoutes(Integer userId) {
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }

        try {
            // 查询用户有权限的菜单
            List<SysMenu> menuList = menuMapper.selectMenusByUserId(userId);

            if (menuList.isEmpty()) {
                return new ArrayList<>();
            }

            // 直接返回一维数组，不进行树形转换
            return convertToMenuVOList(menuList);
        } catch (Exception e) {
            log.error("获取用户{}的异步路由失败", userId, e);
            throw new BusinessException("获取用户路由失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"menuList", "userMenuList", "asyncRoutes"}, allEntries = true)
    public void saveMenu(MenuVO menuVO) {
        if (menuVO == null) {
            throw new BusinessException("菜单信息不能为空");
        }

        // 验证菜单唯一性
        validateMenuUniqueness(menuVO);

        SysMenu menu = new SysMenu();
        copyMenuVOToEntity(menuVO, menu);

        // 设置默认值
        if (menu.getStatus() == null) {
            menu.setStatus(1);
        }

        try {
            menuMapper.insert(menu);
        } catch (Exception e) {
            log.error("新增菜单失败: {}", menuVO.getTitle(), e);
            throw new BusinessException("新增菜单失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"menuList", "userMenuList", "asyncRoutes"}, allEntries = true)
    public void updateMenu(MenuVO menuVO) {
        if (menuVO == null || menuVO.getId() == null) {
            throw new BusinessException("菜单ID不能为空");
        }

        // 检查菜单是否存在
        SysMenu existingMenu = menuMapper.selectById(menuVO.getId());
        if (existingMenu == null) {
            throw new BusinessException("菜单不存在");
        }

        // 验证菜单唯一性（排除自身）
        validateMenuUniquenessForUpdate(menuVO);

        SysMenu menu = new SysMenu();
        // 设置ID，这是更新操作必需的
        menu.setId(menuVO.getId());
        copyMenuVOToEntity(menuVO, menu);

        log.info("复制后的菜单状态值: {}", menu.getStatus());
        log.info("更新菜单ID: {}, parentId: {}", menu.getId(), menu.getParentId());

        try {
            int updated = menuMapper.updateMenuWithParentId(menu);
            if (updated == 0) {
                throw new BusinessException("更新菜单失败，菜单可能已被删除");
            }

            // 处理父子菜单状态联动（只有当状态确实发生变化时才处理）
            if (menu.getStatus() != null && !Objects.equals(menu.getStatus(), existingMenu.getStatus())) {
                log.info("菜单状态发生变化，触发状态联动: menuId={}, oldStatus={}, newStatus={}",
                    menu.getId(), existingMenu.getStatus(), menu.getStatus());
                handleMenuStatusLinkage(menu.getId(), menu.getStatus(), existingMenu.getStatus());
            }

        } catch (Exception e) {
            log.error("更新菜单失败: {}", menuVO.getTitle(), e);
            throw new BusinessException("更新菜单失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"menuList", "userMenuList", "asyncRoutes"}, allEntries = true)
    public void deleteMenu(Integer menuId) {
        if (menuId == null) {
            throw new BusinessException("菜单ID不能为空");
        }

        // 检查菜单是否存在
        SysMenu existingMenu = menuMapper.selectById(menuId);
        if (existingMenu == null) {
            throw new BusinessException("菜单不存在");
        }

        // 检查菜单状态，只有停用状态的菜单才能删除
        if (existingMenu.getStatus() == 1) {
            throw new BusinessException("只有停用状态的菜单才能删除，请先停用该菜单");
        }

        try {
            // 检查是否有禁用状态的子菜单
            List<SysMenu> children = menuMapper.selectList(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<SysMenu>()
                    .eq("parent_id", menuId)
                    .eq("status", 0)
            );

            if (!children.isEmpty()) {
                // 递归删除子菜单
                for (SysMenu child : children) {
                    deleteMenu(child.getId());
                }
            }

            // 删除当前菜单（物理删除，因为已经是禁用状态）
            int deleted = menuMapper.deleteById(menuId);

            // 删除相关的角色菜单关联关系
            deleteMenuRoleRelations(menuId);

            if (deleted == 0) {
                throw new BusinessException("删除菜单失败");
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除菜单失败: {}", menuId, e);
            throw new BusinessException("删除菜单失败");
        }
    }

    /**
     * 转换为MenuVO列表
     */
    private List<MenuVO> convertToMenuVOList(List<SysMenu> menuList) {
        if (menuList == null || menuList.isEmpty()) {
            return new ArrayList<>();
        }

        return menuList.stream()
                .map(this::convertToMenuVO)
                .collect(Collectors.toList());
    }

    /**
     * 转换为MenuVO
     */
    private MenuVO convertToMenuVO(SysMenu menu) {
        MenuVO menuVO = new MenuVO();
        BeanUtils.copyProperties(menu, menuVO);

        // 转换布尔值字段
        menuVO.setShowLink(menu.getShowLink() != null && menu.getShowLink() == 1);
        menuVO.setShowParent(menu.getShowParent() != null && menu.getShowParent() == 1);
        menuVO.setKeepAlive(menu.getKeepAlive() != null && menu.getKeepAlive() == 1);
        menuVO.setFixedTag(menu.getFixedTag() != null && menu.getFixedTag() == 1);
        // hideMenu 是 showLink 的反向值
        menuVO.setHideMenu(menu.getShowLink() == null || menu.getShowLink() == 0);

        // 设置backstage字段
        menuVO.setBackstage(true);

        // 创建Meta信息
        MenuVO.Meta meta = new MenuVO.Meta();
        meta.setTitle(menu.getTitle());
        meta.setIcon(menu.getIcon());
        meta.setRank(menu.getRank());
        meta.setShowLink(menuVO.getShowLink());
        meta.setShowParent(menuVO.getShowParent());
        meta.setKeepAlive(menuVO.getKeepAlive());
        meta.setFixedTag(menuVO.getFixedTag());
        meta.setFrameSrc(menu.getFrameSrc());
        meta.setHideMenu(menuVO.getHideMenu());
        meta.setAuths(menu.getAuths());
        meta.setBackstage(true);

        menuVO.setMeta(meta);

        return menuVO;
    }



    /**
     * 验证菜单唯一性（新增时）
     */
    private void validateMenuUniqueness(MenuVO menuVO) {
        // 检查菜单名称是否重复
        long nameCount = menuMapper.selectCount(
            new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<SysMenu>()
                .eq("title", menuVO.getTitle())
                .eq("status", 1)
        );
        if (nameCount > 0) {
            throw new BusinessException("菜单名称已存在");
        }

        // 检查路由路径是否重复
        if (StringUtils.hasText(menuVO.getPath())) {
            long pathCount = menuMapper.selectCount(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<SysMenu>()
                    .eq("path", menuVO.getPath())
                    .eq("status", 1)
            );
            if (pathCount > 0) {
                throw new BusinessException("路由路径已存在");
            }
        }
    }

    /**
     * 验证菜单唯一性（更新时）
     */
    private void validateMenuUniquenessForUpdate(MenuVO menuVO) {
        // 检查菜单名称是否重复（排除自身）
        long nameCount = menuMapper.selectCount(
            new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<SysMenu>()
                .eq("title", menuVO.getTitle())
                .eq("status", 1)
                .ne("id", menuVO.getId())
        );
        if (nameCount > 0) {
            throw new BusinessException("菜单名称已存在");
        }

        // 检查路由路径是否重复（排除自身）
        if (StringUtils.hasText(menuVO.getPath())) {
            long pathCount = menuMapper.selectCount(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<SysMenu>()
                    .eq("path", menuVO.getPath())
                    .eq("status", 1)
                    .ne("id", menuVO.getId())
            );
            if (pathCount > 0) {
                throw new BusinessException("路由路径已存在");
            }
        }
    }

    /**
     * 复制MenuVO到实体
     */
    private void copyMenuVOToEntity(MenuVO menuVO, SysMenu menu) {
        BeanUtils.copyProperties(menuVO, menu);

        // 特殊处理parentId字段：确保null值能正确设置
        // 当parentId为null时，表示这是一个顶级菜单
        menu.setParentId(menuVO.getParentId());

        // 转换布尔值字段
        menu.setShowLink(menuVO.getShowLink() != null && menuVO.getShowLink() ? 1 : 0);
        menu.setShowParent(menuVO.getShowParent() != null && menuVO.getShowParent() ? 1 : 0);
        menu.setKeepAlive(menuVO.getKeepAlive() != null && menuVO.getKeepAlive() ? 1 : 0);
        menu.setFixedTag(menuVO.getFixedTag() != null && menuVO.getFixedTag() ? 1 : 0);
        menu.setHideMenu(menuVO.getHideMenu() != null && menuVO.getHideMenu() ? 1 : 0);
    }

    /**
     * 处理菜单状态联动
     */
    private void handleMenuStatusLinkage(Integer menuId, Integer newStatus, Integer oldStatus) {
        if (Objects.equals(newStatus, oldStatus)) return;

        log.info("处理菜单{}状态联动，从{}变为{}", menuId, oldStatus, newStatus);

        if (newStatus == 0) {
            updateChildrenStatus(menuId, 0);
        } else {
            updateParentChain(menuId);
            updateChildrenStatus(menuId, 1);
        }
    }

    /**
     * 递归更新子菜单状态
     */
    private void updateChildrenStatus(Integer parentId, Integer status) {
        menuMapper.selectList(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<SysMenu>()
                .eq("parent_id", parentId).eq("status", 1 - status))
                .forEach(child -> {
                    menuMapper.update(null, new com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper<SysMenu>()
                            .eq("id", child.getId()).set("status", status));
                    updateChildrenStatus(child.getId(), status);
                });
    }

    /**
     * 递归更新父菜单状态
     */
    private void updateParentChain(Integer childId) {
        SysMenu current = menuMapper.selectById(childId);
        if (current == null || current.getParentId() == null || current.getParentId() == 0) return;

        SysMenu parent = menuMapper.selectById(current.getParentId());
        if (parent != null && parent.getStatus() == 0) {
            menuMapper.update(null, new com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper<SysMenu>()
                    .eq("id", parent.getId()).set("status", 1));
            updateParentChain(parent.getId());
        }
    }

    /**
     * 删除菜单角色关联
     */
    private void deleteMenuRoleRelations(Integer menuId) {
        List<Integer> menuIds = collectAllMenuIds(menuId);
        if (!menuIds.isEmpty()) {
            roleMenuMapper.deleteByMenuIds(menuIds);
        }
    }

    /**
     * 收集所有菜单ID
     */
    private List<Integer> collectAllMenuIds(Integer menuId) {
        List<Integer> result = new ArrayList<>();
        result.add(menuId);
        menuMapper.selectList(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<SysMenu>()
                .eq("parent_id", menuId))
                .forEach(child -> result.addAll(collectAllMenuIds(child.getId())));
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"menuList", "userMenuList", "asyncRoutes"}, allEntries = true)
    public void toggleMenuStatus(Integer menuId, Integer status) {
        if (menuId == null || status == null) {
            throw new BusinessException("菜单ID和状态不能为空");
        }

        if (status != 0 && status != 1) {
            throw new BusinessException("状态值无效");
        }

        // 获取当前菜单状态
        SysMenu existingMenu = menuMapper.selectById(menuId);
        if (existingMenu == null) {
            throw new BusinessException("菜单不存在");
        }

        try {
            SysMenu menu = new SysMenu();
            menu.setId(menuId);
            menu.setStatus(status);

            int updated = menuMapper.updateById(menu);
            if (updated == 0) {
                throw new BusinessException("更新菜单状态失败，菜单可能不存在");
            }

            // 处理父子菜单状态联动
            handleMenuStatusLinkage(menuId, status, existingMenu.getStatus());

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("切换菜单状态失败: menuId={}, status={}", menuId, status, e);
            throw new BusinessException("切换菜单状态失败");
        }
    }
}
