<template>
  <el-form
    ref="ruleFormRef"
    :model="newFormInline"
    :rules="formRules"
    label-width="100px"
  >
    <el-row :gutter="30">
      <el-col :xs="24" :sm="12">
        <el-form-item label="学号" prop="studentId">
          <el-input
            v-model="newFormInline.studentId"
            clearable
            placeholder="请输入学号"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item label="姓名" prop="name">
          <el-input
            v-model="newFormInline.name"
            clearable
            placeholder="请输入姓名"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item label="性别" prop="gender">
          <el-select
            v-model="newFormInline.gender"
            placeholder="请选择性别"
            class="w-full"
          >
            <el-option label="男" value="男" />
            <el-option label="女" value="女" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item label="出生日期" prop="birthDate">
          <el-date-picker
            v-model="newFormInline.birthDate"
            type="date"
            placeholder="请选择出生日期"
            class="w-full"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item label="联系电话" prop="phone">
          <el-input
            v-model="newFormInline.phone"
            clearable
            placeholder="请输入联系电话"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="newFormInline.email"
            clearable
            placeholder="请输入邮箱地址"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item label="所属学院" prop="collegeCode">
          <el-select
            v-model="newFormInline.collegeCode"
            placeholder="请选择所属学院"
            class="w-full"
            @change="onCollegeChange"
          >
            <el-option
              v-for="item in collegeOptions"
              :key="item.collegeCode"
              :label="item.collegeName"
              :value="item.collegeCode"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item label="所属专业" prop="majorCode">
          <el-select
            v-model="newFormInline.majorCode"
            placeholder="请选择所属专业"
            class="w-full"
            @change="onMajorChange"
          >
            <el-option
              v-for="item in majorOptions"
              :key="item.majorCode"
              :label="item.majorName"
              :value="item.majorCode"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item label="所属班级" prop="classCode">
          <el-select
            v-model="newFormInline.classCode"
            placeholder="请选择所属班级"
            class="w-full"
          >
            <el-option
              v-for="item in classOptions"
              :key="item.classCode"
              :label="item.className"
              :value="item.classCode"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item label="入学日期" prop="enrollmentDate">
          <el-date-picker
            v-model="newFormInline.enrollmentDate"
            type="date"
            placeholder="请选择入学日期"
            class="w-full"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item label="学籍状态" prop="status">
          <el-select
            v-model="newFormInline.status"
            placeholder="请选择学籍状态"
            class="w-full"
          >
            <el-option label="在校" value="在校" />
            <el-option label="休学" value="休学" />
            <el-option label="退学" value="退学" />
            <el-option label="毕业" value="毕业" />
          </el-select>
        </el-form-item>
      </el-col>

    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { formRules } from "./utils/rule";
import { FormProps } from "./utils/types";
import { getAllColleges } from "@/api/basic/college";
import { getMajorsByCollegeCode } from "@/api/basic/major";
import { getClassesByMajorCode } from "@/api/basic/classes";

const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({
    studentId: "",
    name: "",
    gender: "",
    birthDate: "",
    phone: "",
    email: "",
    collegeCode: "",
    majorCode: "",
    classCode: "",
    enrollmentDate: "",
    status: "在读"
  })
});

const ruleFormRef = ref();
const newFormInline = ref(props.formInline);

// 选项数据
const collegeOptions = ref([]);
const majorOptions = ref([]);
const classOptions = ref([]);

// 加载学院选项
async function loadCollegeOptions() {
  try {
    const response = await getAllColleges();
    if (response.success && response.data) {
      collegeOptions.value = response.data;
    }
  } catch (error) {
    // 静默处理错误
  }
}

// 加载专业选项
async function loadMajorOptions(collegeCode: string) {
  if (!collegeCode) {
    majorOptions.value = [];
    return;
  }

  try {
    const response = await getMajorsByCollegeCode(collegeCode);
    if (response.success && response.data) {
      majorOptions.value = response.data;
    }
  } catch (error) {
    majorOptions.value = [];
  }
}

// 加载班级选项
async function loadClassOptions(majorCode: string) {
  if (!majorCode) {
    classOptions.value = [];
    return;
  }

  try {
    const response = await getClassesByMajorCode(majorCode);
    if (response.success && response.data) {
      classOptions.value = response.data;
    }
  } catch (error) {
    classOptions.value = [];
  }
}

// 学院变化处理
async function onCollegeChange(collegeCode: string) {
  // 清空专业和班级选择
  newFormInline.value.majorCode = "";
  newFormInline.value.classCode = "";
  majorOptions.value = [];
  classOptions.value = [];

  if (collegeCode) {
    await loadMajorOptions(collegeCode);
  }
}

// 专业变化处理
async function onMajorChange(majorCode: string) {
  // 清空班级选择
  newFormInline.value.classCode = "";
  classOptions.value = [];

  if (majorCode) {
    await loadClassOptions(majorCode);
  }
}

// 获取表单引用
function getRef() {
  return ruleFormRef.value;
}

// 组件挂载时加载数据
onMounted(async () => {
  await loadCollegeOptions();

  // 如果表单数据中有学院代码，加载对应的专业选项
  if (newFormInline.value.collegeCode) {
    await loadMajorOptions(newFormInline.value.collegeCode);
  }

  // 如果表单数据中有专业代码，加载对应的班级选项
  if (newFormInline.value.majorCode) {
    await loadClassOptions(newFormInline.value.majorCode);
  }
});

defineExpose({ getRef });
</script>
