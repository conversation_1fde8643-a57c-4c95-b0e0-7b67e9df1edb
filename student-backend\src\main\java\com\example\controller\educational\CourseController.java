package com.example.controller.educational;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.common.Result;
import com.example.dto.educational.CourseQueryDTO;
import com.example.service.educational.CourseService;
import com.example.vo.educational.CourseVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 课程管理控制器
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@RestController
@RequestMapping("/api/educational/course")
@RequiredArgsConstructor
@Validated
@Tag(name = "课程管理", description = "课程管理相关接口")
public class CourseController {

    private final CourseService courseService;

    /**
     * 分页查询课程列表
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询课程", description = "根据条件分页查询课程列表")
    public Result<IPage<CourseVO>> getCoursePage(@RequestBody CourseQueryDTO queryDTO) {
        try {
            IPage<CourseVO> result = courseService.getCoursePage(queryDTO);
            return Result.success("查询成功", result);
        } catch (Exception e) {
            log.error("分页查询课程失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID获取课程详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取课程详情", description = "根据课程ID获取课程详细信息")
    @Parameter(name = "id", description = "课程ID", required = true)
    public Result<CourseVO> getCourseById(@PathVariable Integer id) {
        try {
            CourseVO course = courseService.getCourseById(id);
            if (course != null) {
                return Result.success("查询成功", course);
            } else {
                return Result.notFound("课程不存在");
            }
        } catch (Exception e) {
            log.error("获取课程详情失败，课程ID: {}", id, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 新增课程
     */
    @PostMapping
    @Operation(summary = "新增课程", description = "创建新的课程")
    public Result<Void> saveCourse(@Valid @RequestBody CourseVO courseVO) {
        try {
            courseService.saveCourse(courseVO);
            return Result.success("课程创建成功");
        } catch (Exception e) {
            log.error("新增课程失败", e);
            return Result.error("创建失败：" + e.getMessage());
        }
    }

    /**
     * 更新课程
     */
    @PutMapping
    @Operation(summary = "更新课程", description = "更新课程信息")
    public Result<Void> updateCourse(@Valid @RequestBody CourseVO courseVO) {
        try {
            if (courseVO.getId() == null) {
                return Result.badRequest("课程ID不能为空");
            }
            courseService.updateCourse(courseVO);
            return Result.success("课程更新成功");
        } catch (Exception e) {
            log.error("更新课程失败，课程ID: {}", courseVO.getId(), e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除课程
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除课程", description = "根据课程ID删除课程")
    @Parameter(name = "id", description = "课程ID", required = true)
    public Result<Void> deleteCourse(@PathVariable Integer id) {
        try {
            courseService.deleteCourse(id);
            return Result.success("课程删除成功");
        } catch (Exception e) {
            log.error("删除课程失败，课程ID: {}", id, e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除课程
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除课程", description = "根据课程ID列表批量删除课程")
    public Result<Void> batchDeleteCourses(@RequestBody List<Integer> ids) {
        try {
            if (ids == null || ids.isEmpty()) {
                return Result.badRequest("课程ID列表不能为空");
            }
            courseService.batchDeleteCourses(ids);
            return Result.success("批量删除成功");
        } catch (Exception e) {
            log.error("批量删除课程失败，课程IDs: {}", ids, e);
            return Result.error("批量删除失败：" + e.getMessage());
        }
    }

    /**
     * 根据课程代码获取课程信息
     */
    @GetMapping("/code/{courseCode}")
    @Operation(summary = "根据课程代码获取课程", description = "根据课程代码获取课程信息")
    @Parameter(name = "courseCode", description = "课程代码", required = true)
    public Result<CourseVO> getCourseByCode(@PathVariable String courseCode) {
        try {
            CourseVO course = courseService.getCourseByCode(courseCode);
            if (course != null) {
                return Result.success("查询成功", course);
            } else {
                return Result.notFound("课程不存在");
            }
        } catch (Exception e) {
            log.error("根据课程代码获取课程失败，课程代码: {}", courseCode, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据学院代码获取课程列表
     */
    @GetMapping("/college/{collegeCode}")
    @Operation(summary = "根据学院获取课程列表", description = "根据学院代码获取该学院的所有课程")
    @Parameter(name = "collegeCode", description = "学院代码", required = true)
    public Result<List<CourseVO>> getCoursesByCollege(@PathVariable String collegeCode) {
        try {
            List<CourseVO> courses = courseService.getCoursesByCollege(collegeCode);
            return Result.success("查询成功", courses);
        } catch (Exception e) {
            log.error("根据学院获取课程列表失败，学院代码: {}", collegeCode, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }



    /**
     * 根据课程类型获取课程列表
     */
    @GetMapping("/type/{courseType}")
    @Operation(summary = "根据类型获取课程列表", description = "根据课程类型获取课程列表")
    @Parameter(name = "courseType", description = "课程类型", required = true)
    public Result<List<CourseVO>> getCoursesByType(@PathVariable String courseType) {
        try {
            List<CourseVO> courses = courseService.getCoursesByType(courseType);
            return Result.success("查询成功", courses);
        } catch (Exception e) {
            log.error("根据课程类型获取课程列表失败，课程类型: {}", courseType, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有课程列表（不分页）
     */
    @GetMapping("/all")
    @Operation(summary = "获取所有课程", description = "获取所有课程列表，不分页")
    public Result<List<CourseVO>> getAllCourses() {
        try {
            List<CourseVO> courses = courseService.getAllCourses();
            return Result.success("查询成功", courses);
        } catch (Exception e) {
            log.error("获取所有课程失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
}
