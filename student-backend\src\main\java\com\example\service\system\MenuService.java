package com.example.service.system;

import com.example.vo.system.MenuVO;

import java.util.List;

/**
 * 菜单服务接口
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public interface MenuService {

    /**
     * 获取菜单列表
     *
     * @return 菜单列表
     */
    List<MenuVO> getMenuList();

    /**
     * 获取用户菜单列表
     *
     * @param userId 用户ID
     * @return 用户菜单列表
     */
    List<MenuVO> getUserMenuList(Integer userId);

    /**
     * 获取异步路由
     *
     * @param userId 用户ID
     * @return 异步路由列表
     */
    List<MenuVO> getAsyncRoutes(Integer userId);

    /**
     * 新增菜单
     *
     * @param menuVO 菜单信息
     */
    void saveMenu(MenuVO menuVO);

    /**
     * 更新菜单
     *
     * @param menuVO 菜单信息
     */
    void updateMenu(MenuVO menuVO);

    /**
     * 删除菜单
     *
     * @param menuId 菜单ID
     */
    void deleteMenu(Integer menuId);

    /**
     * 切换菜单状态
     *
     * @param menuId 菜单ID
     * @param status 状态
     */
    void toggleMenuStatus(Integer menuId, Integer status);
}
