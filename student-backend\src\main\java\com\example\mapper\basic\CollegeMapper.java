package com.example.mapper.basic;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.dto.basic.CollegeQueryDTO;
import com.example.entity.basic.College;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 学院Mapper
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Mapper
public interface CollegeMapper extends BaseMapper<College> {

    /**
     * 分页查询学院列表
     *
     * @param page 分页参数
     * @param query 查询条件
     * @return 学院列表
     */
    IPage<College> selectCollegePage(IPage<College> page, @Param("query") CollegeQueryDTO query);

    /**
     * 查询所有学院
     *
     * @return 学院列表
     */
    List<College> selectAllColleges();

    /**
     * 根据学院代码查询学院
     *
     * @param collegeCode 学院代码
     * @return 学院信息
     */
    College selectByCollegeCode(@Param("collegeCode") String collegeCode);

    /**
     * 检查学院代码是否存在
     *
     * @param collegeCode 学院代码
     * @param excludeId 排除的学院ID
     * @return 是否存在
     */
    Boolean existsByCollegeCode(@Param("collegeCode") String collegeCode, @Param("excludeId") Integer excludeId);

    /**
     * 检查学院名称是否存在
     *
     * @param collegeName 学院名称
     * @param excludeId 排除的学院ID
     * @return 是否存在
     */
    Boolean existsByCollegeName(@Param("collegeName") String collegeName, @Param("excludeId") Integer excludeId);
}
