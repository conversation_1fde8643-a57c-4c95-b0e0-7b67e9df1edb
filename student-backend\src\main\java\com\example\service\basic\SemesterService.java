package com.example.service.basic;

import com.example.dto.basic.SemesterQueryDTO;
import com.example.vo.basic.SemesterVO;
import com.example.common.PageResult;

import java.util.List;

/**
 * 学年学期服务接口
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
public interface SemesterService {

    /**
     * 分页查询学年学期列表
     *
     * @param query 查询条件
     * @return 学年学期列表
     */
    PageResult<SemesterVO> getSemesterList(SemesterQueryDTO query);

    /**
     * 获取所有学年学期列表
     *
     * @return 学年学期列表
     */
    List<SemesterVO> getAllSemesters();

    /**
     * 根据学年获取学期列表
     *
     * @param academicYear 学年
     * @return 学期列表
     */
    List<SemesterVO> getSemestersByAcademicYear(String academicYear);

    /**
     * 根据ID获取学年学期详情
     *
     * @param id 学期ID
     * @return 学期详情
     */
    SemesterVO getSemesterById(Integer id);

    /**
     * 获取当前学期
     *
     * @return 当前学期
     */
    SemesterVO getCurrentSemester();

    /**
     * 新增学年学期
     *
     * @param semesterVO 学期信息
     */
    void saveSemester(SemesterVO semesterVO);

    /**
     * 更新学年学期
     *
     * @param semesterVO 学期信息
     */
    void updateSemester(SemesterVO semesterVO);

    /**
     * 删除学年学期
     *
     * @param id 学期ID
     */
    void deleteSemester(Integer id);

    /**
     * 设置当前学期
     *
     * @param id 学期ID
     */
    void setCurrentSemester(Integer id);
}
