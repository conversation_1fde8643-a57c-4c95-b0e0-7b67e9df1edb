package com.example.mapper.educational;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.dto.educational.CourseQueryDTO;
import com.example.entity.educational.Course;
import com.example.vo.educational.CourseVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 课程Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Mapper
public interface CourseMapper extends BaseMapper<Course> {

    /**
     * 分页查询课程列表
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 课程分页列表
     */
    IPage<CourseVO> selectCoursePage(Page<CourseVO> page, @Param("query") CourseQueryDTO queryDTO);

    /**
     * 根据课程ID获取课程详情
     *
     * @param id 课程ID
     * @return 课程详情
     */
    CourseVO selectCourseById(@Param("id") Integer id);

    /**
     * 根据课程代码获取课程信息
     *
     * @param courseCode 课程代码
     * @return 课程信息
     */
    CourseVO selectCourseByCode(@Param("courseCode") String courseCode);

    /**
     * 根据学院代码获取课程列表
     *
     * @param collegeCode 学院代码
     * @return 课程列表
     */
    List<CourseVO> selectCoursesByCollege(@Param("collegeCode") String collegeCode);



    /**
     * 根据课程类型获取课程列表
     *
     * @param courseType 课程类型
     * @return 课程列表
     */
    List<CourseVO> selectCoursesByType(@Param("courseType") String courseType);

    /**
     * 获取所有课程列表
     *
     * @return 课程列表
     */
    List<CourseVO> selectAllCourses();

    /**
     * 检查课程代码是否存在
     *
     * @param courseCode 课程代码
     * @param excludeId 排除的课程ID（用于更新时检查）
     * @return 存在的课程数量
     */
    int checkCourseCodeExists(@Param("courseCode") String courseCode, @Param("excludeId") Integer excludeId);

    /**
     * 批量删除课程
     *
     * @param ids 课程ID列表
     * @return 删除的记录数
     */
    int batchDeleteCourses(@Param("ids") List<Integer> ids);

    /**
     * 根据学院代码统计课程数量
     *
     * @param collegeCode 学院代码
     * @return 课程数量
     */
    int countCoursesByCollege(@Param("collegeCode") String collegeCode);

    /**
     * 根据专业代码统计课程数量
     *
     * @param majorCode 专业代码
     * @return 课程数量
     */
    int countCoursesByMajor(@Param("majorCode") String majorCode);
}
