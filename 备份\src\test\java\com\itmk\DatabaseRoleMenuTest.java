package com.itmk;

import com.itmk.web.sys_menu.dto.RouterDTO;
import com.itmk.web.sys_menu.service.SysMenuService;
import com.itmk.web.sys_role.entity.SysRole;
import com.itmk.web.sys_role.service.SysRoleService;
import com.itmk.web.sys_role_menu.entity.SysRoleMenu;
import com.itmk.web.sys_role_menu.service.SysRoleMenuService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * 数据库角色菜单关系测试
 * 验证基于数据库的动态权限分配是否正常工作
 */
@SpringBootTest
public class DatabaseRoleMenuTest {

    @Autowired
    private SysRoleService sysRoleService;

    @Autowired
    private SysMenuService sysMenuService;

    @Autowired
    private SysRoleMenuService sysRoleMenuService;

    /**
     * 测试从数据库获取角色列表
     */
    @Test
    public void testGetRolesFromDatabase() {
        System.out.println("=== 测试从数据库获取角色列表 ===");
        
        List<SysRole> roles = sysRoleService.list();
        System.out.println("数据库中的角色数量: " + roles.size());
        
        for (SysRole role : roles) {
            System.out.println("角色ID: " + role.getRoleId() + 
                ", 角色名称: " + role.getRoleName() + 
                ", 角色类型: " + role.getRoleType());
        }
    }

    /**
     * 测试根据角色名称获取菜单
     */
    @Test
    public void testGetMenuByRoleName() {
        System.out.println("=== 测试根据角色名称获取菜单 ===");
        
        // 测试管理员角色
        List<RouterDTO> adminMenus = sysMenuService.getMenuByRoleName("管理员");
        System.out.println("管理员菜单数量: " + adminMenus.size());
        
        // 测试教师角色
        List<RouterDTO> teacherMenus = sysMenuService.getMenuByRoleName("教师");
        System.out.println("教师菜单数量: " + teacherMenus.size());
        
        // 测试学生角色
        List<RouterDTO> studentMenus = sysMenuService.getMenuByRoleName("学生");
        System.out.println("学生菜单数量: " + studentMenus.size());
    }

    /**
     * 测试角色菜单关联表数据
     */
    @Test
    public void testRoleMenuAssociations() {
        System.out.println("=== 测试角色菜单关联表数据 ===");
        
        List<SysRoleMenu> associations = sysRoleMenuService.list();
        System.out.println("角色菜单关联记录总数: " + associations.size());
        
        // 按角色ID分组统计
        associations.stream()
            .collect(java.util.stream.Collectors.groupingBy(
                SysRoleMenu::getRoleId,
                java.util.stream.Collectors.counting()
            ))
            .forEach((roleId, count) -> {
                System.out.println("角色ID " + roleId + " 关联的菜单数量: " + count);
            });
    }

    /**
     * 测试角色删除的级联处理
     */
    @Test
    public void testRoleDeletionCascade() {
        System.out.println("=== 测试角色删除级联处理（仅模拟，不实际删除） ===");
        
        // 查找一个测试角色
        List<SysRole> roles = sysRoleService.list();
        if (!roles.isEmpty()) {
            SysRole testRole = roles.get(0);
            System.out.println("测试角色: " + testRole.getRoleName() + " (ID: " + testRole.getRoleId() + ")");
            
            // 查看该角色关联的菜单数量
            long menuCount = sysRoleMenuService.lambdaQuery()
                .eq(SysRoleMenu::getRoleId, testRole.getRoleId())
                .count();
            
            System.out.println("该角色关联的菜单数量: " + menuCount);
            System.out.println("如果删除该角色，将同时清理 " + menuCount + " 条角色菜单关联记录");
        }
    }
}
