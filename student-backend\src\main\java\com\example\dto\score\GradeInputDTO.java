package com.example.dto.score;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 成绩录入DTO
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@Schema(description = "成绩录入DTO")
public class GradeInputDTO {

    @Schema(description = "成绩ID（更新时需要）")
    private Integer id;

    @Schema(description = "学号", required = true)
    @NotBlank(message = "学号不能为空")
    private String studentId;

    @Schema(description = "课程代码", required = true)
    @NotBlank(message = "课程代码不能为空")
    private String courseCode;

    @Schema(description = "学期ID", required = true)
    @NotNull(message = "学期ID不能为空")
    private Integer semesterId;

    @Schema(description = "期末成绩", required = true)
    @NotNull(message = "期末成绩不能为空")
    @DecimalMin(value = "0.0", message = "成绩不能小于0")
    @DecimalMax(value = "100.0", message = "成绩不能大于100")
    private BigDecimal finalScore;

    @Schema(description = "绩点")
    @DecimalMin(value = "0.0", message = "绩点不能小于0")
    @DecimalMax(value = "5.0", message = "绩点不能大于5")
    private BigDecimal gradePoint;

    @Schema(description = "是否重修")
    private Boolean isRetake = false;

    @Schema(description = "备注")
    private String remarks;
}
