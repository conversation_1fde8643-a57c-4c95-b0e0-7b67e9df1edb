import { reactive } from "vue";
import type { FormRules } from "element-plus";

/** 成绩录入表单校验规则 */
export const formRules = reactive(<FormRules>{
  studentId: [
    {
      required: true,
      message: "学号为必填项",
      trigger: "blur"
    }
  ],
  studentName: [
    {
      required: true,
      message: "学生姓名为必填项",
      trigger: "blur"
    }
  ],
  finalScore: [
    {
      required: true,
      message: "期末成绩为必填项",
      trigger: "blur"
    },
    {
      type: "number",
      min: 0,
      max: 100,
      message: "成绩应在0-100之间",
      trigger: "blur"
    }
  ]
});

/** 导入表单校验规则 */
export const importFormRules = reactive(<FormRules>{
  selectedCourseCodes: [
    {
      required: true,
      message: "请至少选择一门课程",
      trigger: "change"
    }
  ]
});
