import type { FormRules } from "element-plus";

/** 成绩录入表单验证规则 */
export const gradeFormRules: FormRules = {
  studentId: [
    { required: true, message: "学号为必填项", trigger: "blur" }
  ],
  studentName: [
    { required: true, message: "学生姓名为必填项", trigger: "blur" }
  ],
  courseCode: [
    { required: true, message: "课程代码为必填项", trigger: "blur" }
  ],
  semesterId: [
    { required: true, message: "学期为必填项", trigger: "change" }
  ],
  finalScore: [
    { required: true, message: "期末成绩为必填项", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value === null || value === undefined || value === "") {
          callback(new Error("期末成绩为必填项"));
        } else if (isNaN(Number(value))) {
          callback(new Error("期末成绩必须为数字"));
        } else if (Number(value) < 0 || Number(value) > 100) {
          callback(new Error("期末成绩必须在0-100之间"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ]
};

/** 成绩导入表单验证规则 */
export const importFormRules: FormRules = {
  classCode: [
    { required: true, message: "班级代码为必填项", trigger: "blur" }
  ],
  selectedCourseCodes: [
    {
      validator: (rule, value, callback) => {
        if (!value || !Array.isArray(value) || value.length === 0) {
          callback(new Error("请至少选择一门课程"));
        } else {
          callback();
        }
      },
      trigger: "change"
    }
  ],
  file: [
    { required: true, message: "请选择要导入的文件", trigger: "change" }
  ]
};

/** 成绩查询表单验证规则 */
export const searchFormRules: FormRules = {
  // 查询表单通常不需要必填验证，这里可以添加格式验证
  studentId: [
    {
      pattern: /^[a-zA-Z0-9]+$/,
      message: "学号只能包含字母和数字",
      trigger: "blur"
    }
  ]
};
