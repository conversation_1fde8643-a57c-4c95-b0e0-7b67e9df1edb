package com.example.service.basic;

import com.example.dto.basic.CollegeQueryDTO;
import com.example.vo.basic.CollegeVO;
import com.example.common.PageResult;

import java.util.List;

/**
 * 学院服务接口
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
public interface CollegeService {

    /**
     * 分页查询学院列表
     *
     * @param query 查询条件
     * @return 学院列表
     */
    PageResult<CollegeVO> getCollegeList(CollegeQueryDTO query);

    /**
     * 获取所有学院列表
     *
     * @return 学院列表
     */
    List<CollegeVO> getAllColleges();

    /**
     * 根据ID获取学院详情
     *
     * @param id 学院ID
     * @return 学院详情
     */
    CollegeVO getCollegeById(Integer id);

    /**
     * 新增学院
     *
     * @param collegeVO 学院信息
     */
    void saveCollege(CollegeVO collegeVO);

    /**
     * 更新学院
     *
     * @param collegeVO 学院信息
     */
    void updateCollege(CollegeVO collegeVO);

    /**
     * 删除学院
     *
     * @param id 学院ID
     */
    void deleteCollege(Integer id);
}
