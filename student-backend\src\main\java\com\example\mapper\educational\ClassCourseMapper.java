package com.example.mapper.educational;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.dto.educational.ClassCourseQueryDTO;
import com.example.entity.educational.ClassCourse;
import com.example.vo.educational.ClassCourseVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 班级课程分配Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Mapper
public interface ClassCourseMapper extends BaseMapper<ClassCourse> {

    /**
     * 分页查询班级课程分配列表
     *
     * @param page  分页参数
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<ClassCourseVO> selectClassCoursePage(Page<ClassCourseVO> page, @Param("query") ClassCourseQueryDTO query);

    /**
     * 根据ID获取班级课程分配详情
     *
     * @param id 分配ID
     * @return 班级课程分配详情
     */
    ClassCourseVO selectClassCourseById(@Param("id") Integer id);

    /**
     * 根据班级代码获取课程分配列表
     *
     * @param classCode 班级代码
     * @return 课程分配列表
     */
    List<ClassCourseVO> selectCoursesByClass(@Param("classCode") String classCode);

    /**
     * 根据班级代码和学期ID获取课程分配列表
     *
     * @param classCode  班级代码
     * @param semesterId 学期ID
     * @return 课程分配列表
     */
    List<ClassCourseVO> selectCoursesByClassAndSemester(@Param("classCode") String classCode, @Param("semesterId") Integer semesterId);

    /**
     * 根据课程代码获取分配的班级列表
     *
     * @param courseCode 课程代码
     * @return 班级列表
     */
    List<ClassCourseVO> selectClassesByCourse(@Param("courseCode") String courseCode);

    /**
     * 检查班级课程分配是否存在
     *
     * @param classCode  班级代码
     * @param courseCode 课程代码
     * @param semesterId 学期ID
     * @param excludeId  排除的ID
     * @return 存在数量
     */
    int checkClassCourseExists(@Param("classCode") String classCode,
                              @Param("courseCode") String courseCode,
                              @Param("semesterId") Integer semesterId,
                              @Param("excludeId") Integer excludeId);

    /**
     * 批量删除班级课程分配
     *
     * @param ids ID列表
     * @return 删除数量
     */
    int batchDeleteClassCourses(@Param("ids") List<Integer> ids);

    /**
     * 根据班级代码统计课程数量
     *
     * @param classCode 班级代码
     * @return 课程数量
     */
    int countCoursesByClass(@Param("classCode") String classCode);

    /**
     * 根据课程代码统计分配的班级数量
     *
     * @param courseCode 课程代码
     * @return 班级数量
     */
    int countClassesByCourse(@Param("courseCode") String courseCode);

    /**
     * 根据班级代码和课程代码查询课程分配列表
     *
     * @param classCode  班级代码
     * @param courseCode 课程代码
     * @return 课程分配列表
     */
    List<ClassCourseVO> selectClassCoursesByClassAndCourse(@Param("classCode") String classCode, @Param("courseCode") String courseCode);
}
