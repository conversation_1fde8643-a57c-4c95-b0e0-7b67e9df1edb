package com.example.service.educational;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.dto.educational.CourseQueryDTO;
import com.example.vo.educational.CourseVO;

import java.util.List;

/**
 * 课程服务接口
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
public interface CourseService {

    /**
     * 分页查询课程列表
     *
     * @param queryDTO 查询条件
     * @return 课程分页列表
     */
    IPage<CourseVO> getCoursePage(CourseQueryDTO queryDTO);

    /**
     * 根据课程ID获取课程详情
     *
     * @param id 课程ID
     * @return 课程详情
     */
    CourseVO getCourseById(Integer id);

    /**
     * 新增课程
     *
     * @param courseVO 课程信息
     */
    void saveCourse(CourseVO courseVO);

    /**
     * 更新课程
     *
     * @param courseVO 课程信息
     */
    void updateCourse(CourseVO courseVO);

    /**
     * 删除课程
     *
     * @param id 课程ID
     */
    void deleteCourse(Integer id);

    /**
     * 批量删除课程
     *
     * @param ids 课程ID列表
     */
    void batchDeleteCourses(List<Integer> ids);

    /**
     * 根据课程代码获取课程信息
     *
     * @param courseCode 课程代码
     * @return 课程信息
     */
    CourseVO getCourseByCode(String courseCode);

    /**
     * 根据学院代码获取课程列表
     *
     * @param collegeCode 学院代码
     * @return 课程列表
     */
    List<CourseVO> getCoursesByCollege(String collegeCode);



    /**
     * 根据课程类型获取课程列表
     *
     * @param courseType 课程类型
     * @return 课程列表
     */
    List<CourseVO> getCoursesByType(String courseType);

    /**
     * 获取所有课程列表
     *
     * @return 课程列表
     */
    List<CourseVO> getAllCourses();

    /**
     * 检查课程代码是否存在
     *
     * @param courseCode 课程代码
     * @param excludeId 排除的课程ID（用于更新时检查）
     * @return 是否存在
     */
    boolean checkCourseCodeExists(String courseCode, Integer excludeId);

    /**
     * 根据学院代码统计课程数量
     *
     * @param collegeCode 学院代码
     * @return 课程数量
     */
    int countCoursesByCollege(String collegeCode);


}
