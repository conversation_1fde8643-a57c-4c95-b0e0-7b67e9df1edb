package com.example.common.schedule;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.example.entity.basic.Semester;
import com.example.mapper.basic.SemesterMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.PostConstruct;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;
import java.util.List;

/**
 * 学年学期自动创建服务
 *
 * 学年规则：8月1日-次年7月31日为一个学年
 * 学期规则：8月1日-1月31日为第一学期，2月1日-7月31日为第二学期
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SemesterAutoCreateService {

    private final SemesterMapper semesterMapper;

    /**
     * 每年8月1日自动创建新学年的学期记录
     * cron表达式：0 0 0 1 8 ? 表示每年8月1日0点0分0秒执行
     * 在新学年开始时创建当年学年的学期记录
     */
    @Scheduled(cron = "0 0 0 1 8 ?")
    @Transactional(rollbackFor = Exception.class)
    public void autoCreateCurrentAcademicYear() {
        try {
            LocalDate now = LocalDate.now();
            int currentYear = now.getYear();

            // 8月1日，当前学年应该是 currentYear-nextYear
            String currentAcademicYear = currentYear + "-" + (currentYear + 1);

            // 检查当前学年是否已存在
            List<Semester> existingSemesters = semesterMapper.selectByAcademicYear(currentAcademicYear);
            if (!existingSemesters.isEmpty()) {
                return;
            }

            // 创建第一学期（当前学期）
            createSemesterWithCurrentFlag(currentAcademicYear, 1, currentYear, true);

            // 创建第二学期
            createSemesterWithCurrentFlag(currentAcademicYear, 2, currentYear, false);

        } catch (Exception e) {
            log.error("新学年学期自动创建任务执行失败", e);
            throw e;
        }
    }

    /**
     * 系统启动时检查并创建当前学年学期记录
     * 只创建当前需要的学年学期
     */
    @PostConstruct
    @Transactional(rollbackFor = Exception.class)
    public void initializeSemesters() {
        try {
            // 获取当前应该存在的学年学期
            String[] currentSemesterInfo = getCurrentAcademicYearAndSemester();
            String currentAcademicYear = currentSemesterInfo[0];
            int currentSemesterNumber = Integer.parseInt(currentSemesterInfo[1]);

            // 检查当前学年是否存在
            List<Semester> existingSemesters = semesterMapper.selectByAcademicYear(currentAcademicYear);
            if (existingSemesters.isEmpty()) {
                // 解析学年
                String[] years = currentAcademicYear.split("-");
                int startYear = Integer.parseInt(years[0]);

                // 创建第一学期
                createSemesterWithCurrentFlag(currentAcademicYear, 1, startYear, currentSemesterNumber == 1);

                // 创建第二学期
                createSemesterWithCurrentFlag(currentAcademicYear, 2, startYear, currentSemesterNumber == 2);
            } else {
                // 学年已存在，只需要更新当前学期标记
                autoSetCurrentSemester();
            }

        } catch (Exception e) {
            log.error("初始化学年学期记录失败", e);
        }
    }



    /**
     * 创建学期记录（带当前学期标记）
     *
     * @param academicYear 学年
     * @param semesterNumber 学期号
     * @param startYear 开始年份
     * @param isCurrent 是否为当前学期
     */
    private void createSemesterWithCurrentFlag(String academicYear, int semesterNumber, int startYear, boolean isCurrent) {
        Semester semester = new Semester();
        semester.setAcademicYear(academicYear);
        semester.setSemesterNumber(semesterNumber);

        LocalDate startDate;
        LocalDate endDate;

        if (semesterNumber == 1) {
            // 第一学期：8月1日-1月31日
            semester.setSemesterName(academicYear + "-1");
            startDate = LocalDate.of(startYear, Month.AUGUST, 1);
            endDate = LocalDate.of(startYear + 1, Month.JANUARY, 31);
        } else {
            // 第二学期：2月1日-7月31日
            semester.setSemesterName(academicYear + "-2");
            startDate = LocalDate.of(startYear + 1, Month.FEBRUARY, 1);
            endDate = LocalDate.of(startYear + 1, Month.JULY, 31);
        }

        semester.setStartDate(startDate);
        semester.setEndDate(endDate);
        semester.setIsCurrent(isCurrent);
        semester.setCreatedAt(LocalDateTime.now());
        semester.setUpdatedAt(LocalDateTime.now());

        // 如果设置为当前学期，先清除其他学期的当前标记
        if (isCurrent) {
            semesterMapper.clearAllCurrentFlags();
        }

        semesterMapper.insert(semester);
    }



    /**
     * 获取当前应该是哪个学年和学期
     *
     * @return [学年, 学期号]
     */
    private String[] getCurrentAcademicYearAndSemester() {
        LocalDate now = LocalDate.now();
        int year = now.getYear();
        int month = now.getMonthValue();

        String academicYear;
        int semesterNumber;

        if (month >= 8) {
            // 8月-12月，属于当年开始的学年的第一学期
            academicYear = year + "-" + (year + 1);
            semesterNumber = 1;
        } else if (month >= 2) {
            // 2月-7月，属于上一年开始的学年的第二学期
            academicYear = (year - 1) + "-" + year;
            semesterNumber = 2;
        } else {
            // 1月，属于上一年开始的学年的第一学期
            academicYear = (year - 1) + "-" + year;
            semesterNumber = 1;
        }

        return new String[]{academicYear, String.valueOf(semesterNumber)};
    }



    /**
     * 自动设置当前学期
     * 根据当前日期自动设置正确的当前学期
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoSetCurrentSemester() {
        try {
            String[] currentSemesterInfo = getCurrentAcademicYearAndSemester();
            String academicYear = currentSemesterInfo[0];
            int semesterNumber = Integer.parseInt(currentSemesterInfo[1]);

            // 查找对应的学期记录
            Semester targetSemester = semesterMapper.selectByAcademicYearAndNumber(academicYear, semesterNumber);
            if (targetSemester == null) {
                // 如果找不到当前学期记录，自动创建
                String[] years = academicYear.split("-");
                int startYear = Integer.parseInt(years[0]);

                // 先清除所有当前学期标记
                semesterMapper.clearAllCurrentFlags();

                // 创建第一学期
                createSemesterWithCurrentFlag(academicYear, 1, startYear, semesterNumber == 1);

                // 创建第二学期
                createSemesterWithCurrentFlag(academicYear, 2, startYear, semesterNumber == 2);

                return;
            }

            // 检查是否已经是当前学期
            if (Boolean.TRUE.equals(targetSemester.getIsCurrent())) {
                return;
            }

            // 清除所有当前学期标记
            semesterMapper.clearAllCurrentFlags();

            // 设置新的当前学期
            semesterMapper.setCurrentSemester(targetSemester.getId());

        } catch (Exception e) {
            log.error("自动设置当前学期失败", e);
            throw e;
        }
    }

    /**
     * 每天凌晨1点检查并更新当前学期
     * 在学期切换日期（8月1日和2月1日）自动切换当前学期
     * cron表达式：0 0 1 * * ? 表示每天凌晨1点执行
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void scheduledUpdateCurrentSemester() {
        try {
            LocalDate now = LocalDate.now();
            int month = now.getMonthValue();
            int day = now.getDayOfMonth();

            // 只在学期切换的关键日期执行更新
            if ((month == 8 && day == 1) || (month == 2 && day == 1)) {
                autoSetCurrentSemester();
            }
        } catch (Exception e) {
            log.error("定时更新当前学期任务执行失败", e);
        }
    }
}
