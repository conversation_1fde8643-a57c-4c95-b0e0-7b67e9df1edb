import dayjs from "dayjs";
import editForm from "../form.vue";
import { message } from "@/utils/message";
import { addDialog } from "@/components/ReDialog";
import { type PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h, toRaw } from "vue";
import {
  getMajorList,
  saveMajor,
  updateMajor,
  deleteMajor,
  type MajorItem,
  type MajorQueryParams
} from "@/api/basic/major";
import { getAllColleges, type CollegeItem } from "@/api/basic/college";

export function useMajor() {
  const form = reactive<MajorQueryParams>({
    collegeCode: "",
    majorCode: "",
    majorName: "",
    duration: undefined,
    current: 1,
    size: 10
  });

  const dataList = ref<MajorItem[]>([]);
  const loading = ref(true);
  const collegeOptions = ref<CollegeItem[]>([]);

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  });

  const columns: TableColumnList = [
    {
      label: "序号",
      type: "index",
      width: 70
    },
    {
      label: "所属学院",
      prop: "collegeName",
      minWidth: 150
    },
    {
      label: "专业代码",
      prop: "majorCode",
      minWidth: 120
    },
    {
      label: "专业名称",
      prop: "majorName",
      minWidth: 150
    },
    {
      label: "学制年限",
      prop: "duration",
      minWidth: 100,
      formatter: ({ duration }) => `${duration}年`
    },
    {
      label: "专业描述",
      prop: "description",
      minWidth: 200,
      showOverflowTooltip: true
    },
    {
      label: "创建时间",
      minWidth: 180,
      prop: "createdAt",
      formatter: ({ createdAt }) =>
        dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "操作",
      fixed: "right",
      width: 180,
      slot: "operation"
    }
  ];

  function handleDelete(row: MajorItem) {
    deleteMajor({ id: row.id }).then(res => {
      if (res.success) {
        message(res.message, { type: "success" });
        onSearch();
      } else {
        message(res.message, { type: "error" });
      }
    }).catch(error => {
      message("网络错误，请稍后重试", { type: "error" });
    });
  }

  function handleSizeChange(val: number) {
    form.size = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    form.current = val;
    onSearch();
  }

  function handleSelectionChange(val: MajorItem[]) {
    console.log("handleSelectionChange", val);
  }

  async function onSearch() {
    loading.value = true;
    try {
      const response = await getMajorList(toRaw(form));
      if (response.success && response.data) {
        dataList.value = response.data.list || [];
        pagination.total = response.data.total || 0;
        pagination.currentPage = response.data.pageNum || 1;
        pagination.pageSize = response.data.pageSize || 10;
      } else {
        message(response.message, { type: "error" });
      }
    } catch (error) {
      message("网络错误，请稍后重试", { type: "error" });
    } finally {
      loading.value = false;
    }
  }

  const resetForm = (formEl: any) => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  // 加载学院选项
  async function loadCollegeOptions() {
    try {
      const response = await getAllColleges();
      if (response.success && response.data) {
        collegeOptions.value = response.data;
      }
    } catch (error) {
      console.error("加载学院选项失败:", error);
    }
  }

  function openDialog(title = "新增", row?: MajorItem) {
    const formRef = ref();

    addDialog({
      title: `${title}专业`,
      props: {
        formInline: {
          id: row?.id ?? null,
          majorCode: row?.majorCode ?? "",
          majorName: row?.majorName ?? "",
          collegeCode: row?.collegeCode ?? "",
          duration: row?.duration ?? 4,
          description: row?.description ?? "",
          collegeOptions: collegeOptions.value
        }
      },
      width: "40%",
      draggable: true,
      fullscreenIcon: true,
      closeOnClickModal: false,
      contentRenderer: () => h(editForm, { ref: formRef }),
      beforeSure: (done, { options }) => {
        const FormRef = formRef.value?.getRef();
        const curData = options.props.formInline as MajorItem;

        function chores(successMessage: string) {
          message(successMessage, { type: "success" });
          done(); // 关闭弹框
          onSearch(); // 刷新表格数据
        }

        FormRef?.validate((valid: boolean) => {
          if (valid) {
            // 表单规则校验通过
            if (title === "新增") {
              saveMajor(curData).then((res: any) => {
                if (res.success) {
                  chores(res.message);
                } else {
                  message(res.message, { type: "error" });
                }
              }).catch(() => {
                message("网络错误，请稍后重试", { type: "error" });
              });
            } else {
              updateMajor(curData).then((res: any) => {
                if (res.success) {
                  chores(res.message);
                } else {
                  message(res.message, { type: "error" });
                }
              }).catch(() => {
                message("网络错误，请稍后重试", { type: "error" });
              });
            }
          }
        });
      }
    });
  }

  onMounted(() => {
    onSearch();
    loadCollegeOptions();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    openDialog,
    handleDelete,
    handleSizeChange,
    handleCurrentChange,
    handleSelectionChange
  };
}
