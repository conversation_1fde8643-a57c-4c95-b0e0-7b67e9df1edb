<template>
  <div class="main">
    <!-- 搜索表单 -->
    <el-form
      ref="formRef"
      :inline="true"
      :model="form"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
    >
      <!-- 返回按钮 -->
      <el-form-item>
        <el-button
          :icon="useRenderIcon(leftLine)"
          @click="$emit('back')"
        >
          返回班级列表
        </el-button>
      </el-form-item>

      <el-form-item label="学号：" prop="studentId">
        <el-input
          v-model="form.studentId"
          placeholder="请输入学号"
          clearable
          class="!w-[200px]"
        />
      </el-form-item>
      <el-form-item label="姓名：" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入姓名"
          clearable
          class="!w-[200px]"
        />
      </el-form-item>
      <el-form-item label="性别：" prop="gender">
        <el-select
          v-model="form.gender"
          placeholder="请选择性别"
          clearable
          class="!w-[180px]"
        >
          <el-option label="男" value="男" />
          <el-option label="女" value="女" />
        </el-select>
      </el-form-item>
      <el-form-item label="学籍状态：" prop="status">
        <el-select
          v-model="form.status"
          placeholder="请选择状态"
          clearable
          class="!w-[180px]"
        >
          <el-option label="在读" value="在读" />
          <el-option label="休学" value="休学" />
          <el-option label="退学" value="退学" />
          <el-option label="毕业" value="毕业" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon(Search)"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon(Refresh)" @click="resetForm(formRef)">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <PureTableBar :title="tableTitle" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon(AddFill)"
          @click="openDialog('新增')"
        >
          新增学生
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <!-- 数据表格 -->
        <pure-table
          ref="tableRef"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small'"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @selection-change="handleSelectionChange"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon(EditPen)"
              @click="openDialog('修改', row)"
            >
              修改
            </el-button>
            <el-popconfirm
              :title="`是否确认删除学生：${row.name}？`"
              @confirm="handleDelete(row)"
            >
              <template #reference>
                <el-button
                  class="reset-margin"
                  link
                  type="primary"
                  :size="size"
                  :icon="useRenderIcon(Delete)"
                >
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { useStudent } from "../utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

import Search from "~icons/ep/search";
import Refresh from "~icons/ep/refresh";
import AddFill from "~icons/ri/add-circle-line";
import EditPen from "~icons/ep/edit-pen";
import Delete from "~icons/ep/delete";
import leftLine from "~icons/ri/arrow-left-line";

// 定义 props
interface Props {
  selectedClass: any;
}

const props = defineProps<Props>();

// 定义事件
defineEmits<{
  back: [];
}>();

const formRef = ref();

const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  onSearch,
  resetForm,
  openDialog,
  handleDelete,
  handleSizeChange,
  handleCurrentChange,
  handleSelectionChange
} = useStudent();

// 计算表格标题
const tableTitle = computed(() => {
  if (props.selectedClass) {
    return `${props.selectedClass.className} - 学生列表`;
  }
  return "学生列表";
});

// 监听selectedClass变化，设置查询条件
watch(() => props.selectedClass, (newClass) => {
  if (newClass) {
    form.classCode = newClass.classCode;
    form.collegeCode = newClass.collegeCode;
    form.majorCode = newClass.majorCode;
    pagination.currentPage = 1;
    onSearch();
  }
}, { immediate: true });
</script>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
