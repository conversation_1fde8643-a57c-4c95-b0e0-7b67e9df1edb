<template>
  <div class="main">
    <!-- 班级列表视图 -->
    <ClassSelector
      v-if="currentView === 'classes'"
      title="班级列表"
      @select="handleViewStudents"
    >
      <template #operation="{ row, size }">
        <el-button
          class="reset-margin"
          link
          type="primary"
          :size="size"
          :icon="useRenderIcon(View)"
          @click="handleViewStudents(row)"
        >
          查看学生
        </el-button>
      </template>
    </ClassSelector>

    <!-- 学生列表视图 -->
    <StudentList
      v-else-if="currentView === 'students'"
      :selectedClass="selectedClass"
      @back="handleBackToClassList"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ClassSelector } from "@/components/ReClassSelector";
import StudentList from "./components/StudentList.vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

import View from "~icons/ep/view";

defineOptions({
  name: "Students"
});

// 当前视图状态：'classes' | 'students'
const currentView = ref<'classes' | 'students'>('classes');
const selectedClass = ref<any>(null);

/** 查看班级学生 */
function handleViewStudents(classRow: any) {
  selectedClass.value = classRow;
  currentView.value = 'students';
}

/** 返回班级列表 */
function handleBackToClassList() {
  currentView.value = 'classes';
  selectedClass.value = null;
}
</script>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>


