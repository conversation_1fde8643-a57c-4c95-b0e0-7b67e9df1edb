import { http } from "@/utils/http";

export interface StudentsVO {
  id?: number;
  studentId: string;
  studentName: string;
  gender: string;
  birthDate?: string;
  phone?: string;
  email?: string;
  address?: string;
  classCode: string;
  className?: string;
  majorCode?: string;
  majorName?: string;
  collegeCode?: string;
  collegeName?: string;
  enrollmentYear?: number;
  status?: string;
  createTime?: string;
  updateTime?: string;
}

export interface StudentsQueryDTO {
  studentName?: string;
  studentId?: string;
  classCode?: string;
  majorCode?: string;
  collegeCode?: string;
  gender?: string;
  status?: string;
  current?: number;
  size?: number;
}

export interface StudentsPageVO {
  records: StudentsVO[];
  total: number;
  current: number;
  size: number;
}

/** 获取学生分页列表 */
export const getStudentsPage = (data?: StudentsQueryDTO) => {
  return http.request<StudentsPageVO>("post", "/api/student/students/page", { data });
};

/** 根据班级代码获取学生列表 */
export const getStudentsByClassCode = (classCode: string) => {
  return http.request<StudentsVO[]>("get", `/api/student/students/by-class/${classCode}`);
};

/** 保存学生 */
export const saveStudents = (data: StudentsVO) => {
  return http.request("post", "/api/student/students", { data });
};

/** 更新学生 */
export const updateStudents = (data: StudentsVO) => {
  return http.request("put", "/api/student/students", { data });
};

/** 删除学生 */
export const deleteStudents = (id: number) => {
  return http.request("delete", `/api/student/students/${id}`);
};

/** 批量删除学生 */
export const batchDeleteStudents = (ids: number[]) => {
  return http.request("post", "/api/student/students/batch-delete", { data: ids });
};