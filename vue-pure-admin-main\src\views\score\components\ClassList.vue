<template>
  <div class="main-content">
    <PureTableBar :title="`${majorName} - 班级列表`" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-space>
          <el-select
            v-model="selectedGrade"
            placeholder="选择年级"
            clearable
            @change="onSearch"
            style="width: 120px"
          >
            <el-option
              v-for="grade in gradeOptions"
              :key="grade.value"
              :label="grade.label"
              :value="grade.value"
            />
          </el-select>
          <el-input
            v-model="searchText"
            placeholder="搜索班级名称"
            clearable
            @input="onSearch"
            style="width: 200px"
          >
            <template #prefix>
              <IconifyIconOffline icon="ep:search" />
            </template>
          </el-input>
          <el-button type="info" @click="handleBack">
            返回专业列表
          </el-button>
        </el-space>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small'"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              size="small"
              @click="handleSelect(row)"
            >
              查看成绩
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, watch, computed } from "vue";
import { message } from "@/utils/message";
import { getClassesPage } from "@/api/basic/classes";
import { getYearOptions } from "@/utils/yearOptions";
import { PureTableBar } from "@/components/RePureTableBar";
import { IconifyIconOffline } from "@/components/ReIcon";
import type { PaginationProps } from "@pureadmin/table";

defineOptions({
  name: "ClassList"
});

// Props
interface Props {
  majorCode: string;
  majorName: string;
}

const props = withDefaults(defineProps<Props>(), {
  majorCode: "",
  majorName: ""
});

// Emits
const emit = defineEmits<{
  selectClass: [classInfo: { classCode: string; className: string }];
  back: [];
}>();

// 响应式数据
const loading = ref(true);
const tableRef = ref();
const searchText = ref("");
const selectedGrade = ref("");

// 数据列表
const dataList = ref([]);

// 年级选项
const gradeOptions = computed(() => {
  return getYearOptions().map(year => ({
    label: `${year}级`,
    value: year.toString()
  }));
});

// 分页配置
const pagination = reactive<PaginationProps>({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true
});

// 表格列配置
const columns = ref([
  {
    label: "序号",
    type: "index",
    width: 70,
    align: "center",
    index: (index) => {
      return (pagination.currentPage - 1) * pagination.pageSize + index + 1;
    }
  },
  {
    label: "班级代码",
    prop: "classCode",
    width: 120,
    align: "center"
  },
  {
    label: "班级名称",
    prop: "className",
    minWidth: 250,
    align: "center"
  },

  {
    label: "年级",
    prop: "gradeYear",
    width: 120,
    align: "center",
    cellRenderer: ({ row }) => row.gradeYear ? `${row.gradeYear}级` : "-"
  },
  {
    label: "学生人数",
    prop: "studentCount",
    width: 120,
    align: "center",
    cellRenderer: ({ row }) => row.studentCount || 0
  },
  {
    label: "创建时间",
    prop: "createdAt",
    width: 180,
    align: "center"
  },
  {
    label: "操作",
    fixed: "right",
    width: 120,
    slot: "operation"
  }
]);

// 获取数据
const getData = async () => {
  if (!props.majorCode) return;

  loading.value = true;
  try {
    const result = await getClassesPage({
      current: pagination.currentPage,
      size: pagination.pageSize,
      majorCode: props.majorCode,
      className: searchText.value,
      gradeYear: selectedGrade.value ? parseInt(selectedGrade.value) : undefined
    });

    if (result.success && result.data) {
      // 处理不同的数据结构
      if (result.data.records) {
        dataList.value = result.data.records || [];
        pagination.total = result.data.total || 0;
      } else if (result.data.list) {
        dataList.value = result.data.list || [];
        pagination.total = result.data.total || 0;
      } else {
        dataList.value = [];
        pagination.total = 0;
      }
    } else {
      dataList.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    console.error("获取班级列表失败:", error);
    message("获取班级列表失败", { type: "error" });
  } finally {
    loading.value = false;
  }
};



// 搜索
const onSearch = () => {
  pagination.currentPage = 1;
  getData();
};

// 选择班级
const handleSelect = (row) => {
  emit("selectClass", {
    classCode: row.classCode,
    className: row.className
  });
};

// 返回
const handleBack = () => {
  emit("back");
};

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  getData();
};

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  getData();
};

// 监听专业代码变化
watch(() => props.majorCode, () => {
  if (props.majorCode) {
    getData();
  }
}, { immediate: true });

// 组件挂载
onMounted(() => {
  if (props.majorCode) {
    getData();
  }
});
</script>


