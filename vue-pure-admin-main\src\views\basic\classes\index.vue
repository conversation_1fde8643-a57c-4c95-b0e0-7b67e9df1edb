<script setup lang="ts">
import { useClasses } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { computed } from "vue";
import { getYearOptions } from "@/utils/yearOptions";

// 引入图标
import Delete from "~icons/ep/delete";
import EditPen from "~icons/ep/edit-pen";
import Search from "~icons/ep/search";
import Refresh from "~icons/ep/refresh";
import AddFill from "~icons/ri/add-circle-line";

defineOptions({
  name: "Classes"
});

const {
  queryForm,
  loading,
  columns,
  dataList,
  pagination,
  selectedNum,
  majorOptions,
  teacherOptions,
  collegeOptions,
  tableRef,
  onSearch,
  resetForm,
  onbatchDel,
  openDialog,
  handleDelete,
  onSizeChange,
  onCurrentChange,
  onSelectionCancel,
  handleSelectionChange
} = useClasses();

// 根据选择的学院筛选专业
const filteredMajorOptions = computed(() => {
  if (!queryForm.collegeCode) {
    return majorOptions.value;
  }
  return majorOptions.value.filter(
    major => major.collegeCode === queryForm.collegeCode
  );
});

// 学院变化时的处理
const handleCollegeChange = () => {
  // 清空专业选择
  queryForm.majorCode = "";
};


</script>

<template>
  <div class="main-content">
    <!-- 搜索区域 -->
    <el-form
      ref="formRef"
      :inline="true"
      :model="queryForm"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
    >
      <el-form-item label="所属学院：" prop="collegeCode">
        <el-select
          v-model="queryForm.collegeCode"
          placeholder="请选择学院"
          clearable
          class="!w-[150px]"
          @change="handleCollegeChange"
        >
          <el-option
            v-for="college in collegeOptions"
            :key="college.collegeCode"
            :label="college.collegeName"
            :value="college.collegeCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所属专业：" prop="majorCode">
        <el-select
          v-model="queryForm.majorCode"
          :placeholder="queryForm.collegeCode ? '请选择专业' : '请先选择学院'"
          :disabled="!queryForm.collegeCode"
          clearable
          class="!w-[180px]"
        >
          <el-option
            v-for="major in filteredMajorOptions"
            :key="major.majorCode"
            :label="major.majorName"
            :value="major.majorCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="班级代码：" prop="classCode">
        <el-input
          v-model="queryForm.classCode"
          placeholder="请输入班级代码"
          clearable
          class="!w-[180px]"
        />
      </el-form-item>
      <el-form-item label="班级名称：" prop="className">
        <el-input
          v-model="queryForm.className"
          placeholder="请输入班级名称"
          clearable
          class="!w-[180px]"
        />
      </el-form-item>
      <el-form-item label="入学年份：" prop="gradeYear">
        <el-select
          v-model="queryForm.gradeYear"
          placeholder="请选择年份"
          clearable
          class="!w-[120px]"
        >
          <el-option
            v-for="year in getYearOptions()"
            :key="year"
            :label="`${year}年`"
            :value="year"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon(Search)"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon(Refresh)" @click="resetForm">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格区域 -->
    <PureTableBar title="班级列表" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon(AddFill)"
          @click="openDialog()"
        >
          新增班级
        </el-button>
        <el-button
          type="danger"
          :icon="useRenderIcon(Delete)"
          :disabled="selectedNum === 0"
          @click="onbatchDel"
        >
          批量删除
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          border
          align-whole="center"
          showOverflowTooltip
          table-layout="auto"
          :loading="loading"
          :size="size"
          adaptive
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small' ? true : false"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @selection-change="handleSelectionChange"
          @page-size-change="onSizeChange"
          @page-current-change="onCurrentChange"
        >
          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon(EditPen)"
              @click="openDialog('修改', row)"
            >
              修改
            </el-button>
            <el-popconfirm
              :title="`是否确认删除班级${row.className}?`"
              @confirm="handleDelete(row)"
            >
              <template #reference>
                <el-button
                  class="reset-margin"
                  link
                  type="primary"
                  :size="size"
                  :icon="useRenderIcon(Delete)"
                >
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<style scoped lang="scss">
.main {
  margin: 0;
  padding: 0;
  height: 100%;
  background: var(--el-bg-color-page);
}

.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
