package com.example.service.system;

import com.example.common.PageResult;
import com.example.dto.system.UserQueryDTO;
import com.example.vo.system.UserVO;

import java.util.List;

/**
 * 用户服务接口
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
public interface UserService {

    /**
     * 获取用户列表（分页）
     *
     * @param query 查询条件
     * @return 分页用户列表
     */
    PageResult<UserVO> getUserList(UserQueryDTO query);

    /**
     * 获取所有用户列表
     *
     * @return 所有用户列表
     */
    List<UserVO> getAllUsers();

    /**
     * 根据ID获取用户详情
     *
     * @param id 用户ID
     * @return 用户详情
     */
    UserVO getUserById(Integer id);

    /**
     * 新增用户
     *
     * @param userVO 用户信息
     */
    void saveUser(UserVO userVO);

    /**
     * 更新用户
     *
     * @param userVO 用户信息
     */
    void updateUser(UserVO userVO);

    /**
     * 删除用户
     *
     * @param userId 用户ID
     */
    void deleteUser(Integer userId);

    /**
     * 重置用户密码
     *
     * @param userId 用户ID
     * @param newPassword 新密码
     */
    void resetPassword(Integer userId, String newPassword);

    /**
     * 分配用户角色
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     */
    void assignRoles(Integer userId, List<Integer> roleIds);

    /**
     * 获取用户角色ID列表
     *
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<Integer> getUserRoleIds(Integer userId);

    /**
     * 切换用户状态
     *
     * @param userId 用户ID
     * @param status 状态
     */
    void toggleUserStatus(Integer userId, Integer status);

    /**
     * 根据用户名获取用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    UserVO getUserByUsername(String username);

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    boolean isUsernameExists(String username, Integer excludeId);

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    boolean isEmailExists(String email, Integer excludeId);

    /**
     * 检查手机号是否存在
     *
     * @param phone 手机号
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    boolean isPhoneExists(String phone, Integer excludeId);
}
