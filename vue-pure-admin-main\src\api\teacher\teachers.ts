import { http } from "@/utils/http";

export interface TeacherVO {
  id?: number;
  teacherId: string;
  teacherName: string;
  gender: string;
  birthDate?: string;
  phone?: string;
  email?: string;
  address?: string;
  collegeCode?: string;
  collegeName?: string;
  title?: string;
  degree?: string;
  status?: string;
  createTime?: string;
  updateTime?: string;
}

export interface TeacherQueryDTO {
  teacherName?: string;
  teacherId?: string;
  collegeCode?: string;
  title?: string;
  gender?: string;
  status?: string;
  current?: number;
  size?: number;
}

export interface TeacherPageVO {
  records: TeacherVO[];
  total: number;
  current: number;
  size: number;
}

/** 获取教师分页列表 */
export const getTeachersPage = (data?: TeacherQueryDTO) => {
  return http.request<TeacherPageVO>("post", "/api/teacher/list", { data });
};

/** 获取所有教师列表 */
export const getAllTeachers = () => {
  return http.request<TeacherVO[]>("get", "/api/teacher/all");
};

/** 保存教师 */
export const saveTeachers = (data: TeacherVO) => {
  return http.request("post", "/api/teacher/save", { data });
};

/** 更新教师 */
export const updateTeachers = (data: TeacherVO) => {
  return http.request("post", "/api/teacher/update", { data });
};

/** 删除教师 */
export const deleteTeachers = (id: number) => {
  return http.request("post", "/api/teacher/delete", { data: { id } });
};

/** 批量删除教师 */
export const batchDeleteTeachers = (ids: number[]) => {
  return http.request("post", "/api/teacher/batch-delete", { data: ids });
};