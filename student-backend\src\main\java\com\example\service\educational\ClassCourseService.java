package com.example.service.educational;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.dto.educational.ClassCourseQueryDTO;
import com.example.entity.educational.ClassCourse;
import com.example.vo.educational.ClassCourseVO;

import java.util.List;

/**
 * 班级课程分配服务接口
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
public interface ClassCourseService extends IService<ClassCourse> {

    /**
     * 分页查询班级课程分配列表
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    IPage<ClassCourseVO> getClassCoursePage(ClassCourseQueryDTO queryDTO);

    /**
     * 根据ID获取班级课程分配详情
     *
     * @param id 分配ID
     * @return 班级课程分配详情
     */
    ClassCourseVO getClassCourseById(Integer id);

    /**
     * 保存班级课程分配
     *
     * @param classCourseVO 班级课程分配信息
     */
    void saveClassCourse(ClassCourseVO classCourseVO);

    /**
     * 更新班级课程分配
     *
     * @param classCourseVO 班级课程分配信息
     */
    void updateClassCourse(ClassCourseVO classCourseVO);

    /**
     * 删除班级课程分配
     *
     * @param id 分配ID
     */
    void deleteClassCourse(Integer id);

    /**
     * 批量删除班级课程分配
     *
     * @param ids ID列表
     */
    void batchDeleteClassCourses(List<Integer> ids);

    /**
     * 根据班级代码获取课程分配列表
     *
     * @param classCode 班级代码
     * @return 课程分配列表
     */
    List<ClassCourseVO> getCoursesByClass(String classCode);

    /**
     * 根据班级代码和学期ID获取课程分配列表
     *
     * @param classCode  班级代码
     * @param semesterId 学期ID
     * @return 课程分配列表
     */
    List<ClassCourseVO> getCoursesByClassAndSemester(String classCode, Integer semesterId);

    /**
     * 根据课程代码获取分配的班级列表
     *
     * @param courseCode 课程代码
     * @return 班级列表
     */
    List<ClassCourseVO> getClassesByCourse(String courseCode);

    /**
     * 批量分配课程到班级
     *
     * @param classCode   班级代码
     * @param courseCodes 课程代码列表
     * @param semesterId  学期ID
     */
    void batchAssignCourses(String classCode, List<String> courseCodes, Integer semesterId);

    /**
     * 复制学期课程分配
     *
     * @param sourceClassCode    源班级代码
     * @param sourceSemesterId   源学期ID
     * @param targetClassCode    目标班级代码
     * @param targetSemesterId   目标学期ID
     */
    void copySemesterCourses(String sourceClassCode, Integer sourceSemesterId, String targetClassCode, Integer targetSemesterId);

    /**
     * 根据班级代码统计课程数量
     *
     * @param classCode 班级代码
     * @return 课程数量
     */
    int countCoursesByClass(String classCode);

    /**
     * 根据课程代码统计分配的班级数量
     *
     * @param courseCode 课程代码
     * @return 班级数量
     */
    int countClassesByCourse(String courseCode);

    /**
     * 根据班级代码和课程代码获取课程分配列表
     *
     * @param classCode  班级代码
     * @param courseCode 课程代码
     * @return 课程分配列表
     */
    List<ClassCourseVO> getClassCoursesByClassAndCourse(String classCode, String courseCode);
}
