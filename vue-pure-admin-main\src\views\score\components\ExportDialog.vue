<template>
  <el-dialog
    v-model="visible"
    title="导出设置"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form :model="form" label-width="120px">
      <el-form-item label="选择学期">
        <el-checkbox
          v-model="form.selectAll"
          @change="handleSelectAllSemesters"
          :indeterminate="form.indeterminate"
        >
          全选
        </el-checkbox>
        <div style="margin-top: 10px; max-height: 200px; overflow-y: auto;">
          <el-checkbox-group v-model="form.selectedSemesters">
            <div v-for="semester in semesters" :key="semester.id" style="margin-bottom: 8px;">
              <el-checkbox
                :value="semester.id"
                @change="handleSemesterChange"
              >
                {{ semester.semesterName }}
              </el-checkbox>
            </div>
          </el-checkbox-group>
        </div>
      </el-form-item>

      <el-form-item label="导出格式">
        <el-radio-group v-model="form.format">
          <el-radio value="xlsx">Excel格式 (.xlsx)</el-radio>
          <el-radio value="csv" disabled>CSV格式 (.csv)</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="说明">
        <el-alert
          title="导出说明"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <p v-if="form.selectedSemesters.length === 1">• 导出选中的单个学期成绩</p>
            <p v-else>• 将选中的多个学期成绩合并到一个Excel文件中</p>
            <p v-if="form.selectedSemesters.length > 1">• 每个学期的课程成绩将作为单独的列显示</p>
            <p>• 文件名将包含所有选中的学期信息</p>
          </template>
        </el-alert>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirmExport"
          :loading="loading"
          :disabled="!canExport"
        >
          确认导出
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, computed, watch } from "vue";
import { message } from "@/utils/message";
import { exportMultipleSemestersGrades } from "@/api/score/final-grades";
import { handleFileDownload, generateDefaultFileName } from "@/utils/fileUtils";

defineOptions({
  name: "ExportDialog"
});

// Props
interface Props {
  modelValue: boolean;
  classCode: string;
  className: string;
  semesters: any[];
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  classCode: "",
  className: "",
  semesters: () => []
});

// Emits
const emit = defineEmits<{
  "update:modelValue": [value: boolean];
  "export-success": [];
}>();

// 响应式数据
const loading = ref(false);

// 表单数据
const form = reactive({
  selectedSemesters: [] as number[],
  selectAll: false,
  indeterminate: false,
  format: 'xlsx'
});

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value)
});

const canExport = computed(() => {
  return form.selectedSemesters.length > 0;
});

// 方法
const handleClose = () => {
  visible.value = false;
  resetForm();
};

const resetForm = () => {
  form.selectedSemesters = [];
  form.selectAll = false;
  form.indeterminate = false;
  form.format = 'xlsx';
};

const handleSelectAllSemesters = (checked: boolean) => {
  if (checked) {
    form.selectedSemesters = props.semesters.map(s => s.id);
  } else {
    form.selectedSemesters = [];
  }
  form.indeterminate = false;
};

const handleSemesterChange = () => {
  const checkedCount = form.selectedSemesters.length;
  const totalCount = props.semesters.length;

  form.selectAll = checkedCount === totalCount;
  form.indeterminate = checkedCount > 0 && checkedCount < totalCount;
};

const handleConfirmExport = async () => {
  if (!props.classCode) {
    message("班级代码不能为空", { type: "error" });
    return;
  }

  if (form.selectedSemesters.length === 0) {
    message("请至少选择一个学期", { type: "warning" });
    return;
  }

  loading.value = true;

  try {
    // 统一使用多学期导出接口（后端已优化，支持单学期和多学期）
    await exportGrades();

    emit("export-success");
    handleClose();
    message("导出成功", { type: "success" });
  } catch (error) {
    console.error("导出Excel失败:", error);
    message("导出Excel失败: " + (error.message || "未知错误"), { type: "error" });
  } finally {
    loading.value = false;
  }
};

// 统一的导出方法
const exportGrades = async () => {
  // 使用优化后的多学期导出接口，支持单学期和多学期
  const response = await exportMultipleSemestersGrades(props.classCode, form.selectedSemesters);

  // 生成备用文件名
  const semesterText = form.selectedSemesters.length === 1 ? '单学期' : `${form.selectedSemesters.length}个学期合并`;
  const fallbackFileName = generateDefaultFileName(`${props.className}学业成绩_${semesterText}`, '.xlsx');

  // 处理文件下载
  handleFileDownload(response, fallbackFileName);
};

// 监听弹窗打开，重置表单
watch(visible, (newVal) => {
  if (newVal) {
    resetForm();
  }
});
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-alert__content) {
  padding-left: 8px;
}

:deep(.el-alert__content p) {
  margin: 4px 0;
  font-size: 13px;
  line-height: 1.4;
}
</style>