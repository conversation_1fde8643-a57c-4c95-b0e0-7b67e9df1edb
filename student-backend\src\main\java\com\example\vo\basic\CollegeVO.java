package com.example.vo.basic;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 学院VO
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Data
@Schema(description = "学院信息")
public class CollegeVO {

    @Schema(description = "学院ID")
    private Integer id;

    @Schema(description = "学院代码", required = true)
    @NotBlank(message = "学院代码不能为空")
    @Size(max = 20, message = "学院代码长度不能超过20个字符")
    private String collegeCode;

    @Schema(description = "学院名称", required = true)
    @NotBlank(message = "学院名称不能为空")
    @Size(max = 100, message = "学院名称长度不能超过100个字符")
    private String collegeName;

    @Schema(description = "学院描述")
    @Size(max = 500, message = "学院描述长度不能超过500个字符")
    private String description;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
}
