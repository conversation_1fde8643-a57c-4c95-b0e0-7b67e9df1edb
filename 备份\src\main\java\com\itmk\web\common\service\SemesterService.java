package com.itmk.web.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.itmk.web.common.entity.CommParm;
import com.itmk.web.common.entity.Semester;

import java.util.List;
import java.util.Map;

public interface SemesterService extends IService<Semester> {
    IPage<Semester> getList(CommParm commParm);

    /**
     * 获取所有学年列表
     * @return 学年列表
     */
    List<String> getAcademicYears();

    /**
     * 根据学年获取学期列表
     * @param academicYear 学年
     * @return 学期列表
     */
    List<Semester> getSemestersByAcademicYear(String academicYear);

    /**
     * 获取当前学年和学期信息
     * @return 包含当前学年、学期等信息的Map
     */
    Map<String, Object> getCurrentAcademicYearAndSemester();

    /**
     * 自动创建学期数据（带缓存机制）
     * 根据当前时间自动创建下一学年第一学期或者本学年第二学期
     * 5分钟内重复调用会被跳过
     */
    void autoCreateSemesters();

    /**
     * 强制执行学期创建（忽略缓存机制）
     * 供定时任务使用，确保定时任务能够正常执行
     */
    void forceAutoCreateSemesters();
}