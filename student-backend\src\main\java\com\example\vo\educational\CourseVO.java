package com.example.vo.educational;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 课程VO
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@Schema(description = "课程信息")
public class CourseVO {

    @Schema(description = "课程ID")
    private Integer id;

    @Schema(description = "课程代码", required = true)
    @NotBlank(message = "课程代码不能为空")
    @Size(max = 20, message = "课程代码长度不能超过20个字符")
    private String courseCode;

    @Schema(description = "课程名称", required = true)
    @NotBlank(message = "课程名称不能为空")
    @Size(max = 100, message = "课程名称长度不能超过100个字符")
    private String courseName;

    @Schema(description = "学分", required = true)
    @NotNull(message = "学分不能为空")
    @DecimalMin(value = "0.5", message = "学分不能小于0.5")
    @DecimalMax(value = "10.0", message = "学分不能大于10.0")
    private BigDecimal credits;

    @Schema(description = "课程类型", required = true)
    @NotBlank(message = "课程类型不能为空")
    @Pattern(regexp = "^(必修|选修|实践|通识)$", message = "课程类型只能是：必修、选修、实践、通识")
    private String courseType;

    @Schema(description = "所属学院代码")
    @Size(max = 20, message = "学院代码长度不能超过20个字符")
    private String collegeCode;

    @Schema(description = "所属学院名称")
    private String collegeName;

    @Schema(description = "所属专业代码")
    @Size(max = 20, message = "专业代码长度不能超过20个字符")
    private String majorCode;

    @Schema(description = "所属专业名称")
    private String majorName;

    @Schema(description = "课程描述")
    @Size(max = 1000, message = "课程描述长度不能超过1000个字符")
    private String description;

    @Schema(description = "学期ID")
    private Integer semesterId;

    @Schema(description = "学期名称")
    private String semesterName;

    @Schema(description = "学期号")
    private Integer semesterNumber;

    @Schema(description = "学年")
    private String academicYear;

    @Schema(description = "排序顺序")
    private Integer sortOrder;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
}
