package com.example.vo.basic;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 学年学期VO
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Data
@Schema(description = "学年学期信息")
public class SemesterVO {

    @Schema(description = "学期ID")
    private Integer id;

    @Schema(description = "学年", required = true, example = "2023-2024")
    @NotBlank(message = "学年不能为空")
    @Size(max = 20, message = "学年长度不能超过20个字符")
    private String academicYear;

    @Schema(description = "学期号", required = true, example = "1")
    @NotNull(message = "学期号不能为空")
    @Min(value = 1, message = "学期号不能小于1")
    @Max(value = 2, message = "学期号不能大于2")
    private Integer semesterNumber;

    @Schema(description = "学期名称", required = true, example = "2023-2024学年第一学期")
    @NotBlank(message = "学期名称不能为空")
    @Size(max = 50, message = "学期名称长度不能超过50个字符")
    private String semesterName;

    @Schema(description = "开始日期", required = true)
    @NotNull(message = "开始日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @Schema(description = "结束日期", required = true)
    @NotNull(message = "结束日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @Schema(description = "是否当前学期")
    private Boolean isCurrent;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
}
