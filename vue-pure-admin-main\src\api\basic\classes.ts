import { http } from "@/utils/http";

export type ClassesResult = {
  success: boolean;
  message?: string;
  data?: {
    /** 当前页码 */
    current?: number;
    /** 每页条数 */
    size?: number;
    /** 总条数 */
    total?: number;
    /** 数据 */
    records?: Array<any>;
  };
};

export type ApiResult<T = any> = {
  success: boolean;
  message?: string;
  data?: T;
};

export type ClassesVO = {
  /** 班级ID */
  id?: number;
  /** 班级代码 */
  classCode: string;
  /** 班级名称 */
  className: string;
  /** 所属专业代码 */
  majorCode: string;
  /** 入学年份 */
  gradeYear: number;
  /** 学生人数 */
  studentCount?: number;
  /** 班主任工号 */
  headTeacherCode?: string;
  /** 创建时间 */
  createdAt?: string;
  /** 更新时间 */
  updatedAt?: string;
  /** 专业名称 */
  majorName?: string;
  /** 学院代码 */
  collegeCode?: string;
  /** 班主任姓名 */
  headTeacherName?: string;
  /** 学院名称 */
  collegeName?: string;
};

export type ClassesQueryDTO = {
  /** 班级代码 */
  classCode?: string;
  /** 班级名称 */
  className?: string;
  /** 所属专业代码 */
  majorCode?: string;
  /** 入学年份 */
  gradeYear?: number;
  /** 班主任工号 */
  headTeacherCode?: string;
  /** 学院代码 */
  collegeCode?: string;
  /** 当前页码 */
  current?: number;
  /** 每页大小 */
  size?: number;
};

/** 分页查询班级列表 */
export const getClassesPage = (data?: ClassesQueryDTO) => {
  return http.request<ClassesResult>("post", "/api/basic/classes/page", { data });
};

/** 根据ID查询班级详情 */
export const getClassesById = (id: number) => {
  return http.request<ApiResult<ClassesVO>>("get", `/api/basic/classes/${id}`);
};

/** 查询所有班级列表 */
export const getAllClasses = () => {
  return http.request<ApiResult<Array<ClassesVO>>>("get", "/api/basic/classes/all");
};

/** 根据专业代码查询班级列表 */
export const getClassesByMajorCode = (majorCode: string) => {
  return http.request<ApiResult<Array<ClassesVO>>>("get", `/api/basic/classes/by-major/${majorCode}`);
};

/** 根据班主任工号查询班级列表 */
export const getClassesByHeadTeacher = (headTeacherCode: string) => {
  return http.request<ApiResult<Array<ClassesVO>>>("get", `/api/basic/classes/by-teacher/${headTeacherCode}`);
};

/** 新增班级 */
export const saveClasses = (data: ClassesVO) => {
  return http.request<ApiResult<void>>("post", "/api/basic/classes", { data });
};

/** 更新班级 */
export const updateClasses = (data: ClassesVO) => {
  return http.request<ApiResult<void>>("put", "/api/basic/classes", { data });
};

/** 删除班级 */
export const deleteClasses = (id: number) => {
  return http.request<ApiResult<void>>("delete", `/api/basic/classes/${id}`);
};

/** 批量删除班级 */
export const batchDeleteClasses = (ids: Array<number>) => {
  return http.request<ApiResult<void>>("post", "/api/basic/classes/batch-delete", { data: ids });
};

/** 获取班级在指定学期的课程列表 */
export const getCoursesByClassAndSemester = (classCode: string, semesterId: number) => {
  return http.request<ApiResult<Array<any>>>("get", `/api/basic/classes/${classCode}/courses/${semesterId}`);
};



/** 更新班级学生人数 */
export const updateStudentCount = (classId: number, studentCount: number) => {
  return http.request<ApiResult<void>>("post", "/api/basic/classes/student-count", {
    data: { classId, studentCount }
  });
};
