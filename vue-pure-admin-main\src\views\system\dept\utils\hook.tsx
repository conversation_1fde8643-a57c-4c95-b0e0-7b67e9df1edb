import dayjs from "dayjs";
import editForm from "../form.vue";
import { handleTree } from "@/utils/tree";
import { getDeptList, addDept, updateDept, deleteDept, getAllDepts, toggleDeptStatus } from "@/api/system";
import { usePublicHooks } from "../../hooks";
import { addDialog } from "@/components/ReDialog";
import { reactive, ref, onMounted, h } from "vue";
import type { FormItemProps } from "../utils/types";
import { cloneDeep, isAllEmpty, deviceDetection } from "@pureadmin/utils";
import { ElMessageBox } from "element-plus";

export function useDept() {
  const form = reactive({
    deptName: "",
    status: null,
    leader: ""
  });

  const formRef = ref();
  const dataList = ref([]);
  const loading = ref(true);
  const switchLoadMap = ref({});
  const { tagStyle, switchStyle } = usePublicHooks();

  const columns: TableColumnList = [
    {
      label: "部门名称",
      prop: "deptName",
      width: 180,
      align: "left"
    },
    {
      label: "部门编码",
      prop: "deptCode",
      minWidth: 120
    },
    {
      label: "负责人",
      prop: "leader",
      minWidth: 100
    },
    {
      label: "联系电话",
      prop: "phone",
      minWidth: 120
    },
    {
      label: "邮箱",
      prop: "email",
      minWidth: 160
    },
    {
      label: "排序",
      prop: "sort",
      minWidth: 70
    },
    {
      label: "状态",
      cellRenderer: scope => (
        <el-switch
          size={scope.props.size === "small" ? "small" : "default"}
          loading={switchLoadMap.value[scope.index]?.loading}
          v-model={scope.row.status}
          active-value={1}
          inactive-value={0}
          active-text="已启用"
          inactive-text="已停用"
          inline-prompt
          style={switchStyle.value}
          onChange={() => onChange(scope as any)}
        />
      ),
      minWidth: 90
    },
    {
      label: "创建时间",
      minWidth: 180,
      prop: "createTime",
      formatter: ({ createTime }) =>
        dayjs(createTime).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "备注",
      prop: "remark",
      minWidth: 200
    },
    {
      label: "操作",
      fixed: "right",
      width: 210,
      slot: "operation"
    }
  ];

  function handleSelectionChange(val: any) {
    // 处理选择变化
  }

  function resetForm(formEl: any) {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    const { data } = await getDeptList(); // 这里是返回一维数组结构，前端自行处理成树结构，返回格式要求：唯一id加父节点parentId，parentId取父节点id
    let newData = data;
    if (!isAllEmpty(form.deptName)) {
      // 前端搜索部门名称
      newData = newData.filter(item => item.deptName.includes(form.deptName));
    }
    if (!isAllEmpty(form.status)) {
      // 前端搜索状态
      newData = newData.filter(item => item.status === form.status);
    }
    dataList.value = handleTree(newData); // 处理成树结构
    setTimeout(() => {
      loading.value = false;
    }, 500);
  }

  function onChange({ row, index }) {
    ElMessageBox.confirm(
      `确认要<strong>${
        row.status === 0 ? "停用" : "启用"
      }</strong><strong style='color:var(--el-color-primary)'>${
        row.deptName
      }</strong>吗?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    )
      .then(async () => {
        switchLoadMap.value[index] = Object.assign(
          {},
          switchLoadMap.value[index],
          {
            loading: true
          }
        );
        try {
          await toggleDeptStatus({ deptId: row.id, status: row.status });
          setTimeout(() => {
            switchLoadMap.value[index] = Object.assign(
              {},
              switchLoadMap.value[index],
              {
                loading: false
              }
            );
            onSearch(); // 刷新数据以显示联动效果
          }, 300);
        } catch (error) {
          // 如果失败，恢复原状态
          row.status === 0 ? (row.status = 1) : (row.status = 0);
          switchLoadMap.value[index] = Object.assign(
            {},
            switchLoadMap.value[index],
            {
              loading: false
            }
          );
        }
      })
      .catch(() => {
        row.status === 0 ? (row.status = 1) : (row.status = 0);
      });
  }

  function formatHigherDeptOptions(treeList: any) {
    // 根据返回数据的status字段值判断追加是否禁用disabled字段，返回处理后的树结构，用于上级部门级联选择器的展示（实际开发中也是如此，不可能前端需要的每个字段后端都会返回，这时需要前端自行根据后端返回的某些字段做逻辑处理）
    if (!treeList || !treeList.length) return;
    const newTreeList = [];
    for (let i = 0; i < treeList.length; i++) {
      treeList[i].disabled = treeList[i].status === 0 ? true : false;
      formatHigherDeptOptions(treeList[i].children);
      newTreeList.push(treeList[i]);
    }
    return newTreeList;
  }

  async function openDialog(title = "新增", row?: FormItemProps) {
    try {
      // 获取所有部门数据用于构建上级部门选项
      const { data: allDepts } = await getAllDepts();
      const higherDeptOptions = formatHigherDeptOptions(handleTree(allDepts));

      addDialog({
        title: `${title}部门`,
        props: {
          formInline: {
            higherDeptOptions,
            id: row?.id ?? null,
            parentId: row?.parentId ?? 0,
            deptName: row?.deptName ?? "",
            deptCode: row?.deptCode ?? "",
            leader: row?.leader ?? "",
            phone: row?.phone ?? "",
            email: row?.email ?? "",
            sort: row?.sort ?? 0,
            remark: row?.remark ?? ""
          }
        },
      width: "40%",
      draggable: true,
      fullscreen: deviceDetection(),
      fullscreenIcon: true,
      closeOnClickModal: false,
      contentRenderer: () => h(editForm, { ref: formRef, formInline: null }),
      beforeSure: (done, { options }) => {
        const FormRef = formRef.value.getRef();
        const curData = options.props.formInline as FormItemProps;
        function chores() {
          // 后端已通过HTTP拦截器自动显示成功消息，前端不需要重复显示
          done(); // 关闭弹框
          onSearch(); // 刷新表格数据
        }
        FormRef.validate(async (valid: boolean) => {
          if (valid) {
            try {
              // 表单规则校验通过
              if (title === "新增") {
                // 调用新增接口
                await addDept(curData);
                chores();
              } else {
                // 调用修改接口
                await updateDept(curData);
                chores();
              }
            } catch (error) {
              // 后端已通过HTTP拦截器自动显示错误消息，前端不需要重复显示
            }
          }
        });
      }
    });
    } catch (error) {
      // 后端已通过HTTP拦截器自动显示错误消息，前端不需要重复显示
    }
  }

  async function handleDelete(row: any) {
    try {
      await deleteDept({ id: row.id });
      // 成功和错误消息已在HTTP拦截器中统一处理，使用后端返回的消息
      onSearch();
    } catch (error) {
      // 静默处理错误
    }
  }

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    /** 搜索 */
    onSearch,
    /** 重置 */
    resetForm,
    /** 新增、修改部门 */
    openDialog,
    /** 删除部门 */
    handleDelete,
    handleSelectionChange
  };
}
