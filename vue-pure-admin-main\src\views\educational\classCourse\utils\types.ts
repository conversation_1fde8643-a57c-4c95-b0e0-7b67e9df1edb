/** 班级课程分配表单数据类型 */
export interface ClassCourseFormInline {
  /** 主键ID */
  id?: number;
  /** 班级代码 */
  classCode: string;
  /** 课程代码 */
  courseCode: string;
  /** 学期ID */
  semesterId: number | null;

  /** 是否启用 */
  isActive: boolean;
  /** 排序号 */
  sortOrder: number;
  /** 备注 */
  remark: string;
}

/** 班级课程分配表单属性类型 */
export interface ClassCourseFormProps {
  formInline: ClassCourseFormInline;
}

/** 班级课程分配VO类型 */
export interface ClassCourseVO {
  id?: number;
  classCode: string;
  className?: string;
  majorCode?: string;
  majorName?: string;
  collegeCode?: string;
  collegeName?: string;
  courseCode: string;
  courseName?: string;
  credits?: number;
  courseType?: string;
  semesterId: number;
  semesterName?: string;
  academicYear?: string;

  isActive?: boolean;
  sortOrder?: number;
  remark?: string;
  createdBy?: string;
  createdAt?: string;
  updatedBy?: string;
  updatedAt?: string;
}

/** 查询表单类型 */
export interface QueryFormProps {
  classCode: string;
  courseCode: string;
  courseName: string;

  semesterId: number | null;
  collegeCode: string;
  majorCode: string;
}

/** 班级选项类型 */
export interface ClassOption {
  classCode: string;
  className: string;
}

/** 课程选项类型 */
export interface CourseOption {
  courseCode: string;
  courseName: string;
}

/** 学期选项类型 */
export interface SemesterOption {
  id: number;
  semesterName: string;
}