<template>
  <div class="main-content">
    <PureTableBar title="专业列表" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-space>
          <el-select
            v-model="selectedCollege"
            placeholder="选择学院"
            clearable
            @change="onSearch"
            style="width: 180px"
          >
            <el-option
              v-for="college in collegeOptions"
              :key="college.collegeCode"
              :label="college.collegeName"
              :value="college.collegeCode"
            />
          </el-select>
          <el-input
            v-model="searchText"
            placeholder="搜索专业名称"
            clearable
            @input="onSearch"
            style="width: 200px"
          >
            <template #prefix>
              <IconifyIconOffline icon="ep:search" />
            </template>
          </el-input>
        </el-space>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small'"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              size="small"
              @click="handleSelect(row)"
            >
              查看班级
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from "vue";
import { message } from "@/utils/message";
import { getMajorList } from "@/api/basic/major";
import { getAllColleges } from "@/api/basic/college";
import { PureTableBar } from "@/components/RePureTableBar";
import { IconifyIconOffline } from "@/components/ReIcon";
import type { PaginationProps } from "@pureadmin/table";

defineOptions({
  name: "MajorList"
});

// Emits
const emit = defineEmits<{
  selectMajor: [major: { majorCode: string; majorName: string }];
}>();

// 响应式数据
const loading = ref(true);
const tableRef = ref();
const searchText = ref("");
const selectedCollege = ref("");

// 数据列表
const dataList = ref([]);
const collegeOptions = ref([]);

// 分页配置
const pagination = reactive<PaginationProps>({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true
});

// 表格列配置
const columns = ref([
  {
    label: "序号",
    type: "index",
    width: 70,
    align: "center",
    index: (index) => {
      return (pagination.currentPage - 1) * pagination.pageSize + index + 1;
    }
  },
  {
    label: "专业代码",
    prop: "majorCode",
    width: 120,
    align: "center"
  },
  {
    label: "专业名称",
    prop: "majorName",
    minWidth: 200,
    align: "center"
  },
  {
    label: "学院名称",
    prop: "collegeName",
    minWidth: 150,
    align: "center"
  },
  {
    label: "创建时间",
    prop: "createdAt",
    width: 180,
    align: "center"
  },
  {
    label: "操作",
    fixed: "right",
    width: 120,
    slot: "operation"
  }
]);

// 获取数据
const getData = async () => {
  loading.value = true;
  try {
    const response = await getMajorList({
      current: pagination.currentPage,
      size: pagination.pageSize,
      majorName: searchText.value,
      collegeCode: selectedCollege.value
    });

    // 处理后端返回的数据结构
    if (response && response.data) {
      if (response.data.list) {
        dataList.value = response.data.list || [];
        pagination.total = response.data.total || 0;
      } else if (response.data.records) {
        dataList.value = response.data.records || [];
        pagination.total = response.data.total || 0;
      } else {
        dataList.value = [];
        pagination.total = 0;
      }
    } else if (response && response.list) {
      // 直接返回list的情况
      dataList.value = response.list || [];
      pagination.total = response.total || 0;
    } else {
      dataList.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    console.error("获取专业列表失败:", error);
    message("获取专业列表失败", { type: "error" });
    dataList.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 获取学院列表
const getColleges = async () => {
  try {
    const response = await getAllColleges();
    console.log("学院列表API响应:", response);

    // 处理后端返回的数据结构
    if (response && response.data) {
      collegeOptions.value = response.data || [];
    } else if (Array.isArray(response)) {
      collegeOptions.value = response;
    } else {
      collegeOptions.value = [];
    }

    console.log("处理后的学院数据:", collegeOptions.value);
  } catch (error) {
    console.error("获取学院列表失败:", error);
    message("获取学院列表失败", { type: "error" });
    collegeOptions.value = [];
  }
};

// 搜索
const onSearch = () => {
  pagination.currentPage = 1;
  getData();
};

// 选择专业
const handleSelect = (row) => {
  emit("selectMajor", {
    majorCode: row.majorCode,
    majorName: row.majorName
  });
};

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  getData();
};

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  getData();
};

// 组件挂载
onMounted(() => {
  getColleges();
  getData();
});
</script>

