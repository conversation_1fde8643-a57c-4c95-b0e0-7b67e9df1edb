package com.example.service.educational.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.common.exception.BusinessException;
import com.example.dto.educational.CourseQueryDTO;
import com.example.entity.educational.Course;
import com.example.mapper.educational.CourseMapper;
import com.example.service.educational.CourseService;
import com.example.vo.educational.CourseVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 课程服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@Service("educationalCourseServiceImpl")
@RequiredArgsConstructor
public class CourseServiceImpl implements CourseService {

    private final CourseMapper courseMapper;

    @Override
    public IPage<CourseVO> getCoursePage(CourseQueryDTO queryDTO) {
        // 设置默认分页参数
        if (queryDTO.getCurrent() == null || queryDTO.getCurrent() <= 0) {
            queryDTO.setCurrent(1);
        }
        if (queryDTO.getSize() == null || queryDTO.getSize() <= 0) {
            queryDTO.setSize(10);
        }

        Page<CourseVO> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        return courseMapper.selectCoursePage(page, queryDTO);
    }

    @Override
    @Cacheable(value = "courseDetail", key = "#id")
    public CourseVO getCourseById(Integer id) {
        if (id == null) {
            throw new BusinessException("课程ID不能为空");
        }
        return courseMapper.selectCourseById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"courseList", "coursesByType", "coursesByCollege"}, allEntries = true, beforeInvocation = true)
    public void saveCourse(CourseVO courseVO) {
        if (courseVO == null) {
            throw new BusinessException("课程信息不能为空");
        }

        // 检查课程代码是否已存在
        if (checkCourseCodeExists(courseVO.getCourseCode(), null)) {
            throw new BusinessException("课程代码已存在：" + courseVO.getCourseCode());
        }

        Course course = new Course();
        BeanUtils.copyProperties(courseVO, course);

        int result = courseMapper.insert(course);
        if (result <= 0) {
            throw new BusinessException("课程创建失败");
        }

        log.info("课程创建成功，课程代码: {}", courseVO.getCourseCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"courseList", "coursesByType", "coursesByCollege", "courseDetail", "courseByCode"}, allEntries = true, beforeInvocation = true)
    public void updateCourse(CourseVO courseVO) {
        if (courseVO == null || courseVO.getId() == null) {
            throw new BusinessException("课程ID不能为空");
        }

        // 检查课程是否存在
        Course existingCourse = courseMapper.selectById(courseVO.getId());
        if (existingCourse == null) {
            throw new BusinessException("课程不存在");
        }

        // 检查课程代码是否已被其他课程使用
        if (checkCourseCodeExists(courseVO.getCourseCode(), courseVO.getId())) {
            throw new BusinessException("课程代码已存在：" + courseVO.getCourseCode());
        }

        Course course = new Course();
        BeanUtils.copyProperties(courseVO, course);

        int result = courseMapper.updateById(course);
        if (result <= 0) {
            throw new BusinessException("课程更新失败");
        }

        log.info("课程更新成功，课程ID: {}", courseVO.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"courseList", "coursesByType", "coursesByCollege", "courseDetail", "courseByCode"}, allEntries = true, beforeInvocation = true)
    public void deleteCourse(Integer id) {
        if (id == null) {
            throw new BusinessException("课程ID不能为空");
        }

        Course course = courseMapper.selectById(id);
        if (course == null) {
            throw new BusinessException("课程不存在");
        }

        int result = courseMapper.deleteById(id);
        if (result <= 0) {
            throw new BusinessException("课程删除失败");
        }

        log.info("课程删除成功，课程ID: {}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"courseList", "coursesByType", "coursesByCollege", "courseDetail", "courseByCode"}, allEntries = true)
    public void batchDeleteCourses(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException("课程ID列表不能为空");
        }

        int result = courseMapper.batchDeleteCourses(ids);
        if (result <= 0) {
            throw new BusinessException("批量删除课程失败");
        }

        log.info("批量删除课程成功，删除数量: {}", result);
    }

    @Override
    @Cacheable(value = "courseByCode", key = "#courseCode")
    public CourseVO getCourseByCode(String courseCode) {
        if (courseCode == null || courseCode.trim().isEmpty()) {
            throw new BusinessException("课程代码不能为空");
        }
        return courseMapper.selectCourseByCode(courseCode);
    }

    @Override
    @Cacheable(value = "coursesByCollege", key = "#collegeCode")
    public List<CourseVO> getCoursesByCollege(String collegeCode) {
        if (collegeCode == null || collegeCode.trim().isEmpty()) {
            throw new BusinessException("学院代码不能为空");
        }
        return courseMapper.selectCoursesByCollege(collegeCode);
    }



    @Override
    @Cacheable(value = "coursesByType", key = "#courseType")
    public List<CourseVO> getCoursesByType(String courseType) {
        if (courseType == null || courseType.trim().isEmpty()) {
            throw new BusinessException("课程类型不能为空");
        }
        return courseMapper.selectCoursesByType(courseType);
    }

    @Override
    @Cacheable(value = "courseList", key = "'all'")
    public List<CourseVO> getAllCourses() {
        return courseMapper.selectAllCourses();
    }

    @Override
    public boolean checkCourseCodeExists(String courseCode, Integer excludeId) {
        if (courseCode == null || courseCode.trim().isEmpty()) {
            return false;
        }
        return courseMapper.checkCourseCodeExists(courseCode, excludeId) > 0;
    }

    @Override
    public int countCoursesByCollege(String collegeCode) {
        if (collegeCode == null || collegeCode.trim().isEmpty()) {
            return 0;
        }
        return courseMapper.countCoursesByCollege(collegeCode);
    }


}
