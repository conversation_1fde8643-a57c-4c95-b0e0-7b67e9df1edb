<template>
  <div class="main-content">
    <PureTableBar :title="`${majorName} - ${className}`" :columns="tableColumns" @refresh="onSearch">
      <template #buttons>
        <el-space>
          <el-select
            v-model="form.semesterId"
            placeholder="请选择学期"
            clearable
            @change="onSearch"
            style="width: 180px"
          >
            <el-option
              v-for="semester in semesters"
              :key="semester.id"
              :label="semester.semesterName"
              :value="semester.id"
            />
          </el-select>
          <el-input
            v-model="form.studentName"
            placeholder="学生姓名"
            clearable
            @input="onSearch"
            style="width: 150px"
          />
          <el-input
            v-model="form.studentId"
            placeholder="学号"
            clearable
            @input="onSearch"
            style="width: 150px"
          />
          <el-button type="primary" @click="onSearch">
            搜索
          </el-button>
          <el-button @click="onReset">
            重置
          </el-button>
          <el-button type="info" @click="handleBack">
            返回
          </el-button>
          <el-button type="primary" @click="showExportDialog">
            导出Excel
          </el-button>
        </el-space>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          :key="`table-${courses.length}-${gradeData.length}-${tableColumns.length}`"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="gradeData"
          :columns="tableColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small'"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
        </pure-table>
      </template>
    </PureTableBar>

    <!-- 导出弹窗 -->
    <ExportDialog
      v-model="exportDialogVisible"
      :class-code="props.classCode"
      :class-name="props.className"
      :semesters="semesters"
      @export-success="handleExportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, computed, watch } from "vue";
import { message } from "@/utils/message";
import { getHorizontalGrades } from "@/api/score/final-grades";
import { getAllSemesters } from "@/api/basic/semester";
import { PureTableBar } from "@/components/RePureTableBar";
import ExportDialog from "@/views/score/components/ExportDialog.vue";

import type { PaginationProps } from "@pureadmin/table";
import type { TableColumnList } from "@pureadmin/table";

defineOptions({
  name: "FinalGradesList"
});

// Props
interface Props {
  classCode: string;
  className: string;
  majorName: string;
}

const props = withDefaults(defineProps<Props>(), {
  classCode: "",
  className: "",
  majorName: ""
});

// Emits
const emit = defineEmits<{
  back: [];
}>();

// 响应式数据
const loading = ref(true);
const tableRef = ref();

// 搜索表单
const form = reactive({
  semesterId: null as number | null,
  studentName: "",
  studentId: ""
});

// 学期列表
const semesters = ref([]);

// 成绩数据
const grades = ref([]);
const courses = ref([]);

// 导出弹窗
const exportDialogVisible = ref(false);

// 分页配置
const pagination = reactive<PaginationProps>({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true
});

// 表格列配置
const tableColumns = ref<TableColumnList>([
  {
    label: "学号",
    prop: "studentId",
    width: 120,
    fixed: "left",
    align: "center"
  },
  {
    label: "姓名",
    prop: "studentName",
    width: 100,
    fixed: "left",
    align: "center"
  }
]);

// 生成表格列配置
const generateTableColumns = () => {
  // 基础列
  const baseColumns: TableColumnList = [
    {
      label: "序号",
      type: "index",
      width: 70,
      fixed: "left",
      align: "center",
      index: (index) => {
        return (pagination.currentPage - 1) * pagination.pageSize + index + 1;
      }
    },
    {
      label: "学号",
      prop: "studentId",
      width: 120,
      fixed: "left",
      align: "center"
    },
    {
      label: "姓名",
      prop: "studentName",
      width: 100,
      fixed: "left",
      align: "center"
    }
  ];

  // 课程成绩列 - 直接使用后端排序好的顺序
  const courseColumns: TableColumnList = courses.value.map(course => ({
    label: course.courseName,
    prop: `course_${course.courseCode}`,
    width: 150,
    align: "center",
    cellRenderer: ({ row }) => {
      const score = row[`course_${course.courseCode}`];
      if (score === '-' || score === null || score === undefined || score === '') {
        return '-';
      }
      return score.toString();
    }
  }));

  // 统计列
  const statColumns: TableColumnList = [
    {
      label: "平均分",
      prop: "avgScore",
      width: 80,
      fixed: "right",
      align: "center",
      cellRenderer: ({ row }) => {
        const avgScore = row.avgScore;
        if (avgScore === null || avgScore === undefined || avgScore === '') {
          return "-";
        }
        return Number(avgScore).toFixed(2);
      }
    },
    {
      label: "学业成绩",
      prop: "academicScore",
      width: 100,
      fixed: "right",
      align: "center",
      cellRenderer: ({ row }) => {
        const score = row.academicScore;
        if (score === null || score === undefined || score === '') {
          return "-";
        }
        return Number(score).toFixed(2);
      }
    },
    {
      label: "绩点",
      prop: "calculateGpa",
      width: 80,
      fixed: "right",
      align: "center",
      cellRenderer: ({ row }) => {
        const gpa = row.calculateGpa;
        if (gpa === null || gpa === undefined || gpa === '') {
          return "-";
        }
        return Number(gpa).toFixed(2);
      }
    }

  ];

  tableColumns.value = [...baseColumns, ...courseColumns, ...statColumns];
};

// 计算属性 - 表格数据
const gradeData = computed(() => {
  return grades.value.map(student => {
    const rowData = {
      studentId: student.studentId,
      studentName: student.studentName,
      avgScore: student.avgScore,
      totalCredits: student.totalCredits,
      academicScore: student.academicScore,
      calculateGpa: student.calculateGpa
    };

    // 添加课程成绩数据
    courses.value.forEach(course => {
      const courseCode = course.courseCode;
      const gradeInfo = student.grades?.[courseCode];

      if (gradeInfo && gradeInfo.finalScore !== null && gradeInfo.finalScore !== undefined) {
        let scoreDisplay = Number(gradeInfo.finalScore).toFixed(2);
        if (gradeInfo.isRetake) {
          scoreDisplay += '(重修)';
        }
        rowData[`course_${courseCode}`] = scoreDisplay;
      } else {
        rowData[`course_${courseCode}`] = '-';
      }
    });

    return rowData;
  });
});

// 获取学期列表
const getSemesters = async () => {
  try {
    const { data } = await getAllSemesters();
    semesters.value = data || [];
  } catch (error) {
    console.error("获取学期列表失败:", error);
    message("获取学期列表失败", { type: "error" });
  }
};

// 获取成绩数据
const getGrades = async () => {
  if (!props.classCode) {
    return;
  }

  loading.value = true;
  try {
    const { data } = await getHorizontalGrades(
      props.classCode,
      form.semesterId || undefined,
      pagination.currentPage,
      pagination.pageSize
    );

    if (data) {
      grades.value = data.students || [];
      courses.value = data.courses || [];
      pagination.total = data.total || 0;

      // 生成表格列
      generateTableColumns();
    }
  } catch (error) {
    console.error("获取成绩数据失败:", error);
    message("获取成绩数据失败", { type: "error" });
  } finally {
    loading.value = false;
  }
};

// 搜索
const onSearch = () => {
  pagination.currentPage = 1;
  getGrades();
};

// 重置
const onReset = () => {
  form.semesterId = null;
  form.studentName = "";
  form.studentId = "";
  pagination.currentPage = 1;
  getGrades();
};

// 返回
const handleBack = () => {
  emit('back');
};

// 导出成功回调
const handleExportSuccess = () => {
  message("导出成功", { type: "success" });
};

// 显示导出弹窗
const showExportDialog = () => {
  exportDialogVisible.value = true;
};



// 分页处理
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  getGrades();
};

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  getGrades();
};

// 监听courses变化，重新生成表格列
watch(courses, (newCourses) => {
  if (newCourses && newCourses.length > 0) {
    generateTableColumns();
  }
}, { immediate: true });

// 组件挂载
onMounted(() => {
  getSemesters();
  getGrades();
});
</script>

