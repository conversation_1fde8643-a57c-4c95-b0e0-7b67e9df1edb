package com.example.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.entity.system.SysDept;
import com.example.dto.system.DeptQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 部门Mapper
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Mapper
public interface SysDeptMapper extends BaseMapper<SysDept> {

    /**
     * 查询部门列表
     *
     * @param page 分页参数
     * @param query 查询条件
     * @return 部门列表
     */
    IPage<SysDept> selectDeptList(IPage<SysDept> page, @Param("query") DeptQueryDTO query);

    /**
     * 查询所有启用的部门
     *
     * @return 部门列表
     */
    List<SysDept> selectAllActive();

    /**
     * 根据父级ID查询子部门
     *
     * @param parentId 父级ID
     * @return 子部门列表
     */
    List<SysDept> selectByParentId(@Param("parentId") Integer parentId);

    /**
     * 检查部门名称是否存在
     *
     * @param deptName 部门名称
     * @param excludeId 排除的部门ID
     * @return 是否存在
     */
    Boolean existsByDeptName(@Param("deptName") String deptName, @Param("excludeId") Integer excludeId);

    /**
     * 检查部门编码是否存在
     *
     * @param deptCode 部门编码
     * @param excludeId 排除的部门ID
     * @return 是否存在
     */
    Boolean existsByDeptCode(@Param("deptCode") String deptCode, @Param("excludeId") Integer excludeId);

    /**
     * 根据部门编码查询部门
     *
     * @param deptCode 部门编码
     * @return 部门信息
     */
    SysDept selectByDeptCode(@Param("deptCode") String deptCode);

    /**
     * 查询部门详情
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    SysDept selectDeptDetail(@Param("deptId") Integer deptId);

    /**
     * 根据父级ID查询子部门数量
     *
     * @param parentId 父级ID
     * @return 子部门数量
     */
    Long countByParentId(@Param("parentId") Integer parentId);

    /**
     * 获取最大排序值
     *
     * @param parentId 父级ID
     * @return 最大排序值
     */
    Integer getMaxSort(@Param("parentId") Integer parentId);

    /**
     * 批量更新部门状态
     *
     * @param deptIds 部门ID列表
     * @param status 状态
     */
    void batchUpdateStatus(@Param("deptIds") List<Integer> deptIds, @Param("status") Integer status);

    /**
     * 递归查询所有子部门ID
     *
     * @param deptId 部门ID
     * @return 子部门ID列表
     */
    List<Integer> selectChildrenIds(@Param("deptId") Integer deptId);

    /**
     * 查询部门树结构
     *
     * @return 部门列表
     */
    List<SysDept> selectDeptTree();

    /**
     * 根据部门ID查询用户数量
     *
     * @param deptId 部门ID
     * @return 用户数量
     */
    Long countUsersByDeptId(@Param("deptId") Integer deptId);

    /**
     * 统计部门数量
     *
     * @return 部门数量
     */
    Long countDepts();

    /**
     * 查询顶级部门
     *
     * @return 顶级部门列表
     */
    List<SysDept> selectTopLevelDepts();
}
