<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.itmk.web.school_student.mapper.SchoolStudentMapper">
    <select id="getStuById" resultType="com.itmk.web.school_student.entity.SchoolStudent">
        select st.* ,sm.major_id,scc.college_id from school_student as st
                                                         left join school_class as sc on sc.class_id = st.class_id
                                                         left join school_major as sm on sm.major_id = sc.major_id
                                                         left join school_college as scc  on scc.college_id = sm.college_id
        where st.stu_id =#{stuId}
    </select>

    <select id="getList" resultType="com.itmk.web.school_student.entity.SchoolStudent">
        select st.* ,sm.major_name, scc.college_name, sc.class_name from school_student as st
        left join school_class as sc on sc.class_id = st.class_id
        left join school_major as sm on sm.major_id = sc.major_id
        left join school_college as scc  on scc.college_id = sm.college_id
        where 1=1
        <if test="parm.collegeName != null and parm.collegeName !=''">
            and scc.college_name like  concat('%',#{parm.collegeName},'%')
        </if>
        <if test="parm.majorName != null and parm.majorName !=''">
            and sm.major_name like  concat('%',#{parm.majorName},'%')
        </if>
        <if test="parm.className != null and parm.className !=''">
            and sc.class_name like  concat('%',#{parm.className},'%')
        </if>
        <if test="parm.stuName != null and parm.stuName !=''">
            and st.stu_name like  concat('%',#{parm.stuName},'%')
        </if>
        <if test="parm.classId != null">
            and st.class_id = #{parm.classId}
        </if>
    </select>
</mapper>