package com.example.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.entity.system.SysMenu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统菜单Mapper
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Mapper
public interface SysMenuMapper extends BaseMapper<SysMenu> {

    /**
     * 查询所有菜单
     *
     * @return 菜单列表
     */
    List<SysMenu> selectAllMenus();

    /**
     * 根据用户ID查询菜单
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<SysMenu> selectMenusByUserId(@Param("userId") Integer userId);

    /**
     * 根据角色ID查询菜单ID列表
     *
     * @param roleId 角色ID
     * @return 菜单ID列表
     */
    List<Integer> selectMenuIdsByRoleId(@Param("roleId") Integer roleId);

    /**
     * 查询菜单树（用于权限分配）
     *
     * @return 菜单列表
     */
    List<SysMenu> selectMenuTree();

    /**
     * 根据父级ID查询子菜单
     *
     * @param parentId 父级ID
     * @return 子菜单列表
     */
    List<SysMenu> selectByParentId(@Param("parentId") Integer parentId);

    /**
     * 查询菜单详情
     *
     * @param menuId 菜单ID
     * @return 菜单信息
     */
    SysMenu selectMenuDetail(@Param("menuId") Integer menuId);

    /**
     * 检查菜单名称是否存在
     *
     * @param title 菜单名称
     * @param excludeId 排除的菜单ID
     * @return 是否存在
     */
    Boolean existsByTitle(@Param("title") String title, @Param("excludeId") Integer excludeId);

    /**
     * 检查路由路径是否存在
     *
     * @param path 路由路径
     * @param excludeId 排除的菜单ID
     * @return 是否存在
     */
    Boolean existsByPath(@Param("path") String path, @Param("excludeId") Integer excludeId);

    /**
     * 检查路由名称是否存在
     *
     * @param name 路由名称
     * @param excludeId 排除的菜单ID
     * @return 是否存在
     */
    Boolean existsByName(@Param("name") String name, @Param("excludeId") Integer excludeId);

    /**
     * 根据父级ID查询子菜单数量
     *
     * @param parentId 父级ID
     * @return 子菜单数量
     */
    Long countByParentId(@Param("parentId") Integer parentId);

    /**
     * 获取最大排序值
     *
     * @param parentId 父级ID
     * @return 最大排序值
     */
    Integer getMaxRank(@Param("parentId") Integer parentId);

    /**
     * 批量更新菜单状态
     *
     * @param menuIds 菜单ID列表
     * @param status 状态
     */
    void batchUpdateStatus(@Param("menuIds") List<Integer> menuIds, @Param("status") Integer status);

    /**
     * 递归查询所有子菜单ID
     *
     * @param menuId 菜单ID
     * @return 子菜单ID列表
     */
    List<Integer> selectChildrenIds(@Param("menuId") Integer menuId);

    /**
     * 专门的菜单更新方法，确保parent_id字段能够被正确更新（包括null值）
     *
     * @param menu 菜单实体
     * @return 更新的记录数
     */
    int updateMenuWithParentId(SysMenu menu);
}
