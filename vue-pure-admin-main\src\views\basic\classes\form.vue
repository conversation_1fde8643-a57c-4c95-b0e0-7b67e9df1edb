<script setup lang="ts">
import { ref, computed } from "vue";
import ReCol from "@/components/ReCol";
import { formRules } from "./utils/rule";
import { FormProps } from "./utils/types";
import { getYearOptionsWithLabel, getCurrentYear } from "@/utils/yearOptions";

interface Props {
  formInline: FormProps["formInline"];
}

const props = withDefaults(defineProps<Props>(), {
  formInline: () => ({
    id: undefined,
    classCode: "",
    className: "",
    majorCode: "",
    gradeYear: getCurrentYear(),
    studentCount: 0,
    headTeacherCode: "",
    collegeCode: "",
    majorOptions: [],
    teacherOptions: [],
    collegeOptions: []
  })
});

const ruleFormRef = ref();
const newFormInline = ref(props.formInline);

// 过滤后的专业选项
const filteredMajorOptions = computed(() => {
  if (!newFormInline.value.collegeCode) {
    return newFormInline.value.majorOptions;
  }
  return newFormInline.value.majorOptions.filter(
    major => major.collegeCode === newFormInline.value.collegeCode
  );
});

// 过滤后的教师选项
const filteredTeacherOptions = computed(() => {
  if (!newFormInline.value.collegeCode) {
    return newFormInline.value.teacherOptions;
  }
  return newFormInline.value.teacherOptions.filter(
    teacher => teacher.collegeCode === newFormInline.value.collegeCode
  );
});

// 学院变化时的处理
const handleCollegeChange = () => {
  // 清空专业和教师选择
  newFormInline.value.majorCode = "";
  newFormInline.value.headTeacherCode = "";
};

function getRef() {
  return ruleFormRef.value;
}

const yearOptions = getYearOptionsWithLabel();

defineExpose({ getRef });
</script>

<template>
  <el-form
    ref="ruleFormRef"
    :model="newFormInline"
    :rules="formRules"
    label-width="100px"
  >
    <el-row :gutter="30">
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="所属学院" prop="collegeCode">
          <el-select
            v-model="newFormInline.collegeCode"
            placeholder="请选择所属学院"
            clearable
            filterable
            class="w-full"
            @change="handleCollegeChange"
          >
            <el-option
              v-for="college in newFormInline.collegeOptions"
              :key="college.collegeCode"
              :label="college.collegeName"
              :value="college.collegeCode"
            />
          </el-select>
        </el-form-item>
      </re-col>

      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="所属专业" prop="majorCode">
          <el-select
            v-model="newFormInline.majorCode"
            placeholder="请先选择学院"
            :disabled="!newFormInline.collegeCode"
            clearable
            filterable
            class="w-full"
          >
            <el-option
              v-for="major in filteredMajorOptions"
              :key="major.majorCode"
              :label="major.majorName"
              :value="major.majorCode"
            />
          </el-select>
        </el-form-item>
      </re-col>

      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="班级代码" prop="classCode">
          <el-input
            v-model="newFormInline.classCode"
            clearable
            placeholder="请输入班级代码"
          />
        </el-form-item>
      </re-col>

      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="班级名称" prop="className">
          <el-input
            v-model="newFormInline.className"
            clearable
            placeholder="请输入班级名称"
          />
        </el-form-item>
      </re-col>

      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="入学年份" prop="gradeYear">
          <el-select
            v-model="newFormInline.gradeYear"
            placeholder="请选择入学年份"
            clearable
            class="w-full"
          >
            <el-option
              v-for="year in yearOptions"
              :key="year.value"
              :label="year.label"
              :value="year.value"
            />
          </el-select>
        </el-form-item>
      </re-col>

      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="班主任" prop="headTeacherCode">
          <el-select
            v-model="newFormInline.headTeacherCode"
            placeholder="请先选择学院"
            :disabled="!newFormInline.collegeCode"
            clearable
            filterable
            class="w-full"
          >
            <el-option
              v-for="teacher in filteredTeacherOptions"
              :key="teacher.teacherCode"
              :label="teacher.name"
              :value="teacher.teacherCode"
            />
          </el-select>
        </el-form-item>
      </re-col>

      <re-col v-if="newFormInline.id" :value="12" :xs="24" :sm="24">
        <el-form-item label="学生人数">
          <el-input-number
            v-model="newFormInline.studentCount"
            :min="0"
            :max="200"
            class="w-full"
            disabled
          />
          <div class="text-xs text-gray-500 mt-1">
            学生人数由系统自动统计，不可手动修改
          </div>
        </el-form-item>
      </re-col>
    </el-row>
  </el-form>
</template>
