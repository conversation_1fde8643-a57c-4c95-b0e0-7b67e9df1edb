package com.itmk.web.school_student.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itmk.config.SecurityConfig.BusinessException;
import com.itmk.utils.PageUtils;
import com.itmk.web.school_student.entity.SchoolStudent;
import com.itmk.web.school_student.entity.StuParm;
import com.itmk.web.school_student.mapper.SchoolStudentMapper;
import com.itmk.web.school_student.service.SchoolStudentService;
import com.itmk.web.stu_role.entity.StuRole;
import com.itmk.web.stu_role.service.StuRoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 学生服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-19
 */
@Slf4j
@Service
public class SchoolStudentServiceImpl extends ServiceImpl<SchoolStudentMapper, SchoolStudent> implements SchoolStudentService {

    @Autowired
    private StuRoleService stuRoleService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addStu(SchoolStudent schoolStudent) {
        try {
            // 参数验证
            validateStudent(schoolStudent);

            // 检查学号是否重复
            if (isStudentIdExists(schoolStudent.getStudentId(), null)) {
                throw new BusinessException("学号已存在，请使用其他学号");
            }

            // 新增学生
            int insert = this.baseMapper.insert(schoolStudent);
            if (insert <= 0) {
                throw new BusinessException("新增学生失败");
            }

            // 设置学生的角色
            if (schoolStudent.getRoleId() != null) {
                StuRole stuRole = new StuRole();
                stuRole.setRoleId(schoolStudent.getRoleId());
                stuRole.setStuId(schoolStudent.getStuId());
                boolean saveRole = stuRoleService.save(stuRole);
                if (!saveRole) {
                    throw new BusinessException("设置学生角色失败");
                }
            }

            log.info("新增学生成功，学号: {}, 姓名: {}", schoolStudent.getStudentId(), schoolStudent.getStuName());
        } catch (Exception e) {
            log.error("新增学生失败，学号: {}, 错误: {}", schoolStudent.getStudentId(), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editStu(SchoolStudent schoolStudent) {
        try {
            // 参数验证
            validateStudent(schoolStudent);

            if (schoolStudent.getStuId() == null) {
                throw new BusinessException("学生ID不能为空");
            }

            // 检查学号是否重复（排除当前学生）
            if (isStudentIdExists(schoolStudent.getStudentId(), schoolStudent.getStuId())) {
                throw new BusinessException("学号已存在，请使用其他学号");
            }

            // 编辑学生
            int updateResult = this.baseMapper.updateById(schoolStudent);
            if (updateResult <= 0) {
                throw new BusinessException("更新学生信息失败");
            }

            // 设置学生角色：先删除原来的，再插入新的
            if (schoolStudent.getRoleId() != null) {
                // 先删除原有角色
                QueryWrapper<StuRole> query = new QueryWrapper<>();
                query.lambda().eq(StuRole::getStuId, schoolStudent.getStuId());
                stuRoleService.remove(query);

                // 插入新角色
                StuRole stuRole = new StuRole();
                stuRole.setRoleId(schoolStudent.getRoleId());
                stuRole.setStuId(schoolStudent.getStuId());
                boolean saveRole = stuRoleService.save(stuRole);
                if (!saveRole) {
                    throw new BusinessException("更新学生角色失败");
                }
            }

            log.info("更新学生成功，学生ID: {}, 学号: {}", schoolStudent.getStuId(), schoolStudent.getStudentId());
        } catch (Exception e) {
            log.error("更新学生失败，学生ID: {}, 错误: {}", schoolStudent.getStuId(), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public SchoolStudent getStuById(Long stuId) {
        if (stuId == null) {
            throw new BusinessException("学生ID不能为空");
        }

        SchoolStudent student = this.baseMapper.getStuById(stuId);
        if (student == null) {
            log.warn("未找到学生信息，学生ID: {}", stuId);
        }
        return student;
    }

    @Override
    public IPage<SchoolStudent> getList(StuParm parm) {
        try {
            // 使用优化的分页工具
            IPage<SchoolStudent> page = PageUtils.createPage(parm.getCurrentPage(), parm.getPageSize());

            // 执行查询
            IPage<SchoolStudent> result = this.baseMapper.getList(page, parm);

            log.debug("查询学生列表成功，当前页: {}, 页大小: {}, 总数: {}",
                    page.getCurrent(), page.getSize(), result.getTotal());

            return result;
        } catch (Exception e) {
            log.error("查询学生列表失败: {}", e.getMessage(), e);
            throw new BusinessException("查询学生列表失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteStu(Long stuId) {
        try {
            if (stuId == null) {
                throw new BusinessException("学生ID不能为空");
            }

            // 检查学生是否存在
            SchoolStudent existingStudent = this.baseMapper.selectById(stuId);
            if (existingStudent == null) {
                throw new BusinessException("学生不存在");
            }

            // 删除学生
            int deleteResult = this.baseMapper.deleteById(stuId);
            if (deleteResult <= 0) {
                throw new BusinessException("删除学生失败");
            }

            // 删除学生角色关联
            QueryWrapper<StuRole> query = new QueryWrapper<>();
            query.lambda().eq(StuRole::getStuId, stuId);
            stuRoleService.remove(query);

            log.info("删除学生成功，学生ID: {}, 学号: {}", stuId, existingStudent.getStudentId());
        } catch (Exception e) {
            log.error("删除学生失败，学生ID: {}, 错误: {}", stuId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 验证学生信息
     */
    private void validateStudent(SchoolStudent schoolStudent) {
        if (schoolStudent == null) {
            throw new BusinessException("学生信息不能为空");
        }

        if (schoolStudent.getStudentId() == null || schoolStudent.getStudentId().trim().isEmpty()) {
            throw new BusinessException("学号不能为空");
        }

        if (schoolStudent.getStuName() == null || schoolStudent.getStuName().trim().isEmpty()) {
            throw new BusinessException("学生姓名不能为空");
        }

        if (schoolStudent.getClassId() == null) {
            throw new BusinessException("班级不能为空");
        }
    }

    /**
     * 检查学号是否存在
     */
    private boolean isStudentIdExists(String studentId, Long excludeId) {
        QueryWrapper<SchoolStudent> query = new QueryWrapper<>();
        query.lambda().eq(SchoolStudent::getStudentId, studentId);

        if (excludeId != null) {
            query.lambda().ne(SchoolStudent::getStuId, excludeId);
        }

        return this.baseMapper.selectCount(query) > 0;
    }
}
