package com.itmk.web.sys_stuinfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itmk.utils.PageUtils;
import com.itmk.web.sys_stuinfo.entity.StuParm;
import com.itmk.web.sys_stuinfo.entity.SysStudent;
import com.itmk.web.sys_stuinfo.mapper.SysStudentMapper;
import com.itmk.web.sys_stuinfo.service.SysStudentService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class SysStudentServiceImpl extends ServiceImpl<SysStudentMapper, SysStudent> implements SysStudentService {

    @Override
    public IPage<SysStudent> list(StuParm parm) {
        // 构建查询条件
        QueryWrapper<SysStudent> query = new QueryWrapper<>();
        if (StringUtils.isNotBlank(parm.getStudentId()) || StringUtils.isNotBlank(parm.getName())) {
            query.and(wrapper -> wrapper
                    .like(StringUtils.isNotBlank(parm.getStudentId()), "student_id", parm.getStudentId())
                    .or()
                    .like(StringUtils.isNotBlank(parm.getName()), "name", parm.getName())
            );
        }
        // 创建分页对象
        IPage<SysStudent> page = PageUtils.createPage(parm.getCurrentPage(), parm.getPageSize());
        return this.baseMapper.selectPage(page, query);
    }

    @Override
    public SysStudent findById(Integer id) {
        if (id == null) {
            throw new IllegalArgumentException("ID不能为空");
        }
        return baseMapper.selectById(id);
    }

    @Override
    public boolean deleteById(Integer id) {
        if (id == null) {
            throw new IllegalArgumentException("ID不能为空");
        }
        return removeById(id);
    }

    @Override
    public Integer getMaxId() {
        return baseMapper.selectMaxId();
    }

    @Override
    public boolean isDuplicate(String studentId, String name, Integer excludeId) {
        QueryWrapper<SysStudent> query = new QueryWrapper<>();
        query.lambda()
                .and(wrapper -> wrapper
                        .eq(SysStudent::getStudentId, studentId)
                        .or()
                        .eq(SysStudent::getName, name)
                )
                .ne(excludeId != null, SysStudent::getId, excludeId); // 排除当前记录
        return this.count(query) > 0;
    }

    @Override
    public SysStudent getByStudentId(String studentId) {
        if (StringUtils.isBlank(studentId)) {
            return null;
        }
        QueryWrapper<SysStudent> query = new QueryWrapper<>();
        query.lambda().eq(SysStudent::getStudentId, studentId);
        return this.getOne(query);
    }
}