package com.example.service.educational.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.common.exception.BusinessException;
import com.example.dto.educational.ClassCourseQueryDTO;
import com.example.entity.educational.ClassCourse;
import com.example.mapper.educational.ClassCourseMapper;
import com.example.service.educational.ClassCourseService;
import com.example.vo.educational.ClassCourseVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 班级课程分配服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@Service
public class ClassCourseServiceImpl extends ServiceImpl<ClassCourseMapper, ClassCourse> implements ClassCourseService {

    @Override
    public IPage<ClassCourseVO> getClassCoursePage(ClassCourseQueryDTO queryDTO) {
        Page<ClassCourseVO> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        return baseMapper.selectClassCoursePage(page, queryDTO);
    }

    @Override
    @Cacheable(value = "classCourseDetail", key = "#id")
    public ClassCourseVO getClassCourseById(Integer id) {
        if (id == null) {
            throw new BusinessException("分配ID不能为空");
        }
        return baseMapper.selectClassCourseById(id);
    }

    @Override
    @CacheEvict(value = {"classCourseList", "classCoursesByClass", "classCourseDetail"}, allEntries = true, beforeInvocation = true)
    @Transactional(rollbackFor = Exception.class)
    public void saveClassCourse(ClassCourseVO classCourseVO) {
        // 验证必填字段
        validateClassCourse(classCourseVO);

        // 检查是否已存在相同的分配
        int existCount = baseMapper.checkClassCourseExists(
            classCourseVO.getClassCode(),
            classCourseVO.getCourseCode(),
            classCourseVO.getSemesterId(),
            null
        );
        if (existCount > 0) {
            throw new BusinessException("该班级在此学期已分配该课程");
        }

        // 转换并保存
        ClassCourse classCourse = new ClassCourse();
        BeanUtils.copyProperties(classCourseVO, classCourse);

        // 设置默认值
        if (classCourse.getIsActive() == null) {
            classCourse.setIsActive(true);
        }
        if (classCourse.getSortOrder() == null) {
            classCourse.setSortOrder(0);
        }

        boolean result = save(classCourse);
        if (!result) {
            throw new BusinessException("保存班级课程分配失败");
        }
    }

    @Override
    @CacheEvict(value = {"classCourseList", "classCoursesByClass", "classCourseDetail"}, allEntries = true, beforeInvocation = true)
    @Transactional(rollbackFor = Exception.class)
    public void updateClassCourse(ClassCourseVO classCourseVO) {
        if (classCourseVO.getId() == null) {
            throw new BusinessException("分配ID不能为空");
        }

        // 验证必填字段
        validateClassCourse(classCourseVO);

        // 检查是否已存在相同的分配（排除当前记录）
        int existCount = baseMapper.checkClassCourseExists(
            classCourseVO.getClassCode(),
            classCourseVO.getCourseCode(),
            classCourseVO.getSemesterId(),
            classCourseVO.getId()
        );
        if (existCount > 0) {
            throw new BusinessException("该班级在此学期已分配该课程");
        }

        // 转换并更新
        ClassCourse classCourse = new ClassCourse();
        BeanUtils.copyProperties(classCourseVO, classCourse);

        boolean result = updateById(classCourse);
        if (!result) {
            throw new BusinessException("更新班级课程分配失败");
        }
    }

    @Override
    @CacheEvict(value = {"classCourseList", "classCoursesByClass", "classCourseDetail"}, allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void deleteClassCourse(Integer id) {
        if (id == null) {
            throw new BusinessException("分配ID不能为空");
        }

        boolean result = removeById(id);
        if (!result) {
            throw new BusinessException("删除班级课程分配失败");
        }
    }

    @Override
    @CacheEvict(value = {"classCourseList", "classCoursesByClass", "classCourseDetail"}, allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteClassCourses(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException("删除ID列表不能为空");
        }

        int deleteCount = baseMapper.batchDeleteClassCourses(ids);
        if (deleteCount != ids.size()) {
            throw new BusinessException("批量删除班级课程分配失败");
        }
    }

    @Override
    @Cacheable(value = "classCoursesByClass", key = "#classCode")
    public List<ClassCourseVO> getCoursesByClass(String classCode) {
        if (classCode == null || classCode.trim().isEmpty()) {
            throw new BusinessException("班级代码不能为空");
        }
        return baseMapper.selectCoursesByClass(classCode);
    }

    @Override
    public List<ClassCourseVO> getCoursesByClassAndSemester(String classCode, Integer semesterId) {
        if (classCode == null || classCode.trim().isEmpty()) {
            throw new BusinessException("班级代码不能为空");
        }
        if (semesterId == null) {
            throw new BusinessException("学期ID不能为空");
        }
        return baseMapper.selectCoursesByClassAndSemester(classCode, semesterId);
    }

    @Override
    public List<ClassCourseVO> getClassesByCourse(String courseCode) {
        if (courseCode == null || courseCode.trim().isEmpty()) {
            throw new BusinessException("课程代码不能为空");
        }
        return baseMapper.selectClassesByCourse(courseCode);
    }

    @Override
    @CacheEvict(value = {"classCourseList", "classCoursesByClass"}, allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void batchAssignCourses(String classCode, List<String> courseCodes, Integer semesterId) {
        if (classCode == null || classCode.trim().isEmpty()) {
            throw new BusinessException("班级代码不能为空");
        }
        if (courseCodes == null || courseCodes.isEmpty()) {
            throw new BusinessException("课程代码列表不能为空");
        }
        if (semesterId == null) {
            throw new BusinessException("学期ID不能为空");
        }

        for (String courseCode : courseCodes) {
            // 检查是否已存在
            int existCount = baseMapper.checkClassCourseExists(classCode, courseCode, semesterId, null);
            if (existCount == 0) {
                ClassCourse classCourse = new ClassCourse();
                classCourse.setClassCode(classCode);
                classCourse.setCourseCode(courseCode);
                classCourse.setSemesterId(semesterId);

                classCourse.setIsActive(true);
                classCourse.setSortOrder(0);

                save(classCourse);
            }
        }
    }

    @Override
    @CacheEvict(value = {"classCourseList", "classCoursesByClass"}, allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void copySemesterCourses(String sourceClassCode, Integer sourceSemesterId, String targetClassCode, Integer targetSemesterId) {
        if (sourceClassCode == null || sourceClassCode.trim().isEmpty()) {
            throw new BusinessException("源班级代码不能为空");
        }
        if (targetClassCode == null || targetClassCode.trim().isEmpty()) {
            throw new BusinessException("目标班级代码不能为空");
        }
        if (sourceSemesterId == null || targetSemesterId == null) {
            throw new BusinessException("学期ID不能为空");
        }

        // 获取源班级学期的课程分配
        List<ClassCourseVO> sourceCourses = baseMapper.selectCoursesByClassAndSemester(sourceClassCode, sourceSemesterId);

        for (ClassCourseVO sourceCourse : sourceCourses) {
            // 检查目标班级是否已存在该课程
            int existCount = baseMapper.checkClassCourseExists(targetClassCode, sourceCourse.getCourseCode(), targetSemesterId, null);
            if (existCount == 0) {
                ClassCourse classCourse = new ClassCourse();
                classCourse.setClassCode(targetClassCode);
                classCourse.setCourseCode(sourceCourse.getCourseCode());
                classCourse.setSemesterId(targetSemesterId);

                classCourse.setIsActive(sourceCourse.getIsActive());
                classCourse.setSortOrder(sourceCourse.getSortOrder());
                classCourse.setRemark(sourceCourse.getRemark());

                save(classCourse);
            }
        }
    }

    @Override
    public int countCoursesByClass(String classCode) {
        if (classCode == null || classCode.trim().isEmpty()) {
            return 0;
        }
        return baseMapper.countCoursesByClass(classCode);
    }

    @Override
    public int countClassesByCourse(String courseCode) {
        if (courseCode == null || courseCode.trim().isEmpty()) {
            return 0;
        }
        return baseMapper.countClassesByCourse(courseCode);
    }

    @Override
    public List<ClassCourseVO> getClassCoursesByClassAndCourse(String classCode, String courseCode) {
        if (classCode == null || classCode.trim().isEmpty()) {
            throw new BusinessException("班级代码不能为空");
        }
        if (courseCode == null || courseCode.trim().isEmpty()) {
            throw new BusinessException("课程代码不能为空");
        }
        return baseMapper.selectClassCoursesByClassAndCourse(classCode, courseCode);
    }

    /**
     * 验证班级课程分配数据
     */
    private void validateClassCourse(ClassCourseVO classCourseVO) {
        if (classCourseVO.getClassCode() == null || classCourseVO.getClassCode().trim().isEmpty()) {
            throw new BusinessException("班级代码不能为空");
        }
        if (classCourseVO.getCourseCode() == null || classCourseVO.getCourseCode().trim().isEmpty()) {
            throw new BusinessException("课程代码不能为空");
        }
        if (classCourseVO.getSemesterId() == null) {
            throw new BusinessException("学期ID不能为空");
        }

    }
}
