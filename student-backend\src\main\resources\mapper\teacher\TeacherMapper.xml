<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.teacher.TeacherMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.entity.teacher.Teacher">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="teacher_code" property="teacherCode" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="gender" property="gender" jdbcType="VARCHAR"/>
        <result column="birth_date" property="birthDate" jdbcType="DATE"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="college_code" property="collegeCode" jdbcType="VARCHAR"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="hire_date" property="hireDate" jdbcType="DATE"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- VO结果映射 -->
    <resultMap id="TeacherVOResultMap" type="com.example.vo.teacher.TeacherVO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="teacher_code" property="teacherCode" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="gender" property="gender" jdbcType="VARCHAR"/>
        <result column="birth_date" property="birthDate" jdbcType="DATE"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="college_code" property="collegeCode" jdbcType="VARCHAR"/>
        <result column="college_name" property="collegeName" jdbcType="VARCHAR"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="hire_date" property="hireDate" jdbcType="DATE"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, teacher_code, name, gender, birth_date, phone, email, college_code, title, hire_date, status, created_at, updated_at
    </sql>

    <!-- VO字段（包含学院名称） -->
    <sql id="VO_Column_List">
        t.id, t.teacher_code, t.name, t.gender, t.birth_date, t.phone, t.email, 
        t.college_code, c.college_name, t.title, t.hire_date, t.status, t.created_at, t.updated_at
    </sql>

    <!-- 分页查询教师列表 -->
    <select id="selectTeacherPage" resultMap="TeacherVOResultMap">
        SELECT
        <include refid="VO_Column_List"/>
        FROM teachers t
        LEFT JOIN colleges c ON t.college_code = c.college_code
        <where>
            <if test="query.teacherCode != null and query.teacherCode != ''">
                AND t.teacher_code LIKE CONCAT('%', #{query.teacherCode}, '%')
            </if>
            <if test="query.name != null and query.name != ''">
                AND t.name LIKE CONCAT('%', #{query.name}, '%')
            </if>
            <if test="query.gender != null and query.gender != ''">
                AND t.gender = #{query.gender}
            </if>
            <if test="query.collegeCode != null and query.collegeCode != ''">
                AND t.college_code = #{query.collegeCode}
            </if>
            <if test="query.title != null and query.title != ''">
                AND t.title = #{query.title}
            </if>
            <if test="query.status != null and query.status != ''">
                AND t.status = #{query.status}
            </if>
        </where>
        ORDER BY t.created_at DESC
    </select>

    <!-- 查询所有教师 -->
    <select id="selectAllTeachers" resultMap="TeacherVOResultMap">
        SELECT
        <include refid="VO_Column_List"/>
        FROM teachers t
        LEFT JOIN colleges c ON t.college_code = c.college_code
        ORDER BY t.created_at DESC
    </select>

    <!-- 根据学院代码查询教师列表 -->
    <select id="selectByCollegeCode" resultMap="TeacherVOResultMap">
        SELECT
        <include refid="VO_Column_List"/>
        FROM teachers t
        LEFT JOIN colleges c ON t.college_code = c.college_code
        WHERE t.college_code = #{collegeCode}
        ORDER BY t.created_at DESC
    </select>

    <!-- 根据教师工号查询教师 -->
    <select id="selectByTeacherCode" resultMap="TeacherVOResultMap">
        SELECT
        <include refid="VO_Column_List"/>
        FROM teachers t
        LEFT JOIN colleges c ON t.college_code = c.college_code
        WHERE t.teacher_code = #{teacherCode}
    </select>

    <!-- 根据ID查询教师详情 -->
    <select id="selectTeacherById" resultMap="TeacherVOResultMap">
        SELECT
        <include refid="VO_Column_List"/>
        FROM teachers t
        LEFT JOIN colleges c ON t.college_code = c.college_code
        WHERE t.id = #{id}
    </select>

    <!-- 检查教师工号是否存在 -->
    <select id="checkTeacherCodeExists" resultType="int">
        SELECT COUNT(1)
        FROM teachers
        WHERE teacher_code = #{teacherCode}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

</mapper>
