package com.example.vo.monitor;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 日志响应VO
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
public class LogVO {

    /**
     * 日志ID
     */
    private Long id;

    /**
     * 日志类型：1登录日志、2操作日志、3系统日志
     */
    private Integer logType;

    /**
     * 操作用户
     */
    private String username;

    /**
     * 操作描述
     */
    private String operation;

    /**
     * 请求方法
     */
    private String method;

    /**
     * 请求参数
     */
    private String params;

    /**
     * 返回结果
     */
    private String result;

    /**
     * 执行时间（毫秒）
     */
    private Long executeTime;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 地理位置
     */
    private String location;

    /**
     * 浏览器
     */
    private String browser;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 状态：0失败、1成功
     */
    private Integer status;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
