import { ref, reactive } from "vue";
import { type PaginationProps } from "@pureadmin/table";

export function useTable() {
  const tableRef = ref();
  const loading = ref(false);
  const selectedNum = ref(0);

  // 分页配置
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  });

  // 查询表单
  const queryForm = reactive({
    classCode: "",
    courseCode: "",
    courseName: "",
    semesterId: null,
    collegeCode: "",
    majorCode: ""
  });

  // 分页相关
  function handleSizeChange(val: number) {
    pagination.pageSize = val;
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
  }

  // 选择变化
  function handleSelectionChange(val: any[]) {
    selectedNum.value = val.length;
  }

  // 重置查询表单
  function resetQueryForm() {
    Object.assign(queryForm, {
      classCode: "",
      courseCode: "",
      courseName: "",
      semesterId: null,
      collegeCode: "",
      majorCode: ""
    });
    pagination.currentPage = 1;
  }

  return {
    tableRef,
    loading,
    selectedNum,
    pagination,
    queryForm,
    handleSizeChange,
    handleCurrentChange,
    handleSelectionChange,
    resetQueryForm
  };
}
