<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.itmk.web.school_class.mapper.SchoolClassMapper">
    <select id="getList" resultType="com.itmk.web.school_class.entity.SchoolClass">
        select scc.college_name,sm.major_name,sc.* from school_class as sc
        left join school_major  as sm on sc.major_id = sm.major_id
        left join school_college as scc on scc.college_id = sm.college_id
        where 1=1
        <if test="parm.collegeName != null and parm.collegeName != ''">
            and scc.college_name like concat('%',#{parm.collegeName},'%')
        </if>
        <if test="parm.majorName != null and parm.majorName != ''">
            and sm.major_name like concat('%',#{parm.majorName},'%')
        </if>
        <if test="parm.className != null and parm.className != ''">
            and sc.class_name like concat('%',#{parm.className},'%')
        </if>
        order by sc.class_name asc
    </select>
</mapper>