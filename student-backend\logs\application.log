2025-08-01 11:14:53.627 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 11:14:53.671 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 20728 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 11:14:53.672 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 11:14:53.736 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01 11:14:53.737 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01 11:14:55.852 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 11:14:55.864 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:14:55.865 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:14:55.865 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 11:14:55.918 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:14:55.918 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2181 ms
2025-08-01 11:14:56.608 [restartedMain] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.entity.system.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 11:14:56.626 [restartedMain] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.entity.system.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 11:14:56.733 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-01 11:14:58.089 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 11:14:58.116 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: d79a8c4e-1be5-43ef-b84f-2af85ea3a7d9

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 11:14:58.121 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 11:14:58.576 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 11:14:58.613 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:14:58.624 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 11:14:58.633 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 5.811 seconds (process running for 6.718)
2025-08-01 11:14:59.080 [RMI TCP Connection(3)-*************] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 11:14:59.080 [RMI TCP Connection(3)-*************] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 11:14:59.082 [RMI TCP Connection(3)-*************] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-01 11:15:01.174 [http-nio-8080-exec-1] WARN  com.example.config.PerformanceConfig - 慢方法执行: AuthServiceImpl.login(..) 耗时: 457ms
2025-08-01 11:15:01.175 [http-nio-8080-exec-1] WARN  com.example.config.PerformanceConfig - 慢接口: AuthController.login(..) 耗时: 459ms
2025-08-01 11:15:01.234 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 11:15:01.234 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 11:15:01.235 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 11:15:01.292 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 11:15:07.746 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 11:15:07.746 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 11:15:07.747 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 11:15:07.749 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 11:15:32.197 [http-nio-8080-exec-2] INFO  c.example.service.score.impl.FinalGradeServiceImpl - 开始导出多学期期末成绩: classCode=2023054801, semesterIds=[11, 10, 4, 3, 2, 1]
2025-08-01 11:15:32.199 [http-nio-8080-exec-2] INFO  c.example.service.score.impl.FinalGradeServiceImpl - 开始优化导出成绩: classCode=2023054801, semesterIds=[11, 10, 4, 3, 2, 1]
2025-08-01 11:15:32.260 [http-nio-8080-exec-2] INFO  com.example.common.excel.ApachePoiExportService - 开始Apache POI流式导出，数据行数: 44, 表头数量: 47
2025-08-01 11:15:33.137 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第0列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.137 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第1列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.138 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第2列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.138 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第3列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.138 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第4列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.139 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第5列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.139 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第6列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.139 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第7列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.139 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第8列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.139 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第9列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.139 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第10列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.140 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第11列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.140 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第12列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.140 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第13列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.140 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第14列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.140 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第15列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.140 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第16列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.140 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第17列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.141 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第18列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.141 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第19列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.141 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第20列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.141 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第21列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.141 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第22列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.141 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第23列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.142 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第24列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.142 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第25列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.142 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第26列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.144 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第27列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.145 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第28列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.145 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第29列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.145 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第30列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.145 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第31列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.145 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第32列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.145 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第33列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.145 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第34列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.146 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第35列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.146 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第36列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.147 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第37列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.147 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第38列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.147 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第39列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.147 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第40列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.147 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第41列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.147 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第42列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.147 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第43列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.147 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第44列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.148 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第45列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.148 [http-nio-8080-exec-2] WARN  com.example.common.excel.ExcelUtils - 自动调整第46列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.339 [http-nio-8080-exec-2] INFO  com.example.common.excel.ApachePoiExportService - Apache POI导出成功，数据行数: 44, 文件大小: 14507 bytes
2025-08-01 11:15:33.343 [http-nio-8080-exec-2] INFO  c.example.service.score.impl.FinalGradeServiceImpl - 优化导出成功（Apache POI），学期数: 6, 学生数: 44, 文件大小: 14507 bytes
2025-08-01 11:15:33.463 [http-nio-8080-exec-10] INFO  c.example.service.score.impl.FinalGradeServiceImpl - 开始导出多学期期末成绩: classCode=2023054801, semesterIds=[11, 10, 4, 3, 2, 1]
2025-08-01 11:15:33.466 [http-nio-8080-exec-10] INFO  c.example.service.score.impl.FinalGradeServiceImpl - 开始优化导出成绩: classCode=2023054801, semesterIds=[11, 10, 4, 3, 2, 1]
2025-08-01 11:15:33.570 [http-nio-8080-exec-10] INFO  com.example.common.excel.ApachePoiExportService - 开始Apache POI流式导出，数据行数: 44, 表头数量: 47
2025-08-01 11:15:33.588 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第0列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.588 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第1列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.588 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第2列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.588 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第3列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.589 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第4列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.592 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第5列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.593 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第6列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.593 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第7列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.593 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第8列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.593 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第9列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.593 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第10列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.593 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第11列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.593 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第12列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.593 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第13列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.594 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第14列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.596 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第15列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.596 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第16列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.596 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第17列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.597 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第18列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.597 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第19列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.597 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第20列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.597 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第21列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.597 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第22列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.597 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第23列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.597 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第24列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.597 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第25列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.597 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第26列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.598 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第27列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.598 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第28列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.598 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第29列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.598 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第30列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.598 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第31列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.598 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第32列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.598 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第33列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.598 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第34列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.599 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第35列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.599 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第36列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.600 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第37列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.600 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第38列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.601 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第39列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.602 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第40列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.602 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第41列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.602 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第42列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.602 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第43列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.602 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第44列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.602 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第45列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.602 [http-nio-8080-exec-10] WARN  com.example.common.excel.ExcelUtils - 自动调整第46列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 11:15:33.678 [http-nio-8080-exec-10] INFO  com.example.common.excel.ApachePoiExportService - Apache POI导出成功，数据行数: 44, 文件大小: 14507 bytes
2025-08-01 11:15:33.685 [http-nio-8080-exec-10] INFO  c.example.service.score.impl.FinalGradeServiceImpl - 优化导出成功（Apache POI），学期数: 6, 学生数: 44, 文件大小: 14507 bytes
2025-08-01 11:17:58.191 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 11:17:58.192 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 11:17:58.192 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 11:17:58.192 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 11:17:59.004 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 11:17:59.005 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 11:17:59.005 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 11:17:59.005 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 11:18:11.675 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 11:18:11.675 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 11:18:11.675 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 11:18:11.676 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 11:18:12.604 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 11:18:12.604 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 11:18:12.605 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 11:18:12.605 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 11:18:27.500 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 11:18:27.501 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 11:18:27.501 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 11:18:27.501 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 11:18:28.141 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 11:18:28.141 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 11:18:28.141 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 11:18:28.141 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 11:18:43.930 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 11:18:43.931 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 11:18:43.931 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 11:18:43.931 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 11:18:45.347 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 11:18:45.347 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 11:18:45.347 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 11:18:45.347 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 11:19:07.655 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 11:19:07.656 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 11:19:07.656 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 11:19:07.656 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 11:19:08.322 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 11:19:08.322 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 11:19:08.323 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 11:19:08.323 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 11:19:20.734 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 11:19:20.734 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 11:19:20.734 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 11:19:20.734 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 11:19:21.585 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 11:19:21.585 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 11:19:21.585 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 11:19:21.586 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 11:19:58.175 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 11:19:58.175 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 11:19:58.176 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 11:19:58.176 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 11:19:59.337 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 11:19:59.337 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 11:19:59.337 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 11:19:59.338 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 11:20:35.491 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 11:20:35.491 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 11:20:35.491 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 11:20:35.491 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 11:20:37.534 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 11:20:37.534 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 11:20:37.534 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 11:20:37.534 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 11:22:07.198 [SpringApplicationShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 11:22:07.202 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 11:22:07.209 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-08-01 11:22:07.214 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-08-01 11:22:10.387 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 11:22:10.421 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 11:22:10.422 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 11:22:10.469 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01 11:22:10.469 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01 11:22:12.103 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 11:22:12.114 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:22:12.115 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:22:12.116 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 11:22:12.164 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:22:12.165 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1696 ms
2025-08-01 11:22:12.808 [restartedMain] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.entity.system.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 11:22:12.828 [restartedMain] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.entity.system.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 11:22:12.909 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-01 11:22:14.118 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 11:22:14.155 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 97e91f63-f093-4526-83be-fb5b45deb2ff

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 11:22:14.161 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 11:22:14.584 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 11:22:14.620 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:22:14.632 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 11:22:14.642 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 4.921 seconds (process running for 5.776)
2025-08-01 11:22:15.038 [RMI TCP Connection(2)-*************] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 11:22:15.038 [RMI TCP Connection(2)-*************] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 11:22:15.039 [RMI TCP Connection(2)-*************] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-01 11:26:08.944 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 11:26:08.947 [Thread-6] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 11:26:08.948 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:26:08.951 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 11:26:08.952 [Thread-6] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 11:26:08.956 [Thread-6] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-08-01 11:26:08.959 [Thread-6] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-08-01 11:26:09.052 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 11:26:09.052 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 11:26:09.422 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 11:26:09.423 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:26:09.423 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:26:09.423 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 11:26:09.447 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:26:09.447 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 393 ms
2025-08-01 11:26:09.757 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited
2025-08-01 11:26:10.461 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 11:26:10.480 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 94460517-650b-4701-8f50-b2e50f2ab248

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 11:26:10.481 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 11:26:10.704 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 11:26:10.721 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:26:10.724 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 11:26:10.730 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.724 seconds (process running for 241.864)
2025-08-01 11:26:10.732 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 11:26:21.513 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 11:26:21.514 [Thread-9] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 11:26:21.514 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:26:21.520 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 11:26:21.520 [Thread-9] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 11:26:21.522 [Thread-9] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ...
2025-08-01 11:26:21.523 [Thread-9] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed
2025-08-01 11:26:21.594 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 11:26:21.594 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 11:26:21.939 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 11:26:21.940 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:26:21.940 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:26:21.940 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 11:26:21.962 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:26:21.962 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 366 ms
2025-08-01 11:26:22.245 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-3} inited
2025-08-01 11:26:22.937 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 11:26:22.957 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: c1c70c97-de10-401d-87fa-0a48f348043b

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 11:26:22.958 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 11:26:23.163 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 11:26:23.181 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:26:23.184 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 11:26:23.188 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.621 seconds (process running for 254.322)
2025-08-01 11:26:23.189 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 11:26:45.486 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 1 deletion, 0 modifications)
2025-08-01 11:26:45.487 [Thread-14] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 11:26:45.487 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:26:45.492 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 11:26:45.492 [Thread-14] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 11:26:45.494 [Thread-14] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-3} closing ...
2025-08-01 11:26:45.494 [Thread-14] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-3} closed
2025-08-01 11:26:45.551 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 11:26:45.551 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 11:26:45.882 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 11:26:45.882 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:26:45.882 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:26:45.882 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 11:26:45.905 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:26:45.905 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 353 ms
2025-08-01 11:26:46.300 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-4} inited
2025-08-01 11:26:46.537 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'semesterController' defined in file [C:\Users\<USER>\Desktop\vite3\student-backend\target\classes\com\example\controller\basic\SemesterController.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'com.example.service.basic.SemesterService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-08-01 11:26:46.537 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-4} closing ...
2025-08-01 11:26:46.538 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-4} closed
2025-08-01 11:26:46.539 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-01 11:26:46.544 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-01 11:26:46.564 [restartedMain] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.example.controller.basic.SemesterController required a bean of type 'com.example.service.basic.SemesterService' that could not be found.


Action:

Consider defining a bean of type 'com.example.service.basic.SemesterService' in your configuration.

2025-08-01 11:26:48.065 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 11:26:48.065 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 11:26:48.361 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 11:26:48.362 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:26:48.362 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:26:48.362 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 11:26:48.383 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:26:48.383 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 317 ms
2025-08-01 11:26:48.678 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-5} inited
2025-08-01 11:26:49.350 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 11:26:49.372 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 4b5d5e75-735b-4d1f-b882-748a77886cc9

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 11:26:49.378 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 11:26:49.547 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 11:26:49.565 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:26:49.568 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 11:26:49.573 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.527 seconds (process running for 280.706)
2025-08-01 11:26:49.574 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 11:26:56.659 [http-nio-8080-exec-2] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 11:26:56.659 [http-nio-8080-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 11:26:56.660 [http-nio-8080-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-01 11:28:18.244 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 11:28:18.246 [Thread-19] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 11:28:18.246 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:28:18.263 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 11:28:18.263 [Thread-19] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 11:28:18.273 [Thread-19] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-5} closing ...
2025-08-01 11:28:18.276 [Thread-19] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-5} closed
2025-08-01 11:28:18.359 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 11:28:18.359 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 11:28:18.985 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 11:28:18.986 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:28:18.986 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:28:18.986 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 11:28:19.026 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:28:19.026 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 665 ms
2025-08-01 11:28:19.492 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-6} inited
2025-08-01 11:28:21.220 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 11:28:21.260 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 9be77276-7de3-4713-9d13-5e255f8f8873

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 11:28:21.262 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 11:28:21.587 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 11:28:21.626 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:28:21.630 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 11:28:21.645 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 3.318 seconds (process running for 372.778)
2025-08-01 11:28:21.647 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 11:28:36.954 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 11:28:36.956 [Thread-29] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 11:28:36.956 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:28:36.961 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 11:28:36.961 [Thread-29] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 11:28:36.966 [Thread-29] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-6} closing ...
2025-08-01 11:28:36.967 [Thread-29] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-6} closed
2025-08-01 11:28:37.148 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 11:28:37.149 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 11:28:37.723 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 11:28:37.723 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:28:37.723 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:28:37.724 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 11:28:37.766 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:28:37.767 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 616 ms
2025-08-01 11:28:38.244 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-7} inited
2025-08-01 11:28:39.656 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 11:28:39.697 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 0192d373-149a-42b5-be42-b6bc63f9a9ae

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 11:28:39.700 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 11:28:40.141 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 11:28:40.174 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:28:40.179 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 11:28:40.188 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 3.068 seconds (process running for 391.321)
2025-08-01 11:28:40.189 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 11:28:54.517 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 1 deletion, 0 modifications)
2025-08-01 11:28:54.518 [Thread-34] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 11:28:54.519 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:28:54.524 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 11:28:54.524 [Thread-34] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 11:28:54.528 [Thread-34] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-7} closing ...
2025-08-01 11:28:54.529 [Thread-34] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-7} closed
2025-08-01 11:28:54.628 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 11:28:54.628 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 11:28:55.228 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 11:28:55.229 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:28:55.229 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:28:55.229 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 11:28:55.271 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:28:55.271 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 641 ms
2025-08-01 11:28:57.143 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 11:28:57.167 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: ec91b02e-189b-48bd-92f7-699f0f746a07

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 11:28:57.169 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 11:28:57.378 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 11:28:57.394 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:28:57.397 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 11:28:57.401 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 2.805 seconds (process running for 408.534)
2025-08-01 11:28:57.402 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 11:29:03.031 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 11:29:03.032 [Thread-39] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 11:29:03.032 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:29:03.038 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 11:29:03.039 [Thread-39] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 11:29:03.042 [Thread-39] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-0} closing ...
2025-08-01 11:29:03.111 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 11:29:03.111 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 11:29:03.461 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 11:29:03.461 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:29:03.461 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:29:03.461 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 11:29:03.484 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:29:03.484 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 372 ms
2025-08-01 11:29:04.839 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 11:29:04.866 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 0428c87b-a0af-4373-8e42-92e2b19fc63b

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 11:29:04.867 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 11:29:05.125 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 11:29:05.145 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:29:05.147 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 11:29:05.152 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 2.06 seconds (process running for 416.286)
2025-08-01 11:29:05.154 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 11:29:06.612 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (1 addition, 0 deletions, 0 modifications)
2025-08-01 11:29:06.613 [Thread-43] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 11:29:06.614 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:29:06.618 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 11:29:06.618 [Thread-43] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 11:29:06.620 [Thread-43] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-0} closing ...
2025-08-01 11:29:06.682 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 11:29:06.682 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 11:29:07.013 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 11:29:07.013 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:29:07.013 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:29:07.014 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 11:29:07.035 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:29:07.035 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 352 ms
2025-08-01 11:29:07.334 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-8} inited
2025-08-01 11:29:08.056 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 11:29:08.076 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 52143a08-6f73-4632-a4ba-855aa41e87f7

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 11:29:08.078 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 11:29:08.282 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 11:29:08.300 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:29:08.302 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 11:29:08.307 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.642 seconds (process running for 419.441)
2025-08-01 11:29:08.308 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 11:29:43.398 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 11:29:43.398 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 11:29:43.399 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-01 11:29:57.657 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 11:29:57.659 [Thread-47] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 11:29:57.659 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:29:57.663 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 11:29:57.663 [Thread-47] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 11:29:57.667 [Thread-47] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-8} closing ...
2025-08-01 11:29:57.667 [Thread-47] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-8} closed
2025-08-01 11:29:57.732 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 11:29:57.732 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 11:29:58.054 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 11:29:58.055 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:29:58.055 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:29:58.055 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 11:29:58.076 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:29:58.076 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 342 ms
2025-08-01 11:29:58.386 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-9} inited
2025-08-01 11:29:59.160 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 11:29:59.183 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 2d059d09-0cd9-4364-a729-2e6cefcdd004

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 11:29:59.184 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 11:29:59.388 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 11:29:59.404 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:29:59.407 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 11:29:59.412 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.698 seconds (process running for 470.546)
2025-08-01 11:29:59.413 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 11:30:09.189 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 1 deletion, 0 modifications)
2025-08-01 11:30:09.190 [Thread-53] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 11:30:09.190 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:30:09.195 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 11:30:09.195 [Thread-53] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 11:30:09.197 [Thread-53] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-9} closing ...
2025-08-01 11:30:09.198 [Thread-53] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-9} closed
2025-08-01 11:30:09.264 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 11:30:09.265 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 11:30:09.624 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 11:30:09.624 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:30:09.624 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:30:09.624 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 11:30:09.650 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:30:09.650 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 384 ms
2025-08-01 11:30:10.890 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 11:30:10.912 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: a45c81ad-e016-4403-ae48-a3ee234f660e

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 11:30:10.913 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 11:30:11.153 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 11:30:11.170 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:30:11.173 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 11:30:11.177 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.93 seconds (process running for 482.311)
2025-08-01 11:30:11.178 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 11:30:15.821 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 11:30:15.821 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 11:30:15.822 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-01 11:30:15.836 [http-nio-8080-exec-1] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-10} inited
2025-08-01 11:30:21.999 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 11:30:22.000 [Thread-58] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 11:30:22.001 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:30:22.005 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 11:30:22.006 [Thread-58] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 11:30:22.008 [Thread-58] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-10} closing ...
2025-08-01 11:30:22.008 [Thread-58] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-10} closed
2025-08-01 11:30:22.080 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 11:30:22.080 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 11:30:22.357 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 11:30:22.357 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:30:22.358 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:30:22.358 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 11:30:22.376 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:30:22.376 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 295 ms
2025-08-01 11:30:22.606 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-11} inited
2025-08-01 11:30:23.461 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 11:30:23.479 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: d219c444-89f5-4f5c-bf2a-32431acc88b8

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 11:30:23.480 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 11:30:23.654 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 11:30:23.669 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:30:23.671 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 11:30:23.676 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.612 seconds (process running for 494.81)
2025-08-01 11:30:23.678 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 11:30:38.683 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 11:30:38.685 [Thread-64] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 11:30:38.685 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:30:38.690 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 11:30:38.690 [Thread-64] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 11:30:38.694 [Thread-64] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-11} closing ...
2025-08-01 11:30:38.694 [Thread-64] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-11} closed
2025-08-01 11:30:38.767 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 11:30:38.767 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 11:30:39.063 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 11:30:39.063 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:30:39.063 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:30:39.063 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 11:30:39.082 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:30:39.082 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 314 ms
2025-08-01 11:30:39.357 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-12} inited
2025-08-01 11:30:40.185 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 11:30:40.204 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 106a5334-a0c2-4d51-9a14-8a548b7f31d9

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 11:30:40.206 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 11:30:40.400 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 11:30:40.416 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:30:40.419 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 11:30:40.423 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.672 seconds (process running for 511.557)
2025-08-01 11:30:40.424 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 11:30:55.417 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 11:30:55.419 [Thread-69] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 11:30:55.420 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:30:55.428 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 11:30:55.428 [Thread-69] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 11:30:55.430 [Thread-69] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-12} closing ...
2025-08-01 11:30:55.431 [Thread-69] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-12} closed
2025-08-01 11:30:55.503 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 11:30:55.503 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 11:30:55.783 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 11:30:55.783 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:30:55.783 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:30:55.783 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 11:30:55.802 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:30:55.802 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 298 ms
2025-08-01 11:30:56.050 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-13} inited
2025-08-01 11:30:56.777 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 11:30:56.794 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 4250c632-7095-4d47-9bc0-b74745558882

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 11:30:56.795 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 11:30:56.987 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 11:30:57.006 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:30:57.014 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 11:30:57.021 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.533 seconds (process running for 528.155)
2025-08-01 11:30:57.023 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 11:31:07.849 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 1 deletion, 0 modifications)
2025-08-01 11:31:07.851 [Thread-74] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 11:31:07.851 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:31:07.858 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 11:31:07.858 [Thread-74] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 11:31:07.861 [Thread-74] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-13} closing ...
2025-08-01 11:31:07.862 [Thread-74] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-13} closed
2025-08-01 11:31:07.944 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 11:31:07.944 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 11:31:08.413 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 11:31:08.413 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:31:08.414 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:31:08.414 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 11:31:08.439 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:31:08.439 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 493 ms
2025-08-01 11:31:08.930 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-14} inited
2025-08-01 11:31:09.835 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 11:31:09.858 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 04630dfc-1a61-447f-8bb9-e8209bf0e87a

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 11:31:09.859 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 11:31:10.082 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 11:31:10.102 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:31:10.105 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 11:31:10.110 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 2.189 seconds (process running for 541.244)
2025-08-01 11:31:10.111 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 11:31:10.687 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 11:31:10.718 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 5244 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 11:31:10.719 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 11:31:10.755 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01 11:31:10.755 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01 11:31:12.084 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 11:31:12.095 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:31:12.097 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:31:12.097 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 11:31:12.139 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:31:12.140 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1384 ms
2025-08-01 11:31:12.763 [restartedMain] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.entity.system.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 11:31:12.779 [restartedMain] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.entity.system.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 11:31:12.942 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-01 11:31:14.132 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 11:31:14.164 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 49ce4b9c-d47f-4bb2-91d1-4d25efdbf0c5

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 11:31:14.172 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 11:31:14.548 [restartedMain] WARN  o.s.b.d.autoconfigure.OptionalLiveReloadServer - Unable to start LiveReload server
2025-08-01 11:31:14.589 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:31:14.593 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-08-01 11:31:14.597 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-08-01 11:31:14.600 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-08-01 11:31:14.621 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-01 11:31:14.632 [restartedMain] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-08-01 11:32:17.116 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 11:32:17.118 [Thread-79] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 11:32:17.118 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:32:17.122 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 11:32:17.122 [Thread-79] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 11:32:17.124 [Thread-79] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-14} closing ...
2025-08-01 11:32:17.125 [Thread-79] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-14} closed
2025-08-01 11:32:17.206 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 11:32:17.206 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 11:32:17.485 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 11:32:17.486 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:32:17.486 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:32:17.486 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 11:32:17.505 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:32:17.505 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 298 ms
2025-08-01 11:32:17.763 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-15} inited
2025-08-01 11:32:18.364 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 11:32:18.381 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 6f331470-76d7-45d1-be10-9dc4d1249745

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 11:32:18.382 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 11:32:18.561 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 11:32:18.575 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:32:18.578 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 11:32:18.582 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.393 seconds (process running for 609.715)
2025-08-01 11:32:18.583 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 11:32:27.316 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 11:32:27.317 [Thread-84] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 11:32:27.318 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:32:27.322 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 11:32:27.322 [Thread-84] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 11:32:27.324 [Thread-84] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-15} closing ...
2025-08-01 11:32:27.325 [Thread-84] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-15} closed
2025-08-01 11:32:27.397 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 11:32:27.397 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 11:32:27.662 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 11:32:27.663 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:32:27.663 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:32:27.663 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 11:32:27.683 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:32:27.683 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 285 ms
2025-08-01 11:32:27.935 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-16} inited
2025-08-01 11:32:28.525 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 11:32:28.541 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 7285c0d4-691e-40d0-9700-08c2d3a2fb3b

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 11:32:28.543 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 11:32:28.730 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 11:32:28.744 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:32:28.746 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 11:32:28.750 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.369 seconds (process running for 619.884)
2025-08-01 11:32:28.751 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 11:32:41.650 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 11:32:41.651 [Thread-89] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 11:32:41.651 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:32:41.654 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 11:32:41.654 [Thread-89] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 11:32:41.656 [Thread-89] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-16} closing ...
2025-08-01 11:32:41.657 [Thread-89] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-16} closed
2025-08-01 11:32:41.731 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 11:32:41.732 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 11:32:42.010 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 11:32:42.010 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:32:42.011 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:32:42.011 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 11:32:42.029 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:32:42.029 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 296 ms
2025-08-01 11:32:42.274 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-17} inited
2025-08-01 11:32:43.076 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 11:32:43.092 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 613a3e96-2331-45d2-b0dd-fc20c2aea0bd

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 11:32:43.093 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 11:32:43.264 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 11:32:43.280 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:32:43.283 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 11:32:43.287 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.573 seconds (process running for 634.42)
2025-08-01 11:32:43.288 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 11:34:16.808 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 11:34:16.809 [Thread-94] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 11:34:16.809 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:34:16.813 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 11:34:16.813 [Thread-94] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 11:34:16.816 [Thread-94] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-17} closing ...
2025-08-01 11:34:16.816 [Thread-94] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-17} closed
2025-08-01 11:34:16.886 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 11:34:16.886 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 11:34:17.223 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 11:34:17.224 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:34:17.224 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:34:17.224 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 11:34:17.244 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:34:17.244 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 357 ms
2025-08-01 11:34:17.511 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-18} inited
2025-08-01 11:34:18.368 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 11:34:18.387 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 83f8125a-1fd3-45e7-8401-d731f4e35674

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 11:34:18.389 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 11:34:18.578 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 11:34:18.594 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:34:18.596 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 11:34:18.601 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.732 seconds (process running for 729.734)
2025-08-01 11:34:18.602 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 11:34:32.568 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 11:34:32.569 [Thread-99] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 11:34:32.570 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:34:32.574 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 11:34:32.574 [Thread-99] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 11:34:32.576 [Thread-99] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-18} closing ...
2025-08-01 11:34:32.576 [Thread-99] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-18} closed
2025-08-01 11:34:32.655 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 11:34:32.655 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 11:34:32.948 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 11:34:32.949 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:34:32.949 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:34:32.949 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 11:34:32.968 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:34:32.968 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 312 ms
2025-08-01 11:34:33.213 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-19} inited
2025-08-01 11:34:34.037 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 11:34:34.054 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 0096857f-07a2-4ca6-b70f-471d2e8e371e

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 11:34:34.056 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 11:34:34.233 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 11:34:34.250 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:34:34.252 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 11:34:34.257 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.616 seconds (process running for 745.39)
2025-08-01 11:34:34.258 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 11:34:37.447 [http-nio-8080-exec-2] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 11:34:37.447 [http-nio-8080-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 11:34:37.449 [http-nio-8080-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-01 11:34:52.328 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 11:34:52.329 [Thread-104] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 11:34:52.329 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:34:52.334 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 11:34:52.334 [Thread-104] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 11:34:52.336 [Thread-104] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-19} closing ...
2025-08-01 11:34:52.337 [Thread-104] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-19} closed
2025-08-01 11:34:52.425 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 11:34:52.425 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 11:34:52.747 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 11:34:52.747 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:34:52.747 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:34:52.747 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 11:34:52.768 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:34:52.768 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 342 ms
2025-08-01 11:34:53.037 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-20} inited
2025-08-01 11:34:53.898 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 11:34:53.917 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 8c1469ab-3501-48cd-a2cf-4006d9b59c02

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 11:34:53.918 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 11:34:54.099 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 11:34:54.113 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:34:54.116 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 11:34:54.120 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.713 seconds (process running for 765.254)
2025-08-01 11:34:54.121 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 11:35:48.588 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 1 deletion, 0 modifications)
2025-08-01 11:35:48.589 [Thread-116] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 11:35:48.589 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:35:48.593 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 11:35:48.593 [Thread-116] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 11:35:48.595 [Thread-116] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-20} closing ...
2025-08-01 11:35:48.595 [Thread-116] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-20} closed
2025-08-01 11:35:48.683 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 11:35:48.683 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 11:35:48.960 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 11:35:48.960 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:35:48.960 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:35:48.960 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 11:35:48.979 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:35:48.979 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 295 ms
2025-08-01 11:35:50.138 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 11:35:50.156 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: e3fe2647-2223-4c82-9799-0de96d7e83f9

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 11:35:50.158 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 11:35:50.347 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 11:35:50.365 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:35:50.367 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 11:35:50.371 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.702 seconds (process running for 821.504)
2025-08-01 11:35:50.372 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 11:35:51.827 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (1 addition, 0 deletions, 0 modifications)
2025-08-01 11:35:51.828 [Thread-121] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 11:35:51.828 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:35:51.832 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 11:35:51.832 [Thread-121] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 11:35:51.839 [Thread-121] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-0} closing ...
2025-08-01 11:35:51.933 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 11:35:51.933 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 11:35:52.223 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 11:35:52.224 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 11:35:52.224 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 11:35:52.224 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 11:35:52.241 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 11:35:52.241 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 307 ms
2025-08-01 11:35:52.485 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-21} inited
2025-08-01 11:35:53.284 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 11:35:53.302 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 2080c350-7a3a-47bb-8c40-3e88f576396f

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 11:35:53.303 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 11:35:53.486 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 11:35:53.503 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 11:35:53.506 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 11:35:53.511 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.594 seconds (process running for 824.644)
2025-08-01 11:35:53.512 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 12:14:06.614 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 12:14:06.614 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 12:14:06.616 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-01 12:14:06.629 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 12:14:06.629 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 12:14:06.629 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 12:14:06.662 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 12:18:50.875 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 12:18:50.875 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 12:18:50.875 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 12:18:50.875 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 12:30:46.210 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 12:30:46.211 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 12:30:46.211 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 12:30:46.211 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 12:30:57.652 [http-nio-8080-exec-3] INFO  c.example.service.score.impl.GradeInputServiceImpl - 生成多课程成绩导入模板: classCode=2023054801, courseCodes=[0502943, 0502947, 0505127, 05059092, 0505128, 05159008, 05159003, 3002902, 3007905, 3201901], semesterId=4
2025-08-01 12:30:58.613 [http-nio-8080-exec-5] INFO  c.example.service.score.impl.GradeInputServiceImpl - 生成多课程成绩导入模板: classCode=2023054801, courseCodes=[0502943, 0502947, 0505127, 05059092, 0505128, 05159008, 05159003, 3002902, 3007905, 3201901], semesterId=4
2025-08-01 12:37:54.345 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 12:37:54.345 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 12:37:54.345 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 12:37:54.345 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 12:38:02.278 [http-nio-8080-exec-5] INFO  c.example.service.score.impl.GradeInputServiceImpl - 生成多课程成绩导入模板: classCode=2023054801, courseCodes=[0502901, 0502906, 0503901, 0708901, 1102901, 3002901, 3012905, H303, 0708902, 1102902, 1101901, 0502911, 0502926, 1101902, 3007903, 0602041, 0504906, 05051118, 0505117, 3004901, 04049020, 0404910, 3007904, 0002902, 05159001, 0005930, 05149015, 1103901, 0505938, 3003901, 05059011, 3013902, 0502943, 0505128, 0505127, 0502947, 05159008, 3007905, 05059092, 05159003, 3201901, 3002902], semesterId=null
2025-08-01 12:38:06.040 [http-nio-8080-exec-4] INFO  c.example.service.score.impl.GradeInputServiceImpl - 生成多课程成绩导入模板: classCode=2023054801, courseCodes=[0502901, 0502906, 0503901, 0708901, 1102901, 3002901, 3012905, H303, 0708902, 1102902, 1101901, 0502911, 0502926, 1101902, 3007903, 0602041, 0504906, 05051118, 0505117, 3004901, 04049020, 0404910, 3007904, 0002902, 05159001, 0005930, 05149015, 1103901, 0505938, 3003901, 05059011, 3013902, 0502943, 0505128, 0505127, 0502947, 05159008, 3007905, 05059092, 05159003, 3201901, 3002902], semesterId=null
2025-08-01 12:38:09.800 [http-nio-8080-exec-6] INFO  c.example.service.score.impl.GradeInputServiceImpl - 生成多课程成绩导入模板: classCode=2023054801, courseCodes=[0502901, 0502906, 0503901, 0708901, 1102901, 3002901, 3012905, H303, 0708902, 1102902, 1101901, 0502911, 0502926, 1101902, 3007903, 0602041, 0504906, 05051118, 0505117, 3004901, 04049020, 0404910, 3007904, 0002902, 05159001, 0005930, 05149015, 1103901, 0505938, 3003901, 05059011, 3013902, 0502943, 0505128, 0505127, 0502947, 05159008, 3007905, 05059092, 05159003, 3201901, 3002902], semesterId=null
2025-08-01 12:38:12.343 [http-nio-8080-exec-8] INFO  c.example.service.score.impl.GradeInputServiceImpl - 生成多课程成绩导入模板: classCode=2023054801, courseCodes=[0502901, 0502906, 0503901, 0708901, 1102901, 3002901, 3012905, H303, 0708902, 1102902, 1101901, 0502911, 0502926, 1101902, 3007903, 0602041, 0504906, 05051118, 0505117, 3004901, 04049020, 0404910, 3007904, 0002902, 05159001, 0005930, 05149015, 1103901, 0505938, 3003901, 05059011, 3013902, 0502943, 0505128, 0505127, 0502947, 05159008, 3007905, 05059092, 05159003, 3201901, 3002902], semesterId=null
2025-08-01 12:38:20.654 [http-nio-8080-exec-2] INFO  c.example.service.score.impl.GradeInputServiceImpl - 生成多课程成绩导入模板: classCode=2023054801, courseCodes=[0502901, 0502906, 0503901, 0708901, 1102901, 3002901, 3012905, H303, 0708902, 1102902, 1101901, 0502911, 0502926, 1101902, 3007903, 0602041, 0504906, 05051118, 0505117, 3004901, 04049020, 0404910, 3007904, 0002902, 05159001, 0005930, 05149015, 1103901, 0505938, 3003901, 05059011, 3013902, 0502943, 0505128, 0505127, 0502947, 05159008, 3007905, 05059092, 05159003, 3201901, 3002902], semesterId=null
2025-08-01 12:38:22.301 [http-nio-8080-exec-10] INFO  c.example.service.score.impl.GradeInputServiceImpl - 生成多课程成绩导入模板: classCode=2023054801, courseCodes=[0502901, 0502906, 0503901, 0708901, 1102901, 3002901, 3012905, H303, 0708902, 1102902, 1101901, 0502911, 0502926, 1101902, 3007903, 0602041, 0504906, 05051118, 0505117, 3004901, 04049020, 0404910, 3007904, 0002902, 05159001, 0005930, 05149015, 1103901, 0505938, 3003901, 05059011, 3013902, 0502943, 0505128, 0505127, 0502947, 05159008, 3007905, 05059092, 05159003, 3201901, 3002902], semesterId=null
2025-08-01 12:38:36.218 [http-nio-8080-exec-1] INFO  c.example.service.score.impl.GradeInputServiceImpl - 生成多课程成绩导入模板: classCode=2023054801, courseCodes=[0502901, 0502906, 0503901, 0708901, 1102901, 3002901, 3012905, H303, 0708902, 1102902, 1101901, 0502911, 0502926, 1101902, 3007903, 0602041, 0504906, 05051118, 0505117, 3004901, 04049020, 0404910, 3007904, 0002902, 05159001, 0005930, 05149015, 1103901, 0505938, 3003901, 05059011, 3013902, 0502943, 0505128, 0505127, 0502947, 05159008, 3007905, 05059092, 05159003, 3201901, 3002902], semesterId=null
2025-08-01 12:38:55.460 [http-nio-8080-exec-9] INFO  c.example.service.score.impl.GradeInputServiceImpl - 生成多课程成绩导入模板: classCode=2023054801, courseCodes=[0502901, 0502906, 0503901, 0708901, 1102901, 3002901, 3012905, H303, 0708902, 1102902, 1101901, 0502911, 0502926, 1101902, 3007903, 0602041, 0504906, 05051118, 0505117, 3004901, 04049020, 0404910, 3007904, 0002902, 05159001, 0005930, 05149015, 1103901, 0505938, 3003901, 05059011, 3013902, 0502943, 0505128, 0505127, 0502947, 05159008, 3007905, 05059092, 05159003, 3201901, 3002902], semesterId=null
2025-08-01 12:39:20.177 [http-nio-8080-exec-3] INFO  c.example.service.score.impl.FinalGradeServiceImpl - 开始导出多学期期末成绩: classCode=2023054801, semesterIds=[15, 14, 4, 3, 2, 1]
2025-08-01 12:39:20.178 [http-nio-8080-exec-3] INFO  c.example.service.score.impl.FinalGradeServiceImpl - 开始优化导出成绩: classCode=2023054801, semesterIds=[15, 14, 4, 3, 2, 1]
2025-08-01 12:39:20.226 [http-nio-8080-exec-3] INFO  com.example.common.excel.ApachePoiExportService - 开始Apache POI流式导出，数据行数: 44, 表头数量: 47
2025-08-01 12:39:20.254 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第0列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.255 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第1列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.255 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第2列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.255 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第3列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.255 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第4列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.255 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第5列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.255 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第6列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.255 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第7列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.255 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第8列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.255 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第9列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.255 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第10列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.255 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第11列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.255 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第12列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.255 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第13列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.255 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第14列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.255 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第15列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.255 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第16列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.256 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第17列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.256 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第18列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.256 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第19列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.256 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第20列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.256 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第21列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.256 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第22列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.256 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第23列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.256 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第24列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.256 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第25列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.256 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第26列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.256 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第27列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.256 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第28列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.256 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第29列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.256 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第30列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.256 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第31列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.256 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第32列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.256 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第33列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.256 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第34列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.256 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第35列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.256 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第36列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.256 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第37列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.256 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第38列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.256 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第39列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.256 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第40列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.257 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第41列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.257 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第42列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.257 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第43列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.257 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第44列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.257 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第45列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.257 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第46列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.280 [http-nio-8080-exec-3] INFO  com.example.common.excel.ApachePoiExportService - Apache POI导出成功，数据行数: 44, 文件大小: 14507 bytes
2025-08-01 12:39:20.282 [http-nio-8080-exec-3] INFO  c.example.service.score.impl.FinalGradeServiceImpl - 优化导出成功（Apache POI），学期数: 6, 学生数: 44, 文件大小: 14507 bytes
2025-08-01 12:39:20.338 [http-nio-8080-exec-5] INFO  c.example.service.score.impl.FinalGradeServiceImpl - 开始导出多学期期末成绩: classCode=2023054801, semesterIds=[15, 14, 4, 3, 2, 1]
2025-08-01 12:39:20.340 [http-nio-8080-exec-5] INFO  c.example.service.score.impl.FinalGradeServiceImpl - 开始优化导出成绩: classCode=2023054801, semesterIds=[15, 14, 4, 3, 2, 1]
2025-08-01 12:39:20.383 [http-nio-8080-exec-5] INFO  com.example.common.excel.ApachePoiExportService - 开始Apache POI流式导出，数据行数: 44, 表头数量: 47
2025-08-01 12:39:20.388 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第0列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.388 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第1列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.388 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第2列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.388 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第3列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.388 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第4列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.388 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第5列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.388 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第6列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.388 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第7列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.388 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第8列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.388 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第9列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.388 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第10列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.388 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第11列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.388 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第12列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.388 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第13列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.388 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第14列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.388 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第15列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.388 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第16列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.388 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第17列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.388 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第18列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.388 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第19列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.388 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第20列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.388 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第21列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.388 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第22列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.388 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第23列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.389 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第24列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.389 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第25列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.389 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第26列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.389 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第27列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.389 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第28列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.389 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第29列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.389 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第30列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.389 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第31列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.389 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第32列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.389 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第33列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.389 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第34列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.389 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第35列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.389 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第36列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.389 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第37列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.389 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第38列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.389 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第39列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.389 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第40列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.389 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第41列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.389 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第42列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.389 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第43列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.389 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第44列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.389 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第45列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.389 [http-nio-8080-exec-5] WARN  com.example.common.excel.ExcelUtils - 自动调整第46列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 12:39:20.400 [http-nio-8080-exec-5] INFO  com.example.common.excel.ApachePoiExportService - Apache POI导出成功，数据行数: 44, 文件大小: 14507 bytes
2025-08-01 12:39:20.402 [http-nio-8080-exec-5] INFO  c.example.service.score.impl.FinalGradeServiceImpl - 优化导出成功（Apache POI），学期数: 6, 学生数: 44, 文件大小: 14507 bytes
2025-08-01 12:42:12.562 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 12:42:12.566 [Thread-125] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 12:42:12.567 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 12:42:12.570 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 12:42:12.570 [Thread-125] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 12:42:12.575 [Thread-125] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-21} closing ...
2025-08-01 12:42:12.577 [Thread-125] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-21} closed
2025-08-01 12:42:12.742 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 12:42:12.743 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 12:42:13.361 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 12:42:13.362 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 12:42:13.362 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 12:42:13.362 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 12:42:13.391 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 12:42:13.391 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 646 ms
2025-08-01 12:42:13.813 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-22} inited
2025-08-01 12:42:14.821 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 12:42:14.842 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 6cb5d827-2cd1-40d5-b04d-74d52e16c915

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 12:42:14.843 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 12:42:15.074 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 12:42:15.095 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 12:42:15.098 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 12:42:15.104 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 2.431 seconds (process running for 4806.238)
2025-08-01 12:42:15.106 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 12:42:42.594 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 12:42:42.595 [Thread-132] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 12:42:42.596 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 12:42:42.600 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 12:42:42.600 [Thread-132] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 12:42:42.603 [Thread-132] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-22} closing ...
2025-08-01 12:42:42.603 [Thread-132] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-22} closed
2025-08-01 12:42:42.687 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 12:42:42.687 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 12:42:42.997 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 12:42:42.997 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 12:42:42.998 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 12:42:42.998 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 12:42:43.022 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 12:42:43.022 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 333 ms
2025-08-01 12:42:43.290 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-23} inited
2025-08-01 12:42:44.165 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 12:42:44.183 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: a7d5707b-e146-403c-a821-e50567087d27

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 12:42:44.184 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 12:42:44.379 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 12:42:44.398 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 12:42:44.401 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 12:42:44.406 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.742 seconds (process running for 4835.539)
2025-08-01 12:42:44.406 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 12:54:50.879 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 2 class path changes (0 additions, 2 deletions, 0 modifications)
2025-08-01 12:54:50.880 [Thread-137] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 12:54:50.880 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 12:54:50.883 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 12:54:50.883 [Thread-137] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 12:54:50.886 [Thread-137] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-23} closing ...
2025-08-01 12:54:50.887 [Thread-137] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-23} closed
2025-08-01 12:54:50.969 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 12:54:50.969 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 12:54:51.457 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 12:54:51.457 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 12:54:51.457 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 12:54:51.457 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 12:54:51.486 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 12:54:51.486 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 515 ms
2025-08-01 12:54:51.797 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-24} inited
2025-08-01 12:54:52.221 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'gradeInputController' defined in file [C:\Users\<USER>\Desktop\vite3\student-backend\target\classes\com\example\controller\score\GradeInputController.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'com.example.service.score.GradeInputService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-08-01 12:54:52.222 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-24} closing ...
2025-08-01 12:54:52.222 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-24} closed
2025-08-01 12:54:52.223 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-01 12:54:52.227 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-01 12:54:52.230 [restartedMain] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.example.controller.score.GradeInputController required a bean of type 'com.example.service.score.GradeInputService' that could not be found.


Action:

Consider defining a bean of type 'com.example.service.score.GradeInputService' in your configuration.

2025-08-01 12:54:53.707 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 12:54:53.707 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 12:54:54.033 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 12:54:54.033 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 12:54:54.033 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 12:54:54.033 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 12:54:54.053 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-2].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 12:54:54.053 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 345 ms
2025-08-01 12:54:54.341 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-25} inited
2025-08-01 12:54:55.431 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 12:54:55.453 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: b0af26e0-38d0-4696-afec-02a23f6d203f

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 12:54:55.454 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 12:54:55.682 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 12:54:55.705 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 12:54:55.707 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 12:54:55.713 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 2.025 seconds (process running for 5566.846)
2025-08-01 12:54:55.714 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 13:12:22.393 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat-2].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 13:12:22.395 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 13:12:22.395 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-08-01 13:12:22.411 [http-nio-8080-exec-1] INFO  c.example.service.score.impl.GradeInputServiceImpl - 生成多课程成绩导入模板: classCode=2023054801, courseCodes=[0502901, 0502906, 0503901, 0708901, 1102901, 3002901, 3012905, H303, 0708902, 1102902, 1101901, 0502911, 0502926, 1101902, 3007903, 0602041, 0504906, 05051118, 0505117, 3004901, 04049020, 0404910, 3007904, 0002902, 05159001, 0005930, 05149015, 1103901, 0505938, 3003901, 05059011, 3013902, 0502943, 0505128, 0505127, 0502947, 05159008, 3007905, 05059092, 05159003, 3201901, 3002902], semesterId=null
2025-08-01 13:12:23.881 [http-nio-8080-exec-5] INFO  c.example.service.score.impl.GradeInputServiceImpl - 生成多课程成绩导入模板: classCode=2023054801, courseCodes=[0502901, 0502906, 0503901, 0708901, 1102901, 3002901, 3012905, H303, 0708902, 1102902, 1101901, 0502911, 0502926, 1101902, 3007903, 0602041, 0504906, 05051118, 0505117, 3004901, 04049020, 0404910, 3007904, 0002902, 05159001, 0005930, 05149015, 1103901, 0505938, 3003901, 05059011, 3013902, 0502943, 0505128, 0505127, 0502947, 05159008, 3007905, 05059092, 05159003, 3201901, 3002902], semesterId=null
2025-08-01 13:15:10.902 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 13:15:10.903 [Thread-142] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 13:15:10.903 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 13:15:10.906 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 13:15:10.906 [Thread-142] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 13:15:10.906 [Thread-142] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-25} closing ...
2025-08-01 13:15:10.906 [Thread-142] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-25} closed
2025-08-01 13:15:11.035 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 13:15:11.036 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 13:15:11.471 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 13:15:11.480 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 13:15:11.480 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 13:15:11.480 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 13:15:11.503 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-2].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 13:15:11.503 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 466 ms
2025-08-01 13:15:11.891 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-26} inited
2025-08-01 13:15:12.629 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 13:15:12.647 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 8e3132e5-2fa3-40a5-8e3d-161203eb4a62

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 13:15:12.649 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 13:15:12.859 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 13:15:12.886 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 13:15:12.890 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 13:15:12.898 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.925 seconds (process running for 6784.032)
2025-08-01 13:15:12.900 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 13:15:46.546 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 13:15:46.548 [Thread-151] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 13:15:46.548 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 13:15:46.552 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 13:15:46.552 [Thread-151] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 13:15:46.553 [Thread-151] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-26} closing ...
2025-08-01 13:15:46.553 [Thread-151] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-26} closed
2025-08-01 13:15:46.633 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 13:15:46.633 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 13:15:46.952 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 13:15:46.952 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 13:15:46.952 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 13:15:46.952 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 13:15:46.965 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-2].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 13:15:46.965 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 332 ms
2025-08-01 13:15:47.213 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-27} inited
2025-08-01 13:15:48.062 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 13:15:48.076 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 2a9e9289-3313-45af-89bb-d788d680655a

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 13:15:48.076 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 13:15:48.260 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 13:15:48.282 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 13:15:48.282 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 13:15:48.292 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.676 seconds (process running for 6819.427)
2025-08-01 13:15:48.292 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 13:16:12.501 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 13:16:12.503 [Thread-156] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 13:16:12.505 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 13:16:12.509 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 13:16:12.509 [Thread-156] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 13:16:12.512 [Thread-156] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-27} closing ...
2025-08-01 13:16:12.512 [Thread-156] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-27} closed
2025-08-01 13:16:12.597 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 13:16:12.597 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 13:16:12.883 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 13:16:12.883 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 13:16:12.883 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 13:16:12.883 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 13:16:12.899 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-2].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 13:16:12.899 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 302 ms
2025-08-01 13:16:13.148 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-28} inited
2025-08-01 13:16:13.973 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 13:16:13.989 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 3ebc0f0f-56b6-4163-ba06-b85579b10d3e

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 13:16:13.989 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 13:16:14.172 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 13:16:14.187 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 13:16:14.187 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 13:16:14.195 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.614 seconds (process running for 6845.331)
2025-08-01 13:16:14.199 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 13:16:24.000 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 13:16:24.000 [Thread-161] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 13:16:24.000 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 13:16:24.008 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 13:16:24.008 [Thread-161] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 13:16:24.014 [Thread-161] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-28} closing ...
2025-08-01 13:16:24.014 [Thread-161] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-28} closed
2025-08-01 13:16:24.104 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 13:16:24.104 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 13:16:24.398 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 13:16:24.398 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 13:16:24.398 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 13:16:24.398 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 13:16:24.430 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-2].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 13:16:24.430 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 326 ms
2025-08-01 13:16:24.686 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-29} inited
2025-08-01 13:16:25.374 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 13:16:25.389 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: ea0096fe-c68a-446a-9fdc-2304ff169330

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 13:16:25.389 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 13:16:25.570 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 13:16:25.585 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 13:16:25.590 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 13:16:25.595 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.521 seconds (process running for 6856.729)
2025-08-01 13:16:25.595 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 13:16:35.372 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 13:16:35.374 [Thread-166] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 13:16:35.375 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 13:16:35.379 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 13:16:35.379 [Thread-166] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 13:16:35.383 [Thread-166] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-29} closing ...
2025-08-01 13:16:35.385 [Thread-166] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-29} closed
2025-08-01 13:16:35.460 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 13:16:35.460 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 13:16:35.741 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 13:16:35.741 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 13:16:35.741 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 13:16:35.741 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 13:16:35.756 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-2].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 13:16:35.756 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 296 ms
2025-08-01 13:16:36.020 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-30} inited
2025-08-01 13:16:36.723 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 13:16:36.741 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 9e91c40c-3e88-4796-879e-cf137e0d6aca

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 13:16:36.742 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 13:16:36.921 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 13:16:36.936 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 13:16:36.946 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 13:16:36.951 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.506 seconds (process running for 6868.084)
2025-08-01 13:16:36.952 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 13:17:03.062 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 13:17:03.063 [Thread-171] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 13:17:03.064 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 13:17:03.068 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 13:17:03.068 [Thread-171] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 13:17:03.071 [Thread-171] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-30} closing ...
2025-08-01 13:17:03.073 [Thread-171] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-30} closed
2025-08-01 13:17:03.167 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 13:17:03.167 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 13:17:03.462 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 13:17:03.462 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 13:17:03.462 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 13:17:03.462 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 13:17:03.479 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-2].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 13:17:03.479 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 312 ms
2025-08-01 13:17:03.733 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-31} inited
2025-08-01 13:17:04.406 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 13:17:04.422 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: e7594926-eb4e-4525-a403-e76e9f02dee3

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 13:17:04.422 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 13:17:04.611 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 13:17:04.626 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 13:17:04.630 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 13:17:04.634 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.483 seconds (process running for 6895.768)
2025-08-01 13:17:04.634 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 13:17:22.727 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 13:17:22.727 [Thread-176] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 13:17:22.727 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 13:17:22.735 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 13:17:22.735 [Thread-176] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 13:17:22.737 [Thread-176] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-31} closing ...
2025-08-01 13:17:22.737 [Thread-176] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-31} closed
2025-08-01 13:17:22.830 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 13:17:22.830 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 13:17:23.111 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 13:17:23.111 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 13:17:23.111 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 13:17:23.111 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 13:17:23.129 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-2].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 13:17:23.129 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 299 ms
2025-08-01 13:17:23.368 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-32} inited
2025-08-01 13:17:24.189 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 13:17:24.207 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 0626f500-6018-49cf-bbfa-41f7e82c0ae8

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 13:17:24.209 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 13:17:24.396 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 13:17:24.412 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 13:17:24.415 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 13:17:24.417 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.603 seconds (process running for 6915.553)
2025-08-01 13:17:24.417 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 13:17:50.852 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 13:17:50.852 [Thread-181] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 13:17:50.852 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 13:17:50.860 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 13:17:50.860 [Thread-181] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 13:17:50.861 [Thread-181] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-32} closing ...
2025-08-01 13:17:50.864 [Thread-181] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-32} closed
2025-08-01 13:17:50.949 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 13:17:50.949 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 13:17:51.227 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 13:17:51.227 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 13:17:51.227 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 13:17:51.227 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 13:17:51.242 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-2].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 13:17:51.242 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 293 ms
2025-08-01 13:17:51.489 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-33} inited
2025-08-01 13:17:52.318 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 13:17:52.337 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 60d8d45c-c55f-4c48-9772-49daf178e521

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 13:17:52.339 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 13:17:52.530 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 13:17:52.545 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 13:17:52.545 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 13:17:52.555 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.629 seconds (process running for 6943.689)
2025-08-01 13:17:52.556 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 14:53:05.053 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 6 class path changes (0 additions, 5 deletions, 1 modification)
2025-08-01 14:53:05.057 [Thread-186] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 14:53:05.058 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 14:53:05.064 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 14:53:05.065 [Thread-186] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 14:53:05.069 [Thread-186] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-33} closing ...
2025-08-01 14:53:05.069 [Thread-186] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-33} closed
2025-08-01 14:53:05.245 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 14:53:05.245 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 14:53:05.967 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 14:53:05.968 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 14:53:05.968 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 14:53:05.968 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 14:53:05.998 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-2].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 14:53:05.999 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 748 ms
2025-08-01 14:53:06.501 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-34} inited
2025-08-01 14:53:06.779 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'classCourseController' defined in file [C:\Users\<USER>\Desktop\vite3\student-backend\target\classes\com\example\controller\educational\ClassCourseController.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'com.example.service.educational.ClassCourseService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-08-01 14:53:06.779 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-34} closing ...
2025-08-01 14:53:06.780 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-34} closed
2025-08-01 14:53:06.780 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-01 14:53:06.786 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-01 14:53:06.789 [restartedMain] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.example.controller.educational.ClassCourseController required a bean of type 'com.example.service.educational.ClassCourseService' that could not be found.


Action:

Consider defining a bean of type 'com.example.service.educational.ClassCourseService' in your configuration.

2025-08-01 14:53:08.301 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 9620 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 14:53:08.301 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 14:53:08.644 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 14:53:08.644 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 14:53:08.644 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 14:53:08.644 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 14:53:08.669 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-3].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 14:53:08.669 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 368 ms
2025-08-01 14:53:08.955 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-35} inited
2025-08-01 14:53:09.876 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 14:53:09.894 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: da3f6682-fa83-453c-bba1-7bf8c4600092

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 14:53:09.896 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 14:53:10.142 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 14:53:10.164 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 14:53:10.169 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 14:53:10.175 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.905 seconds (process running for 12661.309)
2025-08-01 14:53:10.175 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 14:59:52.846 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat-3].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 14:59:52.846 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 14:59:52.852 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 6 ms
2025-08-01 14:59:53.124 [http-nio-8080-exec-2] INFO  c.example.service.score.impl.GradeInputServiceImpl - 生成多课程成绩导入模板: classCode=2023054801, courseCodes=[0502901, 0502906, 0503901, 0708901, 1102901, 3002901, 3012905, H303, 0708902, 1102902, 1101901, 0502911, 0502926, 1101902, 3007903, 0602041, 0504906, 05051118, 0505117, 3004901, 04049020, 0404910, 3007904, 0002902, 05159001, 0005930, 05149015, 1103901, 0505938, 3003901, 05059011, 3013902, 0502943, 0505128, 0505127, 0502947, 05159008, 3007905, 05059092, 05159003, 3201901, 3002902], semesterId=null
2025-08-01 14:59:54.560 [http-nio-8080-exec-7] INFO  c.example.service.score.impl.GradeInputServiceImpl - 生成多课程成绩导入模板: classCode=2023054801, courseCodes=[0502901, 0502906, 0503901, 0708901, 1102901, 3002901, 3012905, H303, 0708902, 1102902, 1101901, 0502911, 0502926, 1101902, 3007903, 0602041, 0504906, 05051118, 0505117, 3004901, 04049020, 0404910, 3007904, 0002902, 05159001, 0005930, 05149015, 1103901, 0505938, 3003901, 05059011, 3013902, 0502943, 0505128, 0505127, 0502947, 05159008, 3007905, 05059092, 05159003, 3201901, 3002902], semesterId=null
2025-08-01 15:00:58.523 [SpringApplicationShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 15:00:58.523 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 15:00:58.530 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-35} closing ...
2025-08-01 15:00:58.531 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-35} closed
2025-08-01 15:01:00.301 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 15:01:00.334 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 18884 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 15:01:00.336 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 15:01:00.379 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01 15:01:00.379 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01 15:01:01.912 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 15:01:01.923 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:01:01.927 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 15:01:01.927 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 15:01:01.986 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 15:01:01.987 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1607 ms
2025-08-01 15:01:02.735 [restartedMain] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.entity.system.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 15:01:02.763 [restartedMain] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.entity.system.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 15:01:02.870 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-01 15:01:04.143 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 15:01:04.178 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 10d4e340-997e-490d-85e7-e0fff1d27854

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 15:01:04.188 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 15:01:04.637 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 15:01:04.685 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 15:01:04.703 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 15:01:04.715 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 4.948 seconds (process running for 5.584)
2025-08-01 15:01:04.934 [http-nio-8080-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 15:01:04.935 [http-nio-8080-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 15:01:04.936 [http-nio-8080-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-01 15:01:05.042 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 15:01:05.042 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 15:01:05.042 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 15:01:05.087 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 15:01:05.785 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 15:01:05.785 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 15:01:05.786 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 15:01:05.786 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 15:01:16.338 [http-nio-8080-exec-2] INFO  c.example.service.score.impl.GradeInputServiceImpl - 生成多课程成绩导入模板: classCode=2023054801, courseCodes=[0502901, 0502906, 0503901, 0708901, 1102901, 3002901, 3012905, H303, 0708902, 1102902, 1101901, 0502911, 0502926, 1101902, 3007903, 0602041, 0504906, 05051118, 0505117, 3004901, 04049020, 0404910, 3007904, 0002902, 05159001, 0005930, 05149015, 1103901, 0505938, 3003901, 05059011, 3013902, 0502943, 0505128, 0505127, 0502947, 05159008, 3007905, 05059092, 05159003, 3201901, 3002902], semesterId=null
2025-08-01 15:01:51.087 [http-nio-8080-exec-1] INFO  c.example.service.score.impl.FinalGradeServiceImpl - 开始导出多学期期末成绩: classCode=2023054801, semesterIds=[15, 14, 4, 3, 2, 1]
2025-08-01 15:01:51.095 [http-nio-8080-exec-1] INFO  c.example.service.score.impl.FinalGradeServiceImpl - 开始优化导出成绩: classCode=2023054801, semesterIds=[15, 14, 4, 3, 2, 1]
2025-08-01 15:01:51.142 [http-nio-8080-exec-1] INFO  com.example.common.excel.ApachePoiExportService - 开始Apache POI流式导出，数据行数: 44, 表头数量: 47
2025-08-01 15:01:51.165 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第0列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.165 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第1列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.165 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第2列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.165 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第3列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.165 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第4列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.165 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第5列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.165 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第6列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.167 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第7列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.167 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第8列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.167 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第9列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.167 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第10列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.167 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第11列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.167 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第12列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.167 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第13列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.167 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第14列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.167 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第15列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.167 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第16列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.167 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第17列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.167 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第18列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.168 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第19列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.168 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第20列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.168 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第21列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.168 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第22列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.168 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第23列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.168 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第24列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.168 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第25列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.168 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第26列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.168 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第27列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.168 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第28列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.168 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第29列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.168 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第30列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.168 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第31列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.168 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第32列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.168 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第33列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.168 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第34列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.169 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第35列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.169 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第36列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.169 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第37列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.169 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第38列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.169 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第39列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.169 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第40列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.169 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第41列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.169 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第42列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.169 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第43列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.170 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第44列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.170 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第45列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.170 [http-nio-8080-exec-1] WARN  com.example.common.excel.ExcelUtils - 自动调整第46列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:51.190 [http-nio-8080-exec-1] INFO  com.example.common.excel.ApachePoiExportService - Apache POI导出成功，数据行数: 44, 文件大小: 14507 bytes
2025-08-01 15:01:51.193 [http-nio-8080-exec-1] INFO  c.example.service.score.impl.FinalGradeServiceImpl - 优化导出成功（Apache POI），学期数: 6, 学生数: 44, 文件大小: 14507 bytes
2025-08-01 15:01:55.366 [http-nio-8080-exec-3] INFO  c.example.service.score.impl.FinalGradeServiceImpl - 开始导出多学期期末成绩: classCode=2023054801, semesterIds=[15, 14, 4, 3, 2, 1]
2025-08-01 15:01:55.370 [http-nio-8080-exec-3] INFO  c.example.service.score.impl.FinalGradeServiceImpl - 开始优化导出成绩: classCode=2023054801, semesterIds=[15, 14, 4, 3, 2, 1]
2025-08-01 15:01:55.413 [http-nio-8080-exec-3] INFO  com.example.common.excel.ApachePoiExportService - 开始Apache POI流式导出，数据行数: 44, 表头数量: 47
2025-08-01 15:01:55.413 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第0列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.413 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第1列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.413 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第2列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.413 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第3列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.413 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第4列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.413 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第5列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.413 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第6列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.413 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第7列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.413 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第8列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.413 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第9列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.413 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第10列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.413 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第11列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.413 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第12列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.413 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第13列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.413 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第14列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.413 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第15列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.413 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第16列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.413 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第17列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.413 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第18列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.413 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第19列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.413 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第20列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.413 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第21列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.413 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第22列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.419 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第23列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.419 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第24列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.419 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第25列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.419 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第26列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.419 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第27列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.419 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第28列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.419 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第29列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.419 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第30列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.419 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第31列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.419 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第32列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.419 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第33列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.420 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第34列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.420 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第35列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.420 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第36列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.420 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第37列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.420 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第38列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.420 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第39列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.420 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第40列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.420 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第41列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.420 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第42列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.420 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第43列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.420 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第44列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.420 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第45列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.420 [http-nio-8080-exec-3] WARN  com.example.common.excel.ExcelUtils - 自动调整第46列宽度失败: Could not auto-size column. Make sure the column was tracked prior to auto-sizing the column.
2025-08-01 15:01:55.433 [http-nio-8080-exec-3] INFO  com.example.common.excel.ApachePoiExportService - Apache POI导出成功，数据行数: 44, 文件大小: 14507 bytes
2025-08-01 15:01:55.433 [http-nio-8080-exec-3] INFO  c.example.service.score.impl.FinalGradeServiceImpl - 优化导出成功（Apache POI），学期数: 6, 学生数: 44, 文件大小: 14507 bytes
2025-08-01 15:04:06.597 [http-nio-8080-exec-8] INFO  c.example.service.score.impl.GradeInputServiceImpl - 开始导入多课程成绩: classCode=2023054801, courseCodes=[0502901, 0502906, 0503901, 0708901, 1102901, 3002901, 3012905, H303, 0708902, 1102902, 1101901, 0502911, 0502926, 1101902, 3007903, 0602041, 0504906, 05051118, 0505117, 3004901, 04049020, 0404910, 3007904, 0002902, 05159001, 0005930, 05149015, 1103901, 0505938, 3003901, 05059011, 3013902, 0502943, 0505128, 0505127, 0502947, 05159008, 3007905, 05059092, 05159003, 3201901, 3002902], semesterId=null, fileName=课程成绩导入模板.xlsx
2025-08-01 15:04:06.958 [http-nio-8080-exec-8] ERROR c.example.service.score.impl.GradeInputServiceImpl - 批量保存成绩失败
org.springframework.dao.DuplicateKeyException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry '************-0005930-3-0' for key 'grades.uk_student_course_semester'
### The error may exist in com/example/mapper/score/GradeInputMapper.java (best guess)
### The error may involve com.example.mapper.score.GradeInputMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO grades  ( student_id, course_code, semester_id, final_score,  is_retake,  created_by, created_at, updated_by, updated_at )  VALUES (  ?, ?, ?, ?,  ?,  ?, ?, ?, ?  )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry '************-0005930-3-0' for key 'grades.uk_student_course_semester'
; Duplicate entry '************-0005930-3-0' for key 'grades.uk_student_course_semester'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:254)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy119.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy3/jdk.proxy3.$Proxy147.insert(Unknown Source)
	at com.example.service.score.impl.GradeInputServiceImpl.processMultiCourseImportData(GradeInputServiceImpl.java:824)
	at com.example.service.score.impl.GradeInputServiceImpl.importMultiCourseGrades(GradeInputServiceImpl.java:573)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.example.service.score.impl.GradeInputServiceImpl$$SpringCGLIB$$0.importMultiCourseGrades(<generated>)
	at com.example.controller.score.GradeInputController.importMultiCourseGrades(GradeInputController.java:209)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.example.controller.score.GradeInputController$$SpringCGLIB$$0.importMultiCourseGrades(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.example.filter.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:64)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1570)
Caused by: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry '************-0005930-3-0' for key 'grades.uk_student_course_semester'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at jdk.proxy4/jdk.proxy4.$Proxy126.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy124.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy123.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 163 common frames omitted
2025-08-01 15:04:06.961 [http-nio-8080-exec-8] INFO  c.example.service.score.impl.GradeInputServiceImpl - 多课程成绩导入完成: 总数=50, 成功=0, 失败=50
2025-08-01 15:04:49.540 [http-nio-8080-exec-6] INFO  c.example.service.score.impl.GradeInputServiceImpl - 开始导入多课程成绩: classCode=2023054801, courseCodes=[0502901, 0502906, 0503901, 0708901, 1102901, 3002901, 3012905, H303, 0708902, 1102902, 1101901, 0502911, 0502926, 1101902, 3007903, 0602041, 0504906, 05051118, 0505117, 3004901, 04049020, 0404910, 3007904, 0002902, 05159001, 0005930, 05149015, 1103901, 0505938, 3003901, 05059011, 3013902, 0502943, 0505128, 0505127, 0502947, 05159008, 3007905, 05059092, 05159003, 3201901, 3002902], semesterId=null, fileName=课程成绩导入模板.xlsx
2025-08-01 15:04:49.665 [http-nio-8080-exec-6] ERROR c.example.service.score.impl.GradeInputServiceImpl - 批量保存成绩失败
org.springframework.dao.DuplicateKeyException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry '************-0005930-3-0' for key 'grades.uk_student_course_semester'
### The error may exist in com/example/mapper/score/GradeInputMapper.java (best guess)
### The error may involve com.example.mapper.score.GradeInputMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO grades  ( student_id, course_code, semester_id, final_score,  is_retake,  created_by, created_at, updated_by, updated_at )  VALUES (  ?, ?, ?, ?,  ?,  ?, ?, ?, ?  )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry '************-0005930-3-0' for key 'grades.uk_student_course_semester'
; Duplicate entry '************-0005930-3-0' for key 'grades.uk_student_course_semester'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:254)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy119.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy3/jdk.proxy3.$Proxy147.insert(Unknown Source)
	at com.example.service.score.impl.GradeInputServiceImpl.processMultiCourseImportData(GradeInputServiceImpl.java:824)
	at com.example.service.score.impl.GradeInputServiceImpl.importMultiCourseGrades(GradeInputServiceImpl.java:573)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.example.service.score.impl.GradeInputServiceImpl$$SpringCGLIB$$0.importMultiCourseGrades(<generated>)
	at com.example.controller.score.GradeInputController.importMultiCourseGrades(GradeInputController.java:209)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.example.controller.score.GradeInputController$$SpringCGLIB$$0.importMultiCourseGrades(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.example.filter.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:64)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1570)
Caused by: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry '************-0005930-3-0' for key 'grades.uk_student_course_semester'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at jdk.proxy4/jdk.proxy4.$Proxy126.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy124.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy123.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 163 common frames omitted
2025-08-01 15:04:49.665 [http-nio-8080-exec-6] INFO  c.example.service.score.impl.GradeInputServiceImpl - 多课程成绩导入完成: 总数=45, 成功=0, 失败=45
2025-08-01 15:05:17.505 [http-nio-8080-exec-5] INFO  c.example.service.score.impl.GradeInputServiceImpl - 开始导入多课程成绩: classCode=2023054801, courseCodes=[0502901, 0502906, 0503901, 0708901, 1102901, 3002901, 3012905, H303, 0708902, 1102902, 1101901, 0502911, 0502926, 1101902, 3007903, 0602041, 0504906, 05051118, 0505117, 3004901, 04049020, 0404910, 3007904, 0002902, 05159001, 0005930, 05149015, 1103901, 0505938, 3003901, 05059011, 3013902, 0502943, 0505128, 0505127, 0502947, 05159008, 3007905, 05059092, 05159003, 3201901, 3002902], semesterId=null, fileName=课程成绩导入模板.xlsx
2025-08-01 15:05:17.595 [http-nio-8080-exec-5] ERROR c.example.service.score.impl.GradeInputServiceImpl - 批量保存成绩失败
org.springframework.dao.DuplicateKeyException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry '************-0005930-3-0' for key 'grades.uk_student_course_semester'
### The error may exist in com/example/mapper/score/GradeInputMapper.java (best guess)
### The error may involve com.example.mapper.score.GradeInputMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO grades  ( student_id, course_code, semester_id, final_score,  is_retake,  created_by, created_at, updated_by, updated_at )  VALUES (  ?, ?, ?, ?,  ?,  ?, ?, ?, ?  )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry '************-0005930-3-0' for key 'grades.uk_student_course_semester'
; Duplicate entry '************-0005930-3-0' for key 'grades.uk_student_course_semester'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:254)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy119.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy3/jdk.proxy3.$Proxy147.insert(Unknown Source)
	at com.example.service.score.impl.GradeInputServiceImpl.processMultiCourseImportData(GradeInputServiceImpl.java:824)
	at com.example.service.score.impl.GradeInputServiceImpl.importMultiCourseGrades(GradeInputServiceImpl.java:573)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.example.service.score.impl.GradeInputServiceImpl$$SpringCGLIB$$0.importMultiCourseGrades(<generated>)
	at com.example.controller.score.GradeInputController.importMultiCourseGrades(GradeInputController.java:209)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.example.controller.score.GradeInputController$$SpringCGLIB$$0.importMultiCourseGrades(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.example.filter.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:64)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1570)
Caused by: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry '************-0005930-3-0' for key 'grades.uk_student_course_semester'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at jdk.proxy4/jdk.proxy4.$Proxy126.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy124.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy123.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 163 common frames omitted
2025-08-01 15:05:17.595 [http-nio-8080-exec-5] INFO  c.example.service.score.impl.GradeInputServiceImpl - 多课程成绩导入完成: 总数=44, 成功=0, 失败=44
2025-08-01 15:06:16.778 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 15:06:16.783 [Thread-6] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 15:06:16.784 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:06:16.788 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 15:06:16.788 [Thread-6] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 15:06:16.795 [Thread-6] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-08-01 15:06:16.798 [Thread-6] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-08-01 15:06:16.914 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 18884 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 15:06:16.914 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 15:06:17.406 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 15:06:17.406 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:06:17.406 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 15:06:17.406 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 15:06:17.422 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 15:06:17.422 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 505 ms
2025-08-01 15:06:17.807 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited
2025-08-01 15:06:18.597 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 15:06:18.624 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: f2d4b2e3-1646-4a7f-8186-cc6761addfce

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 15:06:18.629 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 15:06:18.859 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 15:06:18.876 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 15:06:18.876 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 15:06:18.876 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 2.018 seconds (process running for 319.757)
2025-08-01 15:06:18.890 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 15:07:29.719 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 2 class path changes (0 additions, 2 deletions, 0 modifications)
2025-08-01 15:07:29.719 [Thread-14] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 15:07:29.719 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:07:29.719 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 15:07:29.719 [Thread-14] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 15:07:29.719 [Thread-14] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ...
2025-08-01 15:07:29.719 [Thread-14] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed
2025-08-01 15:07:29.789 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 18884 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 15:07:29.789 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 15:07:30.185 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 15:07:30.185 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:07:30.185 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 15:07:30.185 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 15:07:30.213 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 15:07:30.213 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 424 ms
2025-08-01 15:07:30.539 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-3} inited
2025-08-01 15:07:30.933 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'gradeInputController' defined in file [C:\Users\<USER>\Desktop\vite3\student-backend\target\classes\com\example\controller\score\GradeInputController.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'com.example.service.score.GradeInputService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-08-01 15:07:30.933 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-3} closing ...
2025-08-01 15:07:30.933 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-3} closed
2025-08-01 15:07:30.934 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-01 15:07:30.939 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-01 15:07:30.953 [restartedMain] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.example.controller.score.GradeInputController required a bean of type 'com.example.service.score.GradeInputService' that could not be found.


Action:

Consider defining a bean of type 'com.example.service.score.GradeInputService' in your configuration.

2025-08-01 15:07:32.453 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 18884 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 15:07:32.463 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 15:07:32.769 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 15:07:32.770 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:07:32.770 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 15:07:32.770 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 15:07:32.788 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 15:07:32.788 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 322 ms
2025-08-01 15:07:33.048 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-4} inited
2025-08-01 15:07:33.819 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 15:07:33.838 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 8ca325ea-f22d-44ae-a55d-515a4fbced78

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 15:07:33.839 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 15:07:34.030 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 15:07:34.049 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 15:07:34.051 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 15:07:34.057 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.63 seconds (process running for 394.925)
2025-08-01 15:07:34.058 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 15:07:38.853 [http-nio-8080-exec-2] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 15:07:38.853 [http-nio-8080-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 15:07:38.854 [http-nio-8080-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-01 15:07:38.869 [http-nio-8080-exec-2] INFO  c.example.service.score.impl.GradeInputServiceImpl - 开始导入多课程成绩: classCode=2023054801, courseCodes=[0502901, 0502906, 0503901, 0708901, 1102901, 3002901, 3012905, H303, 0708902, 1102902, 1101901, 0502911, 0502926, 1101902, 3007903, 0602041, 0504906, 05051118, 0505117, 3004901, 04049020, 0404910, 3007904, 0002902, 05159001, 0005930, 05149015, 1103901, 0505938, 3003901, 05059011, 3013902, 0502943, 0505128, 0505127, 0502947, 05159008, 3007905, 05059092, 05159003, 3201901, 3002902], semesterId=null, fileName=课程成绩导入模板.xlsx
2025-08-01 15:07:49.099 [http-nio-8080-exec-2] INFO  c.example.service.score.impl.GradeInputServiceImpl - 批量保存成绩成功，数量: 1351
2025-08-01 15:07:49.099 [http-nio-8080-exec-2] INFO  c.example.service.score.impl.GradeInputServiceImpl - 多课程成绩导入完成: 总数=44, 成功=1351, 失败=-1307
2025-08-01 15:08:12.222 [http-nio-8080-exec-3] INFO  c.example.service.score.impl.GradeInputServiceImpl - 开始导入多课程成绩: classCode=2023054801, courseCodes=[0502901, 0502906, 0503901, 0708901, 1102901, 3002901, 3012905, H303, 0708902, 1102902, 1101901, 0502911, 0502926, 1101902, 3007903, 0602041, 0504906, 05051118, 0505117, 3004901, 04049020, 0404910, 3007904, 0002902, 05159001, 0005930, 05149015, 1103901, 0505938, 3003901, 05059011, 3013902, 0502943, 0505128, 0505127, 0502947, 05159008, 3007905, 05059092, 05159003, 3201901, 3002902], semesterId=null, fileName=课程成绩导入模板.xlsx
2025-08-01 15:08:21.160 [http-nio-8080-exec-3] INFO  c.example.service.score.impl.GradeInputServiceImpl - 批量保存成绩成功，数量: 1351
2025-08-01 15:08:21.160 [http-nio-8080-exec-3] INFO  c.example.service.score.impl.GradeInputServiceImpl - 多课程成绩导入完成: 总数=44, 成功=1351, 失败=-1307
2025-08-01 15:08:32.327 [http-nio-8080-exec-5] INFO  c.example.service.score.impl.GradeInputServiceImpl - 开始导入多课程成绩: classCode=2023054801, courseCodes=[0502901, 0502906, 0503901, 0708901, 1102901, 3002901, 3012905, H303, 0708902, 1102902, 1101901, 0502911, 0502926, 1101902, 3007903, 0602041, 0504906, 05051118, 0505117, 3004901, 04049020, 0404910, 3007904, 0002902, 05159001, 0005930, 05149015, 1103901, 0505938, 3003901, 05059011, 3013902, 0502943, 0505128, 0505127, 0502947, 05159008, 3007905, 05059092, 05159003, 3201901, 3002902], semesterId=null, fileName=课程成绩导入模板.xlsx
2025-08-01 15:08:41.647 [http-nio-8080-exec-5] INFO  c.example.service.score.impl.GradeInputServiceImpl - 批量保存成绩成功，数量: 1351
2025-08-01 15:08:41.647 [http-nio-8080-exec-5] INFO  c.example.service.score.impl.GradeInputServiceImpl - 多课程成绩导入完成: 总数=44, 成功=1351, 失败=-1307
2025-08-01 15:10:39.835 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 15:10:39.837 [Thread-19] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 15:10:39.837 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:10:39.840 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 15:10:39.840 [Thread-19] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 15:10:39.842 [Thread-19] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-4} closing ...
2025-08-01 15:10:39.844 [Thread-19] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-4} closed
2025-08-01 15:10:39.955 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 18884 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 15:10:39.955 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 15:10:40.493 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 15:10:40.494 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:10:40.495 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 15:10:40.495 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 15:10:40.519 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 15:10:40.519 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 562 ms
2025-08-01 15:10:40.879 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-5} inited
2025-08-01 15:10:41.697 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 15:10:41.721 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 128d46b8-fc83-4423-b866-f39ecbb225cb

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 15:10:41.722 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 15:10:41.962 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 15:10:41.982 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 15:10:41.984 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 15:10:41.989 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 2.097 seconds (process running for 582.859)
2025-08-01 15:10:41.991 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 15:11:00.256 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 15:11:00.256 [Thread-28] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 15:11:00.261 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:11:00.261 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 15:11:00.261 [Thread-28] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 15:11:00.275 [Thread-28] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-5} closing ...
2025-08-01 15:11:00.275 [Thread-28] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-5} closed
2025-08-01 15:11:00.394 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 18884 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 15:11:00.410 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 15:11:01.001 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 15:11:01.001 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:11:01.001 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 15:11:01.001 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 15:11:01.048 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 15:11:01.048 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 638 ms
2025-08-01 15:11:01.538 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-6} inited
2025-08-01 15:11:03.001 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 15:11:03.035 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: e1fe87a2-ff79-4fb3-86fa-6d7a2b96c734

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 15:11:03.035 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 15:11:03.508 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 15:11:03.541 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 15:11:03.546 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 15:11:03.546 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 3.183 seconds (process running for 604.426)
2025-08-01 15:11:03.546 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 15:11:25.436 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 15:11:25.436 [Thread-33] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 15:11:25.436 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:11:25.436 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 15:11:25.436 [Thread-33] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 15:11:25.448 [Thread-33] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-6} closing ...
2025-08-01 15:11:25.448 [Thread-33] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-6} closed
2025-08-01 15:11:25.551 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 18884 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 15:11:25.556 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 15:11:25.955 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 15:11:25.955 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:11:25.955 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 15:11:25.955 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 15:11:25.998 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 15:11:25.998 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 442 ms
2025-08-01 15:11:26.489 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-7} inited
2025-08-01 15:11:28.165 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 15:11:28.198 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: f6cfed64-9ee9-4d0b-9866-d86752ca3d42

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 15:11:28.198 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 15:11:28.692 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 15:11:28.721 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 15:11:28.726 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 15:11:28.726 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 3.214 seconds (process running for 629.605)
2025-08-01 15:11:28.726 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 15:11:48.559 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 15:11:48.559 [Thread-38] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 15:11:48.559 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:11:48.559 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 15:11:48.559 [Thread-38] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 15:11:48.559 [Thread-38] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-7} closing ...
2025-08-01 15:11:48.572 [Thread-38] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-7} closed
2025-08-01 15:11:48.719 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 18884 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 15:11:48.719 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 15:11:49.317 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 15:11:49.317 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:11:49.317 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 15:11:49.317 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 15:11:49.338 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 15:11:49.338 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 619 ms
2025-08-01 15:11:49.930 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-8} inited
2025-08-01 15:11:51.429 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 15:11:51.461 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 7ade2ca3-4bc6-468f-a112-cf750a9cdaac

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 15:11:51.477 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 15:11:51.912 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 15:11:51.928 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 15:11:51.961 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 15:11:51.977 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 3.29 seconds (process running for 652.854)
2025-08-01 15:11:51.977 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 15:12:15.811 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 2 class path changes (0 additions, 2 deletions, 0 modifications)
2025-08-01 15:12:15.811 [Thread-43] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 15:12:15.811 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:12:15.811 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 15:12:15.811 [Thread-43] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 15:12:15.811 [Thread-43] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-8} closing ...
2025-08-01 15:12:15.811 [Thread-43] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-8} closed
2025-08-01 15:12:15.923 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 18884 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 15:12:15.923 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 15:12:16.530 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 15:12:16.531 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:12:16.531 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 15:12:16.531 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 15:12:16.573 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 15:12:16.573 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 650 ms
2025-08-01 15:12:17.118 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-9} inited
2025-08-01 15:12:17.752 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'gradeInputController' defined in file [C:\Users\<USER>\Desktop\vite3\student-backend\target\classes\com\example\controller\score\GradeInputController.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'com.example.service.score.GradeInputService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-08-01 15:12:17.752 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-9} closing ...
2025-08-01 15:12:17.752 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-9} closed
2025-08-01 15:12:17.752 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-01 15:12:17.752 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-01 15:12:17.768 [restartedMain] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.example.controller.score.GradeInputController required a bean of type 'com.example.service.score.GradeInputService' that could not be found.


Action:

Consider defining a bean of type 'com.example.service.score.GradeInputService' in your configuration.

2025-08-01 15:12:20.350 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 18884 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 15:12:20.350 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 15:12:20.638 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 15:12:20.638 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:12:20.639 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 15:12:20.639 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 15:12:20.659 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-2].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 15:12:20.659 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 308 ms
2025-08-01 15:12:20.907 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-10} inited
2025-08-01 15:12:21.737 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 15:12:21.756 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: ca3c3696-a715-4751-824f-a5135e418b8d

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 15:12:21.757 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 15:12:21.938 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 15:12:21.956 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 15:12:21.959 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 15:12:21.964 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.629 seconds (process running for 682.832)
2025-08-01 15:12:21.965 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 15:12:24.977 [http-nio-8080-exec-2] INFO  o.a.c.c.ContainerBase.[Tomcat-2].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 15:12:24.977 [http-nio-8080-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 15:12:24.978 [http-nio-8080-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-01 15:12:33.732 [http-nio-8080-exec-3] INFO  c.example.service.score.impl.GradeInputServiceImpl - 开始导入多课程成绩: classCode=2023054801, courseCodes=[0502901, 0502906, 0503901, 0708901, 1102901, 3002901, 3012905, H303, 0708902, 1102902, 1101901, 0502911, 0502926, 1101902, 3007903, 0602041, 0504906, 05051118, 0505117, 3004901, 04049020, 0404910, 3007904, 0002902, 05159001, 0005930, 05149015, 1103901, 0505938, 3003901, 05059011, 3013902, 0502943, 0505128, 0505127, 0502947, 05159008, 3007905, 05059092, 05159003, 3201901, 3002902], semesterId=null, fileName=课程成绩导入模板.xlsx
2025-08-01 15:12:42.635 [http-nio-8080-exec-3] INFO  c.example.service.score.impl.GradeInputServiceImpl - 批量保存成绩成功，数量: 1351
2025-08-01 15:12:42.635 [http-nio-8080-exec-3] INFO  c.example.service.score.impl.GradeInputServiceImpl - 多课程成绩导入完成: 总数=1351, 成功=1351, 失败=0
2025-08-01 15:13:57.281 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 15:13:57.286 [Thread-48] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 15:13:57.286 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:13:57.309 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 15:13:57.310 [Thread-48] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 15:13:57.312 [Thread-48] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-10} closing ...
2025-08-01 15:13:57.312 [Thread-48] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-10} closed
2025-08-01 15:13:57.424 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 18884 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 15:13:57.424 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 15:13:57.868 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 15:13:57.868 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:13:57.868 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 15:13:57.868 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 15:13:57.897 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-2].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 15:13:57.900 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 469 ms
2025-08-01 15:13:58.366 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-11} inited
2025-08-01 15:14:00.341 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 15:14:00.382 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: e1507168-e293-44cb-b791-683d4bbe5295

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 15:14:00.384 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 15:14:00.819 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 15:14:00.858 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 15:14:00.861 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 15:14:00.870 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 3.482 seconds (process running for 781.74)
2025-08-01 15:14:00.870 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 15:15:03.222 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat-2].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 15:15:03.222 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 15:15:03.222 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-08-01 15:15:10.346 [http-nio-8080-exec-3] INFO  c.example.service.score.impl.GradeInputServiceImpl - 开始导入多课程成绩: classCode=2023054801, courseCodes=[0502901, 0502906, 0503901, 0708901, 1102901, 3002901, 3012905, H303, 0708902, 1102902, 1101901, 0502911, 0502926, 1101902, 3007903, 0602041, 0504906, 05051118, 0505117, 3004901, 04049020, 0404910, 3007904, 0002902, 05159001, 0005930, 05149015, 1103901, 0505938, 3003901, 05059011, 3013902, 0502943, 0505128, 0505127, 0502947, 05159008, 3007905, 05059092, 05159003, 3201901, 3002902], semesterId=null, fileName=课程成绩导入模板.xlsx
2025-08-01 15:15:19.240 [http-nio-8080-exec-3] INFO  c.example.service.score.impl.GradeInputServiceImpl - 批量保存成绩成功，数量: 1351
2025-08-01 15:15:19.240 [http-nio-8080-exec-3] INFO  c.example.service.score.impl.GradeInputServiceImpl - 多课程成绩导入完成: 总数=1351, 成功=1351, 失败=0
2025-08-01 15:16:31.279 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 2 class path changes (0 additions, 2 deletions, 0 modifications)
2025-08-01 15:16:31.280 [Thread-57] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 15:16:31.280 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:16:31.285 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 15:16:31.286 [Thread-57] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 15:16:31.288 [Thread-57] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-11} closing ...
2025-08-01 15:16:31.289 [Thread-57] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-11} closed
2025-08-01 15:16:31.350 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 18884 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 15:16:31.351 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 15:16:31.673 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 15:16:31.673 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:16:31.673 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 15:16:31.673 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 15:16:31.692 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-2].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 15:16:31.693 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 340 ms
2025-08-01 15:16:31.970 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-12} inited
2025-08-01 15:16:32.365 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'gradeInputController' defined in file [C:\Users\<USER>\Desktop\vite3\student-backend\target\classes\com\example\controller\score\GradeInputController.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'com.example.service.score.GradeInputService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-08-01 15:16:32.365 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-12} closing ...
2025-08-01 15:16:32.366 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-12} closed
2025-08-01 15:16:32.366 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-01 15:16:32.371 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-01 15:16:32.373 [restartedMain] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.example.controller.score.GradeInputController required a bean of type 'com.example.service.score.GradeInputService' that could not be found.


Action:

Consider defining a bean of type 'com.example.service.score.GradeInputService' in your configuration.

2025-08-01 15:16:33.869 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 18884 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 15:16:33.869 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 15:16:34.158 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 15:16:34.159 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:16:34.159 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 15:16:34.159 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 15:16:34.176 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-3].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 15:16:34.176 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 305 ms
2025-08-01 15:16:34.419 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-13} inited
2025-08-01 15:16:35.302 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 15:16:35.318 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: db835ba7-4386-40a5-8a88-6e2146e4c71b

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 15:16:35.320 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 15:16:35.505 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 15:16:35.522 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 15:16:35.524 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 15:16:35.529 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.675 seconds (process running for 936.398)
2025-08-01 15:16:35.530 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 15:16:39.013 [http-nio-8080-exec-2] INFO  o.a.c.c.ContainerBase.[Tomcat-3].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 15:16:39.013 [http-nio-8080-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 15:16:39.014 [http-nio-8080-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-01 15:16:47.364 [http-nio-8080-exec-3] INFO  c.example.service.score.impl.GradeInputServiceImpl - 开始导入多课程成绩: classCode=2023054801, courseCodes=[0502901, 0502906, 0503901, 0708901, 1102901, 3002901, 3012905, H303, 0708902, 1102902, 1101901, 0502911, 0502926, 1101902, 3007903, 0602041, 0504906, 05051118, 0505117, 3004901, 04049020, 0404910, 3007904, 0002902, 05159001, 0005930, 05149015, 1103901, 0505938, 3003901, 05059011, 3013902, 0502943, 0505128, 0505127, 0502947, 05159008, 3007905, 05059092, 05159003, 3201901, 3002902], semesterId=null, fileName=课程成绩导入模板.xlsx
2025-08-01 15:16:56.368 [http-nio-8080-exec-3] INFO  c.example.service.score.impl.GradeInputServiceImpl - 批量保存成绩成功，数量: 1351
2025-08-01 15:16:56.368 [http-nio-8080-exec-3] INFO  c.example.service.score.impl.GradeInputServiceImpl - 多课程成绩导入完成: 总数=1351, 成功=1351, 失败=0
2025-08-01 15:17:40.405 [http-nio-8080-exec-10] INFO  c.example.service.score.impl.GradeInputServiceImpl - 生成多课程成绩导入模板: classCode=2023054801, courseCodes=[H303], semesterId=1
2025-08-01 15:18:05.202 [http-nio-8080-exec-1] INFO  c.example.service.score.impl.GradeInputServiceImpl - 开始导入多课程成绩: classCode=2023054801, courseCodes=[H303], semesterId=1, fileName=课程成绩导入模板 (1).xlsx
2025-08-01 15:18:05.297 [http-nio-8080-exec-1] INFO  c.example.service.score.impl.GradeInputServiceImpl - 批量保存成绩成功，数量: 1
2025-08-01 15:18:05.297 [http-nio-8080-exec-1] INFO  c.example.service.score.impl.GradeInputServiceImpl - 多课程成绩导入完成: 总数=1, 成功=1, 失败=0
2025-08-01 15:20:44.731 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 15:20:44.731 [Thread-63] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 15:20:44.731 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:20:44.737 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 15:20:44.737 [Thread-63] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 15:20:44.738 [Thread-63] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-13} closing ...
2025-08-01 15:20:44.738 [Thread-63] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-13} closed
2025-08-01 15:20:44.850 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 18884 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 15:20:44.850 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 15:20:45.240 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 15:20:45.253 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:20:45.253 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 15:20:45.253 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 15:20:45.259 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-3].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 15:20:45.259 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 409 ms
2025-08-01 15:20:45.618 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-14} inited
2025-08-01 15:20:46.291 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 15:20:46.310 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 50b3dc56-fd97-41b6-8881-51e91f0af52c

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 15:20:46.310 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 15:20:46.513 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 15:20:46.544 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 15:20:46.544 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 15:20:46.544 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.739 seconds (process running for 1187.421)
2025-08-01 15:20:46.544 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 15:21:06.576 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 15:21:06.576 [Thread-72] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 15:21:06.576 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:21:06.576 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 15:21:06.576 [Thread-72] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 15:21:06.576 [Thread-72] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-14} closing ...
2025-08-01 15:21:06.576 [Thread-72] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-14} closed
2025-08-01 15:21:06.655 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 18884 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 15:21:06.655 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 15:21:06.946 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 15:21:06.946 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:21:06.946 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 15:21:06.946 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 15:21:06.965 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-3].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 15:21:06.965 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 310 ms
2025-08-01 15:21:07.208 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-15} inited
2025-08-01 15:21:07.826 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 15:21:07.848 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 08952954-da3f-45f9-ba47-cd906333eb71

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 15:21:07.850 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 15:21:08.038 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 15:21:08.049 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 15:21:08.059 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 15:21:08.059 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.417 seconds (process running for 1208.932)
2025-08-01 15:21:08.059 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 15:21:59.123 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 15:21:59.124 [Thread-77] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 15:21:59.124 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:21:59.128 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 15:21:59.128 [Thread-77] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 15:21:59.130 [Thread-77] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-15} closing ...
2025-08-01 15:21:59.131 [Thread-77] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-15} closed
2025-08-01 15:21:59.206 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 18884 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 15:21:59.207 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 15:21:59.514 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 15:21:59.514 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:21:59.515 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 15:21:59.515 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 15:21:59.536 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-3].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 15:21:59.536 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 328 ms
2025-08-01 15:21:59.787 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-16} inited
2025-08-01 15:22:00.433 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 15:22:00.454 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: e55446f4-8029-4de1-9cf9-3894a8136d08

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 15:22:00.456 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 15:22:00.669 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 15:22:00.683 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 15:22:00.683 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 15:22:00.691 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.506 seconds (process running for 1261.561)
2025-08-01 15:22:00.691 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 15:23:05.742 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 15:23:05.743 [Thread-82] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 15:23:05.743 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:23:05.749 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 15:23:05.749 [Thread-82] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 15:23:05.751 [Thread-82] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-16} closing ...
2025-08-01 15:23:05.752 [Thread-82] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-16} closed
2025-08-01 15:23:05.816 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 18884 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 15:23:05.817 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 15:23:06.127 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 15:23:06.128 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:23:06.128 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 15:23:06.128 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 15:23:06.149 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-3].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 15:23:06.149 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 331 ms
2025-08-01 15:23:06.391 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-17} inited
2025-08-01 15:23:07.071 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 15:23:07.092 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 59ea48c6-e628-47b8-bcf1-1d73fac872d8

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 15:23:07.094 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 15:23:07.300 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 15:23:07.317 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 15:23:07.319 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 15:23:07.321 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.522 seconds (process running for 1328.192)
2025-08-01 15:23:07.321 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 15:23:38.863 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 15:23:38.864 [Thread-87] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 15:23:38.864 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:23:38.869 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 15:23:38.869 [Thread-87] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 15:23:38.871 [Thread-87] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-17} closing ...
2025-08-01 15:23:38.872 [Thread-87] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-17} closed
2025-08-01 15:23:38.946 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 18884 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 15:23:38.946 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 15:23:39.230 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 15:23:39.230 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:23:39.230 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 15:23:39.230 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 15:23:39.250 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-3].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 15:23:39.250 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 304 ms
2025-08-01 15:23:39.509 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-18} inited
2025-08-01 15:23:40.161 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 15:23:40.174 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 6330bbb3-1e7f-4c62-ae68-b04e6d4dd747

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 15:23:40.181 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 15:23:40.368 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 15:23:40.387 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 15:23:40.390 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 15:23:40.390 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.461 seconds (process running for 1361.264)
2025-08-01 15:23:40.396 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 15:31:06.774 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat-3].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 15:31:06.774 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 15:31:06.774 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-08-01 15:31:06.774 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 15:31:06.774 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 15:31:06.774 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 15:31:06.792 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 15:31:07.219 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 15:31:07.219 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 15:31:07.220 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 15:31:07.220 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 15:36:16.802 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 4 class path changes (0 additions, 4 deletions, 0 modifications)
2025-08-01 15:36:16.802 [Thread-92] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 15:36:16.802 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:36:16.802 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 15:36:16.802 [Thread-92] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 15:36:16.802 [Thread-92] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-18} closing ...
2025-08-01 15:36:16.802 [Thread-92] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-18} closed
2025-08-01 15:36:16.945 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 18884 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 15:36:16.945 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 15:36:17.542 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 15:36:17.542 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:36:17.542 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 15:36:17.542 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 15:36:17.558 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-3].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 15:36:17.558 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 613 ms
2025-08-01 15:36:18.019 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-19} inited
2025-08-01 15:36:19.159 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 15:36:19.183 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: a7deebc3-b584-4a3e-9ffb-322a995a709d

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 15:36:19.184 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 15:36:19.435 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 15:36:19.458 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 15:36:19.460 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 15:36:19.467 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 2.595 seconds (process running for 2120.336)
2025-08-01 15:36:19.469 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 15:36:20.924 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 4 class path changes (4 additions, 0 deletions, 0 modifications)
2025-08-01 15:36:20.926 [Thread-103] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 15:36:20.926 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:36:20.929 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 15:36:20.929 [Thread-103] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 15:36:20.932 [Thread-103] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-19} closing ...
2025-08-01 15:36:20.932 [Thread-103] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-19} closed
2025-08-01 15:36:21.004 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 18884 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 15:36:21.004 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 15:36:21.324 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 15:36:21.324 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 15:36:21.324 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 15:36:21.324 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 15:36:21.348 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-3].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 15:36:21.348 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 342 ms
2025-08-01 15:36:21.629 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-20} inited
2025-08-01 15:36:22.517 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 15:36:22.535 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: b3ff749c-1e8e-4461-a011-951edfc7ee52

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 15:36:22.536 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 15:36:22.727 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 15:36:22.745 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 15:36:22.748 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 15:36:22.752 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.77 seconds (process running for 2123.621)
2025-08-01 15:36:22.754 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 16:17:23.979 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat-3].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 16:17:23.979 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 16:17:23.982 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-08-01 16:17:24.211 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:17:24.211 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:17:24.217 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:17:24.273 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:17:24.732 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:17:24.732 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:17:24.732 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:17:24.732 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:18:12.320 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:18:12.320 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:18:12.320 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:18:12.320 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:18:12.805 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:18:12.806 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:18:12.807 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:18:12.807 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:19:04.721 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:19:04.721 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:19:04.721 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:19:04.722 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:19:05.087 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:19:05.087 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:19:05.087 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:19:05.087 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:20:25.932 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:20:25.932 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:20:25.932 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:20:25.932 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:20:26.293 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:20:26.293 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:20:26.293 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:20:26.294 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:23:45.514 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:23:45.514 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:23:45.514 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:23:45.520 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:24:59.742 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:24:59.742 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:24:59.743 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:24:59.743 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:25:01.423 [http-nio-8080-exec-10] ERROR c.example.common.exception.GlobalExceptionHandler - 缺少请求参数: Required request parameter 'classCode' for method parameter type String is not present
2025-08-01 16:25:01.423 [http-nio-8080-exec-10] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'classCode' for method parameter type String is not present]
2025-08-01 16:25:16.778 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:25:16.778 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:25:16.778 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:25:16.779 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:25:17.070 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:25:17.071 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:25:17.071 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:25:17.071 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:25:17.942 [http-nio-8080-exec-1] ERROR c.example.common.exception.GlobalExceptionHandler - 缺少请求参数: Required request parameter 'classCode' for method parameter type String is not present
2025-08-01 16:25:17.942 [http-nio-8080-exec-1] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'classCode' for method parameter type String is not present]
2025-08-01 16:28:59.426 [http-nio-8080-exec-5] ERROR c.example.common.exception.GlobalExceptionHandler - 缺少请求参数: Required request parameter 'classCode' for method parameter type String is not present
2025-08-01 16:28:59.428 [http-nio-8080-exec-5] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'classCode' for method parameter type String is not present]
2025-08-01 16:30:04.365 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:30:04.366 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:30:04.366 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:30:04.366 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:30:04.728 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:30:04.729 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:30:04.729 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:30:04.729 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:30:24.559 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:30:24.562 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:30:24.562 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:30:24.562 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:30:24.969 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:30:24.969 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:30:24.969 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:30:24.969 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:31:21.512 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:31:21.513 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:31:21.513 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:31:21.513 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:31:21.809 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:31:21.810 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:31:21.810 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:31:21.810 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:32:24.721 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:32:24.721 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:32:24.721 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:32:24.721 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:32:25.074 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:32:25.074 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:32:25.074 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:32:25.074 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:32:38.308 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:32:38.308 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:32:38.308 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:32:38.308 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:32:38.885 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:32:38.885 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:32:38.885 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:32:38.885 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:33:13.606 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:33:13.606 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:33:13.606 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:33:13.606 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:33:14.023 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:33:14.023 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:33:14.023 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:33:14.023 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:33:51.679 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:33:51.680 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:33:51.680 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:33:51.680 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:34:15.452 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 16:34:15.454 [Thread-108] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 16:34:15.454 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 16:34:15.457 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 16:34:15.457 [Thread-108] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 16:34:15.467 [Thread-108] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-20} closing ...
2025-08-01 16:34:15.470 [Thread-108] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-20} closed
2025-08-01 16:34:15.831 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 18884 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 16:34:15.831 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 16:34:17.152 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 16:34:17.155 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 16:34:17.155 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 16:34:17.155 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 16:34:17.209 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-3].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 16:34:17.209 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1361 ms
2025-08-01 16:34:18.197 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-21} inited
2025-08-01 16:34:20.048 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 16:34:20.104 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 786ebef1-1245-4a0d-a9e7-562fee0d0cc5

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 16:34:20.111 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 16:34:20.638 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 16:34:20.692 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 16:34:20.698 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 16:34:20.708 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 5.151 seconds (process running for 5601.582)
2025-08-01 16:34:20.708 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 16:35:12.713 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-01 16:35:12.713 [Thread-115] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 16:35:12.713 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 16:35:12.729 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 16:35:12.729 [Thread-115] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 16:35:12.733 [Thread-115] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-21} closing ...
2025-08-01 16:35:12.735 [Thread-115] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-21} closed
2025-08-01 16:35:12.919 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 18884 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 16:35:12.919 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 16:35:13.707 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 16:35:13.707 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 16:35:13.707 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 16:35:13.707 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 16:35:13.778 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-3].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 16:35:13.778 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 853 ms
2025-08-01 16:35:14.475 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-22} inited
2025-08-01 16:35:16.165 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 16:35:16.209 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 2b4f8934-619a-45b3-ab2b-d0f89bf8f3fb

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 16:35:16.211 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 16:35:16.674 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 16:35:16.724 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 16:35:16.731 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 16:35:16.740 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 3.93 seconds (process running for 5657.608)
2025-08-01 16:35:16.741 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 16:35:19.049 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat-3].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 16:35:19.049 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 16:35:19.050 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-01 16:35:19.063 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:35:19.063 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:35:19.064 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:35:19.080 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:36:01.991 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 1 deletion, 0 modifications)
2025-08-01 16:36:01.991 [Thread-120] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 16:36:01.991 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 16:36:01.997 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 16:36:01.997 [Thread-120] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 16:36:01.999 [Thread-120] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-22} closing ...
2025-08-01 16:36:01.999 [Thread-120] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-22} closed
2025-08-01 16:36:02.064 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 18884 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 16:36:02.064 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 16:36:02.377 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 16:36:02.377 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 16:36:02.377 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 16:36:02.377 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 16:36:02.396 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-3].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 16:36:02.396 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 326 ms
2025-08-01 16:36:02.680 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-23} inited
2025-08-01 16:36:03.360 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 16:36:03.388 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: dd5501ae-d3c2-4b84-b4ce-5ceac66ea8c4

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 16:36:03.388 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 16:36:03.587 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 16:36:03.598 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 16:36:03.598 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 16:36:03.598 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.56 seconds (process running for 5704.481)
2025-08-01 16:36:03.613 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 16:36:05.042 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (1 addition, 0 deletions, 0 modifications)
2025-08-01 16:36:05.042 [Thread-125] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 16:36:05.042 [tomcat-shutdown] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-01 16:36:05.042 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-01 16:36:05.042 [Thread-125] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-01 16:36:05.042 [Thread-125] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-23} closing ...
2025-08-01 16:36:05.042 [Thread-125] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-23} closed
2025-08-01 16:36:05.120 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 18884 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 16:36:05.120 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 16:36:05.417 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 16:36:05.417 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 16:36:05.417 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 16:36:05.417 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 16:36:05.441 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-3].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 16:36:05.441 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 321 ms
2025-08-01 16:36:05.700 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-24} inited
2025-08-01 16:36:06.309 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 16:36:06.319 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 3d8aa0c5-28fe-4126-b79c-c58296ceb5d9

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 16:36:06.319 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 16:36:06.515 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 16:36:06.538 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 16:36:06.541 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 16:36:06.545 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 1.441 seconds (process running for 5707.414)
2025-08-01 16:36:06.545 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-08-01 16:38:07.900 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat-3].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 16:38:07.900 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 16:38:07.900 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-08-01 16:38:07.904 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:38:07.904 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:38:07.904 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:38:07.916 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:38:07.937 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:38:07.937 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:38:07.937 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:38:07.938 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:38:18.299 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:38:18.299 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:38:18.299 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:38:18.299 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:38:18.735 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:38:18.735 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:38:18.736 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:38:18.736 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:38:52.704 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:38:52.704 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:38:52.704 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:38:52.704 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:38:53.064 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:38:53.064 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:38:53.064 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:38:53.064 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:40:19.069 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:40:19.070 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:40:19.070 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:40:19.070 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:40:19.429 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:40:19.429 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:40:19.429 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:40:19.429 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:40:47.027 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:40:47.027 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:40:47.027 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:40:47.027 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:40:47.629 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:40:47.629 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:40:47.629 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:40:47.629 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:48:35.287 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:48:35.287 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:48:35.287 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:48:35.287 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:50:53.919 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:50:53.919 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:50:53.919 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:50:53.919 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:50:54.325 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:50:54.325 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:50:54.325 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:50:54.325 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:51:46.740 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:51:46.740 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:51:46.740 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:51:46.740 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:51:47.283 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:51:47.283 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:51:47.283 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:51:47.283 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:52:06.079 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:52:06.079 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:52:06.079 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:52:06.079 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:52:06.109 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:52:06.109 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:52:06.109 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:52:06.109 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:52:28.483 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:52:28.483 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:52:28.483 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:52:28.483 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:52:28.767 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:52:28.767 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:52:28.767 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:52:28.767 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:52:45.200 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:52:45.201 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:52:45.201 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:52:45.201 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:52:45.551 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:52:45.551 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:52:45.551 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:52:45.551 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:58:42.785 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:58:42.785 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:58:42.785 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:58:42.785 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:58:43.098 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:58:43.098 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:58:43.098 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:58:43.098 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:59:07.587 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:59:07.587 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:59:07.587 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:59:07.587 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:59:07.756 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:59:07.756 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:59:07.756 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:59:07.756 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:59:18.238 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:59:18.238 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:59:18.238 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:59:18.238 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 16:59:18.582 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 16:59:18.582 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 16:59:18.582 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 16:59:18.582 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:02:15.992 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:02:15.992 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:02:15.992 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:02:15.992 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:03:18.628 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:03:18.629 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:03:18.629 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:03:18.629 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:03:18.717 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:03:18.717 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:03:18.718 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:03:18.718 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:04:22.000 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:04:22.000 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:04:22.000 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:04:22.002 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:04:22.314 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:04:22.314 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:04:22.314 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:04:22.315 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:05:22.536 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:05:22.536 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:05:22.536 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:05:22.536 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:05:23.693 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:05:23.693 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:05:23.693 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:05:23.693 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:06:37.010 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:06:37.011 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:06:37.011 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:06:37.011 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:06:37.629 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:06:37.629 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:06:37.629 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:06:37.629 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:06:55.672 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:06:55.672 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:06:55.672 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:06:55.672 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:06:56.832 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:06:56.832 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:06:56.832 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:06:56.832 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:07:08.907 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:07:08.907 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:07:08.907 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:07:08.907 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:07:09.246 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:07:09.246 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:07:09.246 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:07:09.246 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:21:14.143 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:21:14.143 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:21:14.143 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:21:14.143 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:21:14.347 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:21:14.347 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:21:14.347 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:21:14.347 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:22:05.030 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:22:05.030 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:22:05.030 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:22:05.030 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:22:05.430 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:22:05.430 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:22:05.430 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:22:05.430 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:24:02.035 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:24:02.035 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:24:02.035 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:24:02.035 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:24:02.451 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:24:02.451 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:24:02.451 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:24:02.451 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:24:15.535 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:24:15.535 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:24:15.535 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:24:15.535 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:24:15.931 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:24:15.931 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:24:15.931 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:24:15.931 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:24:30.501 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:24:30.501 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:24:30.501 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:24:30.501 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:24:30.610 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:24:30.610 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:24:30.610 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:24:30.610 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:26:46.821 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:26:46.822 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:26:46.822 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:26:46.822 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:26:47.047 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:26:47.047 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:26:47.047 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:26:47.047 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:27:01.336 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:27:01.336 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:27:01.336 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:27:01.336 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:27:01.760 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:27:01.760 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:27:01.760 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:27:01.760 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:27:17.347 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:27:17.347 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:27:17.347 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:27:17.347 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:27:17.623 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:27:17.623 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:27:17.623 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:27:17.623 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:27:32.215 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:27:32.215 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:27:32.215 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:27:32.215 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:27:32.900 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:27:32.900 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:27:32.900 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:27:32.900 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:27:50.374 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:27:50.374 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:27:50.374 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:27:50.374 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:27:50.679 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:27:50.679 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:27:50.679 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:27:50.679 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:28:06.672 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:28:06.672 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:28:06.672 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:28:06.672 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:28:07.717 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:28:07.717 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:28:07.717 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:28:07.717 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:28:18.887 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:28:18.887 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:28:18.887 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:28:18.888 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:28:20.153 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:28:20.153 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:28:20.153 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:28:20.153 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:28:37.184 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:28:37.184 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:28:37.184 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:28:37.185 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:28:38.041 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:28:38.041 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:28:38.041 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:28:38.041 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:29:49.635 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:29:49.635 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:29:49.635 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:29:49.635 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:29:50.091 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:29:50.091 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:29:50.091 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:29:50.091 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:34:35.943 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:34:35.943 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:34:35.944 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:34:35.944 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:34:36.720 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:34:36.720 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:34:36.720 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:34:36.720 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:34:57.481 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:34:57.481 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:34:57.481 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:34:57.481 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:34:57.550 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:34:57.550 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:34:57.550 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:34:57.550 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:36:42.100 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:36:42.100 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:36:42.100 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:36:42.100 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:36:42.739 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:36:42.739 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:36:42.739 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:36:42.739 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:36:58.915 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:36:58.915 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:36:58.915 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:36:58.915 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:36:59.453 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:36:59.453 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:36:59.453 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:36:59.454 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:37:29.981 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:37:29.981 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:37:29.982 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:37:29.982 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:37:30.764 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:37:30.764 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:37:30.764 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:37:30.764 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:38:41.671 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:38:41.671 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:38:41.671 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:38:41.671 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:38:42.555 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:38:42.555 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:38:42.555 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:38:42.556 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:38:59.193 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:38:59.194 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:38:59.194 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:38:59.194 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:39:00.291 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:39:00.291 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:39:00.291 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:39:00.291 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:41:38.685 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:41:38.685 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:41:38.685 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:41:38.685 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:41:39.398 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:41:39.398 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:41:39.398 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:41:39.398 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:41:56.784 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:41:56.784 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:41:56.784 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:41:56.784 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:41:57.906 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:41:57.906 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:41:57.906 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:41:57.906 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:42:19.909 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:42:19.909 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:42:19.909 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:42:19.909 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:42:20.510 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:42:20.510 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:42:20.510 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:42:20.511 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:42:38.957 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:42:38.957 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:42:38.957 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:42:38.957 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:42:40.140 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:42:40.140 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:42:40.140 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:42:40.140 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:42:56.203 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:42:56.203 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:42:56.203 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:42:56.203 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:42:56.948 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:42:56.948 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:42:56.948 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:42:56.948 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:43:09.822 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:43:09.822 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:43:09.822 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:43:09.822 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:43:10.766 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:43:10.766 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:43:10.766 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:43:10.766 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:47:35.711 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:47:35.711 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:47:35.711 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:47:35.711 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:47:35.898 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:47:35.898 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:47:35.898 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:47:35.898 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:47:58.162 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:47:58.162 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:47:58.162 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:47:58.162 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:47:58.687 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:47:58.687 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:47:58.687 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:47:58.687 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:48:30.744 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:48:30.744 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:48:30.744 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:48:30.744 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:48:31.365 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:48:31.365 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:48:31.365 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:48:31.365 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:57:05.575 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:57:05.576 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:57:05.576 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:57:05.576 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:57:06.163 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:57:06.163 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:57:06.163 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:57:06.163 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:57:37.710 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:57:37.711 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:57:37.711 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:57:37.711 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:57:37.903 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:57:37.903 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:57:37.903 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:57:37.904 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:57:50.423 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:57:50.423 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:57:50.423 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:57:50.423 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:57:50.709 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:57:50.709 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:57:50.709 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:57:50.709 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:58:03.660 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:58:03.660 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:58:03.660 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:58:03.660 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 17:58:03.714 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 17:58:03.714 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 17:58:03.714 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 17:58:03.714 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:01:33.626 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:01:33.626 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:01:33.626 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:01:33.627 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:01:34.816 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:01:34.816 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:01:34.816 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:01:34.816 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:02:58.508 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:02:58.509 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:02:58.509 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:02:58.509 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:02:58.535 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:02:58.535 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:02:58.535 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:02:58.535 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:03:23.539 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:03:23.539 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:03:23.539 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:03:23.539 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:03:23.724 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:03:23.724 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:03:23.724 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:03:23.724 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:03:50.262 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:03:50.262 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:03:50.262 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:03:50.262 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:03:51.003 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:03:51.003 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:03:51.003 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:03:51.003 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:04:14.912 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:04:14.912 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:04:14.912 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:04:14.912 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:04:15.605 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:04:15.606 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:04:15.606 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:04:15.606 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:04:39.874 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:04:39.874 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:04:39.874 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:04:39.874 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:04:40.820 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:04:40.820 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:04:40.820 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:04:40.820 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:07:57.955 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:07:57.956 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:07:57.956 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:07:57.956 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:07:58.540 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:07:58.540 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:07:58.540 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:07:58.540 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:08:12.632 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:08:12.633 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:08:12.633 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:08:12.633 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:08:21.628 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:08:21.628 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:08:21.628 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:08:21.628 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:08:45.216 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:08:45.216 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:08:45.216 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:08:45.216 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:09:00.815 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:09:00.815 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:09:00.815 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:09:00.815 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:09:01.470 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:09:01.470 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:09:01.470 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:09:01.470 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:09:20.287 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:09:20.287 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:09:20.287 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:09:20.287 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:09:20.997 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:09:20.997 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:09:20.997 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:09:20.997 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:09:35.303 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:09:35.304 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:09:35.304 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:09:35.304 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:09:35.474 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:09:35.474 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:09:35.474 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:09:35.474 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:09:50.195 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:09:50.195 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:09:50.195 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:09:50.195 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:09:50.643 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:09:50.643 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:09:50.643 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:09:50.643 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:11:00.412 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:11:00.412 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:11:00.412 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:11:00.413 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:11:00.813 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:11:00.813 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:11:00.813 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:11:00.813 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:11:13.491 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:11:13.491 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:11:13.491 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:11:13.491 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:11:13.750 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:11:13.750 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:11:13.750 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:11:13.750 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:11:26.337 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:11:26.337 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:11:26.337 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:11:26.337 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:11:27.122 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:11:27.122 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:11:27.122 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:11:27.122 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:20:04.283 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:20:04.283 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:20:04.284 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:20:04.284 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:37:22.825 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:37:22.825 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:37:22.825 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:37:22.825 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:37:23.510 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:37:23.510 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:37:23.510 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:37:23.510 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:37:37.754 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:37:37.754 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:37:37.754 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:37:37.754 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:37:38.842 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:37:38.842 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:37:38.842 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:37:38.842 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:37:52.100 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:37:52.101 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:37:52.101 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:37:52.101 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:37:54.330 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:37:54.330 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:37:54.330 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:37:54.330 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:38:03.940 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:38:03.940 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:38:03.940 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:38:03.940 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:38:04.355 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:38:04.355 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:38:04.355 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:38:04.355 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:38:15.911 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:38:15.911 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:38:15.911 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:38:15.911 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:38:16.447 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:38:16.447 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:38:16.447 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:38:16.447 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:39:04.723 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:39:04.723 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:39:04.723 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:39:04.723 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:39:05.466 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:39:05.466 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:39:05.466 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:39:05.466 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:39:33.672 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:39:33.672 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:39:33.672 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:39:33.672 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 18:39:34.115 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 18:39:34.115 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 18:39:34.115 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 18:39:34.115 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:05:58.304 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:05:58.304 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:05:58.304 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:05:58.304 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:07:46.988 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:07:46.988 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:07:46.988 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:07:46.988 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:07:47.177 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:07:47.177 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:07:47.179 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:07:47.179 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:17:27.371 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:17:27.371 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:17:27.371 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:17:27.371 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:37:27.922 [http-nio-8080-exec-4] WARN  com.example.config.PerformanceConfig - 慢方法执行: AuthServiceImpl.refreshToken(..) 耗时: 281ms
2025-08-01 19:37:27.922 [http-nio-8080-exec-4] WARN  com.example.config.PerformanceConfig - 慢接口: AuthController.refreshToken(..) 耗时: 281ms
2025-08-01 19:37:28.042 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:37:28.042 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:37:28.042 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:37:28.042 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:37:28.139 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:37:28.139 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:37:28.139 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:37:28.139 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:51:02.739 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:51:02.739 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:51:02.739 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:51:02.739 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:51:03.295 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:51:03.295 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:51:03.295 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:51:03.295 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:51:23.213 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:51:23.213 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:51:23.213 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:51:23.213 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:51:23.627 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:51:23.627 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:51:23.628 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:51:23.628 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:51:37.397 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:51:37.397 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:51:37.397 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:51:37.398 [http-nio-8080-exec-6] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:51:38.034 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:51:38.034 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:51:38.035 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:51:38.035 [http-nio-8080-exec-8] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:51:58.581 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:51:58.581 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:51:58.581 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:51:58.581 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:51:59.645 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:51:59.645 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:51:59.645 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:51:59.646 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:52:17.766 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:52:17.766 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:52:17.766 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:52:17.766 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:52:18.480 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:52:18.480 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:52:18.480 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:52:18.480 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:52:36.139 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:52:36.139 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:52:36.139 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:52:36.139 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:52:36.732 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:52:36.732 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:52:36.733 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:52:36.733 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:52:51.934 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:52:51.934 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:52:51.934 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:52:51.934 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:52:52.535 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:52:52.535 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:52:52.535 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:52:52.535 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:53:06.477 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:53:06.477 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:53:06.477 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:53:06.477 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:53:07.709 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:53:07.709 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:53:07.709 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:53:07.709 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:56:45.418 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:56:45.419 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:56:45.419 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:56:45.419 [http-nio-8080-exec-7] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:56:45.530 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:56:45.531 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:56:45.531 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:56:45.531 [http-nio-8080-exec-9] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:56:57.069 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:56:57.069 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:56:57.069 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:56:57.069 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:56:57.218 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:56:57.218 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:56:57.218 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:56:57.218 [http-nio-8080-exec-5] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:57:10.864 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:57:10.864 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:57:10.864 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:57:10.864 [http-nio-8080-exec-3] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 19:57:11.666 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 19:57:11.666 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 19:57:11.666 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 19:57:11.666 [http-nio-8080-exec-2] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 20:04:41.985 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 20:04:41.985 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 20:04:41.985 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 20:04:41.985 [http-nio-8080-exec-4] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 20:04:42.281 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 20:04:42.281 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 20:04:42.281 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 20:04:42.281 [http-nio-8080-exec-10] INFO  com.example.controller.AuthController - 获取到24个路由
2025-08-01 20:56:27.443 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 20:56:27.470 [restartedMain] INFO  com.example.StudentManagementApplication - Starting StudentManagementApplication using Java 22.0.1 with PID 25756 (C:\Users\<USER>\Desktop\vite3\student-backend\target\classes started by 张 in C:\Users\<USER>\Desktop\vite3\student-backend)
2025-08-01 20:56:27.471 [restartedMain] INFO  com.example.StudentManagementApplication - The following 1 profile is active: "test"
2025-08-01 20:56:27.505 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01 20:56:27.505 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01 20:56:28.948 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-01 20:56:28.960 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 20:56:28.962 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 20:56:28.962 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01 20:56:29.022 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 20:56:29.022 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1516 ms
2025-08-01 20:56:29.803 [restartedMain] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.entity.system.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 20:56:29.829 [restartedMain] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.entity.system.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 20:56:29.960 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-01 20:56:31.341 [restartedMain] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 20:56:31.370 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 495869d7-b8df-41ab-b803-aeb7126451b4

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 20:56:31.376 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 20:56:31.819 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-08-01 20:56:31.873 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 20:56:31.888 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-01 20:56:31.899 [restartedMain] INFO  com.example.StudentManagementApplication - Started StudentManagementApplication in 4.93 seconds (process running for 5.515)
2025-08-01 20:56:32.388 [RMI TCP Connection(1)-**********] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 20:56:32.389 [RMI TCP Connection(1)-**********] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 20:56:32.391 [RMI TCP Connection(1)-**********] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-01 21:23:11.119 [http-nio-8080-exec-2] WARN  com.example.config.PerformanceConfig - 慢方法执行: AuthServiceImpl.login(..) 耗时: 478ms
2025-08-01 21:23:11.120 [http-nio-8080-exec-2] WARN  com.example.config.PerformanceConfig - 慢接口: AuthController.login(..) 耗时: 479ms
2025-08-01 21:23:11.171 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 收到获取异步路由请求
2025-08-01 21:23:11.171 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 解析到的token: 存在
2025-08-01 21:23:11.172 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到用户ID: 1
2025-08-01 21:23:11.219 [http-nio-8080-exec-1] INFO  com.example.controller.AuthController - 获取到24个路由
