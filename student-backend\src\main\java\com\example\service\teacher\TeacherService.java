package com.example.service.teacher;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.dto.teacher.TeacherQueryDTO;
import com.example.vo.teacher.TeacherVO;

import java.util.List;

/**
 * 教师服务接口
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
public interface TeacherService {

    /**
     * 分页查询教师列表
     *
     * @param queryDTO 查询条件
     * @return 教师列表
     */
    IPage<TeacherVO> getTeacherList(TeacherQueryDTO queryDTO);

    /**
     * 获取所有教师列表
     *
     * @return 教师列表
     */
    List<TeacherVO> getAllTeachers();

    /**
     * 根据学院代码获取教师列表
     *
     * @param collegeCode 学院代码
     * @return 教师列表
     */
    List<TeacherVO> getTeachersByCollegeCode(String collegeCode);

    /**
     * 根据ID获取教师详情
     *
     * @param id 教师ID
     * @return 教师详情
     */
    TeacherVO getTeacherById(Integer id);

    /**
     * 根据教师工号获取教师信息
     *
     * @param teacherCode 教师工号
     * @return 教师信息
     */
    TeacherVO getTeacherByCode(String teacherCode);

    /**
     * 新增教师
     *
     * @param teacherVO 教师信息
     */
    void saveTeacher(TeacherVO teacherVO);

    /**
     * 更新教师
     *
     * @param teacherVO 教师信息
     */
    void updateTeacher(TeacherVO teacherVO);

    /**
     * 删除教师
     *
     * @param id 教师ID
     */
    void deleteTeacher(Integer id);

    /**
     * 检查教师工号是否存在
     *
     * @param teacherCode 教师工号
     * @param excludeId   排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean checkTeacherCodeExists(String teacherCode, Integer excludeId);
}
