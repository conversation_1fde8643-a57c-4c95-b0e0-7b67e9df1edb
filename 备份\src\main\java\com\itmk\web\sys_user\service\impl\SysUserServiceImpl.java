package com.itmk.web.sys_user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itmk.utils.PageUtils;
import com.itmk.web.sys_user.entity.PageParm;
import com.itmk.web.sys_user.entity.SysUser;
import com.itmk.web.sys_user.mapper.SysUserMapper;
import com.itmk.web.sys_user.service.SysUserService;
import com.itmk.web.sys_user_role.entity.SysUserRole;
import com.itmk.web.sys_user_role.service.SysUserRoleService;
import com.itmk.web.sys_role.entity.SysRole;
import com.itmk.web.sys_role.service.SysRoleService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {
    @Autowired
    private SysUserRoleService sysUserRoleService;
    @Autowired
    private SysRoleService sysRoleService;
    @Override
    public IPage<SysUser> list(PageParm parm) {
        //构造分页对象
        IPage<SysUser> page = PageUtils.createPage(parm.getCurrentPage(), parm.getPageSize());
        //构造查询条件
        QueryWrapper<SysUser> query = new QueryWrapper<>();
        if(StringUtils.isNotBlank(parm.getNickName())){
            query.lambda().like(SysUser::getNickName,parm.getNickName());
        }
        if(StringUtils.isNotBlank(parm.getPhone())){
            query.lambda().like(SysUser::getPhone,parm.getPhone());
        }

        // 查询用户列表
        IPage<SysUser> result = this.baseMapper.selectPage(page,query);

        // 为每个用户关联角色信息
        result.getRecords().forEach(user -> {
            // 查询用户角色关联
            QueryWrapper<SysUserRole> roleQuery = new QueryWrapper<>();
            roleQuery.lambda().eq(SysUserRole::getUserId, user.getUserId());
            SysUserRole userRole = sysUserRoleService.getOne(roleQuery);

            if (userRole != null) {
                // 查询角色信息
                SysRole role = sysRoleService.getById(userRole.getRoleId());
                if (role != null) {
                    user.setRoleId(role.getRoleId());
                    user.setRoleName(role.getRoleName());
                }
            }
        });

        return result;
    }

    @Override
    @Transactional
    public void add(SysUser user) {
        //保存用户
        int insert = this.baseMapper.insert(user);
        //保存角色
        if(insert >0){
            //角色保存
            SysUserRole role = new SysUserRole();
            role.setUserId(user.getUserId());
            role.setRoleId(user.getRoleId());
            sysUserRoleService.save(role);
        }
    }

    @Override
    @Transactional
    public void edit(SysUser user) {
        //编辑用户
        int i = this.baseMapper.updateById(user);
        //角色：先删除，重新插入
        if(i > 0){
            //先删除
            QueryWrapper<SysUserRole> query = new QueryWrapper<>();
            query.lambda().eq(SysUserRole::getUserId,user.getUserId());
            sysUserRoleService.remove(query);
            //重新插入
            SysUserRole role = new SysUserRole();
            role.setUserId(user.getUserId());
            role.setRoleId(user.getRoleId());
            sysUserRoleService.save(role);
        }
    }

    @Override
    @Transactional
    public boolean deleteUser(Long userId) {
        // 1. 删除 sys_user_role 表中的关联记录
        QueryWrapper<SysUserRole> query = new QueryWrapper<>();
        query.lambda().eq(SysUserRole::getUserId, userId);
        sysUserRoleService.remove(query);

        // 2. 删除 sys_user 表中的记录
        return this.removeById(userId);
    }
}
