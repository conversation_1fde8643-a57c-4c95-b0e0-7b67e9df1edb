<script setup lang="ts">
import { ref } from "vue";
import ReCol from "@/components/ReCol";
import { formRules } from "../utils/rule";
import type { FormProps } from "../utils/types";

const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({
    title: "新增",
    id: undefined,
    studentId: "",
    studentName: "",
    courseCode: "",
    courseName: "",
    semesterId: 0,
    semesterName: "",
    finalScore: undefined,
    gradePoint: undefined,
    isRetake: false,
    remarks: ""
  })
});

const ruleFormRef = ref();
const newFormInline = ref(props.formInline);

// 计算绩点
const calculateGradePoint = (score: number) => {
  if (score < 60) return 0;
  return Math.round(((score / 10) - 5) * 100) / 100;
};

// 监听成绩变化，自动计算绩点
const handleScoreChange = () => {
  if (newFormInline.value.finalScore && newFormInline.value.finalScore >= 0) {
    newFormInline.value.gradePoint = calculateGradePoint(newFormInline.value.finalScore);
  } else {
    newFormInline.value.gradePoint = undefined;
  }
};

function getRef() {
  return ruleFormRef.value;
}

defineExpose({ getRef });
</script>

<template>
  <el-form
    ref="ruleFormRef"
    :model="newFormInline"
    :rules="formRules"
    label-width="100px"
  >
    <el-row :gutter="30">
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="学号" prop="studentId">
          <el-input
            v-model="newFormInline.studentId"
            clearable
            placeholder="请输入学号"
            :disabled="newFormInline.title === '修改'"
          />
        </el-form-item>
      </re-col>

      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="学生姓名" prop="studentName">
          <el-input
            v-model="newFormInline.studentName"
            clearable
            placeholder="请输入学生姓名"
            :disabled="newFormInline.title === '修改'"
          />
        </el-form-item>
      </re-col>

      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="课程代码">
          <el-input
            v-model="newFormInline.courseCode"
            disabled
            placeholder="课程代码"
          />
        </el-form-item>
      </re-col>

      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="课程名称">
          <el-input
            v-model="newFormInline.courseName"
            disabled
            placeholder="课程名称"
          />
        </el-form-item>
      </re-col>

      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="学期">
          <el-input
            v-model="newFormInline.semesterName"
            disabled
            placeholder="学期"
          />
        </el-form-item>
      </re-col>

      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="期末成绩" prop="finalScore">
          <el-input-number
            v-model="newFormInline.finalScore"
            :min="0"
            :max="100"
            :precision="1"
            placeholder="请输入期末成绩"
            style="width: 100%"
            @change="handleScoreChange"
          />
        </el-form-item>
      </re-col>

      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="绩点">
          <el-input-number
            v-model="newFormInline.gradePoint"
            :min="0"
            :max="5"
            :precision="2"
            disabled
            placeholder="自动计算"
            style="width: 100%"
          />
        </el-form-item>
      </re-col>

      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="是否重修">
          <el-switch
            v-model="newFormInline.isRetake"
            inline-prompt
            active-text="是"
            inactive-text="否"
          />
        </el-form-item>
      </re-col>

      <re-col :value="24">
        <el-form-item label="备注">
          <el-input
            v-model="newFormInline.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </re-col>
    </el-row>
  </el-form>
</template>
