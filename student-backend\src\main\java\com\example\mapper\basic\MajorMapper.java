package com.example.mapper.basic;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.dto.basic.MajorQueryDTO;
import com.example.entity.basic.Major;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 专业Mapper
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Mapper
public interface MajorMapper extends BaseMapper<Major> {

    /**
     * 分页查询专业列表
     *
     * @param page 分页参数
     * @param query 查询条件
     * @return 专业列表
     */
    IPage<Major> selectMajorPage(IPage<Major> page, @Param("query") MajorQueryDTO query);

    /**
     * 查询所有专业
     *
     * @return 专业列表
     */
    List<Major> selectAllMajors();

    /**
     * 根据学院代码查询专业列表
     *
     * @param collegeCode 学院代码
     * @return 专业列表
     */
    List<Major> selectMajorsByCollegeCode(@Param("collegeCode") String collegeCode);

    /**
     * 根据专业代码查询专业
     *
     * @param majorCode 专业代码
     * @return 专业信息
     */
    Major selectByMajorCode(@Param("majorCode") String majorCode);

    /**
     * 检查专业代码是否存在
     *
     * @param majorCode 专业代码
     * @param excludeId 排除的专业ID
     * @return 是否存在
     */
    Boolean existsByMajorCode(@Param("majorCode") String majorCode, @Param("excludeId") Integer excludeId);

    /**
     * 检查专业名称是否存在
     *
     * @param majorName 专业名称
     * @param excludeId 排除的专业ID
     * @return 是否存在
     */
    Boolean existsByMajorName(@Param("majorName") String majorName, @Param("excludeId") Integer excludeId);

    /**
     * 根据学院代码统计专业数量
     *
     * @param collegeCode 学院代码
     * @return 专业数量
     */
    Integer countByCollegeCode(@Param("collegeCode") String collegeCode);
}
