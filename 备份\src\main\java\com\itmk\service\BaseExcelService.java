package com.itmk.service;

import com.itmk.utils.excel.ExcelConfig;
import com.itmk.utils.excel.ExcelUtils;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 通用Excel导入导出服务基类
 * 提供标准的Excel导入导出功能模板
 */
public abstract class BaseExcelService<T> {

    /**
     * 导出Excel
     * @param dataList 要导出的数据列表
     * @param config Excel配置
     * @return Excel文件的字节数组
     */
    public byte[] exportExcel(List<T> dataList, ExcelConfig<T> config) throws IOException {
        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet(config.getSheetName());
        
        // 创建样式
        CellStyle headerStyle = ExcelUtils.createHeaderStyle(workbook);
        CellStyle dataStyle = ExcelUtils.createDataStyle(workbook);
        
        // 创建标题行
        String[] headers = config.getFullHeaders();
        createHeaderRow(sheet, headers, headerStyle);
        
        // 创建数据行
        int rowIndex = 1;
        for (int i = 0; i < dataList.size(); i++) {
            T entity = dataList.get(i);
            Row row = sheet.createRow(rowIndex++);
            Object[] values = config.getFullDataRow(entity, i + 1);
            
            for (int j = 0; j < values.length; j++) {
                Cell cell = row.createCell(j);
                ExcelUtils.setCellValue(cell, values[j], dataStyle);
            }
        }
        
        // 自动调整列宽
        ExcelUtils.autoSizeColumns(sheet, headers.length);
        
        // 转换为字节数组
        try (java.io.ByteArrayOutputStream out = new java.io.ByteArrayOutputStream()) {
            workbook.write(out);
            workbook.close();
            return out.toByteArray();
        }
    }

    /**
     * 导入Excel
     * @param file 上传的Excel文件
     * @param config Excel配置
     * @return 导入结果
     */
    public ImportResult<T> importExcel(MultipartFile file, ExcelConfig<T> config) throws IOException {
        ImportResult<T> result = new ImportResult<>();
        
        // 验证文件
        if (file == null || file.isEmpty()) {
            result.setSuccess(false);
            result.setMessage("文件不能为空");
            return result;
        }
        
        if (!ExcelUtils.isValidExcelFile(file.getOriginalFilename())) {
            result.setSuccess(false);
            result.setMessage("文件格式不正确，请上传Excel文件(.xlsx或.xls)");
            return result;
        }
        
        try (Workbook workbook = WorkbookFactory.create(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            
            // 验证表头
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                result.setSuccess(false);
                result.setMessage("Excel文件格式错误：缺少表头");
                return result;
            }
            
            String[] expectedHeaders = config.getFullHeaders();
            if (!validateHeaders(headerRow, expectedHeaders)) {
                result.setSuccess(false);
                result.setMessage("Excel文件表头格式不正确，请使用正确的模板");
                return result;
            }
            
            // 读取数据
            List<T> successList = new ArrayList<>();
            List<ImportError> errorList = new ArrayList<>();
            
            int lastRowNum = sheet.getLastRowNum();
            for (int i = 1; i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);
                if (row == null || isEmptyRow(row)) {
                    continue;
                }
                
                try {
                    Object[] rowData = extractRowData(row, expectedHeaders.length);
                    Object[] entityData = config.extractEntityData(rowData);
                    T entity = config.getDataBuilder().apply(entityData);
                    
                    // 验证数据
                    if (config.getValidator() != null) {
                        ExcelConfig.ValidationResult validation = config.getValidator().apply(entity);
                        if (!validation.isValid()) {
                            errorList.add(new ImportError(i + 1, validation.getErrorMessage()));
                            continue;
                        }
                    }
                    
                    successList.add(entity);
                } catch (Exception e) {
                    errorList.add(new ImportError(i + 1, "数据解析失败: " + e.getMessage()));
                }
            }
            
            result.setSuccess(true);
            result.setSuccessCount(successList.size());
            result.setErrorCount(errorList.size());
            result.setSuccessData(successList);
            result.setErrors(errorList);
            result.setMessage(String.format("导入完成：成功%d条，失败%d条", 
                                           successList.size(), errorList.size()));
            
            return result;
        }
    }

    /**
     * 生成Excel模板
     * @param config Excel配置
     * @return Excel模板的字节数组
     */
    public byte[] generateTemplate(ExcelConfig<T> config) throws IOException {
        return ExcelUtils.createTemplate(config.getFullHeaders(), config.getSheetName());
    }

    /**
     * 创建表头行
     */
    private void createHeaderRow(Sheet sheet, String[] headers, CellStyle headerStyle) {
        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            ExcelUtils.setCellValue(cell, headers[i], headerStyle);
        }
    }

    /**
     * 验证表头
     */
    private boolean validateHeaders(Row headerRow, String[] expectedHeaders) {
        if (headerRow.getLastCellNum() < expectedHeaders.length) {
            return false;
        }
        
        for (int i = 0; i < expectedHeaders.length; i++) {
            Cell cell = headerRow.getCell(i);
            String cellValue = ExcelUtils.getCellValueAsString(cell);
            if (!expectedHeaders[i].equals(cellValue)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 提取行数据
     */
    private Object[] extractRowData(Row row, int columnCount) {
        Object[] data = new Object[columnCount];
        for (int i = 0; i < columnCount; i++) {
            Cell cell = row.getCell(i);
            data[i] = ExcelUtils.getCellValueAsString(cell);
        }
        return data;
    }

    /**
     * 检查是否为空行
     */
    private boolean isEmptyRow(Row row) {
        if (row == null) {
            return true;
        }
        
        for (int i = 0; i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                String cellValue = ExcelUtils.getCellValueAsString(cell);
                if (cellValue != null && !cellValue.trim().isEmpty()) {
                    return false;
                }
            }
        }
        
        return true;
    }

    /**
     * 导入结果类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImportResult<T> {
        private boolean success;
        private String message;
        private int successCount;
        private int errorCount;
        private List<T> successData;
        private List<ImportError> errors;
    }

    /**
     * 导入错误信息类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImportError {
        private int rowNumber;
        private String errorMessage;
    }
}
