import { reactive } from "vue";
import type { FormRules } from "element-plus";

/** 班级课程分配表单验证规则 */
export const classCourseFormRules = reactive<FormRules>({
  classCode: [
    {
      required: true,
      message: "请选择班级",
      trigger: "change"
    }
  ],
  courseCode: [
    {
      required: true,
      message: "请选择课程",
      trigger: "change"
    }
  ],
  semesterId: [
    {
      required: true,
      message: "请选择学期",
      trigger: "change"
    }
  ],

  sortOrder: [
    {
      required: true,
      message: "请输入排序号",
      trigger: "blur"
    },
    {
      type: "number",
      min: 0,
      message: "排序号不能小于0",
      trigger: "blur"
    }
  ]
});