package com.example.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.entity.system.SysRoleMenu;
import com.example.entity.system.SysMenu;
import com.example.entity.system.SysRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色菜单关联Mapper
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Mapper
public interface SysRoleMenuMapper extends BaseMapper<SysRoleMenu> {

    /**
     * 根据角色ID查询菜单ID列表
     *
     * @param roleId 角色ID
     * @return 菜单ID列表
     */
    List<Integer> selectMenuIdsByRoleId(@Param("roleId") Integer roleId);

    /**
     * 根据菜单ID查询角色ID列表
     *
     * @param menuId 菜单ID
     * @return 角色ID列表
     */
    List<Integer> selectRoleIdsByMenuId(@Param("menuId") Integer menuId);

    /**
     * 检查角色菜单关系是否存在
     *
     * @param roleId 角色ID
     * @param menuId 菜单ID
     * @return 是否存在
     */
    Boolean existsRoleMenu(@Param("roleId") Integer roleId, @Param("menuId") Integer menuId);

    /**
     * 根据角色ID删除角色菜单关系
     *
     * @param roleId 角色ID
     */
    void deleteByRoleId(@Param("roleId") Integer roleId);

    /**
     * 根据菜单ID删除角色菜单关系
     *
     * @param menuId 菜单ID
     */
    void deleteByMenuId(@Param("menuId") Integer menuId);

    /**
     * 删除指定角色菜单关系
     *
     * @param roleId 角色ID
     * @param menuId 菜单ID
     */
    void deleteRoleMenu(@Param("roleId") Integer roleId, @Param("menuId") Integer menuId);

    /**
     * 批量插入角色菜单关系
     *
     * @param roleMenus 角色菜单关系列表
     */
    void batchInsert(@Param("roleMenus") List<SysRoleMenu> roleMenus);

    /**
     * 批量删除角色菜单关系
     *
     * @param roleMenus 角色菜单关系列表
     */
    void batchDelete(@Param("roleMenus") List<SysRoleMenu> roleMenus);

    /**
     * 根据角色ID列表删除角色菜单关系
     *
     * @param roleIds 角色ID列表
     */
    void deleteByRoleIds(@Param("roleIds") List<Integer> roleIds);

    /**
     * 根据菜单ID列表删除角色菜单关系
     *
     * @param menuIds 菜单ID列表
     */
    void deleteByMenuIds(@Param("menuIds") List<Integer> menuIds);

    /**
     * 查询角色菜单关系列表
     *
     * @param roleId 角色ID
     * @param menuId 菜单ID
     * @return 角色菜单关系列表
     */
    List<SysRoleMenu> selectRoleMenuList(@Param("roleId") Integer roleId, @Param("menuId") Integer menuId);

    /**
     * 统计角色菜单关系数量
     *
     * @param roleId 角色ID
     * @param menuId 菜单ID
     * @return 关系数量
     */
    Long countRoleMenus(@Param("roleId") Integer roleId, @Param("menuId") Integer menuId);

    /**
     * 查询角色拥有的菜单详情
     *
     * @param roleId 角色ID
     * @return 菜单列表
     */
    List<SysMenu> selectMenusByRoleId(@Param("roleId") Integer roleId);

    /**
     * 查询菜单被分配给的角色详情
     *
     * @param menuId 菜单ID
     * @return 角色列表
     */
    List<SysRole> selectRolesByMenuId(@Param("menuId") Integer menuId);

    /**
     * 根据用户ID查询菜单权限
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<SysMenu> selectMenusByUserId(@Param("userId") Integer userId);

    /**
     * 批量保存角色菜单权限
     *
     * @param roleId 角色ID
     * @param menuIds 菜单ID列表
     */
    void saveRoleMenus(@Param("roleId") Integer roleId, @Param("menuIds") List<Integer> menuIds);
}
