package com.example.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.entity.system.SysUserRole;
import com.example.entity.system.SysUser;
import com.example.entity.system.SysRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户角色关联Mapper
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Mapper
public interface SysUserRoleMapper extends BaseMapper<SysUserRole> {

    /**
     * 根据用户ID查询角色ID列表
     *
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<Integer> selectRoleIdsByUserId(@Param("userId") Integer userId);

    /**
     * 根据角色ID查询用户ID列表
     *
     * @param roleId 角色ID
     * @return 用户ID列表
     */
    List<Integer> selectUserIdsByRoleId(@Param("roleId") Integer roleId);

    /**
     * 检查用户角色关系是否存在
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 是否存在
     */
    Boolean existsUserRole(@Param("userId") Integer userId, @Param("roleId") Integer roleId);

    /**
     * 根据用户ID删除用户角色关系
     *
     * @param userId 用户ID
     */
    void deleteByUserId(@Param("userId") Integer userId);

    /**
     * 根据角色ID删除用户角色关系
     *
     * @param roleId 角色ID
     */
    void deleteByRoleId(@Param("roleId") Integer roleId);

    /**
     * 删除指定用户角色关系
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     */
    void deleteUserRole(@Param("userId") Integer userId, @Param("roleId") Integer roleId);

    /**
     * 批量插入用户角色关系
     *
     * @param userRoles 用户角色关系列表
     */
    void batchInsert(@Param("userRoles") List<SysUserRole> userRoles);

    /**
     * 批量删除用户角色关系
     *
     * @param userRoles 用户角色关系列表
     */
    void batchDelete(@Param("userRoles") List<SysUserRole> userRoles);

    /**
     * 根据用户ID列表删除用户角色关系
     *
     * @param userIds 用户ID列表
     */
    void deleteByUserIds(@Param("userIds") List<Integer> userIds);

    /**
     * 根据角色ID列表删除用户角色关系
     *
     * @param roleIds 角色ID列表
     */
    void deleteByRoleIds(@Param("roleIds") List<Integer> roleIds);

    /**
     * 查询用户角色关系列表
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 用户角色关系列表
     */
    List<SysUserRole> selectUserRoleList(@Param("userId") Integer userId, @Param("roleId") Integer roleId);

    /**
     * 统计用户角色关系数量
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 关系数量
     */
    Long countUserRoles(@Param("userId") Integer userId, @Param("roleId") Integer roleId);

    /**
     * 查询拥有指定角色的用户详情
     *
     * @param roleId 角色ID
     * @return 用户列表
     */
    List<SysUser> selectUsersByRoleId(@Param("roleId") Integer roleId);

    /**
     * 查询用户拥有的角色详情
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<SysRole> selectRolesByUserId(@Param("userId") Integer userId);
}
