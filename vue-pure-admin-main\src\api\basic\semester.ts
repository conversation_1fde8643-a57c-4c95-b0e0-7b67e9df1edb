import { http } from "@/utils/http";

export interface SemesterItem {
  id?: number;
  academicYear: string;
  semesterNumber: number;
  semesterName: string;
  startDate: string;
  endDate: string;
  isCurrent: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface SemesterQueryDTO {
  academicYear?: string;
  semesterNumber?: number;
  semesterName?: string;
  isCurrent?: boolean;
  current?: number;
  size?: number;
}

export interface SemesterPageVO {
  list: SemesterItem[];
  total: number;
  pageNum: number;
  pageSize: number;
  totalPages?: number;
}

/** 获取学期分页列表 */
export const getSemesterList = (data?: SemesterQueryDTO) => {
  return http.request<{success: boolean; data: SemesterPageVO; message: string}>("post", "/api/basic/semester/list", { data });
};

/** 获取所有学期列表 */
export const getAllSemesters = () => {
  return http.request<SemesterItem[]>("get", "/api/basic/semester/all");
};

/** 保存学期 */
export const saveSemester = (data: SemesterItem) => {
  return http.request("post", "/api/basic/semester/save", { data });
};

/** 更新学期 */
export const updateSemester = (data: SemesterItem) => {
  return http.request("post", "/api/basic/semester/update", { data });
};

/** 删除学期 */
export const deleteSemester = (id: number) => {
  return http.request("post", `/api/basic/semester/delete/${id}`);
};

/** 批量删除学期 */
export const batchDeleteSemesters = (ids: number[]) => {
  return http.request("post", "/api/basic/semester/batch-delete", { data: ids });
};

/** 根据学年获取学期列表 */
export const getSemestersByYear = (academicYear: string) => {
  return http.request<SemesterItem[]>("get", `/api/basic/semester/year/${academicYear}`);
};