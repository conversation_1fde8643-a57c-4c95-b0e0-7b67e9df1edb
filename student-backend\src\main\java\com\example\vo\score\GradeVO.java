package com.example.vo.score;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 成绩VO
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Schema(description = "成绩信息")
public class GradeVO {

    @Schema(description = "成绩ID")
    private Integer id;

    @Schema(description = "学号", required = true)
    @NotBlank(message = "学号不能为空")
    private String studentId;

    @Schema(description = "学生姓名")
    private String studentName;

    @Schema(description = "班级代码")
    private String classCode;

    @Schema(description = "班级名称")
    private String className;

    @Schema(description = "专业代码")
    private String majorCode;

    @Schema(description = "专业名称")
    private String majorName;

    @Schema(description = "学院代码")
    private String collegeCode;

    @Schema(description = "学院名称")
    private String collegeName;

    @Schema(description = "课程代码", required = true)
    @NotBlank(message = "课程代码不能为空")
    private String courseCode;

    @Schema(description = "课程名称")
    private String courseName;

    @Schema(description = "课程类型")
    private String courseType;

    @Schema(description = "学分")
    private BigDecimal credits;

    @Schema(description = "学期ID", required = true)
    @NotNull(message = "学期ID不能为空")
    private Integer semesterId;

    @Schema(description = "学期名称")
    private String semesterName;

    @Schema(description = "总成绩", required = true)
    @NotNull(message = "总成绩不能为空")
    @DecimalMin(value = "0", message = "成绩不能小于0")
    @DecimalMax(value = "100", message = "成绩不能大于100")
    private BigDecimal finalScore;

    @Schema(description = "绩点")
    @DecimalMin(value = "0", message = "绩点不能小于0")
    @DecimalMax(value = "4", message = "绩点不能大于4")
    private BigDecimal gradePoint;

    @Schema(description = "是否重修")
    private Boolean isRetake;

    @Schema(description = "备注")
    private String remarks;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
}
