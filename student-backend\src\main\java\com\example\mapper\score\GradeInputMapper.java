package com.example.mapper.score;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.dto.score.GradeInputQueryDTO;
import com.example.entity.score.Grade;
import com.example.vo.score.GradeInputVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 成绩录入Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Mapper
public interface GradeInputMapper extends BaseMapper<Grade> {

    /**
     * 分页查询成绩录入列表
     *
     * @param page   分页参数
     * @param params 查询参数
     * @return 分页结果
     */
    IPage<GradeInputVO> selectGradeInputPage(Page<GradeInputVO> page, @Param("params") GradeInputQueryDTO params);

    /**
     * 根据班级和课程获取学生成绩录入列表
     *
     * @param classCode  班级代码
     * @param courseCode 课程代码
     * @param semesterId 学期ID
     * @return 学生成绩录入列表
     */
    List<GradeInputVO> selectStudentGradeInputList(@Param("classCode") String classCode,
                                                   @Param("courseCode") String courseCode,
                                                   @Param("semesterId") Integer semesterId);

    /**
     * 根据ID获取成绩详情
     *
     * @param id 成绩ID
     * @return 成绩详情
     */
    GradeInputVO selectGradeInputById(@Param("id") Integer id);

    /**
     * 检查成绩是否已存在
     *
     * @param studentId  学号
     * @param courseCode 课程代码
     * @param semesterId 学期ID
     * @return 数量
     */
    int checkGradeExists(@Param("studentId") String studentId,
                        @Param("courseCode") String courseCode,
                        @Param("semesterId") Integer semesterId);

    /**
     * 根据条件查询成绩
     *
     * @param studentId  学号
     * @param courseCode 课程代码
     * @param semesterId 学期ID
     * @return 成绩实体
     */
    Grade selectGradeByCondition(@Param("studentId") String studentId,
                                @Param("courseCode") String courseCode,
                                @Param("semesterId") Integer semesterId);
}
