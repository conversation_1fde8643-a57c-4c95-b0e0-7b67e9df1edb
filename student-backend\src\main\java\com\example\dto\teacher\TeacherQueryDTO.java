package com.example.dto.teacher;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 教师查询DTO
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Data
@Schema(description = "教师查询条件")
public class TeacherQueryDTO {

    @Schema(description = "当前页码", example = "1")
    private Integer current;

    @Schema(description = "每页大小", example = "10")
    private Integer size;

    @Schema(description = "教师工号")
    private String teacherCode;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "性别")
    private String gender;

    @Schema(description = "所属学院代码")
    private String collegeCode;

    @Schema(description = "职称")
    private String title;

    @Schema(description = "状态")
    private String status;

    @Override
    public String toString() {
        return "TeacherQueryDTO{" +
                "current=" + current +
                ", size=" + size +
                ", teacherCode='" + teacherCode + '\'' +
                ", name='" + name + '\'' +
                ", gender='" + gender + '\'' +
                ", collegeCode='" + collegeCode + '\'' +
                ", title='" + title + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
