<template>
  <div class="main-content">
    <!-- 专业列表 -->
    <MajorList
      v-if="currentView === 'majors'"
      @select-major="handleSelectMajor"
    />

    <!-- 班级列表 -->
    <ClassList
      v-if="currentView === 'classes'"
      :major-code="selectedMajor?.majorCode"
      :major-name="selectedMajor?.majorName"
      @select-class="handleSelectClass"
      @back="handleBackToMajors"
    />

    <!-- 课程选择 -->
    <CourseSelection
      v-if="currentView === 'courses'"
      :class-code="selectedClass?.classCode"
      :class-name="selectedClass?.className"
      :major-name="selectedMajor?.majorName"
      @back="handleBackToClasses"
      @select-course="handleSelectCourse"
    />

    <!-- 成绩录入 -->
    <GradeInputList
      v-if="currentView === 'input'"
      :class-code="selectedClass?.classCode"
      :class-name="selectedClass?.className"
      :major-name="selectedMajor?.majorName"
      :course-code="selectedCourse?.courseCode"
      :course-name="selectedCourse?.courseName"
      :semester-id="selectedCourse?.semesterId"
      :semester-name="selectedCourse?.semesterName"
      @back="handleBackToCourses"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElMessage } from "element-plus";
import MajorList from "../components/MajorList.vue";
import ClassList from "../components/ClassList.vue";
import CourseSelection from "./components/CourseSelection.vue";
import GradeInputList from "./components/GradeInputList.vue";

defineOptions({
  name: "GradeInput"
});

// 当前视图
const currentView = ref<'majors' | 'classes' | 'courses' | 'input'>('majors');

// 选中的专业、班级、课程
const selectedMajor = ref<{ majorCode: string; majorName: string } | null>(null);
const selectedClass = ref<{ classCode: string; className: string } | null>(null);
const selectedCourse = ref<{ courseCode: string; courseName: string; semesterId: number; semesterName: string } | null>(null);

// 选择专业
const handleSelectMajor = (major: { majorCode: string; majorName: string }) => {
  selectedMajor.value = major;
  currentView.value = 'classes';
};

// 选择班级
const handleSelectClass = (classInfo: { classCode: string; className: string }) => {
  selectedClass.value = classInfo;
  currentView.value = 'courses';
};

// 选择课程
const handleSelectCourse = (course: { courseCode: string; courseName: string; semesterId: number; semesterName: string }) => {
  selectedCourse.value = course;
  currentView.value = 'input';
};

// 返回到专业列表
const handleBackToMajors = () => {
  currentView.value = 'majors';
  selectedMajor.value = null;
  selectedClass.value = null;
  selectedCourse.value = null;
};

// 返回到班级列表
const handleBackToClasses = () => {
  currentView.value = 'classes';
  selectedClass.value = null;
  selectedCourse.value = null;
};

// 返回到课程选择页面
const handleBackToCourses = () => {
  currentView.value = 'courses';
  selectedCourse.value = null;
};


</script>


