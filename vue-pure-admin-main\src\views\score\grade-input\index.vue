<template>
  <div class="main-content">
    <!-- 面包屑导航 -->
    <el-breadcrumb separator="/" class="breadcrumb-nav">
      <el-breadcrumb-item
        v-for="(item, index) in breadcrumbs"
        :key="index"
        :class="{ 'is-link': !item.disabled }"
        @click="!item.disabled && handleBreadcrumbClick(index)"
      >
        {{ item.title }}
      </el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 表格工具栏 -->
    <PureTableBar
      :title="getTableTitle()"
      :columns="currentColumns"
      @refresh="handleRefresh"
    >
      <template #buttons>
        <el-space>
          <!-- 搜索框 - 仅在成绩列表页显示 -->
          <template v-if="currentView === 'grades'">
            <el-input
              v-model="searchForm.studentName"
              placeholder="学生姓名"
              clearable
              @input="onSearch"
              style="width: 150px"
            />
            <el-input
              v-model="searchForm.studentId"
              placeholder="学号"
              clearable
              @input="onSearch"
              style="width: 150px"
            />
            <el-button type="primary" @click="onSearch">
              搜索
            </el-button>
            <el-button @click="resetSearch">
              重置
            </el-button>
          </template>

          <!-- 操作按钮 -->
          <el-button
            v-if="currentView === 'grades'"
            type="success"
            @click="openDialog('新增')"
          >
            新增成绩
          </el-button>

          <el-button
            v-if="currentView === 'courses'"
            type="success"
            @click="openImportDialog"
          >
            成绩导入
          </el-button>

          <el-button
            v-if="currentView !== 'majors'"
            type="info"
            @click="goBack"
          >
            返回
          </el-button>
        </el-space>
      </template>

      <template v-slot="{ size, dynamicColumns }">
        <el-table
          ref="tableRef"
          v-loading="loading"
          :data="currentDataList"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          style="width: 100%"
        >
          <!-- 动态列 -->
          <el-table-column
            v-for="column in currentColumns"
            :key="column.prop || column.type"
            :type="column.type"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            :fixed="column.fixed"
            :formatter="column.formatter"
          >
            <template v-if="column.slot === 'operation'" #default="{ row }">
              <!-- 专业操作 -->
              <el-button
                v-if="currentView === 'majors'"
                type="primary"
                link
                size="small"
                @click="selectMajor(row)"
              >
                查看班级
              </el-button>

              <!-- 班级操作 -->
              <el-button
                v-if="currentView === 'classes'"
                type="primary"
                link
                size="small"
                @click="selectClass(row)"
              >
                查看课程
              </el-button>

              <!-- 课程操作 -->
              <template v-if="currentView === 'courses'">
                <el-button
                  type="primary"
                  link
                  size="small"
                  @click="selectCourse(row)"
                >
                  成绩录入
                </el-button>
              </template>

              <!-- 成绩操作 -->
              <template v-if="currentView === 'grades'">
                <el-button
                  type="primary"
                  link
                  size="small"
                  @click="openDialog('修改', row)"
                >
                  修改
                </el-button>
                <el-button
                  type="danger"
                  link
                  size="small"
                  @click="handleDelete(row)"
                >
                  删除
                </el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <el-pagination
          v-if="currentView === 'grades'"
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          style="margin-top: 16px; justify-content: flex-end"
        />
      </template>
    </PureTableBar>

    <!-- 成绩导入对话框 -->
    <MultiCourseImportDialog
      v-model="importDialogVisible"
      :class-code="currentSelection.class?.classCode || ''"
      :class-name="currentSelection.class?.className || ''"
      @success="handleImportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useGradeInput } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

import EditPen from "~icons/ep/edit-pen";
import Delete from "~icons/ep/delete";
import AddFill from "~icons/ri/add-circle-line";
import Upload from "~icons/ep/upload";
import Refresh from "~icons/ep/refresh";

// 导入组件
import MultiCourseImportDialog from "./components/MultiCourseImportDialog.vue";


defineOptions({
  name: "GradeInput"
});

const tableRef = ref();

const {
  loading,
  currentView,
  currentSelection,
  searchForm,
  pagination,
  breadcrumbs,
  currentColumns,
  currentDataList,
  selectMajor,
  selectClass,
  selectCourse,
  goBack,
  onSearch,
  resetSearch,
  openDialog,
  openImportDialog,
  handleImportSuccess,
  importDialogVisible,
  handleDelete,
  handleSizeChange,
  handleCurrentChange,
  deviceDetection
} = useGradeInput(tableRef);

// 获取表格标题
const getTableTitle = () => {
  switch (currentView.value) {
    case 'majors':
      return '专业列表';
    case 'classes':
      return `${currentSelection.major?.majorName} - 班级列表`;
    case 'courses':
      return `${currentSelection.major?.majorName} - ${currentSelection.class?.className} - 课程列表`;
    case 'grades':
      return `${currentSelection.major?.majorName} - ${currentSelection.class?.className} - ${currentSelection.course?.courseName} - 成绩列表`;
    default:
      return '成绩录入';
  }
};

// 面包屑点击处理
const handleBreadcrumbClick = (index: number) => {
  if (index === 0) {
    // 点击"成绩录入"，回到专业列表
    currentView.value = 'majors';
    currentSelection.major = undefined;
    currentSelection.class = undefined;
    currentSelection.course = undefined;
  } else if (index === 1 && currentSelection.major) {
    // 点击专业名，回到班级列表
    currentView.value = 'classes';
    currentSelection.class = undefined;
    currentSelection.course = undefined;
  } else if (index === 2 && currentSelection.class) {
    // 点击班级名，回到课程列表
    currentView.value = 'courses';
    currentSelection.course = undefined;
  }
};

// 刷新处理
const handleRefresh = () => {
  switch (currentView.value) {
    case 'majors':
      // 重新加载专业列表的逻辑在hook中处理
      break;
    case 'classes':
      if (currentSelection.major) {
        // 重新加载班级列表
      }
      break;
    case 'courses':
      if (currentSelection.class) {
        // 重新加载课程列表
      }
      break;
    case 'grades':
      if (currentSelection.course) {
        // 重新加载成绩列表
      }
      break;
  }
};
</script>

<style lang="scss" scoped>
.main-content {
  margin: 10px 0 0 !important;
}

.breadcrumb-nav {
  margin-bottom: 16px;

  .is-link {
    cursor: pointer;

    &:hover {
      color: var(--el-color-primary);
    }
  }
}
</style>


