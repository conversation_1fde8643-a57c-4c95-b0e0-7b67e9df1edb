<template>
  <div class="main">
    <!-- 查询表单 -->
    <el-form
      ref="formRef"
      :inline="true"
      :model="form"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
    >
      <el-form-item label="课程代码：" prop="courseCode">
        <el-input
          v-model="form.courseCode"
          placeholder="请输入课程代码"
          clearable
          class="!w-[200px]"
        />
      </el-form-item>
      <el-form-item label="课程名称：" prop="courseName">
        <el-input
          v-model="form.courseName"
          placeholder="请输入课程名称"
          clearable
          class="!w-[200px]"
        />
      </el-form-item>
      <el-form-item label="课程类型：" prop="courseType">
        <el-select
          v-model="form.courseType"
          placeholder="请选择课程类型"
          clearable
          class="!w-[180px]"
        >
          <el-option
            v-for="item in courseTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所属学院：" prop="collegeCode">
        <el-select
          v-model="form.collegeCode"
          placeholder="请选择学院"
          clearable
          filterable
          class="!w-[200px]"
          @change="onCollegeChange"
        >
          <el-option
            v-for="item in collegeOptions"
            :key="item.collegeCode"
            :label="item.collegeName"
            :value="item.collegeCode"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon(Search)"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon(Refresh)" @click="resetForm(formRef)">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <PureTableBar title="课程列表" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon(AddFill)"
          @click="openDialog('新增')"
        >
          新增课程
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <!-- 数据表格 -->
        <pure-table
          ref="tableRef"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small'"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @selection-change="handleSelectionChange"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon(EditPen)"
              @click="openDialog('修改', row)"
            >
              修改
            </el-button>
            <el-popconfirm
              :title="`是否确认删除课程：${row.courseName}？`"
              @confirm="handleDelete(row)"
            >
              <template #reference>
                <el-button
                  class="reset-margin"
                  link
                  type="primary"
                  :size="size"
                  :icon="useRenderIcon(Delete)"
                >
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useCourse } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

import Delete from "~icons/ep/delete";
import EditPen from "~icons/ep/edit-pen";
import Search from "~icons/ep/search";
import Refresh from "~icons/ep/refresh";
import AddFill from "~icons/ri/add-circle-line";

defineOptions({
  name: "Course"
});

const formRef = ref();

const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  collegeOptions,
  courseTypeOptions,
  onSearch,
  resetForm,
  onCollegeChange,
  openDialog,
  handleDelete,
  handleSizeChange,
  handleCurrentChange,
  handleSelectionChange
} = useCourse();
</script>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
