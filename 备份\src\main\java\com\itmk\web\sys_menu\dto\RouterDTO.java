package com.itmk.web.sys_menu.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 路由数据传输对象
 * 基于Vue Router规范设计
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RouterDTO {

    /**
     * 路由路径
     */
    private String path;

    /**
     * 组件路径
     */
    private String component;

    /**
     * 是否总是显示
     */
    private Boolean alwaysShow;

    /**
     * 路由名称
     */
    private String name;

    /**
     * 路由元信息
     */
    private Meta meta;

    /**
     * 子路由列表
     */
    private List<RouterDTO> children = new ArrayList<>();

    /**
     * 路由元信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Meta {
        /**
         * 页面标题
         */
        private String title;

        /**
         * 菜单图标
         */
        private String icon;

        /**
         * 权限角色
         */
        private String[] roles;

        /**
         * 是否隐藏
         */
        private Boolean hidden;

        /**
         * 是否缓存
         */
        private Boolean keepAlive;

        /**
         * 面包屑
         */
        private Boolean breadcrumb;

        /**
         * 权限代码
         */
        private String[] permissions;

        // Setter methods for compatibility
        public void setTitle(String title) {
            this.title = title;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public void setRoles(String[] roles) {
            this.roles = roles;
        }

        public void setPermissions(String[] permissions) {
            this.permissions = permissions;
        }
    }

    /**
     * 添加子路由
     */
    public void addChild(RouterDTO child) {
        if (children == null) {
            children = new ArrayList<>();
        }
        children.add(child);
    }

    /**
     * 是否有子路由
     */
    public boolean hasChildren() {
        return children != null && !children.isEmpty();
    }

    /**
     * 设置为Layout组件
     */
    public void setAsLayout() {
        this.component = "Layout";
        this.alwaysShow = true;
    }

    // Additional setter methods for compatibility
    public void setChildren(List<RouterDTO> children) {
        this.children = children;
    }

    public void setAlwaysShow(Boolean alwaysShow) {
        this.alwaysShow = alwaysShow;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public void setComponent(String component) {
        this.component = component;
    }

    public void setMeta(Meta meta) {
        this.meta = meta;
    }

    /**
     * 创建Meta对象的便捷方法
     */
    public static Meta createMeta(String title, String icon, String[] roles) {
        Meta meta = new Meta();
        meta.setTitle(title);
        meta.setIcon(icon);
        meta.setRoles(roles);
        return meta;
    }

    /**
     * 创建Meta对象的便捷方法（包含权限）
     */
    public static Meta createMeta(String title, String icon, String[] roles, String[] permissions) {
        Meta meta = createMeta(title, icon, roles);
        meta.setPermissions(permissions);
        return meta;
    }
}
