/*
 Navicat Premium Dump SQL

 Source Server         : localhost_3306
 Source Server Type    : MySQL
 Source Server Version : 80300 (8.3.0)
 Source Host           : localhost:3306
 Source Schema         : vue_admin

 Target Server Type    : MySQL
 Target Server Version : 80300 (8.3.0)
 File Encoding         : 65001

 Date: 28/07/2025 16:14:26
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for classes
-- ----------------------------
DROP TABLE IF EXISTS `classes`;
CREATE TABLE `classes`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '班级ID',
  `class_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '班级代码',
  `class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '班级名称',
  `major_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '所属专业代码',
  `grade_year` year NOT NULL COMMENT '入学年份',
  `student_count` int NULL DEFAULT 0 COMMENT '学生人数',
  `head_teacher_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '班主任工号',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_class_code`(`class_code` ASC) USING BTREE,
  INDEX `idx_major_code`(`major_code` ASC) USING BTREE,
  INDEX `idx_head_teacher_code`(`head_teacher_code` ASC) USING BTREE,
  INDEX `idx_grade_year`(`grade_year` ASC) USING BTREE,
  CONSTRAINT `fk_classes_head_teacher` FOREIGN KEY (`head_teacher_code`) REFERENCES `teachers` (`teacher_code`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_classes_major` FOREIGN KEY (`major_code`) REFERENCES `majors` (`major_code`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '班级信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of classes
-- ----------------------------
INSERT INTO `classes` VALUES (1, '2023054801', '过程控制231', 'JSJYYJS', 2023, 44, 'Test001', '2025-07-28 09:14:43', '2025-07-28 13:58:53');
INSERT INTO `classes` VALUES (2, '2023054802', '过程控制232', 'JSJYYJS', 2023, 0, NULL, '2025-07-28 09:14:43', '2025-07-28 09:14:43');
INSERT INTO `classes` VALUES (3, '2023054803', '过程控制233', 'JSJYYJS', 2023, 0, NULL, '2025-07-28 09:14:43', '2025-07-28 09:14:43');
INSERT INTO `classes` VALUES (4, '2024054801', '过程控制241', 'JSJYYJS', 2024, 0, NULL, '2025-07-28 09:14:43', '2025-07-28 09:14:43');

-- ----------------------------
-- Table structure for colleges
-- ----------------------------
DROP TABLE IF EXISTS `colleges`;
CREATE TABLE `colleges`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '学院ID',
  `college_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '学院代码',
  `college_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '学院名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '学院描述',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_college_code`(`college_code` ASC) USING BTREE,
  UNIQUE INDEX `uk_college_name`(`college_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '学院信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of colleges
-- ----------------------------
INSERT INTO `colleges` VALUES (1, '1112', '信息工程学院', '信息工程学院', '2025-07-27 00:02:08', '2025-07-27 00:52:09');
INSERT INTO `colleges` VALUES (2, 'test', '测试学院', '测试学院', '2025-07-27 18:39:50', '2025-07-27 18:39:50');

-- ----------------------------
-- Table structure for courses
-- ----------------------------
DROP TABLE IF EXISTS `courses`;
CREATE TABLE `courses`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '课程ID',
  `course_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '课程代码',
  `course_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '课程名称',
  `credits` decimal(4, 2) NOT NULL COMMENT '学分',
  `course_type` enum('必修','选修','实践','通识') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '必修' COMMENT '课程类型',
  `college_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所属学院代码',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '课程描述',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_course_code`(`course_code` ASC) USING BTREE,
  INDEX `idx_course_type`(`course_type` ASC) USING BTREE,
  INDEX `idx_college_code`(`college_code` ASC) USING BTREE,
  CONSTRAINT `fk_courses_college` FOREIGN KEY (`college_code`) REFERENCES `colleges` (`college_code`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 43 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '课程信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of courses
-- ----------------------------
INSERT INTO `courses` VALUES (1, '0502901', '计算机操作技术', 4.50, '必修', '1112', '', '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (2, '0502906', 'C语言程序设计(少)', 3.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (3, '0503901', '数学应用与实践', 3.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (4, '0708901', '高职实用英语Ⅰ', 5.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (5, '1102901', '体育与健康Ⅰ', 1.50, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (6, '3002901', '思想道德与法治', 3.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (7, '3012905', '军事训练', 2.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (8, 'H303', '大学生心理健康教育', 1.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (9, '0502911', '面向对象程序设计', 3.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (10, '0502926', 'C语言程序设计实训(少)', 4.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (11, '0504906', '网页设计与制作(多)', 3.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (12, '05051118', '面向对象程序设计实训', 2.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (13, '0505117', 'web前端开发实训', 2.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (14, '0602041', '石化基础', 4.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (15, '0708902', '高职实用英语Ⅱ', 3.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (16, '1101901', '大学语文', 1.50, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (17, '1101902', '国家安全教育', 1.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (18, '1102902', '体育与健康Ⅱ', 1.50, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (19, '3004901', '毛泽东思想和中国特色社会主义理论体系概论', 2.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (20, '3007903', '形势与政策（二）', 1.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (21, '0002902', '职业素养', 1.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (22, '0005930', '化工总控工取证实训', 4.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (23, '04049020', 'DCS技术及应用', 3.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (24, '0404910', '过程检测与控制', 2.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (25, '05059011', '数据库MySQL', 2.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (26, '0505938', 'Android应用程序设计', 3.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (27, '05149015', '单片机应用技术', 3.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (28, '05159001', 'UI设计基础', 3.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (29, '1103901', '美育与大学生艺术素养', 1.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (30, '3003901', '习近平新时代中国特色社会主义思想概论', 3.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (31, '3007904', '形势与政策（三）', 1.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (32, '3013902', '劳动教育', 1.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (33, '0502943', '软件测试技术', 2.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (34, '0502947', '软件工程及UML', 2.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (35, '0505127', '前端开发框架', 2.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (36, '05059092', 'java web应用程序开发', 4.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (37, '0505128', '微信小程序开发', 2.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (38, '05159008', '移动web开发', 4.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (39, '05159003', '1+X取证综合实训', 3.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (40, '3002902', '思想政治理论课实践', 1.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (41, '3007905', '形势与政策（四）', 1.00, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');
INSERT INTO `courses` VALUES (42, '3201901', '就业指导', 1.50, '必修', '1112', NULL, '2025-07-28 14:03:49', '2025-07-28 16:10:57');

-- ----------------------------
-- Table structure for majors
-- ----------------------------
DROP TABLE IF EXISTS `majors`;
CREATE TABLE `majors`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '专业ID',
  `major_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '专业代码',
  `major_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '专业名称',
  `college_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '所属学院代码',
  `duration` tinyint NOT NULL DEFAULT 3 COMMENT '学制年限',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '专业描述',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_major_code`(`major_code` ASC) USING BTREE,
  INDEX `idx_college_code`(`college_code` ASC) USING BTREE,
  CONSTRAINT `fk_majors_college` FOREIGN KEY (`college_code`) REFERENCES `colleges` (`college_code`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '专业信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of majors
-- ----------------------------
INSERT INTO `majors` VALUES (1, 'WLWGCJS', '物联网工程技术', '1112', 4, '物联网工程技术专业（本科）', '2025-07-27 10:00:00', '2025-07-27 11:15:54');
INSERT INTO `majors` VALUES (2, 'RGZNGCJS', '人工智能工程技术', '1112', 4, '人工智能工程技术专业（本科）', '2025-07-27 10:00:00', '2025-07-27 10:00:00');
INSERT INTO `majors` VALUES (3, 'DSJJS', '大数据技术', '1112', 3, '大数据技术专业', '2025-07-27 10:00:00', '2025-07-27 10:00:00');
INSERT INTO `majors` VALUES (4, 'YDYYKF', '移动应用开发', '1112', 3, '移动应用开发专业', '2025-07-27 10:00:00', '2025-07-27 10:00:00');
INSERT INTO `majors` VALUES (5, 'XXAQJSYY', '信息安全技术应用', '1112', 3, '信息安全技术应用专业', '2025-07-27 10:00:00', '2025-07-27 10:00:00');
INSERT INTO `majors` VALUES (6, 'JSJWLJS', '计算机网络技术', '1112', 3, '计算机网络技术专业', '2025-07-27 10:00:00', '2025-07-27 10:00:00');
INSERT INTO `majors` VALUES (7, 'JSJYYJS', '计算机应用技术', '1112', 3, '计算机应用技术专业（过程控制方向）', '2025-07-27 10:00:00', '2025-07-27 10:00:00');
INSERT INTO `majors` VALUES (9, '2222222222', '测试', 'test', 4, '测试', '2025-07-27 22:25:47', '2025-07-27 22:26:45');

-- ----------------------------
-- Table structure for semesters
-- ----------------------------
DROP TABLE IF EXISTS `semesters`;
CREATE TABLE `semesters`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '学期ID',
  `academic_year` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '学年',
  `semester_number` tinyint NOT NULL COMMENT '学期号',
  `semester_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '学期名称',
  `start_date` date NOT NULL COMMENT '开始日期',
  `end_date` date NOT NULL COMMENT '结束日期',
  `is_current` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否当前学期',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_academic_year_semester`(`academic_year` ASC, `semester_number` ASC) USING BTREE,
  UNIQUE INDEX `uk_semester_name`(`semester_name` ASC) USING BTREE,
  INDEX `idx_academic_year`(`academic_year` ASC) USING BTREE,
  INDEX `idx_is_current`(`is_current` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '学年学期表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of semesters
-- ----------------------------
INSERT INTO `semesters` VALUES (1, '2023-2024', 1, '2023-2024-1', '2023-09-01', '2024-01-15', 0, '2025-07-27 10:00:00', '2025-07-27 12:09:41');
INSERT INTO `semesters` VALUES (2, '2023-2024', 2, '2023-2024-2', '2024-02-26', '2024-07-15', 0, '2025-07-27 10:00:00', '2025-07-27 10:00:00');
INSERT INTO `semesters` VALUES (3, '2024-2025', 1, '2024-2025-1', '2024-09-01', '2025-01-15', 0, '2025-07-27 10:00:00', '2025-07-27 12:14:45');
INSERT INTO `semesters` VALUES (4, '2024-2025', 2, '2024-2025-2', '2025-02-24', '2025-07-15', 1, '2025-07-27 10:00:00', '2025-07-27 12:14:45');

-- ----------------------------
-- Table structure for students
-- ----------------------------
DROP TABLE IF EXISTS `students`;
CREATE TABLE `students`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '学生ID',
  `student_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '学号',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '姓名',
  `gender` enum('男','女') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '性别',
  `birth_date` date NULL DEFAULT NULL COMMENT '出生日期',
  `id_card` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '身份证号',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '电话号码',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `class_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '所属班级代码',
  `enrollment_date` date NOT NULL COMMENT '入学日期',
  `status` enum('在校','毕业','退学','休学','转学') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '在校' COMMENT '学籍状态',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_student_id`(`student_id` ASC) USING BTREE,
  INDEX `idx_class_code`(`class_code` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  CONSTRAINT `fk_students_class` FOREIGN KEY (`class_code`) REFERENCES `classes` (`class_code`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 46 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '学生信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of students
-- ----------------------------
INSERT INTO `students` VALUES (2, '201805350214', '郭世成', '男', '2000-01-01', '622201199910217555', '15193464223', '', '2023054801', '2023-09-01', '在校', '2025-07-28 08:52:05', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (3, '202008130142', '张文辉', '男', NULL, '62272720000810013X', '18193382508', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (4, '202105490101', '陈博', '男', NULL, '620503200201062311', '19193147675', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (5, '202305480101', '柏廷轩', '男', NULL, '622201200507298418', '18189610729', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (6, '202305480102', '摆金荣', '男', NULL, '620422200409161419', '18298698724', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (7, '202305480103', '包建龙', '男', NULL, '622925200503103512', '18193015081', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (8, '202305480104', '曹怡', '女', NULL, '622826200508300428', '15719659613', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (9, '202305480105', '陈积栋', '男', NULL, '62222320050105033X', '19100813250', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (10, '202305480106', '陈伊帆', '男', NULL, '622901200502056530', '18215001801', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (11, '202305480107', '丁世浩', '男', NULL, '620103200507085311', '15117257128', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (12, '202305480109', '杜俊豪', '男', NULL, '622821200412200439', '19100812092', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (13, '202305480111', '高碧霞', '女', NULL, '62112520050407072X', '18894445737', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (14, '202305480112', '何虎', '男', NULL, '622823200408020016', '15682951802', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (15, '202305480113', '何振', '男', NULL, '622722200501113338', '19893368320', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (16, '202305480114', '贺娇阳', '女', NULL, '621123200410132421', '19100815183', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (17, '202305480115', '李超', '男', NULL, '62232220040819301X', '19393520966', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (18, '202305480116', '刘畅', '男', NULL, '622421200412140315', '18893229334', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (19, '202305480117', '卢嘉馨', '女', NULL, '622801200501300428', '15109478336', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (20, '202305480118', '马宝君', '女', NULL, '620321200509281221', '13399450389', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (21, '202305480119', '马文鹏', '男', NULL, '622921200710270051', '18394040051', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (22, '202305480120', '马文宇', '男', NULL, '622723200411100716', '18309443649', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (23, '202305480122', '慕来喜', '男', NULL, '621022200405132118', '19049311582', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (24, '202305480123', '牛汝杰', '女', NULL, '620523200504302648', '17394436246', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (25, '202305480124', '邵文霞', '女', NULL, '620502200409035121', '18894581422', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (26, '202305480125', '孙柏豪', '男', NULL, '621126200405250019', '19100813224', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (27, '202305480126', '汪俊', '男', NULL, '622925200408184510', '18215138421', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (28, '202305480127', '汪淼', '女', NULL, '620502200509176829', '15193873021', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (29, '202305480128', '王彩霞', '女', NULL, '622625200411192224', '19100812171', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (30, '202305480129', '王君灵', '女', NULL, '62052220051021172X', '15009383976', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (31, '202305480130', '王意婷', '女', NULL, '622623200502110324', '15609395171', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (32, '202305480131', '王银宇', '男', NULL, '622123200405041219', '13139486073', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (33, '202305480132', '魏博翔', '男', NULL, '622421200501206814', '18719628352', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (34, '202305480133', '严新辉', '男', NULL, '622925200411034513', '15002667457', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (35, '202305480134', '杨洁', '女', NULL, '622427200309110941', '18309478477', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (36, '202305480135', '杨伟强', '男', NULL, '622429200307105033', '17748802238', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (37, '202305480136', '殷正平', '男', NULL, '623021200309251614', '15352419851', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (38, '202305480137', '张涛涛', '男', NULL, '622827200504140312', '18143744334', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (39, '202305480138', '张亚钦', '男', NULL, '622326200409225832', '19893503682', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (40, '202305480139', '赵丹', '女', NULL, '620523200503062603', '19100811894', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (41, '202305480140', '赵海星', '男', NULL, '622621200409072711', '19100811148', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (42, '202305480141', '赵鑫博', '男', NULL, '620104200411221979', '15339847829', '', '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:53');
INSERT INTO `students` VALUES (43, '202305480142', '朱小丫', '女', NULL, '620421200411243361', '18393094079', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (44, '202305480143', '朱兴成', '男', NULL, '620622200401041819', '18394394619', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');
INSERT INTO `students` VALUES (45, '202305480144', '邹子龙', '男', NULL, '51190220050317001X', '15209464593', NULL, '2023054801', '2023-09-01', '在校', '2025-07-28 13:58:18', '2025-07-28 13:58:18');

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '部门ID',
  `parent_id` int NULL DEFAULT 0 COMMENT '父部门ID',
  `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '部门名称',
  `dept_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '部门编码',
  `leader` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '负责人',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系电话',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：0禁用、1启用',
  `sort` int NULL DEFAULT 0 COMMENT '排序',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_dept_name`(`dept_name` ASC) USING BTREE,
  UNIQUE INDEX `uk_dept_code`(`dept_code` ASC) USING BTREE,
  INDEX `idx_parent_id`(`parent_id` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_sort`(`sort` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 704 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统部门表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dept
-- ----------------------------
INSERT INTO `sys_dept` VALUES (1, 0, '兰州石化职业技术大学', 'LZPUVT', '校长', '0931-7941000', '<EMAIL>', 1, 1, '兰州石化职业技术大学', '2025-07-26 10:00:00', '2025-07-26 16:42:42');
INSERT INTO `sys_dept` VALUES (54, 1, '信息工程学院', 'XXGC', '院长', '0931-7941054', '<EMAIL>', 1, 54, '信息工程专业教学', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (140, 54, '物联网工程技术系', 'WLWGCJS', '系主任', '0931-7941140', '<EMAIL>', 1, 1, '物联网工程技术专业（本科）', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (141, 54, '人工智能工程技术系', 'RGZNGCJS', '系主任', '0931-7941141', '<EMAIL>', 1, 2, '人工智能工程技术专业（本科）', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (142, 54, '大数据技术系', 'DSJJS', '系主任', '0931-7941142', '<EMAIL>', 1, 3, '大数据技术专业', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (143, 54, '移动应用开发系', 'YDYYKF', '系主任', '0931-7941143', '<EMAIL>', 1, 4, '移动应用开发专业', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (144, 54, '信息安全技术应用系', 'XXAQJSYY', '系主任', '0931-7941144', '<EMAIL>', 1, 5, '信息安全技术应用专业', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (145, 54, '计算机网络技术系', 'JSJWLJS', '系主任', '0931-7941145', '<EMAIL>', 1, 6, '计算机网络技术专业', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (146, 54, '计算机应用技术系', 'JSJYYJS', '系主任', '0931-7941146', '<EMAIL>', 1, 7, '计算机应用技术专业（过程控制方向）', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (440, 140, '物联网工程技术教研室', 'WLWGCJSJYS', '教研室主任', '0931-7941440', '<EMAIL>', 1, 1, '物联网工程技术专业教学研究', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (441, 141, '人工智能工程技术教研室', 'RGZNGCJSJYS', '教研室主任', '0931-7941441', '<EMAIL>', 1, 1, '人工智能工程技术专业教学研究', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (442, 142, '大数据技术教研室', 'DSJJSJYS', '教研室主任', '0931-7941442', '<EMAIL>', 1, 1, '大数据技术专业教学研究', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (443, 143, '移动应用开发教研室', 'YDYYKFJYS', '教研室主任', '0931-7941443', '<EMAIL>', 1, 1, '移动应用开发专业教学研究', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (444, 144, '信息安全技术应用教研室', 'XXAQJSYYJYS', '教研室主任', '0931-7941444', '<EMAIL>', 1, 1, '信息安全技术应用专业教学研究', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (445, 145, '计算机网络技术教研室', 'JSJWLJSJYS', '教研室主任', '0931-7941445', '<EMAIL>', 1, 1, '计算机网络技术专业教学研究', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (446, 146, '计算机应用技术教研室', 'JSJYYJSJYS', '教研室主任', '0931-7941446', '<EMAIL>', 1, 1, '计算机应用技术专业教学研究', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (600, 54, '信息技术实践教学中心', 'XXJSSJJXZX', '中心主任', '0931-7941600', '<EMAIL>', 1, 0, '信息技术实践教学中心，建筑面积约5000平方米', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (610, 600, '人工智能工程实训基地', 'RGZNGCSJJD', '基地主任', '0931-7941610', '<EMAIL>', 1, 1, '人工智能工程实训基地', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (611, 610, '软件技术实训室', 'RJJSSX', '实训室主任', '0931-7941611', '<EMAIL>', 1, 1, '软件技术实训室', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (612, 610, '1+X云计算实训室', 'YJSSX', '实训室主任', '0931-7941612', '<EMAIL>', 1, 2, '1+X云计算实训室', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (613, 610, 'AI应用开发实训室', 'AIYYKFSX', '实训室主任', '0931-7941613', '<EMAIL>', 1, 3, 'AI应用开发实训室', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (620, 600, '物联网工程实训基地', 'WLWGCSJJD', '基地主任', '0931-7941620', '<EMAIL>', 1, 2, '物联网工程实训基地', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (621, 620, '传感器技术实训室', 'CGQJSSX', '实训室主任', '0931-7941621', '<EMAIL>', 1, 1, '传感器技术实训室', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (622, 620, '传感网应用开发实训室', 'CGWKFSX', '实训室主任', '0931-7941622', '<EMAIL>', 1, 2, '传感网应用开发实训室', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (623, 620, '物联网虚拟仿真中心', 'WLWXNFZZX', '中心主任', '0931-7941623', '<EMAIL>', 1, 3, '物联网虚拟仿真中心', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (624, 620, '物联网工程开发实训室', 'WLWGCKFSX', '实训室主任', '0931-7941624', '<EMAIL>', 1, 4, '物联网工程开发实训室', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (625, 620, '物联网安装调试实训室', 'WLWAZTSSX', '实训室主任', '0931-7941625', '<EMAIL>', 1, 5, '物联网安装调试实训室', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (630, 600, '大数据技术实训基地', 'DSJJSSJJD', '基地主任', '0931-7941630', '<EMAIL>', 1, 3, '大数据技术实训基地', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (631, 630, '大数据开发实训室', 'DSJKFSX', '实训室主任', '0931-7941631', '<EMAIL>', 1, 1, '大数据开发实训室', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (632, 630, '大数据分析实训室', 'DSJFXSX', '实训室主任', '0931-7941632', '<EMAIL>', 1, 2, '大数据分析实训室', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (640, 600, '移动开发实训基地', 'YDKFSJJD', '基地主任', '0931-7941640', '<EMAIL>', 1, 4, '移动开发实训基地', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (641, 640, '移动开发实训室', 'YDKFSX', '实训室主任', '0931-7941641', '<EMAIL>', 1, 1, '移动开发实训室', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (642, 640, 'Web开发实训室', 'WEBKFSX', '实训室主任', '0931-7941642', '<EMAIL>', 1, 2, 'Web开发实训室', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (650, 600, '网络与安全实训基地', 'WLAQSJJD', '基地主任', '0931-7941650', '<EMAIL>', 1, 5, '网络与安全实训基地', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (651, 650, '综合布线实训室', 'ZHBLSX', '实训室主任', '0931-7941651', '<EMAIL>', 1, 1, '综合布线实训室', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (652, 650, '网络技术实训室', 'WLJSSX', '实训室主任', '0931-7941652', '<EMAIL>', 1, 2, '网络技术实训室', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (653, 650, '信息安全实训室', 'XXAQSX', '实训室主任', '0931-7941653', '<EMAIL>', 1, 3, '信息安全实训室', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (654, 650, 'ICT实训室', 'ICTSX', '实训室主任', '0931-7941654', '<EMAIL>', 1, 4, 'ICT实训室', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (655, 650, '网络攻防实训室', 'WLGFSX', '实训室主任', '0931-7941655', '<EMAIL>', 1, 5, '网络攻防实训室', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (660, 600, '信息技术实训基地', 'XXJSSJJD', '基地主任', '0931-7941660', '<EMAIL>', 1, 6, '信息技术实训基地', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (661, 660, '信息技术实训室1', 'XXJSSX1', '实训室主任', '0931-7941661', '<EMAIL>', 1, 1, '信息技术实训室1', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (662, 660, '信息技术实训室2', 'XXJSSX2', '实训室主任', '0931-7941662', '<EMAIL>', 1, 2, '信息技术实训室2', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (663, 660, '信息技术实训室3', 'XXJSSX3', '实训室主任', '0931-7941663', '<EMAIL>', 1, 3, '信息技术实训室3', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (664, 660, '实训教学研修室', 'SXJXYX', '研修室主任', '0931-7941664', '<EMAIL>', 1, 4, '实训教学研修室', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (665, 660, '硬件检测实训室', 'YJJCSX', '实训室主任', '0931-7941665', '<EMAIL>', 1, 5, '硬件检测实训室', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (666, 660, '数据库开发实训室', 'SJKKFSX', '实训室主任', '0931-7941666', '<EMAIL>', 1, 6, '数据库开发实训室', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (667, 660, 'ICT考试中心', 'ICTKSZX', '中心主任', '0931-7941667', '<EMAIL>', 1, 7, 'ICT考试中心（Pearson VUE国际认证）', '2025-07-26 10:00:00', '2025-07-26 16:23:46');
INSERT INTO `sys_dept` VALUES (668, 660, '创新创业中心', 'CXCYZX', '中心主任', '0931-7941668', '<EMAIL>', 1, 8, '创新创业中心', '2025-07-26 10:00:00', '2025-07-26 16:23:46');

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `parent_id` int NULL DEFAULT 0 COMMENT '父菜单ID，0为顶级',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单名称',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '路由名称',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '路由路径',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '组件路径',
  `menu_type` tinyint NULL DEFAULT 0 COMMENT '菜单类型：0菜单、1iframe、2外链、3按钮',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图标',
  `rank` int NULL DEFAULT 99 COMMENT '排序',
  `redirect` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '重定向路径',
  `auths` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '权限标识',
  `show_link` tinyint NULL DEFAULT 1 COMMENT '是否显示：0隐藏，1显示',
  `keep_alive` tinyint NULL DEFAULT 0 COMMENT '是否缓存：0否，1是',
  `frame_src` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'iframe地址',
  `frame_loading` tinyint NULL DEFAULT 1 COMMENT 'iframe加载状态',
  `hidden_tag` tinyint NULL DEFAULT 0 COMMENT '是否隐藏标签',
  `fixed_tag` tinyint NULL DEFAULT 0 COMMENT '是否固定标签',
  `show_parent` tinyint NULL DEFAULT 0 COMMENT '是否显示父级',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：0禁用，1启用',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 40 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '菜单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, 0, '首页', 'Home', '/welcome', 'welcome/index.vue', 0, 'ep:home-filled', 1, '', 'page:welcome', 1, 1, '', 1, 0, 0, 0, 1, '2025-07-25 01:38:59', '2025-07-27 14:59:46');
INSERT INTO `sys_menu` VALUES (2, 0, '系统管理', 'System', '/system', '', 0, 'ri:settings-3-line', 7, '/system/user/index', NULL, 1, 1, '', 1, 0, 0, 1, 1, '2025-07-25 01:38:59', '2025-07-27 12:36:57');
INSERT INTO `sys_menu` VALUES (3, 2, '用户管理', 'SystemUser', '/system/user/index', 'system/user/index.vue', 0, 'ri:admin-line', 1, '', 'system:user:list', 1, 1, '', 1, 0, 0, 1, 1, '2025-07-25 01:38:59', '2025-07-27 15:00:51');
INSERT INTO `sys_menu` VALUES (4, 2, '角色管理', 'SystemRole', '/system/role/index', 'system/role/index.vue', 0, 'ri:admin-fill', 2, NULL, 'system:role:list', 1, 1, NULL, 1, 0, 0, 0, 1, '2025-07-25 01:38:59', '2025-07-27 15:00:51');
INSERT INTO `sys_menu` VALUES (5, 2, '菜单管理', 'SystemMenu', '/system/menu/index', 'system/menu/index.vue', 0, 'ep:menu', 4, '', 'system:menu:list', 1, 1, '', 1, 0, 0, 0, 1, '2025-07-25 01:38:59', '2025-07-27 15:00:51');
INSERT INTO `sys_menu` VALUES (6, 2, '部门管理', 'SystemDept', '/system/dept/index', 'system/dept/index.vue', 0, 'ri:git-branch-line', 3, '', 'system:dept:list', 1, 1, '', 1, 0, 0, 0, 1, '2025-07-25 17:43:19', '2025-07-27 15:00:51');
INSERT INTO `sys_menu` VALUES (7, 0, '系统监控', 'Monitor', '/monitor', '', 0, 'ep:monitor', 8, '', NULL, 1, 0, '', 1, 0, 0, 1, 1, '2025-07-25 17:43:19', '2025-07-27 16:00:59');
INSERT INTO `sys_menu` VALUES (8, 7, '在线用户', 'OnlineUser', '/monitor/online-user', 'monitor/online/index', 0, 'ri:user-voice-line', 1, '', 'monitor:online:list', 1, 1, '', 1, 0, 0, 1, 1, '2025-07-25 17:43:19', '2025-07-27 16:00:59');
INSERT INTO `sys_menu` VALUES (9, 7, '登录日志', 'LoginLog', '/monitor/login-logs', 'monitor/logs/login/index', 0, 'ri:window-line', 2, '', 'monitor:loginlog:list', 1, 1, '', 1, 0, 0, 0, 1, '2025-07-25 17:43:19', '2025-07-27 16:00:59');
INSERT INTO `sys_menu` VALUES (10, 7, '操作日志', 'OperationLog', '/monitor/operation-logs', 'monitor/logs/operation/index', 0, 'ri:history-fill', 3, NULL, 'monitor:operlog:list', 1, 1, NULL, 1, 0, 0, 0, 1, '2025-07-25 17:43:19', '2025-07-27 16:00:59');
INSERT INTO `sys_menu` VALUES (11, 7, '系统日志', 'SystemLog', '/monitor/system-logs', 'monitor/logs/system/index', 0, 'ri:file-search-line', 4, '', 'monitor:syslog:list', 1, 1, '', 1, 0, 0, 1, 1, '2025-07-25 17:43:19', '2025-07-27 16:00:59');
INSERT INTO `sys_menu` VALUES (12, 0, '基础设置', 'BasicSettings', '/basic', '', 0, 'ri:settings-4-line', 2, '/basic/college/index', '', 1, 1, '', NULL, NULL, 0, 1, 1, '2025-07-27 00:10:33', '2025-07-28 08:57:20');
INSERT INTO `sys_menu` VALUES (13, 12, '学院管理', 'College', '/basic/college/index', 'basic/college/index', 0, 'ep:office-building', 2, '', 'basic:college:list', 1, 1, '', 1, 0, 0, 1, 1, '2025-07-27 00:10:33', '2025-07-27 13:08:51');
INSERT INTO `sys_menu` VALUES (14, 12, '专业管理', 'Major', '/basic/major/index', 'basic/major/index.vue', 0, 'ep:collection', 3, '', 'basic:major:list', 1, 1, '', 1, 0, 0, 0, 1, '2025-07-27 11:15:04', '2025-07-27 15:00:51');
INSERT INTO `sys_menu` VALUES (23, 12, '学年学期管理', 'Semester', '/basic/semester/index', 'basic/semester/index.vue', 0, 'ep:calendar', 1, '', 'basic:semester:list', 1, 1, '', 1, 0, 0, 0, 1, '2025-07-27 11:30:34', '2025-07-27 15:00:51');
INSERT INTO `sys_menu` VALUES (24, 0, '教师管理', 'Teacher', '/teacher', '', 0, 'ep:user', 3, '/teacher/index', 'teacher:list', 1, 1, '', 1, 0, 0, 1, 0, '2025-07-27 13:08:51', '2025-07-27 20:06:16');
INSERT INTO `sys_menu` VALUES (25, 0, '学生列表', 'Student', '/student/index', 'students/index.vue', 0, 'ep:avatar', 4, '', 'student:list', 1, 1, '', NULL, NULL, 0, 1, 1, '2025-07-27 13:08:51', '2025-07-28 16:09:52');
INSERT INTO `sys_menu` VALUES (36, 0, '教师列表', '教师管理', '/teacher/index', 'teacher/index.vue', 0, 'ep:user', 3, '', '', 1, 1, '', NULL, NULL, 0, 1, 1, '2025-07-27 14:25:24', '2025-07-27 20:06:26');
INSERT INTO `sys_menu` VALUES (37, 12, '班级管理', 'Classes', '/basic/classes', 'basic/classes/index', 0, 'ep:school', 4, '', 'basic:classes:list', 1, 1, '', NULL, NULL, 0, 1, 1, '2025-07-27 17:52:19', '2025-07-27 17:55:32');
INSERT INTO `sys_menu` VALUES (38, 0, '教务管理', '教务管理', '/course', '', 0, 'ep:histogram', 5, '/course/index', '', 1, 1, '', NULL, NULL, 0, 1, 1, '2025-07-28 09:43:45', '2025-07-28 16:09:47');
INSERT INTO `sys_menu` VALUES (39, 38, '课程管理', '课程', '/course/index', 'educational/course/index.vue', 0, '', 1, '', '', 1, 1, '', NULL, NULL, 0, 1, 1, '2025-07-28 09:46:03', '2025-07-28 14:26:14');

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色名称',
  `role_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色编码',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '描述',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：0禁用，1启用',
  `sort` int NULL DEFAULT 0 COMMENT '排序',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_role_code`(`role_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '角色表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, 'admin', 'admin', 'Administrator', 1, 0, NULL, '2025-07-25 00:48:46', '2025-07-26 22:43:13');
INSERT INTO `sys_role` VALUES (3, '学生', 'student', '学生角色，可查看个人信息、成绩、课表等', 1, 0, '学生用户角色', '2025-07-26 22:30:53', '2025-07-26 23:04:48');
INSERT INTO `sys_role` VALUES (4, '教师', 'teacher', '教师角色，可管理课程、录入成绩、查看班级信息等', 1, 0, '教师用户角色', '2025-07-26 22:30:53', '2025-07-26 23:04:48');
INSERT INTO `sys_role` VALUES (5, '班主任', 'head_teacher', '班主任角色，除教师权限外，还可管理班级学生', 1, 0, '班主任用户角色', '2025-07-26 22:30:53', '2025-07-26 23:04:48');

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `role_id` int NOT NULL COMMENT '角色ID',
  `menu_id` int NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '角色菜单关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES (1, 1);
INSERT INTO `sys_role_menu` VALUES (1, 2);
INSERT INTO `sys_role_menu` VALUES (1, 3);
INSERT INTO `sys_role_menu` VALUES (1, 4);
INSERT INTO `sys_role_menu` VALUES (1, 5);
INSERT INTO `sys_role_menu` VALUES (1, 6);
INSERT INTO `sys_role_menu` VALUES (1, 7);
INSERT INTO `sys_role_menu` VALUES (1, 8);
INSERT INTO `sys_role_menu` VALUES (1, 9);
INSERT INTO `sys_role_menu` VALUES (1, 10);
INSERT INTO `sys_role_menu` VALUES (1, 11);
INSERT INTO `sys_role_menu` VALUES (1, 12);
INSERT INTO `sys_role_menu` VALUES (1, 13);
INSERT INTO `sys_role_menu` VALUES (1, 14);
INSERT INTO `sys_role_menu` VALUES (1, 23);
INSERT INTO `sys_role_menu` VALUES (1, 24);
INSERT INTO `sys_role_menu` VALUES (1, 25);
INSERT INTO `sys_role_menu` VALUES (1, 36);
INSERT INTO `sys_role_menu` VALUES (1, 37);
INSERT INTO `sys_role_menu` VALUES (1, 38);
INSERT INTO `sys_role_menu` VALUES (1, 39);

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像URL',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机号',
  `gender` tinyint NULL DEFAULT 2 COMMENT '性别：0女、1男、2未知',
  `dept_id` int NULL DEFAULT NULL COMMENT '部门ID',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：0禁用，1启用',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `last_login_time` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '最后登录IP',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_username`(`username` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1, 'admin', '$2b$12$H50pQY6FFPwe0n9K9tXLtOgFklIDXvtKSt6ze7gyfhZuPVo9bF3oq', 'admin', 'https://avatars.githubusercontent.com/u/44761321', '<EMAIL>', '15888888888', 1, 611, 1, '', NULL, NULL, '2025-07-25 00:48:30', '2025-07-26 16:23:33');
INSERT INTO `sys_user` VALUES (2, 'user', '$2b$12$H50pQY6FFPwe0n9K9tXLtOgFklIDXvtKSt6ze7gyfhZuPVo9bF3oq', '普通用户', 'https://avatars.githubusercontent.com/u/52823142', '<EMAIL>', '15666666666', 1, 1, 1, '', NULL, NULL, '2025-07-25 00:47:07', '2025-07-26 16:25:09');

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `user_id` int NOT NULL COMMENT '用户ID',
  `role_id` int NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户角色关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES (1, 1);
INSERT INTO `sys_user_role` VALUES (2, 1);

-- ----------------------------
-- Table structure for teachers
-- ----------------------------
DROP TABLE IF EXISTS `teachers`;
CREATE TABLE `teachers`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '教师ID',
  `teacher_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '教师工号',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '姓名',
  `gender` enum('男','女') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '性别',
  `birth_date` date NULL DEFAULT NULL COMMENT '出生日期',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '电话号码',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `college_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '所属学院代码',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '职称',
  `hire_date` date NULL DEFAULT NULL COMMENT '入职日期',
  `status` enum('在职','离职','退休') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '在职' COMMENT '状态',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_teacher_code`(`teacher_code` ASC) USING BTREE,
  INDEX `idx_college_code`(`college_code` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  CONSTRAINT `fk_teachers_college` FOREIGN KEY (`college_code`) REFERENCES `colleges` (`college_code`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '教师信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of teachers
-- ----------------------------
INSERT INTO `teachers` VALUES (2, 'Test001', '王', '女', '2000-07-01', '18143744334', '<EMAIL>', '1112', '教授', '2000-07-27', '在职', '2025-07-27 18:18:35', '2025-07-28 07:23:26');

-- ----------------------------
-- View structure for v_dept_hierarchy
-- ----------------------------
DROP VIEW IF EXISTS `v_dept_hierarchy`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_dept_hierarchy` AS select `d1`.`id` AS `id`,`d1`.`dept_name` AS `dept_name`,`d1`.`dept_code` AS `dept_code`,`d1`.`leader` AS `leader`,`d1`.`phone` AS `phone`,`d1`.`email` AS `email`,`d1`.`status` AS `status`,`d1`.`sort` AS `sort`,`d1`.`remark` AS `remark`,`d1`.`create_time` AS `create_time`,`d1`.`update_time` AS `update_time`,`d1`.`parent_id` AS `parent_id`,`d2`.`dept_name` AS `parent_name`,(case when (`d1`.`parent_id` = 0) then '一级部门' when (`d2`.`parent_id` = 0) then '二级部门' when (`d3`.`parent_id` = 0) then '三级部门' when (`d4`.`parent_id` = 0) then '四级部门' else '五级及以下部门' end) AS `dept_level`,concat(coalesce(`d4`.`dept_name`,''),(case when (`d4`.`dept_name` is not null) then ' > ' else '' end),coalesce(`d3`.`dept_name`,''),(case when (`d3`.`dept_name` is not null) then ' > ' else '' end),coalesce(`d2`.`dept_name`,''),(case when (`d2`.`dept_name` is not null) then ' > ' else '' end),`d1`.`dept_name`) AS `full_path` from (((`sys_dept` `d1` left join `sys_dept` `d2` on((`d1`.`parent_id` = `d2`.`id`))) left join `sys_dept` `d3` on((`d2`.`parent_id` = `d3`.`id`))) left join `sys_dept` `d4` on((`d3`.`parent_id` = `d4`.`id`)));

SET FOREIGN_KEY_CHECKS = 1;
