package com.example.dto.system;

import lombok.Data;

/**
 * 部门查询DTO
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
public class DeptQueryDTO {

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 状态：0禁用、1启用
     */
    private Integer status;

    /**
     * 负责人
     */
    private String leader;

    /**
     * 创建时间开始
     */
    private String createTimeStart;

    /**
     * 创建时间结束
     */
    private String createTimeEnd;

    /**
     * 排序字段
     */
    private String orderBy = "sort";

    /**
     * 排序方向：asc、desc
     */
    private String orderDirection = "asc";
}
