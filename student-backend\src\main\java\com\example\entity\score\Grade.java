package com.example.entity.score;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 成绩实体类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("grades")
public class Grade {

    /**
     * 成绩ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 学号
     */
    @TableField("student_id")
    private String studentId;

    /**
     * 班级代码（不存储在数据库中，用于Excel导入时的临时存储）
     */
    @TableField(exist = false)
    private String classCode;

    /**
     * 课程代码
     */
    @TableField("course_code")
    private String courseCode;

    /**
     * 学期ID
     */
    @TableField("semester_id")
    private Integer semesterId;

    /**
     * 总成绩
     */
    @TableField("final_score")
    private BigDecimal finalScore;

    /**
     * 绩点
     */
    @TableField("grade_point")
    private BigDecimal gradePoint;

    /**
     * 是否重修
     */
    @TableField("is_retake")
    private Boolean isRetake;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新人
     */
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
