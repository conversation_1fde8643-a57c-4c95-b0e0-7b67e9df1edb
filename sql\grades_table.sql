-- ========================================
-- 期末成绩表
-- ========================================

-- 设置安全模式
SET FOREIGN_KEY_CHECKS = 0;

-- 删除已存在的表
DROP TABLE IF EXISTS `grades`;

-- 创建期末成绩表
CREATE TABLE `grades` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '成绩ID',
  `student_id` varchar(20) NOT NULL COMMENT '学号',
  `course_code` varchar(20) NOT NULL COMMENT '课程代码',
  `semester_id` int NOT NULL COMMENT '学期ID',
  `final_score` decimal(5,2) NOT NULL COMMENT '总成绩',
  `grade_point` decimal(3,2) COMMENT '绩点',
  `is_retake` tinyint(1) DEFAULT 0 COMMENT '是否重修',
  `remarks` varchar(255) COMMENT '备注',
  `created_by` varchar(50) COMMENT '创建人',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(50) COMMENT '更新人',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_student_course_semester` (`student_id`, `course_code`, `semester_id`, `is_retake`),
  KEY `idx_student_id` (`student_id`),
  KEY `idx_course_code` (`course_code`),
  KEY `idx_semester_id` (`semester_id`),
  KEY `idx_final_score` (`final_score`),
  CONSTRAINT `fk_grades_student` FOREIGN KEY (`student_id`) REFERENCES `students` (`student_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_grades_course` FOREIGN KEY (`course_code`) REFERENCES `courses` (`course_code`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_grades_semester` FOREIGN KEY (`semester_id`) REFERENCES `semesters` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='期末成绩表';

-- 插入示例数据
INSERT INTO `grades` (`student_id`, `course_code`, `semester_id`, `final_score`, `grade_point`, `is_retake`, `remarks`, `created_by`) VALUES
-- CS2023-01班学生成绩
('2023001001', 'GE102', 3, 85.50, 3.5, 0, '期末考试成绩', 'system'),
('2023001001', 'GE101', 3, 92.00, 4.0, 0, '期末考试成绩', 'system'),
('2023001001', 'CS101', 3, 88.75, 3.7, 0, '期末考试成绩', 'system'),
('2023001002', 'GE102', 3, 78.00, 2.8, 0, '期末考试成绩', 'system'),
('2023001002', 'GE101', 3, 85.50, 3.5, 0, '期末考试成绩', 'system'),
('2023001002', 'CS101', 3, 82.25, 3.2, 0, '期末考试成绩', 'system'),
('2023001003', 'GE102', 3, 90.00, 3.8, 0, '期末考试成绩', 'system'),
('2023001003', 'GE101', 3, 87.50, 3.6, 0, '期末考试成绩', 'system'),
('2023001003', 'CS101', 3, 91.00, 3.9, 0, '期末考试成绩', 'system'),

-- CS2023-02班学生成绩
('2023001004', 'GE102', 3, 83.00, 3.3, 0, '期末考试成绩', 'system'),
('2023001004', 'GE101', 3, 89.00, 3.7, 0, '期末考试成绩', 'system'),
('2023001004', 'CS101', 3, 86.50, 3.5, 0, '期末考试成绩', 'system'),
('2023001005', 'GE102', 3, 76.50, 2.6, 0, '期末考试成绩', 'system'),
('2023001005', 'GE101', 3, 81.00, 3.0, 0, '期末考试成绩', 'system'),
('2023001005', 'CS101', 3, 79.75, 2.9, 0, '期末考试成绩', 'system'),

-- SW2023-01班学生成绩
('2023002001', 'GE102', 3, 88.00, 3.6, 0, '期末考试成绩', 'system'),
('2023002001', 'GE101', 3, 93.50, 4.0, 0, '期末考试成绩', 'system'),
('2023002001', 'CS101', 3, 90.25, 3.8, 0, '期末考试成绩', 'system'),
('2023002002', 'GE102', 3, 82.50, 3.2, 0, '期末考试成绩', 'system'),
('2023002002', 'GE101', 3, 86.00, 3.5, 0, '期末考试成绩', 'system'),
('2023002002', 'CS101', 3, 84.75, 3.4, 0, '期末考试成绩', 'system'),

-- 第二学期成绩
('2023001001', 'CS102', 4, 87.00, 3.6, 0, '期末考试成绩', 'system'),
('2023001001', 'CS201', 4, 91.50, 3.9, 0, '期末考试成绩', 'system'),
('2023001002', 'CS102', 4, 80.00, 3.0, 0, '期末考试成绩', 'system'),
('2023001002', 'CS201', 4, 83.75, 3.3, 0, '期末考试成绩', 'system'),

-- 重修成绩示例
('2023001005', 'GE102', 3, 82.00, 3.1, 1, '重修后成绩', 'system');

-- 恢复安全模式
SET FOREIGN_KEY_CHECKS = 1;

-- 验证表结构
DESCRIBE `grades`;

-- 查看示例数据
SELECT 
    g.id,
    g.student_id,
    s.name as student_name,
    g.course_code,
    c.course_name,
    g.semester_id,
    sem.semester_name,
    g.final_score,
    g.grade_point,
    g.is_retake,
    g.remarks
FROM grades g
LEFT JOIN students s ON g.student_id = s.student_id
LEFT JOIN courses c ON g.course_code = c.course_code
LEFT JOIN semesters sem ON g.semester_id = sem.id
ORDER BY g.student_id, g.semester_id, g.course_code
LIMIT 10;
