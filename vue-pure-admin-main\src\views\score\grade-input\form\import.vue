<script setup lang="ts">
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { UploadFilled } from "@element-plus/icons-vue";
import type { UploadFile } from "element-plus";
import ReCol from "@/components/ReCol";
import type { ImportFormProps } from "../utils/types";

const props = withDefaults(defineProps<ImportFormProps>(), {
  formInline: () => ({
    title: "成绩导入",
    classCode: "",
    className: "",
    selectedSemesterId: "",
    selectedCourseCodes: [],
    availableSemesters: [],
    availableCourses: []
  })
});

const ruleFormRef = ref();
const newFormInline = ref(props.formInline);
const selectedFile = ref<File | null>(null);

// 文件上传处理
const handleFileChange = (file: UploadFile) => {
  selectedFile.value = file.raw || null;
  return false; // 阻止自动上传
};

const handleFileRemove = () => {
  selectedFile.value = null;
};

// 下载模板
const handleDownloadTemplate = () => {
  ElMessage.info("模板下载功能开发中...");
};

// 开始导入
const handleImport = () => {
  if (!selectedFile.value) {
    ElMessage.error("请选择要导入的文件");
    return;
  }
  ElMessage.info("导入功能开发中...");
};

function getRef() {
  return ruleFormRef.value;
}

defineExpose({ getRef });
</script>

<template>
  <el-form
    ref="ruleFormRef"
    :model="newFormInline"
    label-width="100px"
  >
    <el-row :gutter="30">
      <re-col :value="24">
        <el-form-item label="班级信息">
          <el-tag type="info">{{ props.formInline.classCode }} - {{ props.formInline.className }}</el-tag>
        </el-form-item>
      </re-col>

      <re-col :value="24">
        <el-form-item label="操作">
          <el-space>
            <el-button
              type="primary"
              @click="handleDownloadTemplate"
            >
              下载导入模板
            </el-button>
            <el-button
              type="success"
              @click="handleImport"
              :disabled="!selectedFile"
            >
              开始导入
            </el-button>
          </el-space>
        </el-form-item>
      </re-col>

      <re-col :value="24">
        <el-form-item label="选择文件">
          <el-upload
            :auto-upload="false"
            :show-file-list="true"
            :limit="1"
            accept=".xlsx,.xls"
            @change="handleFileChange"
            @remove="handleFileRemove"
          >
            <el-button :icon="UploadFilled">选择Excel文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                只能上传 .xlsx/.xls 文件，且不超过 10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </re-col>
    </el-row>
  </el-form>
</template>
