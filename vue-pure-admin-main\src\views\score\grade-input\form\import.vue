<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { UploadFilled } from "@element-plus/icons-vue";
import type { UploadFile, UploadFiles } from "element-plus";
import ReCol from "@/components/ReCol";
import { importFormRules } from "../utils/rule";
import type { ImportFormProps, CourseOption, SemesterOption } from "../utils/types";

// 导入API
import { getAllSemesters } from "@/api/basic/semester";
import { getCoursesByClass, getCoursesByClassAndSemester } from "@/api/educational/classCourse";
import { downloadMultiCourseTemplate, importMultiCourseGrades } from "@/api/score/grade-input";

const props = withDefaults(defineProps<ImportFormProps>(), {
  formInline: () => ({
    title: "成绩导入",
    classCode: "",
    className: "",
    semesterId: "",
    selectedCourseCodes: [],
    file: undefined
  })
});

const ruleFormRef = ref();
const newFormInline = ref(props.formInline);

// 响应式数据
const loading = ref(false);
const semestersLoading = ref(false);
const coursesLoading = ref(false);
const downloadLoading = ref(false);
const importLoading = ref(false);

const availableSemesters = ref<SemesterOption[]>([]);
const availableCourses = ref<CourseOption[]>([]);
const selectedFile = ref<File>();
const importResult = ref();

// 加载学期列表
async function loadSemesters() {
  semestersLoading.value = true;
  try {
    const response = await getAllSemesters();
    availableSemesters.value = (response.data || []).filter(semester => 
      semester && semester.id != null && semester.id !== undefined
    );
  } catch (error) {
    console.error("加载学期列表失败:", error);
    ElMessage.error("加载学期列表失败");
  } finally {
    semestersLoading.value = false;
  }
}

// 加载课程列表
async function loadCourses(semesterId?: number | string, autoSelectAll: boolean = false) {
  if (!newFormInline.value.classCode) {
    return;
  }

  coursesLoading.value = true;
  try {
    let response;
    if (semesterId === "" || semesterId === null || semesterId === undefined) {
      response = await getCoursesByClass(newFormInline.value.classCode);
    } else {
      response = await getCoursesByClassAndSemester(newFormInline.value.classCode, Number(semesterId));
    }

    availableCourses.value = response.data || [];

    if (autoSelectAll) {
      newFormInline.value.selectedCourseCodes = availableCourses.value.map(course => course.courseCode);
    } else {
      const availableCourseCodes = availableCourses.value.map(course => course.courseCode);
      newFormInline.value.selectedCourseCodes = (newFormInline.value.selectedCourseCodes || []).filter(code => 
        availableCourseCodes.includes(code)
      );
    }
  } catch (error) {
    console.error("加载课程列表失败:", error);
    ElMessage.error("加载课程列表失败");
    availableCourses.value = [];
    newFormInline.value.selectedCourseCodes = [];
  } finally {
    coursesLoading.value = false;
  }
}

// 学期变化处理
function handleSemesterChange(semesterId: number | string) {
  loadCourses(semesterId, true);
}

// 文件上传处理
function handleFileChange(uploadFile: UploadFile, uploadFiles: UploadFiles) {
  if (uploadFile.raw) {
    selectedFile.value = uploadFile.raw;
    newFormInline.value.file = uploadFile.raw;
  }
}

// 文件移除处理
function handleFileRemove() {
  selectedFile.value = undefined;
  newFormInline.value.file = undefined;
}

// 下载模板
async function handleDownloadTemplate() {
  if (!newFormInline.value.selectedCourseCodes || newFormInline.value.selectedCourseCodes.length === 0) {
    ElMessage.error("请先选择课程");
    return;
  }

  downloadLoading.value = true;
  try {
    const response = await downloadMultiCourseTemplate(
      newFormInline.value.classCode!,
      newFormInline.value.selectedCourseCodes,
      newFormInline.value.semesterId === "" ? undefined : Number(newFormInline.value.semesterId)
    );

    const blob = new Blob([response], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = "课程成绩导入模板.xlsx";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    ElMessage.success("模板下载成功");
  } catch (error) {
    console.error("下载模板失败:", error);
    ElMessage.error("下载模板失败");
  } finally {
    downloadLoading.value = false;
  }
}

// 开始导入
async function handleImport() {
  if (!selectedFile.value) {
    ElMessage.error("请选择要导入的文件");
    return;
  }

  if (!newFormInline.value.selectedCourseCodes || newFormInline.value.selectedCourseCodes.length === 0) {
    ElMessage.error("请先选择课程");
    return;
  }

  try {
    await ElMessageBox.confirm(
      '确定要导入成绩吗？导入过程中请勿关闭页面。',
      '确认导入',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    importLoading.value = true;
    importResult.value = null;

    const result = await importMultiCourseGrades(
      selectedFile.value,
      newFormInline.value.classCode!,
      newFormInline.value.selectedCourseCodes,
      newFormInline.value.semesterId === "" ? undefined : Number(newFormInline.value.semesterId)
    );

    importResult.value = result.data;

    if (result.data.success) {
      ElMessage.success("导入完成");
    } else {
      ElMessage.error("导入失败，请查看错误详情");
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error("导入失败:", error);
      ElMessage.error("导入失败");
    }
  } finally {
    importLoading.value = false;
  }
}

// 监听弹窗显示状态
watch(() => props.formInline, (newVal) => {
  if (newVal) {
    loadSemesters();
    newFormInline.value.semesterId = "";
    loadCourses("", true);
  }
}, { immediate: true });

function getRef() {
  return ruleFormRef.value;
}

defineExpose({ getRef, handleImport });
</script>

<template>
  <el-form
    ref="ruleFormRef"
    :model="newFormInline"
    :rules="importFormRules"
    label-width="100px"
  >
    <!-- 班级信息 -->
    <el-row :gutter="30">
      <re-col :value="24">
        <el-form-item label="班级信息">
          <el-tag type="info">{{ newFormInline.classCode }} - {{ newFormInline.className }}</el-tag>
        </el-form-item>
      </re-col>
    </el-row>

    <!-- 学期选择 -->
    <el-row :gutter="30">
      <re-col :value="24">
        <el-form-item label="选择学期">
          <el-select
            v-model="newFormInline.semesterId"
            placeholder="请选择学期"
            style="width: 100%"
            @change="handleSemesterChange"
            :loading="semestersLoading"
          >
            <el-option label="全部学期" value="" />
            <el-option
              v-for="semester in availableSemesters"
              :key="semester.id"
              :label="semester.semesterName"
              :value="semester.id"
            />
          </el-select>
        </el-form-item>
      </re-col>
    </el-row>

    <!-- 课程选择 -->
    <el-row :gutter="30">
      <re-col :value="24">
        <el-form-item label="选择课程" prop="selectedCourseCodes">
          <div class="course-selection" v-loading="coursesLoading">
            <el-checkbox-group v-model="newFormInline.selectedCourseCodes">
              <div v-for="course in availableCourses" :key="course.courseCode" class="course-item">
                <el-checkbox :value="course.courseCode">{{ course.courseName }}</el-checkbox>
              </div>
            </el-checkbox-group>
            <div v-if="availableCourses.length === 0 && !coursesLoading" class="no-courses">
              暂无可用课程
            </div>
          </div>
        </el-form-item>
      </re-col>
    </el-row>

    <!-- 下载模板 -->
    <el-row :gutter="30">
      <re-col :value="24">
        <el-form-item label="下载模板">
          <el-button
            type="primary"
            @click="handleDownloadTemplate"
            :loading="downloadLoading"
          >
            下载导入模板
          </el-button>
        </el-form-item>
      </re-col>
    </el-row>

    <!-- 文件上传 -->
    <el-row :gutter="30">
      <re-col :value="24">
        <el-form-item label="选择文件" prop="file">
          <el-upload
            class="upload-demo"
            drag
            :auto-upload="false"
            :limit="1"
            accept=".xlsx,.xls"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                只能上传 xlsx/xls 文件，且不超过 10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </re-col>
    </el-row>

    <!-- 导入按钮 -->
    <el-row :gutter="30">
      <re-col :value="24">
        <el-form-item>
          <el-button
            type="success"
            @click="handleImport"
            :loading="importLoading"
            :disabled="!selectedFile"
          >
            开始导入
          </el-button>
        </el-form-item>
      </re-col>
    </el-row>

    <!-- 导入结果 -->
    <el-row :gutter="30" v-if="importResult">
      <re-col :value="24">
        <el-form-item label="导入结果">
          <el-alert
            :title="importResult.summary"
            :type="importResult.success ? 'success' : 'error'"
            :closable="false"
          >
            <template #default>
              <p>总计：{{ importResult.totalRows }}条</p>
              <p>成功：{{ importResult.successRows }}条</p>
              <p>失败：{{ importResult.failedRows }}条</p>
              <div v-if="importResult.errorMessages && importResult.errorMessages.length > 0">
                <p><strong>错误信息：</strong></p>
                <ul>
                  <li v-for="(error, index) in importResult.errorMessages" :key="index">
                    {{ error }}
                  </li>
                </ul>
              </div>
            </template>
          </el-alert>
        </el-form-item>
      </re-col>
    </el-row>
  </el-form>
</template>

<style scoped>
.course-selection {
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  padding: 10px;
}

.course-item {
  margin-bottom: 8px;
}

.course-item:last-child {
  margin-bottom: 0;
}

.no-courses {
  text-align: center;
  color: var(--el-text-color-secondary);
  padding: 20px;
}

.upload-demo {
  width: 100%;
}
</style>
