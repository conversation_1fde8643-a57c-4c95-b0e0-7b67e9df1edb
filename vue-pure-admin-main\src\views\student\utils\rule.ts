import { reactive } from "vue";
import type { FormRules } from "element-plus";

/** 自定义表单规则校验 */
export const formRules = reactive(<FormRules>{
  collegeCode: [
    { required: true, message: "请选择所属学院", trigger: "change" }
  ],
  majorCode: [
    { required: true, message: "请选择所属专业", trigger: "change" }
  ],
  classCode: [
    { required: true, message: "请选择所属班级", trigger: "change" }
  ],
  studentId: [
    { required: true, message: "学号为必填项", trigger: "blur" },
    { min: 2, max: 20, message: "学号长度应为2-20个字符", trigger: "blur" }
  ],
  name: [
    { required: true, message: "姓名为必填项", trigger: "blur" },
    { min: 2, max: 50, message: "姓名长度应为2-50个字符", trigger: "blur" }
  ],
  gender: [
    { required: true, message: "请选择性别", trigger: "change" }
  ],
  enrollmentDate: [
    { required: true, message: "请选择入学日期", trigger: "change" }
  ],
  status: [
    { required: true, message: "请选择学籍状态", trigger: "change" }
  ],
  birthDate: [
    {
      validator: (rule, value, callback) => {
        if (value && new Date(value) > new Date()) {
          callback(new Error("出生日期不能晚于当前日期"));
        } else {
          callback();
        }
      },
      trigger: "change"
    }
  ],
  idCard: [
    {
      pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
      message: "请输入正确的身份证号码",
      trigger: "blur"
    }
  ],
  phone: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号码",
      trigger: "blur"
    }
  ],
  email: [
    {
      type: "email",
      message: "请输入正确的邮箱地址",
      trigger: "blur"
    }
  ]
});
