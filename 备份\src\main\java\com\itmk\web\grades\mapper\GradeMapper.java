package com.itmk.web.grades.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.itmk.web.grades.entity.Grade;
import com.itmk.web.grades.entity.GradeParm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface GradeMapper extends BaseMapper<Grade> {
    IPage<Grade> getList(IPage<Grade> page, GradeParm parm);
    IPage<Grade> calculategpa(IPage<Grade> page, GradeParm parm);
    
    // 获取学生学业成绩，支持按学期名称或学期ID查询
    List<Map<String, Object>> getStudentGpa(@Param("semesterName") String semesterName, @Param("semesterId") Integer semesterId);
    
    // 获取学生学业成绩，支持多个学期ID
    List<Map<String, Object>> getStudentGpaByIds(@Param("semesterIds") List<Integer> semesterIds);
    
    // 兼容旧方法
    default List<Map<String, Object>> getStudentGpa(String semesterName) {
        return getStudentGpa(semesterName, null);
    }
}