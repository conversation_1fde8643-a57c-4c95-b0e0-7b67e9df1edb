<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="vue_admin@localhost">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.53">
    <root id="1">
      <DefaultCasing>lower/lower</DefaultCasing>
      <DefaultEngine>InnoDB</DefaultEngine>
      <DefaultTmpEngine>InnoDB</DefaultTmpEngine>
      <ServerVersion>8.3.0</ServerVersion>
    </root>
    <collation id="2" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="3" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
    </collation>
    <collation id="4" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="5" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
    </collation>
    <collation id="6" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="7" parent="1" name="big5_bin">
      <Charset>big5</Charset>
    </collation>
    <collation id="8" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="9" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="10" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="11" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="12" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="13" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="14" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="15" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="16" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="17" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="18" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="19" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="20" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
    </collation>
    <collation id="21" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="22" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="23" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="24" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="25" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
    </collation>
    <collation id="26" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="27" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
    </collation>
    <collation id="28" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="29" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
    </collation>
    <collation id="30" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="31" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
    </collation>
    <collation id="32" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="33" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
    </collation>
    <collation id="34" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="35" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
    </collation>
    <collation id="36" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="37" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
    </collation>
    <collation id="38" parent="1" name="gb18030_chinese_ci">
      <Charset>gb18030</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="39" parent="1" name="gb18030_bin">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="40" parent="1" name="gb18030_unicode_520_ci">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="41" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="42" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
    </collation>
    <collation id="43" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="44" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
    </collation>
    <collation id="45" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="46" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
    </collation>
    <collation id="47" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="48" parent="1" name="greek_bin">
      <Charset>greek</Charset>
    </collation>
    <collation id="49" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="50" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
    </collation>
    <collation id="51" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="52" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
    </collation>
    <collation id="53" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="54" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
    </collation>
    <collation id="55" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="56" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
    </collation>
    <collation id="57" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="58" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
    </collation>
    <collation id="59" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="60" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="61" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="62" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="63" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
    </collation>
    <collation id="64" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="65" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
    </collation>
    <collation id="66" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="67" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
    </collation>
    <collation id="68" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="69" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="70" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="71" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
    </collation>
    <collation id="72" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="73" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
    </collation>
    <collation id="74" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="75" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="76" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="77" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
    </collation>
    <collation id="78" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="79" parent="1" name="macce_bin">
      <Charset>macce</Charset>
    </collation>
    <collation id="80" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="81" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
    </collation>
    <collation id="82" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="83" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
    </collation>
    <collation id="84" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="85" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
    </collation>
    <collation id="86" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="87" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
    </collation>
    <collation id="88" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="89" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="90" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="91" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="92" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="93" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="94" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="95" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="96" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="97" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="98" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="99" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="100" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="101" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="102" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="103" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="104" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="105" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="106" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="107" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="108" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="109" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="110" parent="1" name="ucs2_german2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="111" parent="1" name="ucs2_croatian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="112" parent="1" name="ucs2_unicode_520_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="113" parent="1" name="ucs2_vietnamese_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="114" parent="1" name="ucs2_general_mysql500_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="115" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="116" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
    </collation>
    <collation id="117" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="118" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
    </collation>
    <collation id="119" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="120" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="121" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="122" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="123" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="124" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="125" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="126" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="127" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="128" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="129" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="130" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="131" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="132" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="133" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="134" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="135" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="136" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="137" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="138" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="139" parent="1" name="utf16_german2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="140" parent="1" name="utf16_croatian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="141" parent="1" name="utf16_unicode_520_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="142" parent="1" name="utf16_vietnamese_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="143" parent="1" name="utf16le_general_ci">
      <Charset>utf16le</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="144" parent="1" name="utf16le_bin">
      <Charset>utf16le</Charset>
    </collation>
    <collation id="145" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="146" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
    </collation>
    <collation id="147" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="148" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="149" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="150" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="151" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="152" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="153" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="154" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="155" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="156" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="157" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="158" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="159" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="160" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="161" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="162" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="163" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="164" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="165" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="166" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="167" parent="1" name="utf32_german2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="168" parent="1" name="utf32_croatian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="169" parent="1" name="utf32_unicode_520_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="170" parent="1" name="utf32_vietnamese_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="171" parent="1" name="utf8mb3_general_ci">
      <Charset>utf8mb3</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="172" parent="1" name="utf8mb3_tolower_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="173" parent="1" name="utf8mb3_bin">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="174" parent="1" name="utf8mb3_unicode_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="175" parent="1" name="utf8mb3_icelandic_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="176" parent="1" name="utf8mb3_latvian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="177" parent="1" name="utf8mb3_romanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="178" parent="1" name="utf8mb3_slovenian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="179" parent="1" name="utf8mb3_polish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="180" parent="1" name="utf8mb3_estonian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="181" parent="1" name="utf8mb3_spanish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="182" parent="1" name="utf8mb3_swedish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="183" parent="1" name="utf8mb3_turkish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="184" parent="1" name="utf8mb3_czech_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="185" parent="1" name="utf8mb3_danish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="186" parent="1" name="utf8mb3_lithuanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="187" parent="1" name="utf8mb3_slovak_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="188" parent="1" name="utf8mb3_spanish2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="189" parent="1" name="utf8mb3_roman_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="190" parent="1" name="utf8mb3_persian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="191" parent="1" name="utf8mb3_esperanto_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="192" parent="1" name="utf8mb3_hungarian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="193" parent="1" name="utf8mb3_sinhala_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="194" parent="1" name="utf8mb3_german2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="195" parent="1" name="utf8mb3_croatian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="196" parent="1" name="utf8mb3_unicode_520_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="197" parent="1" name="utf8mb3_vietnamese_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="198" parent="1" name="utf8mb3_general_mysql500_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="199" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="200" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="201" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="202" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="203" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="204" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="205" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="206" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="207" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="208" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="209" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="210" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="211" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="212" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="213" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="214" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="215" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="216" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="217" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="218" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="219" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="220" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="221" parent="1" name="utf8mb4_german2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="222" parent="1" name="utf8mb4_croatian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="223" parent="1" name="utf8mb4_unicode_520_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="224" parent="1" name="utf8mb4_vietnamese_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="225" parent="1" name="utf8mb4_0900_ai_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="226" parent="1" name="utf8mb4_de_pb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="227" parent="1" name="utf8mb4_is_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="228" parent="1" name="utf8mb4_lv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="229" parent="1" name="utf8mb4_ro_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="230" parent="1" name="utf8mb4_sl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="231" parent="1" name="utf8mb4_pl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="232" parent="1" name="utf8mb4_et_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="233" parent="1" name="utf8mb4_es_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="234" parent="1" name="utf8mb4_sv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="235" parent="1" name="utf8mb4_tr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="236" parent="1" name="utf8mb4_cs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="237" parent="1" name="utf8mb4_da_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="238" parent="1" name="utf8mb4_lt_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="239" parent="1" name="utf8mb4_sk_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="240" parent="1" name="utf8mb4_es_trad_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="241" parent="1" name="utf8mb4_la_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="242" parent="1" name="utf8mb4_eo_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="243" parent="1" name="utf8mb4_hu_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="244" parent="1" name="utf8mb4_hr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="245" parent="1" name="utf8mb4_vi_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="246" parent="1" name="utf8mb4_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="247" parent="1" name="utf8mb4_de_pb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="248" parent="1" name="utf8mb4_is_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="249" parent="1" name="utf8mb4_lv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="250" parent="1" name="utf8mb4_ro_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="251" parent="1" name="utf8mb4_sl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="252" parent="1" name="utf8mb4_pl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="253" parent="1" name="utf8mb4_et_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="254" parent="1" name="utf8mb4_es_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="255" parent="1" name="utf8mb4_sv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="256" parent="1" name="utf8mb4_tr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="257" parent="1" name="utf8mb4_cs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="258" parent="1" name="utf8mb4_da_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="259" parent="1" name="utf8mb4_lt_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="260" parent="1" name="utf8mb4_sk_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="261" parent="1" name="utf8mb4_es_trad_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="262" parent="1" name="utf8mb4_la_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="263" parent="1" name="utf8mb4_eo_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="264" parent="1" name="utf8mb4_hu_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="265" parent="1" name="utf8mb4_hr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="266" parent="1" name="utf8mb4_vi_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="267" parent="1" name="utf8mb4_ja_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="268" parent="1" name="utf8mb4_ja_0900_as_cs_ks">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="269" parent="1" name="utf8mb4_0900_as_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="270" parent="1" name="utf8mb4_ru_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="271" parent="1" name="utf8mb4_ru_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="272" parent="1" name="utf8mb4_zh_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="273" parent="1" name="utf8mb4_0900_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="274" parent="1" name="utf8mb4_nb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="275" parent="1" name="utf8mb4_nb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="276" parent="1" name="utf8mb4_nn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="277" parent="1" name="utf8mb4_nn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="278" parent="1" name="utf8mb4_sr_latn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="279" parent="1" name="utf8mb4_sr_latn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="280" parent="1" name="utf8mb4_bs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="281" parent="1" name="utf8mb4_bs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="282" parent="1" name="utf8mb4_bg_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="283" parent="1" name="utf8mb4_bg_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="284" parent="1" name="utf8mb4_gl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="285" parent="1" name="utf8mb4_gl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="286" parent="1" name="utf8mb4_mn_cyrl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="287" parent="1" name="utf8mb4_mn_cyrl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <schema id="288" parent="1" name="mysql">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="289" parent="1" name="information_schema">
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <schema id="290" parent="1" name="performance_schema">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="291" parent="1" name="sys">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="292" parent="1" name="shuiguo">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="293" parent="1" name="db_wms">
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <schema id="294" parent="1" name="students">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="295" parent="1" name="cookieshop">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="296" parent="1" name="jdbc">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="297" parent="1" name="xuesheng">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="298" parent="1" name="peach_ai">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="299" parent="1" name="mybatis">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="300" parent="1" name="employee_db">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="301" parent="1" name="logistics_system">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="302" parent="1" name="vue_admin">
      <AutoIntrospectionLevel>3</AutoIntrospectionLevel>
      <Current>1</Current>
      <LastIntrospectionLevel>3</LastIntrospectionLevel>
      <LastIntrospectionLocalTimestamp>2025-08-01.13:25:50</LastIntrospectionLocalTimestamp>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </schema>
    <user id="303" parent="1" name="authbase">
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="304" parent="1" name="mysql.infoschema">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="305" parent="1" name="mysql.session">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="306" parent="1" name="mysql.sys">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="307" parent="1" name="root">
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <table id="308" parent="302" name="class_courses">
      <Comment>班级课程分配表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="309" parent="302" name="classes">
      <Comment>班级信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="310" parent="302" name="colleges">
      <Comment>学院信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="311" parent="302" name="courses">
      <Comment>课程信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="312" parent="302" name="grades">
      <Comment>期末成绩表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="313" parent="302" name="majors">
      <Comment>专业信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="314" parent="302" name="quality_evaluation">
      <Comment>基本素质测评成绩表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="315" parent="302" name="semesters">
      <Comment>学年学期表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="316" parent="302" name="students">
      <Comment>学生信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="317" parent="302" name="sys_dept">
      <Comment>系统部门表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="318" parent="302" name="sys_menu">
      <Comment>菜单表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="319" parent="302" name="sys_role">
      <Comment>角色表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="320" parent="302" name="sys_role_menu">
      <Comment>角色菜单关联表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="321" parent="302" name="sys_user">
      <Comment>用户表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="322" parent="302" name="sys_user_role">
      <Comment>用户角色关联表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="323" parent="302" name="teachers">
      <Comment>教师信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <view id="324" parent="302" name="v_class_course_details">
      <Definer>root@localhost</Definer>
      <DetailsLevel>3</DetailsLevel>
      <SourceTextLength>1263</SourceTextLength>
    </view>
    <view id="325" parent="302" name="v_class_course_statistics">
      <Definer>root@localhost</Definer>
      <DetailsLevel>3</DetailsLevel>
      <SourceTextLength>1110</SourceTextLength>
    </view>
    <view id="326" parent="302" name="v_dept_hierarchy">
      <Definer>root@localhost</Definer>
      <DetailsLevel>3</DetailsLevel>
      <SourceTextLength>1162</SourceTextLength>
    </view>
    <column id="327" parent="308" name="id">
      <AutoIncrement>59</AutoIncrement>
      <Comment>分配ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="328" parent="308" name="class_code">
      <Comment>班级代码</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="329" parent="308" name="course_code">
      <Comment>课程代码</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="330" parent="308" name="semester_id">
      <Comment>学期ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="331" parent="308" name="is_active">
      <Comment>是否启用（1-启用，0-停用）</Comment>
      <DefaultExpression>1</DefaultExpression>
      <Position>5</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="332" parent="308" name="sort_order">
      <Comment>排序</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>6</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="333" parent="308" name="remark">
      <Comment>备注</Comment>
      <Position>7</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="334" parent="308" name="created_by">
      <Comment>创建人</Comment>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="335" parent="308" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>9</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="336" parent="308" name="updated_by">
      <Comment>更新人</Comment>
      <Position>10</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="337" parent="308" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>11</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <foreign-key id="338" parent="308" name="fk_class_courses_class">
      <ColNames>class_code</ColNames>
      <OnDelete>cascade</OnDelete>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>class_code</RefColNames>
      <RefTableName>classes</RefTableName>
    </foreign-key>
    <foreign-key id="339" parent="308" name="fk_class_courses_course">
      <ColNames>course_code</ColNames>
      <OnDelete>cascade</OnDelete>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>course_code</RefColNames>
      <RefTableName>courses</RefTableName>
    </foreign-key>
    <foreign-key id="340" parent="308" name="fk_class_courses_semester">
      <ColNames>semester_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>id</RefColNames>
      <RefTableName>semesters</RefTableName>
    </foreign-key>
    <index id="341" parent="308" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="342" parent="308" name="uk_class_course_semester">
      <ColNames>class_code
course_code
semester_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="343" parent="308" name="idx_class_code">
      <ColNames>class_code</ColNames>
      <Type>btree</Type>
    </index>
    <index id="344" parent="308" name="idx_course_code">
      <ColNames>course_code</ColNames>
      <Type>btree</Type>
    </index>
    <index id="345" parent="308" name="idx_semester_id">
      <ColNames>semester_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="346" parent="308" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="347" parent="308" name="uk_class_course_semester">
      <UnderlyingIndexName>uk_class_course_semester</UnderlyingIndexName>
    </key>
    <column id="348" parent="309" name="id">
      <AutoIncrement>8</AutoIncrement>
      <Comment>班级ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="349" parent="309" name="class_code">
      <Comment>班级代码</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="350" parent="309" name="class_name">
      <Comment>班级名称</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="351" parent="309" name="major_code">
      <Comment>所属专业代码</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="352" parent="309" name="grade_year">
      <Comment>入学年份</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>year|0s</StoredType>
    </column>
    <column id="353" parent="309" name="student_count">
      <Comment>学生人数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>6</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="354" parent="309" name="head_teacher_code">
      <Comment>班主任工号</Comment>
      <Position>7</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="355" parent="309" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>8</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="356" parent="309" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <foreign-key id="357" parent="309" name="fk_classes_major">
      <ColNames>major_code</ColNames>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>major_code</RefColNames>
      <RefTableName>majors</RefTableName>
    </foreign-key>
    <foreign-key id="358" parent="309" name="fk_classes_head_teacher">
      <ColNames>head_teacher_code</ColNames>
      <OnDelete>set-null</OnDelete>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>teacher_code</RefColNames>
      <RefTableName>teachers</RefTableName>
    </foreign-key>
    <index id="359" parent="309" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="360" parent="309" name="uk_class_code">
      <ColNames>class_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="361" parent="309" name="idx_major_code">
      <ColNames>major_code</ColNames>
      <Type>btree</Type>
    </index>
    <index id="362" parent="309" name="idx_grade_year">
      <ColNames>grade_year</ColNames>
      <Type>btree</Type>
    </index>
    <index id="363" parent="309" name="idx_head_teacher_code">
      <ColNames>head_teacher_code</ColNames>
      <Type>btree</Type>
    </index>
    <key id="364" parent="309" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="365" parent="309" name="uk_class_code">
      <UnderlyingIndexName>uk_class_code</UnderlyingIndexName>
    </key>
    <column id="366" parent="310" name="id">
      <AutoIncrement>3</AutoIncrement>
      <Comment>学院ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="367" parent="310" name="college_code">
      <Comment>&#x7f;学院代码</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="368" parent="310" name="college_name">
      <Comment>学院名称</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="369" parent="310" name="description">
      <Comment>学院描述</Comment>
      <Position>4</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="370" parent="310" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>5</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="371" parent="310" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>6</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <index id="372" parent="310" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="373" parent="310" name="uk_college_code">
      <ColNames>college_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="374" parent="310" name="uk_college_name">
      <ColNames>college_name</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="375" parent="310" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="376" parent="310" name="uk_college_code">
      <UnderlyingIndexName>uk_college_code</UnderlyingIndexName>
    </key>
    <key id="377" parent="310" name="uk_college_name">
      <UnderlyingIndexName>uk_college_name</UnderlyingIndexName>
    </key>
    <column id="378" parent="311" name="id">
      <AutoIncrement>43</AutoIncrement>
      <Comment>课程ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="379" parent="311" name="course_code">
      <Comment>课程代码</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="380" parent="311" name="course_name">
      <Comment>课程名称</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="381" parent="311" name="credits">
      <Comment>学分</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>decimal(4,2 digit)|0s</StoredType>
    </column>
    <column id="382" parent="311" name="course_type">
      <Comment>课程类型</Comment>
      <DefaultExpression>&apos;必修&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>enum(&apos;必修&apos;, &apos;选修&apos;, &apos;实践&apos;, &apos;通识&apos;)|0e</StoredType>
    </column>
    <column id="383" parent="311" name="college_code">
      <Comment>所属学院代码</Comment>
      <Position>6</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="384" parent="311" name="description">
      <Comment>课程描述</Comment>
      <Position>7</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="385" parent="311" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>8</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="386" parent="311" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <foreign-key id="387" parent="311" name="fk_courses_college">
      <ColNames>college_code</ColNames>
      <OnDelete>set-null</OnDelete>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>college_code</RefColNames>
      <RefTableName>colleges</RefTableName>
    </foreign-key>
    <index id="388" parent="311" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="389" parent="311" name="uk_course_code">
      <ColNames>course_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="390" parent="311" name="idx_course_type">
      <ColNames>course_type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="391" parent="311" name="idx_college_code">
      <ColNames>college_code</ColNames>
      <Type>btree</Type>
    </index>
    <key id="392" parent="311" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="393" parent="311" name="uk_course_code">
      <UnderlyingIndexName>uk_course_code</UnderlyingIndexName>
    </key>
    <column id="394" parent="312" name="id">
      <AutoIncrement>2702</AutoIncrement>
      <Comment>成绩ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="395" parent="312" name="student_id">
      <Comment>学号</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="396" parent="312" name="course_code">
      <Comment>课程代码</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="397" parent="312" name="semester_id">
      <Comment>学期ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="398" parent="312" name="final_score">
      <Comment>总成绩</Comment>
      <Position>5</Position>
      <StoredType>decimal(5,2 digit)|0s</StoredType>
    </column>
    <column id="399" parent="312" name="grade_point">
      <Comment>绩点</Comment>
      <Position>6</Position>
      <StoredType>decimal(3,2 digit)|0s</StoredType>
    </column>
    <column id="400" parent="312" name="is_retake">
      <Comment>是否重修</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>7</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="401" parent="312" name="remarks">
      <Comment>备注</Comment>
      <Position>8</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="402" parent="312" name="created_by">
      <Comment>创建人</Comment>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="403" parent="312" name="updated_by">
      <Comment>更新人</Comment>
      <Position>10</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="404" parent="312" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>11</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="405" parent="312" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>12</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <foreign-key id="406" parent="312" name="fk_grades_student">
      <ColNames>student_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>student_id</RefColNames>
      <RefTableName>students</RefTableName>
    </foreign-key>
    <foreign-key id="407" parent="312" name="fk_grades_course">
      <ColNames>course_code</ColNames>
      <OnDelete>cascade</OnDelete>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>course_code</RefColNames>
      <RefTableName>courses</RefTableName>
    </foreign-key>
    <foreign-key id="408" parent="312" name="fk_grades_semester">
      <ColNames>semester_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>id</RefColNames>
      <RefTableName>semesters</RefTableName>
    </foreign-key>
    <index id="409" parent="312" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="410" parent="312" name="uk_student_course_semester">
      <ColNames>student_id
course_code
semester_id
is_retake</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="411" parent="312" name="idx_course_code">
      <ColNames>course_code</ColNames>
      <Type>btree</Type>
    </index>
    <index id="412" parent="312" name="idx_semester_id">
      <ColNames>semester_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="413" parent="312" name="idx_final_score">
      <ColNames>final_score</ColNames>
      <Type>btree</Type>
    </index>
    <key id="414" parent="312" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="415" parent="312" name="uk_student_course_semester">
      <UnderlyingIndexName>uk_student_course_semester</UnderlyingIndexName>
    </key>
    <column id="416" parent="313" name="id">
      <AutoIncrement>10</AutoIncrement>
      <Comment>专业ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="417" parent="313" name="major_code">
      <Comment>专业代码</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="418" parent="313" name="major_name">
      <Comment>专业名称</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="419" parent="313" name="college_code">
      <Comment>所属学院代码</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="420" parent="313" name="duration">
      <Comment>学制年限</Comment>
      <DefaultExpression>3</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="421" parent="313" name="description">
      <Comment>专业描述</Comment>
      <Position>6</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="422" parent="313" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>7</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="423" parent="313" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>8</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <foreign-key id="424" parent="313" name="fk_majors_college">
      <ColNames>college_code</ColNames>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>college_code</RefColNames>
      <RefTableName>colleges</RefTableName>
    </foreign-key>
    <index id="425" parent="313" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="426" parent="313" name="uk_major_code">
      <ColNames>major_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="427" parent="313" name="idx_college_code">
      <ColNames>college_code</ColNames>
      <Type>btree</Type>
    </index>
    <key id="428" parent="313" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="429" parent="313" name="uk_major_code">
      <UnderlyingIndexName>uk_major_code</UnderlyingIndexName>
    </key>
    <column id="430" parent="314" name="evaluation_id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="431" parent="314" name="student_id">
      <Comment>学号</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="432" parent="314" name="add_score">
      <Comment>加分项</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>3</Position>
      <StoredType>decimal(6,2 digit)|0s</StoredType>
    </column>
    <column id="433" parent="314" name="reduce_score">
      <Comment>扣分项</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>4</Position>
      <StoredType>decimal(6,2 digit)|0s</StoredType>
    </column>
    <column id="434" parent="314" name="add_score_remark">
      <Comment>加分说明</Comment>
      <Position>5</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="435" parent="314" name="reduce_score_remark">
      <Comment>扣分说明</Comment>
      <Position>6</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="436" parent="314" name="semester_id">
      <Comment>学期ID（关联semesters表）</Comment>
      <Position>7</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="437" parent="314" name="period_score">
      <Comment>周期得分</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>8</Position>
      <StoredType>decimal(6,2 digit)|0s</StoredType>
    </column>
    <column id="438" parent="314" name="total_score">
      <Comment>总得分</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>9</Position>
      <StoredType>decimal(6,2 digit)|0s</StoredType>
    </column>
    <column id="439" parent="314" name="prev_period_score">
      <Comment>上一学期总分（仅在当前为第二学期时有值）</Comment>
      <Position>10</Position>
      <StoredType>decimal(6,2 digit)|0s</StoredType>
    </column>
    <column id="440" parent="314" name="class_ranking">
      <Comment>班级排名</Comment>
      <Position>11</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="441" parent="314" name="major_ranking">
      <Comment>专业排名</Comment>
      <Position>12</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="442" parent="314" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>13</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="443" parent="314" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>14</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <foreign-key id="444" parent="314" name="fk_quality_evaluation_student">
      <ColNames>student_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>student_id</RefColNames>
      <RefTableName>students</RefTableName>
    </foreign-key>
    <foreign-key id="445" parent="314" name="fk_quality_evaluation_semester">
      <ColNames>semester_id</ColNames>
      <OnDelete>set-null</OnDelete>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>id</RefColNames>
      <RefTableName>semesters</RefTableName>
    </foreign-key>
    <index id="446" parent="314" name="PRIMARY">
      <ColNames>evaluation_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="447" parent="314" name="uk_student_semester">
      <ColNames>student_id
semester_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="448" parent="314" name="idx_student_id">
      <ColNames>student_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="449" parent="314" name="idx_semester_id">
      <ColNames>semester_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="450" parent="314" name="idx_total_score">
      <ColNames>total_score</ColNames>
      <Type>btree</Type>
    </index>
    <key id="451" parent="314" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="452" parent="314" name="uk_student_semester">
      <UnderlyingIndexName>uk_student_semester</UnderlyingIndexName>
    </key>
    <column id="453" parent="315" name="id">
      <AutoIncrement>16</AutoIncrement>
      <Comment>学期ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="454" parent="315" name="academic_year">
      <Comment>学年</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="455" parent="315" name="semester_number">
      <Comment>学期号</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="456" parent="315" name="semester_name">
      <Comment>学期名称</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="457" parent="315" name="start_date">
      <Comment>开始日期</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="458" parent="315" name="end_date">
      <Comment>结束日期</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="459" parent="315" name="is_current">
      <Comment>是否当前学期</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="460" parent="315" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>8</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="461" parent="315" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <index id="462" parent="315" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="463" parent="315" name="uk_academic_year_semester">
      <ColNames>academic_year
semester_number</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="464" parent="315" name="uk_semester_name">
      <ColNames>semester_name</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="465" parent="315" name="idx_academic_year">
      <ColNames>academic_year</ColNames>
      <Type>btree</Type>
    </index>
    <index id="466" parent="315" name="idx_is_current">
      <ColNames>is_current</ColNames>
      <Type>btree</Type>
    </index>
    <key id="467" parent="315" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="468" parent="315" name="uk_academic_year_semester">
      <UnderlyingIndexName>uk_academic_year_semester</UnderlyingIndexName>
    </key>
    <key id="469" parent="315" name="uk_semester_name">
      <UnderlyingIndexName>uk_semester_name</UnderlyingIndexName>
    </key>
    <column id="470" parent="316" name="id">
      <AutoIncrement>47</AutoIncrement>
      <Comment>学生ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="471" parent="316" name="student_id">
      <Comment>学号</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="472" parent="316" name="name">
      <Comment>姓名</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="473" parent="316" name="gender">
      <Comment>性别</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>enum(&apos;男&apos;, &apos;女&apos;)|0e</StoredType>
    </column>
    <column id="474" parent="316" name="birth_date">
      <Comment>出生日期</Comment>
      <Position>5</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="475" parent="316" name="id_card">
      <Comment>身份证号</Comment>
      <Position>6</Position>
      <StoredType>varchar(18)|0s</StoredType>
    </column>
    <column id="476" parent="316" name="phone">
      <Comment>电话号码</Comment>
      <Position>7</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="477" parent="316" name="email">
      <Comment>邮箱</Comment>
      <Position>8</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="478" parent="316" name="class_code">
      <Comment>所属班级代码</Comment>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="479" parent="316" name="enrollment_date">
      <Comment>入学日期</Comment>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="480" parent="316" name="status">
      <Comment>学籍状态</Comment>
      <DefaultExpression>&apos;在校&apos;</DefaultExpression>
      <Position>11</Position>
      <StoredType>enum(&apos;在校&apos;, &apos;毕业&apos;, &apos;退学&apos;, &apos;休学&apos;, &apos;转学&apos;)|0e</StoredType>
    </column>
    <column id="481" parent="316" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>12</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="482" parent="316" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>13</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <foreign-key id="483" parent="316" name="fk_students_class">
      <ColNames>class_code</ColNames>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>class_code</RefColNames>
      <RefTableName>classes</RefTableName>
    </foreign-key>
    <index id="484" parent="316" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="485" parent="316" name="uk_student_id">
      <ColNames>student_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="486" parent="316" name="idx_class_code">
      <ColNames>class_code</ColNames>
      <Type>btree</Type>
    </index>
    <index id="487" parent="316" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <key id="488" parent="316" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="489" parent="316" name="uk_student_id">
      <UnderlyingIndexName>uk_student_id</UnderlyingIndexName>
    </key>
    <column id="490" parent="317" name="id">
      <AutoIncrement>703</AutoIncrement>
      <Comment>部门ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="491" parent="317" name="parent_id">
      <Comment>父部门ID</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="492" parent="317" name="dept_name">
      <Comment>部门名称</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="493" parent="317" name="dept_code">
      <Comment>部门编码</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="494" parent="317" name="leader">
      <Comment>负责人</Comment>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="495" parent="317" name="phone">
      <Comment>联系电话</Comment>
      <Position>6</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="496" parent="317" name="email">
      <Comment>邮箱</Comment>
      <Position>7</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="497" parent="317" name="status">
      <Comment>状态：0禁用、1启用</Comment>
      <DefaultExpression>1</DefaultExpression>
      <Position>8</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="498" parent="317" name="sort">
      <Comment>排序</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>9</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="499" parent="317" name="remark">
      <Comment>备注</Comment>
      <Position>10</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="500" parent="317" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="501" parent="317" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="502" parent="317" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="503" parent="317" name="uk_dept_name">
      <ColNames>dept_name</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="504" parent="317" name="uk_dept_code">
      <ColNames>dept_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="505" parent="317" name="idx_parent_id">
      <ColNames>parent_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="506" parent="317" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="507" parent="317" name="idx_sort">
      <ColNames>sort</ColNames>
      <Type>btree</Type>
    </index>
    <key id="508" parent="317" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="509" parent="317" name="uk_dept_name">
      <UnderlyingIndexName>uk_dept_name</UnderlyingIndexName>
    </key>
    <key id="510" parent="317" name="uk_dept_code">
      <UnderlyingIndexName>uk_dept_code</UnderlyingIndexName>
    </key>
    <column id="511" parent="318" name="id">
      <AutoIncrement>45</AutoIncrement>
      <Comment>菜单ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="512" parent="318" name="parent_id">
      <Comment>父菜单ID，0为顶级</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="513" parent="318" name="title">
      <Comment>菜单名称</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="514" parent="318" name="name">
      <Comment>路由名称</Comment>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="515" parent="318" name="path">
      <Comment>路由路径</Comment>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="516" parent="318" name="component">
      <Comment>组件路径</Comment>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="517" parent="318" name="menu_type">
      <Comment>菜单类型：0菜单、1iframe、2外链、3按钮</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>7</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="518" parent="318" name="icon">
      <Comment>图标</Comment>
      <Position>8</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="519" parent="318" name="rank">
      <Comment>排序</Comment>
      <DefaultExpression>99</DefaultExpression>
      <Position>9</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="520" parent="318" name="redirect">
      <Comment>重定向路径</Comment>
      <Position>10</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="521" parent="318" name="auths">
      <Comment>权限标识</Comment>
      <Position>11</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="522" parent="318" name="show_link">
      <Comment>是否显示：0隐藏，1显示</Comment>
      <DefaultExpression>1</DefaultExpression>
      <Position>12</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="523" parent="318" name="keep_alive">
      <Comment>是否缓存：0否，1是</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>13</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="524" parent="318" name="frame_src">
      <Comment>iframe地址</Comment>
      <Position>14</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="525" parent="318" name="frame_loading">
      <Comment>iframe加载状态</Comment>
      <DefaultExpression>1</DefaultExpression>
      <Position>15</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="526" parent="318" name="hidden_tag">
      <Comment>是否隐藏标签</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>16</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="527" parent="318" name="fixed_tag">
      <Comment>是否固定标签</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>17</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="528" parent="318" name="show_parent">
      <Comment>是否显示父级</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>18</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="529" parent="318" name="status">
      <Comment>状态：0禁用，1启用</Comment>
      <DefaultExpression>1</DefaultExpression>
      <Position>19</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="530" parent="318" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>20</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="531" parent="318" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>21</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="532" parent="318" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="533" parent="318" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="534" parent="319" name="id">
      <AutoIncrement>6</AutoIncrement>
      <Comment>角色ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="535" parent="319" name="role_name">
      <Comment>角色名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="536" parent="319" name="role_code">
      <Comment>角色编码</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="537" parent="319" name="description">
      <Comment>描述</Comment>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="538" parent="319" name="status">
      <Comment>状态：0禁用，1启用</Comment>
      <DefaultExpression>1</DefaultExpression>
      <Position>5</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="539" parent="319" name="sort">
      <Comment>排序</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>6</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="540" parent="319" name="remark">
      <Comment>备注</Comment>
      <Position>7</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="541" parent="319" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="542" parent="319" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="543" parent="319" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="544" parent="319" name="uk_role_code">
      <ColNames>role_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="545" parent="319" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="546" parent="319" name="uk_role_code">
      <UnderlyingIndexName>uk_role_code</UnderlyingIndexName>
    </key>
    <column id="547" parent="320" name="role_id">
      <Comment>角色ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="548" parent="320" name="menu_id">
      <Comment>菜单ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <index id="549" parent="320" name="PRIMARY">
      <ColNames>role_id
menu_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="550" parent="320" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="551" parent="321" name="id">
      <AutoIncrement>3</AutoIncrement>
      <Comment>用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="552" parent="321" name="username">
      <Comment>用户名</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="553" parent="321" name="password">
      <Comment>密码</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="554" parent="321" name="nickname">
      <Comment>昵称</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="555" parent="321" name="avatar">
      <Comment>头像URL</Comment>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="556" parent="321" name="email">
      <Comment>邮箱</Comment>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="557" parent="321" name="phone">
      <Comment>手机号</Comment>
      <Position>7</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="558" parent="321" name="gender">
      <Comment>性别：0女、1男、2未知</Comment>
      <DefaultExpression>2</DefaultExpression>
      <Position>8</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="559" parent="321" name="dept_id">
      <Comment>部门ID</Comment>
      <Position>9</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="560" parent="321" name="status">
      <Comment>状态：0禁用，1启用</Comment>
      <DefaultExpression>1</DefaultExpression>
      <Position>10</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="561" parent="321" name="remark">
      <Comment>备注</Comment>
      <Position>11</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="562" parent="321" name="last_login_time">
      <Comment>最后登录时间</Comment>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="563" parent="321" name="last_login_ip">
      <Comment>最后登录IP</Comment>
      <Position>13</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="564" parent="321" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="565" parent="321" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>15</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="566" parent="321" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="567" parent="321" name="uk_username">
      <ColNames>username</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="568" parent="321" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="569" parent="321" name="uk_username">
      <UnderlyingIndexName>uk_username</UnderlyingIndexName>
    </key>
    <column id="570" parent="322" name="user_id">
      <Comment>用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="571" parent="322" name="role_id">
      <Comment>角色ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <index id="572" parent="322" name="PRIMARY">
      <ColNames>user_id
role_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="573" parent="322" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="574" parent="323" name="id">
      <AutoIncrement>3</AutoIncrement>
      <Comment>教师ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="575" parent="323" name="teacher_code">
      <Comment>教师工号</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="576" parent="323" name="name">
      <Comment>姓名</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="577" parent="323" name="gender">
      <Comment>性别</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>enum(&apos;男&apos;, &apos;女&apos;)|0e</StoredType>
    </column>
    <column id="578" parent="323" name="birth_date">
      <Comment>出生日期</Comment>
      <Position>5</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="579" parent="323" name="phone">
      <Comment>电话号码</Comment>
      <Position>6</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="580" parent="323" name="email">
      <Comment>邮箱</Comment>
      <Position>7</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="581" parent="323" name="college_code">
      <Comment>所属学院代码</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="582" parent="323" name="title">
      <Comment>职称</Comment>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="583" parent="323" name="hire_date">
      <Comment>入职日期</Comment>
      <Position>10</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="584" parent="323" name="status">
      <Comment>状态</Comment>
      <DefaultExpression>&apos;在职&apos;</DefaultExpression>
      <Position>11</Position>
      <StoredType>enum(&apos;在职&apos;, &apos;离职&apos;, &apos;退休&apos;)|0e</StoredType>
    </column>
    <column id="585" parent="323" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>12</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="586" parent="323" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>13</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <foreign-key id="587" parent="323" name="fk_teachers_college">
      <ColNames>college_code</ColNames>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>college_code</RefColNames>
      <RefTableName>colleges</RefTableName>
    </foreign-key>
    <index id="588" parent="323" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="589" parent="323" name="uk_teacher_code">
      <ColNames>teacher_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="590" parent="323" name="idx_college_code">
      <ColNames>college_code</ColNames>
      <Type>btree</Type>
    </index>
    <index id="591" parent="323" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <key id="592" parent="323" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="593" parent="323" name="uk_teacher_code">
      <UnderlyingIndexName>uk_teacher_code</UnderlyingIndexName>
    </key>
    <column id="594" parent="326" name="id">
      <Comment>部门ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="595" parent="326" name="dept_name">
      <Comment>部门名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="596" parent="326" name="dept_code">
      <Comment>部门编码</Comment>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="597" parent="326" name="leader">
      <Comment>负责人</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="598" parent="326" name="phone">
      <Comment>联系电话</Comment>
      <Position>5</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="599" parent="326" name="email">
      <Comment>邮箱</Comment>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="600" parent="326" name="status">
      <Comment>状态：0禁用、1启用</Comment>
      <Position>7</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="601" parent="326" name="sort">
      <Comment>排序</Comment>
      <Position>8</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="602" parent="326" name="remark">
      <Comment>备注</Comment>
      <Position>9</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="603" parent="326" name="create_time">
      <Comment>创建时间</Comment>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="604" parent="326" name="update_time">
      <Comment>更新时间</Comment>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="605" parent="326" name="parent_id">
      <Comment>父部门ID</Comment>
      <Position>12</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="606" parent="326" name="parent_name">
      <Comment>部门名称</Comment>
      <Position>13</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="607" parent="326" name="dept_level">
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>varchar(7)|0s</StoredType>
    </column>
    <column id="608" parent="326" name="full_path">
      <Position>15</Position>
      <StoredType>varchar(209)|0s</StoredType>
    </column>
  </database-model>
</dataSource>