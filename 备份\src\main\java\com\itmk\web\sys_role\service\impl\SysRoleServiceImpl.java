package com.itmk.web.sys_role.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itmk.utils.PageUtils;
import com.itmk.utils.TreeBuilder;
import com.itmk.web.sys_menu.dto.MenuDTO;
import com.itmk.web.sys_menu.entity.SysMenu;
import com.itmk.web.sys_menu.service.SysMenuService;
import com.itmk.web.sys_role.entity.AssignParm;
import com.itmk.web.sys_role.entity.AssignVo;
import com.itmk.web.sys_role.entity.RoleParm;
import com.itmk.web.sys_role.entity.SysRole;
import com.itmk.web.sys_role.mapper.SysRoleMapper;
import com.itmk.web.sys_role.service.SysRoleService;
import com.itmk.web.sys_user.entity.SysUser;
import com.itmk.web.sys_user.service.SysUserService;
import com.itmk.web.sys_role_menu.entity.SysRoleMenu;
import com.itmk.web.sys_role_menu.service.SysRoleMenuService;
import com.itmk.web.sys_user_role.entity.SysUserRole;
import com.itmk.web.sys_user_role.service.SysUserRoleService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements SysRoleService {
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysMenuService sysMenuService;
    @Autowired
    private SysRoleMenuService sysRoleMenuService;
    @Autowired
    private SysUserRoleService sysUserRoleService;
    @Override
    public IPage<SysRole> list(RoleParm parm) {
        //构造分页对象
        IPage<SysRole> page = PageUtils.createPage(parm.getCurrentPage(), parm.getPageSize());
        //构造查询条件
        QueryWrapper<SysRole> query = new QueryWrapper<>();
        if(StringUtils.isNotEmpty(parm.getRoleName())){
            query.lambda().like(SysRole::getRoleName,parm.getRoleName());
        }
        return this.baseMapper.selectPage(page,query);
    }

    //查询用户对应的角色信息
    @Override
    public AssignVo getAssignShow(AssignParm parm) {
        //查询当前用户的信息
        SysUser user = sysUserService.getById(parm.getUserId());
        //菜单数据
        List<SysMenu> list = null;
        if(user.getIsAdmin().equals("1")){ //如果是超级管理员，拥有所有的权限
            QueryWrapper<SysMenu> query = new QueryWrapper<>();
            query.lambda().orderByAsc(SysMenu::getOrderNum);
            //查询所有的菜单
            list = sysMenuService.list(query);
        }else{
            // 查询当前用户的菜单 - 需要直接查询SysMenu实体
            list = sysMenuService.getMenuEntitiesByUserId(user.getUserId());
        }
        //组装菜单树
        List<MenuDTO> menuList = TreeBuilder.buildMenuTree(list, 0L);
        //查询角色原来的菜单
        List<MenuDTO> roleList = sysMenuService.getMenuByRoleId(parm.getRoleId());
        // 过滤出菜单id
        List<Long> ids = new ArrayList<>();
        Optional.ofNullable(roleList).orElse(new ArrayList<>()).stream().filter(item -> item != null).forEach(item -> {
            ids.add(item.getMenuId());
        });
        //组装返回值
        AssignVo vo = new AssignVo();
        vo.setMenuList(menuList);
        vo.setCheckList(ids.toArray());
        return vo;
    }

    /**
     * 安全删除角色（级联删除相关数据）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRoleSafely(Long roleId) {
        if (roleId == null) {
            throw new IllegalArgumentException("角色ID不能为空");
        }

        // 检查角色是否存在
        SysRole role = this.getById(roleId);
        if (role == null) {
            throw new IllegalArgumentException("角色不存在");
        }

        // 1. 删除角色菜单关联
        QueryWrapper<SysRoleMenu> roleMenuQuery = new QueryWrapper<>();
        roleMenuQuery.lambda().eq(SysRoleMenu::getRoleId, roleId);
        sysRoleMenuService.remove(roleMenuQuery);

        // 2. 删除用户角色关联
        QueryWrapper<SysUserRole> userRoleQuery = new QueryWrapper<>();
        userRoleQuery.lambda().eq(SysUserRole::getRoleId, roleId);
        sysUserRoleService.remove(userRoleQuery);

        // 3. 删除角色本身
        return this.removeById(roleId);
    }
}
