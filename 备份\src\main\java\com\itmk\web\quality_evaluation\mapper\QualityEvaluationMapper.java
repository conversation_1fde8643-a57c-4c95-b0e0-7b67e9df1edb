package com.itmk.web.quality_evaluation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.itmk.web.quality_evaluation.entity.QualityEvaluation;
import com.itmk.web.quality_evaluation.entity.QualityEvaluationParm;
import org.apache.ibatis.annotations.Param;

/**
 * 基本素质测评成绩Mapper接口
 */
public interface QualityEvaluationMapper extends BaseMapper<QualityEvaluation> {

    /**
     * 分页查询基本素质测评成绩
     * @param page 分页参数
     * @param parm 查询参数
     * @return 分页结果
     */
    IPage<QualityEvaluation> getList(IPage<QualityEvaluation> page, @Param("parm") QualityEvaluationParm parm);
    
    /**
     * 更新第二学期的prev_period_score字段为上一学期的总分
     * @return 更新的记录数
     */
    int updatePrevPeriodScore();
    
    /**
     * 更新第二学期的period_score字段为prev_period_score
     * @return 更新的记录数
     */
    int updatePeriodScoreFromPrev();
    
    /**
     * 更新第二学期记录的基础分和总分
     * @param id 记录ID
     * @param prevPeriodScore 上一学期总分
     * @param periodScore 本学期基础分
     * @param totalScore 本学期总分
     * @return 更新的记录数
     */
    int updateScores(@Param("id") Integer id,
                    @Param("prevPeriodScore") Double prevPeriodScore,
                    @Param("periodScore") Double periodScore,
                    @Param("totalScore") Double totalScore);

    /**
     * 批量重新计算第二学期的总分
     * @return 更新的记录数
     */
    int recalculateSecondSemesterTotalScores();
} 