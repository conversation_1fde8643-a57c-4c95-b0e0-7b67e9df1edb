interface FormItemProps {
  /** 学生ID */
  id?: number;
  /** 学号 */
  studentId: string;
  /** 姓名 */
  name: string;
  /** 性别 */
  gender: string;
  /** 出生日期 */
  birthDate?: string;
  /** 联系电话 */
  phone?: string;
  /** 邮箱 */
  email?: string;
  /** 所属学院代码 */
  collegeCode?: string;
  /** 所属专业代码 */
  majorCode?: string;
  /** 所属班级代码 */
  classCode?: string;
  /** 入学日期 */
  enrollmentDate?: string;
  /** 学籍状态 */
  status?: string;
}

interface FormProps {
  formInline: FormItemProps;
}

interface QueryFormProps {
  /** 学号 */
  studentId: string;
  /** 姓名 */
  name: string;
  /** 性别 */
  gender: string;
  /** 所属学院代码 */
  collegeCode: string;
  /** 所属专业代码 */
  majorCode: string;
  /** 所属班级代码 */
  classCode: string;
  /** 学籍状态 */
  status: string;
  /** 入学日期 */
  enrollmentDate: string;
  /** 当前页码 */
  current: number;
  /** 每页大小 */
  size: number;
}

export type { FormItemProps, FormProps, QueryFormProps };
