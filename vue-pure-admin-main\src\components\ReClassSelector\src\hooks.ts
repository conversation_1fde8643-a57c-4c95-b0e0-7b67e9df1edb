import { message } from "@/utils/message";
import { type PaginationProps, type TableColumns } from "@pureadmin/table";
import { reactive, ref, onMounted } from "vue";
import {
  getClassesPage,
  type ClassesVO,
  type ClassesQueryDTO
} from "@/api/basic/classes";
import { getAllColleges, type CollegeItem } from "@/api/basic/college";
import { getAllMajors, type MajorItem } from "@/api/basic/major";

interface ClassQueryForm {
  collegeCode: string;
  majorCode: string;
  classCode: string;
  className: string;
  gradeYear: number | null;
  headTeacherCode: string;
}

export function useClassSelector() {
  const queryForm = reactive<ClassQueryForm>({
    collegeCode: "",
    majorCode: "",
    classCode: "",
    className: "",
    gradeYear: null,
    headTeacherCode: ""
  });

  const dataList = ref<ClassesVO[]>([]);
  const loading = ref(true);
  const collegeOptions = ref<CollegeItem[]>([]);
  const majorOptions = ref<MajorItem[]>([]);

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  });

  const columns = [
    {
      label: "序号",
      type: "index",
      width: 70
    },
    {
      label: "学院",
      prop: "collegeName",
      minWidth: 120
    },
    {
      label: "所属专业",
      prop: "majorName",
      minWidth: 150
    },
    {
      label: "班级代码",
      prop: "classCode",
      minWidth: 120
    },
    {
      label: "班级名称",
      prop: "className",
      minWidth: 150
    },
    {
      label: "入学年份",
      prop: "gradeYear",
      minWidth: 100
    },
    {
      label: "学生人数",
      prop: "studentCount",
      minWidth: 100
    },
    {
      label: "班主任",
      prop: "headTeacherName",
      minWidth: 120
    },
    {
      label: "创建时间",
      prop: "createdAt",
      minWidth: 180,
      formatter: ({ createdAt }) =>
        createdAt ? new Date(createdAt).toLocaleString() : ""
    },
    {
      label: "操作",
      fixed: "right",
      width: 120,
      slot: "operation"
    }
  ];

  /** 搜索 */
  async function onSearch() {
    loading.value = true;
    try {
      const query: ClassesQueryDTO = {
        ...queryForm,
        gradeYear: queryForm.gradeYear || undefined,
        current: pagination.currentPage,
        size: pagination.pageSize
      };

      const result = await getClassesPage(query);
      if (result.success && result.data) {
        dataList.value = result.data.records || [];
        pagination.total = result.data.total || 0;
      } else {
        dataList.value = [];
        pagination.total = 0;
        message("查询班级列表失败", { type: "error" });
      }
    } catch (error) {
      message("查询班级列表失败", { type: "error" });
      dataList.value = [];
      pagination.total = 0;
    } finally {
      loading.value = false;
    }
  }

  /** 重置搜索表单 */
  function resetForm() {
    queryForm.collegeCode = "";
    queryForm.majorCode = "";
    queryForm.classCode = "";
    queryForm.className = "";
    queryForm.gradeYear = null;
    queryForm.headTeacherCode = "";
    onSearch();
  }

  /** 加载基础数据 */
  async function loadBaseData() {
    try {
      // 加载学院列表
      const collegesRes = await getAllColleges();
      if (collegesRes.success && collegesRes.data) {
        collegeOptions.value = collegesRes.data;
      } else {
        collegeOptions.value = [];
      }

      // 加载专业列表
      const majorsRes = await getAllMajors();
      if (majorsRes.success && majorsRes.data) {
        majorOptions.value = majorsRes.data;
      } else {
        majorOptions.value = [];
      }
    } catch (error) {
      collegeOptions.value = [];
      majorOptions.value = [];
    }
  }

  // 学院变化处理
  function onCollegeChange() {
    queryForm.majorCode = "";
  }

  /** 分页改变 */
  function onSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function onCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  function handleSelectionChange(_val: ClassesVO[]) {
    // 处理选择变化
  }

  onMounted(() => {
    loadBaseData().then(() => {
      onSearch();
    });
  });

  return {
    queryForm,
    loading,
    columns,
    dataList,
    pagination,
    collegeOptions,
    majorOptions,
    onSearch,
    resetForm,
    onCollegeChange,
    onSizeChange,
    onCurrentChange,
    handleSelectionChange
  };
}
