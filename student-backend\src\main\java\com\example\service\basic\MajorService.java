package com.example.service.basic;

import com.example.dto.basic.MajorQueryDTO;
import com.example.vo.basic.MajorVO;
import com.example.common.PageResult;

import java.util.List;

/**
 * 专业服务接口
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
public interface MajorService {

    /**
     * 分页查询专业列表
     *
     * @param query 查询条件
     * @return 专业列表
     */
    PageResult<MajorVO> getMajorList(MajorQueryDTO query);

    /**
     * 获取所有专业列表
     *
     * @return 专业列表
     */
    List<MajorVO> getAllMajors();

    /**
     * 根据学院代码获取专业列表
     *
     * @param collegeCode 学院代码
     * @return 专业列表
     */
    List<MajorVO> getMajorsByCollegeCode(String collegeCode);

    /**
     * 根据ID获取专业详情
     *
     * @param id 专业ID
     * @return 专业详情
     */
    MajorVO getMajorById(Integer id);

    /**
     * 新增专业
     *
     * @param majorVO 专业信息
     */
    void saveMajor(MajorVO majorVO);

    /**
     * 更新专业
     *
     * @param majorVO 专业信息
     */
    void updateMajor(MajorVO majorVO);

    /**
     * 删除专业
     *
     * @param id 专业ID
     */
    void deleteMajor(Integer id);
}
