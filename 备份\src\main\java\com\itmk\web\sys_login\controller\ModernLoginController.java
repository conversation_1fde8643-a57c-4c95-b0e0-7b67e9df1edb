package com.itmk.web.sys_login.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.itmk.config.jwt.JwtUtils;

import com.itmk.utils.ResultUtils;
import com.itmk.utils.ResultVo;
import com.itmk.web.school_student.entity.SchoolStudent;
import com.itmk.web.school_student.service.SchoolStudentService;
import com.itmk.web.school_teacher.entity.SchoolTeacher;
import com.itmk.web.school_teacher.service.SchoolTeacherService;

import com.itmk.web.sys_login.entity.LoginResult;
import com.itmk.web.sys_login.entity.UserInfoResponse;
import com.itmk.utils.TreeBuilder;
import com.itmk.web.sys_menu.dto.RouterDTO;
import com.itmk.web.sys_menu.entity.RouterVO;
import com.itmk.web.sys_menu.entity.SysMenu;
import com.itmk.web.sys_menu.service.SysMenuService;
import com.itmk.web.sys_role.entity.SysRole;
import com.itmk.web.sys_role.service.SysRoleService;
import com.itmk.web.sys_user.entity.SysUser;
import com.itmk.web.sys_user.service.SysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.Collections;
import java.util.ArrayList;

/**
 * 登录控制器
 */
@RestController
@RequestMapping("/api/auth")
@CrossOrigin(origins = "*", maxAge = 3600)
public class ModernLoginController {

    private static final Logger logger = LoggerFactory.getLogger(ModernLoginController.class);

    @Autowired
    private SchoolStudentService schoolStudentService;

    @Autowired
    private SchoolTeacherService schoolTeacherService;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private SysMenuService sysMenuService;

    @Autowired
    private SysRoleService sysRoleService;



    /**
     * 用户登录接口
     * 基于Spring Security最佳实践实现
     * 解决JSON反序列化超时问题
     *
     * @param request HTTP请求对象
     * @return 登录结果
     */
    @PostMapping("/login")
    public ResponseEntity<ResultVo> login(HttpServletRequest request) {
        logger.info("登录接口被调用，IP: {}", getClientIpAddress(request));

        try {
            LoginRequest loginRequest = parseLoginRequestFromBody(request);

            if (loginRequest == null) {
                return ResponseEntity.ok(ResultUtils.error("请求体格式错误", 400));
            }

            if (!isValidLoginRequest(loginRequest)) {
                return ResponseEntity.ok(ResultUtils.error("用户名、密码或用户类型不能为空", 400));
            }

            String encryptedPassword = DigestUtils.md5DigestAsHex(loginRequest.getPassword().getBytes());
            AuthenticationResult authResult = authenticateUser(loginRequest.getUsername(),
                encryptedPassword, loginRequest.getUserType());

            if (!authResult.isSuccess()) {
                return ResponseEntity.ok(ResultUtils.error(authResult.getMessage(), 401));
            }

            String token = generateJwtToken(authResult.getUser(), loginRequest.getUserType());
            LoginResult loginResult = buildLoginResult(authResult.getUser(), token, loginRequest.getUserType());

            return ResponseEntity.ok(ResultUtils.success("登录成功", loginResult));

        } catch (Exception e) {
            logger.error("登录过程发生异常: {}", e.getMessage(), e);
            logger.error("异常堆栈:", e);
            return ResponseEntity.ok(ResultUtils.error("登录失败，系统异常: " + e.getMessage(), 500));
        }
    }

    /**
     * 用户登出接口
     */
    @PostMapping("/logout")
    public ResponseEntity<ResultVo> logout(HttpServletRequest request) {
        logger.info("用户登出请求，IP: {}", getClientIpAddress(request));

        // 这里可以添加token黑名单逻辑
        // 或者清除服务端session等操作

        return ResponseEntity.ok(ResultUtils.success("登出成功"));
    }

    /**
     * 获取用户信息接口
     * 根据userId和userType获取用户详细信息
     */
    @GetMapping("/getInfo")
    public ResponseEntity<ResultVo> getUserInfo(@RequestParam Long userId, @RequestParam String userType, HttpServletRequest request) {
        logger.info("获取用户信息请求，userId: {}, userType: {}, IP: {}", userId, userType, getClientIpAddress(request));

        try {
            // 验证参数
            if (userId == null || !StringUtils.hasText(userType)) {
                return ResponseEntity.ok(ResultUtils.error("用户ID和用户类型不能为空", 400));
            }

            // 根据用户类型获取用户信息
            UserInfoResponse userInfo = getUserInfoByType(userId, userType);

            if (userInfo == null) {
                return ResponseEntity.ok(ResultUtils.error("用户不存在", 404));
            }

            return ResponseEntity.ok(ResultUtils.success("获取用户信息成功", userInfo));

        } catch (Exception e) {
            logger.error("获取用户信息异常: {}", e.getMessage(), e);
            return ResponseEntity.ok(ResultUtils.error("获取用户信息失败: " + e.getMessage(), 500));
        }
    }

    /**
     * 获取用户菜单列表接口
     * 根据userId和userType获取用户的菜单权限
     */
    @GetMapping("/getMenuList")
    public ResponseEntity<ResultVo> getMenuList(@RequestParam Long userId, @RequestParam String userType, HttpServletRequest request) {
        logger.info("获取用户菜单请求，userId: {}, userType: {}, IP: {}", userId, userType, getClientIpAddress(request));

        try {
            // 验证参数
            if (userId == null || !StringUtils.hasText(userType)) {
                return ResponseEntity.ok(ResultUtils.error("用户ID和用户类型不能为空", 400));
            }

            // 根据用户类型获取菜单列表
            List<RouterVO> menuList = getMenuListByUserType(userId, userType);

            if (menuList == null) {
                return ResponseEntity.ok(ResultUtils.error("获取菜单失败", 500));
            }

            return ResponseEntity.ok(ResultUtils.success("获取菜单成功", menuList));

        } catch (Exception e) {
            logger.error("获取用户菜单异常: {}", e.getMessage(), e);
            return ResponseEntity.ok(ResultUtils.error("获取用户菜单失败: " + e.getMessage(), 500));
        }
    }





    /**
     * 验证登录请求参数
     */
    private boolean isValidLoginRequest(LoginRequest request) {
        return request != null
            && StringUtils.hasText(request.getUsername())
            && StringUtils.hasText(request.getPassword())
            && StringUtils.hasText(request.getUserType());
    }

    /**
     * 用户认证
     */
    private AuthenticationResult authenticateUser(String username, String password, String userType) {
        switch (userType) {
            case "0": // 学生
                return authenticateStudent(username, password);
            case "1": // 教师
                return authenticateTeacher(username, password);
            case "2": // 管理员
                return authenticateAdmin(username, password);
            default:
                return AuthenticationResult.failure("不支持的用户类型");
        }
    }

    /**
     * 学生认证
     */
    private AuthenticationResult authenticateStudent(String studentId, String password) {
        QueryWrapper<SchoolStudent> query = new QueryWrapper<>();
        query.lambda().eq(SchoolStudent::getStudentId, studentId)
                     .eq(SchoolStudent::getPassword, password);

        SchoolStudent student = schoolStudentService.getOne(query);

        if (student == null) {
            return AuthenticationResult.failure("学号或密码错误");
        }

        // 检查学生账号状态
        if (!student.isEnabled()) {
            return AuthenticationResult.failure("账号已被禁用，请联系管理员");
        }

        if (!student.isAccountNonLocked()) {
            return AuthenticationResult.failure("账号已被锁定，请联系管理员");
        }

        return AuthenticationResult.success(student);
    }

    /**
     * 教师认证
     */
    private AuthenticationResult authenticateTeacher(String teacherNum, String password) {
        QueryWrapper<SchoolTeacher> query = new QueryWrapper<>();
        query.lambda().eq(SchoolTeacher::getTeacherNum, teacherNum)
                     .eq(SchoolTeacher::getPassword, password);

        SchoolTeacher teacher = schoolTeacherService.getOne(query);

        if (teacher == null) {
            return AuthenticationResult.failure("工号或密码错误");
        }

        return AuthenticationResult.success(teacher);
    }

    /**
     * 管理员认证
     */
    private AuthenticationResult authenticateAdmin(String username, String password) {
        QueryWrapper<SysUser> query = new QueryWrapper<>();
        query.lambda().eq(SysUser::getUsername, username)
                     .eq(SysUser::getPassword, password);

        SysUser admin = sysUserService.getOne(query);

        if (admin == null) {
            return AuthenticationResult.failure("用户名或密码错误");
        }

        return AuthenticationResult.success(admin);
    }

    /**
     * 根据用户类型获取用户信息
     */
    private UserInfoResponse getUserInfoByType(Long userId, String userType) {
        switch (userType) {
            case "0": // 学生
                return getStudentInfo(userId, userType);
            case "1": // 教师
                return getTeacherInfo(userId, userType);
            case "2": // 管理员
                return getAdminInfo(userId, userType);
            default:
                logger.warn("不支持的用户类型: {}", userType);
                return null;
        }
    }

    /**
     * 获取学生信息
     */
    private UserInfoResponse getStudentInfo(Long userId, String userType) {
        SchoolStudent student = schoolStudentService.getById(userId);
        if (student == null) {
            return null;
        }

        UserInfoResponse response = new UserInfoResponse();
        response.setUserId(student.getStuId());
        response.setUsername(student.getStudentId());
        response.setName(student.getStuName());
        response.setUserType(userType);
        response.setSex(student.getSex());
        response.setPhone(student.getPhone());
        response.setIsEnabled(student.isEnabled());
        response.setIsAccountNonLocked(student.isAccountNonLocked());
        response.setClassId(student.getClassId());
        response.setIntoTime(student.getIntoTime());

        // 获取学生的权限列表
        List<RouterDTO> routerList = sysMenuService.getMenuByStudentId(userId);
        String[] roles = getUserRolesFromRouters(routerList);
        response.setRoles(roles);

        return response;
    }

    /**
     * 获取教师信息
     */
    private UserInfoResponse getTeacherInfo(Long userId, String userType) {
        SchoolTeacher teacher = schoolTeacherService.getById(userId);
        if (teacher == null) {
            return null;
        }

        UserInfoResponse response = new UserInfoResponse();
        response.setUserId(teacher.getTeacherId());
        response.setUsername(teacher.getTeacherNum());
        response.setName(teacher.getTeacherName());
        response.setUserType(userType);
        response.setSex(teacher.getSex());
        response.setPhone(teacher.getPhone());
        // 教师实体类没有账户状态字段，设置默认值
        response.setIsEnabled(true);
        response.setIsAccountNonLocked(true);
        // 可以添加教师特有字段
        // response.setPosition(teacher.getPosition());
        // response.setDepartment(teacher.getDepartment());

        // 获取教师的权限列表
        List<RouterDTO> routerList = sysMenuService.getMenuByTeacherId(userId);
        String[] roles = getUserRolesFromRouters(routerList);
        response.setRoles(roles);

        return response;
    }

    /**
     * 获取管理员信息
     */
    private UserInfoResponse getAdminInfo(Long userId, String userType) {
        SysUser admin = sysUserService.getById(userId);
        if (admin == null) {
            return null;
        }

        UserInfoResponse response = new UserInfoResponse();
        response.setUserId(admin.getUserId());
        response.setUsername(admin.getUsername());
        response.setName(admin.getNickName() != null ? admin.getNickName() : admin.getUsername());
        response.setUserType(userType);
        response.setSex(admin.getSex());
        response.setPhone(admin.getPhone());
        response.setEmail(admin.getEmail());
        response.setIsEnabled(admin.isEnabled());
        response.setIsAccountNonLocked(admin.isAccountNonLocked());
        response.setIsAdmin(admin.getIsAdmin());
        response.setNickName(admin.getNickName());

        // 获取管理员的权限列表
        List<RouterDTO> routerList;
        if ("1".equals(admin.getIsAdmin())) {
            // 超级管理员获取所有权限
            List<SysMenu> allMenus = sysMenuService.list();
            routerList = TreeBuilder.buildRouterTree(allMenus, 0L);
        } else {
            // 普通管理员根据角色获取权限
            routerList = sysMenuService.getMenuByUserId(userId);
        }
        String[] roles = getUserRolesFromRouters(routerList);
        response.setRoles(roles);

        return response;
    }

    /**
     * 从菜单列表中提取权限码（权限字段已移除，返回空数组）
     */
    private String[] getUserRoles(List<SysMenu> menuList) {
        // 权限字段已移除，返回空数组
        return new String[0];
    }

    /**
     * 生成JWT Token
     */
    private String generateJwtToken(Object user, String userType) {
        Map<String, String> claims = new HashMap<>();

        if ("0".equals(userType)) { // 学生
            SchoolStudent student = (SchoolStudent) user;
            claims.put("username", student.getStudentId());
            claims.put("userId", String.valueOf(student.getStuId()));
            claims.put("userType", "0");
        } else if ("1".equals(userType)) { // 教师
            SchoolTeacher teacher = (SchoolTeacher) user;
            claims.put("username", teacher.getTeacherNum());
            claims.put("userId", String.valueOf(teacher.getTeacherId()));
            claims.put("userType", "1");
        } else if ("2".equals(userType)) { // 管理员
            SysUser admin = (SysUser) user;
            claims.put("username", admin.getUsername());
            claims.put("userId", String.valueOf(admin.getUserId()));
            claims.put("userType", "2");
        }

        return jwtUtils.generateToken(claims);
    }

    /**
     * 构建登录结果
     */
    private LoginResult buildLoginResult(Object user, String token, String userType) {
        LoginResult result = new LoginResult();
        result.setToken(token);
        result.setUserType(userType);

        if ("0".equals(userType)) { // 学生
            SchoolStudent student = (SchoolStudent) user;
            result.setUserId(student.getStuId());
            result.setUsername(student.getStudentId());
        } else if ("1".equals(userType)) { // 教师
            SchoolTeacher teacher = (SchoolTeacher) user;
            result.setUserId(teacher.getTeacherId());
            result.setUsername(teacher.getTeacherNum());
        } else if ("2".equals(userType)) { // 管理员
            SysUser admin = (SysUser) user;
            result.setUserId(admin.getUserId());
            result.setUsername(admin.getUsername());
        }

        return result;
    }

    private LoginRequest parseLoginRequestFromBody(HttpServletRequest request) {
        try {
            StringBuilder requestBody = new StringBuilder();

            try (java.io.InputStream inputStream = request.getInputStream();
                 java.io.InputStreamReader reader = new java.io.InputStreamReader(inputStream, "UTF-8")) {

                char[] buffer = new char[1024];
                int bytesRead;
                while ((bytesRead = reader.read(buffer)) != -1) {
                    requestBody.append(buffer, 0, bytesRead);
                }
            }

            String jsonBody = requestBody.toString();
            logger.debug("接收到的请求体: {}", jsonBody);
            return StringUtils.hasText(jsonBody) ? parseJsonToLoginRequest(jsonBody) : null;

        } catch (Exception e) {
            logger.error("解析请求体失败", e);
            return null;
        }
    }

    private LoginRequest parseJsonToLoginRequest(String json) {
        try {
            LoginRequest request = new LoginRequest();
            json = json.trim();
            if (json.startsWith("{") && json.endsWith("}")) {
                json = json.substring(1, json.length() - 1);
            }

            String[] pairs = json.split(",");
            for (String pair : pairs) {
                String[] keyValue = pair.split(":", 2);
                if (keyValue.length == 2) {
                    String key = keyValue[0].trim().replace("\"", "");
                    String value = keyValue[1].trim().replace("\"", "");

                    switch (key) {
                        case "username":
                            request.setUsername(value);
                            break;
                        case "password":
                            request.setPassword(value);
                            break;
                        case "userType":
                            request.setUserType(value);
                            break;
                    }
                }
            }
            return request;
        } catch (Exception e) {
            logger.error("JSON解析失败", e);
            return null;
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.hasText(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (StringUtils.hasText(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    /**
     * 登录请求DTO
     */
    public static class LoginRequest {
        private String username;
        private String password;
        private String userType;

        // Getters and Setters
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }

        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }

        public String getUserType() { return userType; }
        public void setUserType(String userType) { this.userType = userType; }

        @Override
        public String toString() {
            return "LoginRequest{username='" + username + "', userType='" + userType + "'}";
        }
    }

    /**
     * 认证结果封装类
     */
    private static class AuthenticationResult {
        private boolean success;
        private String message;
        private Object user;

        private AuthenticationResult(boolean success, String message, Object user) {
            this.success = success;
            this.message = message;
            this.user = user;
        }

        public static AuthenticationResult success(Object user) {
            return new AuthenticationResult(true, "认证成功", user);
        }

        public static AuthenticationResult failure(String message) {
            return new AuthenticationResult(false, message, null);
        }

        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public Object getUser() { return user; }
    }

    /**
     * 根据用户类型获取菜单列表
     */
    private List<RouterVO> getMenuListByUserType(Long userId, String userType) {
        try {
            List<RouterDTO> routerList = null;

            switch (userType) {
                case "0": // 学生
                    routerList = sysMenuService.getMenuByStudentId(userId);
                    break;
                case "1": // 教师
                    routerList = sysMenuService.getMenuByTeacherId(userId);
                    break;
                case "2": // 管理员
                    // 检查是否是超级管理员
                    SysUser admin = sysUserService.getById(userId);
                    if (admin != null && "1".equals(admin.getIsAdmin())) {
                        // 超级管理员获取所有菜单
                        List<SysMenu> allMenus = sysMenuService.list();
                        routerList = TreeBuilder.buildRouterTree(allMenus, 0L);
                    } else {
                        // 普通管理员根据角色获取菜单
                        routerList = sysMenuService.getMenuByUserId(userId);
                    }
                    break;
                default:
                    logger.warn("不支持的用户类型: {}", userType);
                    return null;
            }

            if (routerList == null || routerList.isEmpty()) {
                logger.warn("用户 {} (类型: {}) 没有分配任何菜单权限", userId, userType);
                return new ArrayList<>();
            }

            // 转换为旧的RouterVO格式以保持兼容性
            List<RouterVO> legacyRouterList = convertToLegacyRouterVO(routerList);

            logger.info("用户 {} (类型: {}) 获取到 {} 个菜单项", userId, userType, legacyRouterList.size());
            return legacyRouterList;

        } catch (Exception e) {
            logger.error("获取用户菜单时发生异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 转换新的RouterDTO为旧的RouterVO格式（保持兼容性）
     */
    private List<RouterVO> convertToLegacyRouterVO(List<RouterDTO> routerList) {
        List<RouterVO> legacyList = new ArrayList<>();

        for (RouterDTO router : routerList) {
            RouterVO legacy = new RouterVO();
            legacy.setPath(router.getPath() != null ? router.getPath() : "");
            legacy.setComponent(router.getComponent() != null ? router.getComponent() : "");
            legacy.setName(router.getName() != null ? router.getName() : "");
            legacy.setAlwaysShow(router.getAlwaysShow() != null && router.getAlwaysShow());

            // 转换Meta信息
            if (router.getMeta() != null) {
                RouterVO routerVO = new RouterVO();
                RouterVO.Meta legacyMeta = routerVO.new Meta(
                    router.getMeta().getTitle() != null ? router.getMeta().getTitle() : "",
                    router.getMeta().getIcon() != null ? router.getMeta().getIcon() : "",
                    router.getMeta().getRoles() != null ? router.getMeta().getRoles() : new String[0]
                );
                legacy.setMeta(legacyMeta);
            }

            // 递归转换子路由
            if (router.hasChildren()) {
                legacy.setChildren(convertToLegacyRouterVO(router.getChildren()));
            }

            legacyList.add(legacy);
        }

        return legacyList;
    }

    /**
     * 从RouterDTO列表中提取角色信息
     */
    private String[] getUserRolesFromRouters(List<RouterDTO> routerList) {
        List<String> roles = new ArrayList<>();

        for (RouterDTO router : routerList) {
            if (router.getMeta() != null && router.getMeta().getRoles() != null) {
                for (String role : router.getMeta().getRoles()) {
                    if (!roles.contains(role)) {
                        roles.add(role);
                    }
                }
            }

            // 递归处理子路由
            if (router.hasChildren()) {
                String[] childRoles = getUserRolesFromRouters(router.getChildren());
                for (String role : childRoles) {
                    if (!roles.contains(role)) {
                        roles.add(role);
                    }
                }
            }
        }

        return roles.toArray(new String[0]);
    }

    /**
     * 根据角色名称获取菜单列表
     */
    @GetMapping("/getMenuByRole")
    public ResultVo getMenuByRole(@RequestParam String roleName) {
        try {
            logger.info("根据角色获取菜单，角色名称: {}", roleName);

            // 验证角色是否存在（从数据库查询）
            QueryWrapper<SysRole> roleQuery = new QueryWrapper<>();
            roleQuery.lambda().eq(SysRole::getRoleName, roleName);
            SysRole role = sysRoleService.getOne(roleQuery);

            if (role == null) {
                return ResultUtils.error("无效的角色名称: " + roleName);
            }

            // 获取角色对应的菜单
            List<RouterDTO> routerList = sysMenuService.getMenuByRoleName(roleName);
            List<RouterVO> menuList = convertToLegacyRouterVO(routerList);

            return ResultUtils.success("获取菜单成功", menuList);

        } catch (Exception e) {
            logger.error("根据角色获取菜单时发生异常，角色: {}", roleName, e);
            return ResultUtils.error("获取菜单失败：" + e.getMessage());
        }
    }

    /**
     * 根据权限代码列表获取菜单
     */
    @PostMapping("/getMenuByPermissions")
    public ResultVo getMenuByPermissions(@RequestBody List<String> permissions) {
        try {
            logger.info("根据权限代码获取菜单，权限数量: {}", permissions.size());

            if (permissions == null || permissions.isEmpty()) {
                return ResultUtils.error("权限代码列表不能为空");
            }

            // 获取权限对应的菜单
            List<RouterDTO> routerList = sysMenuService.getMenuByPermissions(permissions);
            List<RouterVO> menuList = convertToLegacyRouterVO(routerList);

            return ResultUtils.success("获取菜单成功", menuList);

        } catch (Exception e) {
            logger.error("根据权限代码获取菜单时发生异常", e);
            return ResultUtils.error("获取菜单失败：" + e.getMessage());
        }
    }

    /**
     * 解析权限字符串并获取菜单
     */
    @GetMapping("/getMenuByPermissionString")
    public ResultVo getMenuByPermissionString(@RequestParam String permissionString) {
        try {
            logger.info("解析权限字符串获取菜单: {}", permissionString);

            // 解析权限字符串
            List<String> permissions = parsePermissionString(permissionString);

            if (permissions.isEmpty()) {
                return ResultUtils.error("无效的权限字符串");
            }

            // 获取权限对应的菜单
            List<RouterDTO> routerList = sysMenuService.getMenuByPermissions(permissions);
            List<RouterVO> menuList = convertToLegacyRouterVO(routerList);

            return ResultUtils.success("获取菜单成功", menuList);

        } catch (Exception e) {
            logger.error("解析权限字符串获取菜单时发生异常", e);
            return ResultUtils.error("获取菜单失败：" + e.getMessage());
        }
    }

    /**
     * 解析权限字符串为权限列表
     * 处理类似 "sys:system:indexsys:sysUserList:index" 这样连接的权限字符串
     */
    private List<String> parsePermissionString(String permissionString) {
        if (permissionString == null || permissionString.trim().isEmpty()) {
            return Collections.emptyList();
        }

        List<String> permissions = new ArrayList<>();
        String[] parts = permissionString.split("sys:");

        for (String part : parts) {
            if (!part.trim().isEmpty()) {
                permissions.add("sys:" + part.trim());
            }
        }

        return permissions;
    }

    /**
     * 获取所有可用角色列表
     */
    @GetMapping("/getAllRoles")
    public ResultVo getAllRoles() {
        try {
            // 从数据库获取所有角色
            List<SysRole> roles = sysRoleService.list();
            Set<String> roleNames = roles.stream()
                .map(SysRole::getRoleName)
                .collect(Collectors.toSet());
            return ResultUtils.success("获取角色列表成功", roleNames);
        } catch (Exception e) {
            logger.error("获取角色列表时发生异常", e);
            return ResultUtils.error("获取角色列表失败：" + e.getMessage());
        }
    }
}
