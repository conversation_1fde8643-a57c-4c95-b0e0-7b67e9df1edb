package com.example.dto.basic;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 专业查询DTO
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Data
@Schema(description = "专业查询条件")
public class MajorQueryDTO {

    @Schema(description = "当前页码", example = "1")
    private Integer current;

    @Schema(description = "每页大小", example = "10")
    private Integer size;

    @Schema(description = "专业代码")
    private String majorCode;

    @Schema(description = "专业名称")
    private String majorName;

    @Schema(description = "所属学院代码")
    private String collegeCode;

    @Schema(description = "学制年限")
    private Integer duration;

    @Schema(description = "排序字段")
    private String sortField;

    @Schema(description = "排序方向")
    private String sortOrder;

    @Override
    public String toString() {
        return "MajorQueryDTO{" +
                "current=" + current +
                ", size=" + size +
                ", majorCode='" + majorCode + '\'' +
                ", majorName='" + majorName + '\'' +
                ", collegeCode='" + collegeCode + '\'' +
                ", duration=" + duration +
                ", sortField='" + sortField + '\'' +
                ", sortOrder='" + sortOrder + '\'' +
                '}';
    }
}
