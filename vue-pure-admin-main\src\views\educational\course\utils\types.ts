interface FormItemProps {
  /** 课程ID */
  id?: number;
  /** 课程代码 */
  courseCode: string;
  /** 课程名称 */
  courseName: string;
  /** 学分 */
  credits: number;
  /** 课程类型 */
  courseType: string;
  /** 所属学院代码 */
  collegeCode?: string;
  /** 课程描述 */
  description?: string;
}

interface FormProps {
  formInline: FormItemProps;
}

interface QueryFormProps {
  /** 课程代码 */
  courseCode: string;
  /** 课程名称 */
  courseName: string;
  /** 课程类型 */
  courseType: string;
  /** 所属学院代码 */
  collegeCode: string;
}

export type { FormItemProps, FormProps, QueryFormProps };
