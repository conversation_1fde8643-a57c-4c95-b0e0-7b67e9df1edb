package com.example.service.score;

import com.example.common.PageResult;
import com.example.dto.score.GradeQueryDTO;
import com.example.vo.score.GradeVO;

import java.util.List;
import java.util.Map;

/**
 * 期末成绩服务接口
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface FinalGradeService {

    /**
     * 分页查询期末成绩列表
     *
     * @param params 查询参数
     * @return 分页结果
     */
    PageResult<GradeVO> getGradePage(GradeQueryDTO params);

    /**
     * 根据班级查询期末成绩列表
     *
     * @param classCode  班级代码
     * @param semesterId 学期ID
     * @return 期末成绩列表
     */
    List<GradeVO> getGradesByClass(String classCode, Integer semesterId);

    /**
     * 根据学生查询期末成绩列表
     *
     * @param studentId  学号
     * @param semesterId 学期ID
     * @return 期末成绩列表
     */
    List<GradeVO> getGradesByStudent(String studentId, Integer semesterId);

    /**
     * 获取横向期末成绩数据
     *
     * @param classCode  班级代码
     * @param semesterId 学期ID
     * @param pageNum    页码
     * @param pageSize   页大小
     * @return 横向成绩数据
     */
    Map<String, Object> getHorizontalGrades(String classCode, Integer semesterId, Integer pageNum, Integer pageSize);

    /**
     * 导出期末成绩
     *
     * @param classCode  班级代码
     * @param semesterId 学期ID
     * @return Excel文件字节数组
     */
    byte[] exportGrades(String classCode, Integer semesterId);

    /**
     * 导出多学期合并期末成绩
     *
     * @param classCode   班级代码
     * @param semesterIds 学期ID列表
     * @return Excel文件字节数组
     */
    byte[] exportMultipleSemestersGrades(String classCode, List<Integer> semesterIds);

    /**
     * 根据班级代码获取班级名称
     *
     * @param classCode 班级代码
     * @return 班级名称
     */
    String getClassNameByCode(String classCode);
}
