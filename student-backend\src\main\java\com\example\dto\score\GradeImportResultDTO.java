package com.example.dto.score;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 成绩导入结果DTO
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class GradeImportResultDTO {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 总行数
     */
    private int totalRows;

    /**
     * 成功导入行数
     */
    private int successRows;

    /**
     * 失败行数
     */
    private int failedRows;

    /**
     * 错误信息列表
     */
    private List<String> errorMessages = new ArrayList<>();

    /**
     * 详细错误信息（行号 -> 错误信息）
     */
    private List<ErrorDetail> errorDetails = new ArrayList<>();

    /**
     * 错误详情
     */
    @Data
    public static class ErrorDetail {
        /**
         * 行号
         */
        private Integer rowNumber;

        /**
         * 学号
         */
        private String studentId;

        /**
         * 错误信息
         */
        private String errorMessage;

        public ErrorDetail(Integer rowNumber, String studentId, String errorMessage) {
            this.rowNumber = rowNumber;
            this.studentId = studentId;
            this.errorMessage = errorMessage;
        }
    }

    /**
     * 添加错误详情
     */
    public void addErrorDetail(Integer rowNumber, String studentId, String errorMessage) {
        this.errorDetails.add(new ErrorDetail(rowNumber, studentId, errorMessage));
        this.errorMessages.add(String.format("第%d行（学号：%s）：%s", rowNumber, studentId, errorMessage));
    }

    /**
     * 添加错误信息
     */
    public void addErrorMessage(String errorMessage) {
        this.errorMessages.add(errorMessage);
    }

    /**
     * 获取结果摘要
     */
    public String getSummary() {
        return String.format("导入完成：总计%d行，成功%d行，失败%d行", totalRows, successRows, failedRows);
    }
}
