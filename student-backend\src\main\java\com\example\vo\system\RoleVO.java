package com.example.vo.system;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色响应VO
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
public class RoleVO {

    /**
     * 角色ID
     */
    private Integer id;

    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    private String roleName;

    /**
     * 角色编码
     */
    @NotBlank(message = "角色编码不能为空")
    private String roleCode;

    /**
     * 角色描述
     */
    private String description;

    /**
     * 状态：0禁用、1启用
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 菜单权限ID列表
     */
    private List<Integer> menuIds;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
