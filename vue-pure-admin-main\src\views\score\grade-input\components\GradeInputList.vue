<template>
  <div class="main-content">
    <PureTableBar
      :title="`${majorName} - ${className} - ${courseName} - ${semesterName} - 成绩录入`"
      :columns="columns"
      @refresh="onSearch"
    >
      <template #buttons>
        <el-space>
          <el-input
            v-model="form.studentName"
            placeholder="学生姓名"
            clearable
            @input="onSearch"
            style="width: 150px"
          />
          <el-input
            v-model="form.studentId"
            placeholder="学号"
            clearable
            @input="onSearch"
            style="width: 150px"
          />
          <el-button type="primary" @click="onSearch">
            搜索
          </el-button>
          <el-button @click="onReset">
            重置
          </el-button>
          <el-button type="info" @click="handleBack">
            返回
          </el-button>
        </el-space>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="gradeData"
          :columns="dynamicColumns"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
        >
          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon(EditPen)"
              @click="handleEdit(row)"
            >
              {{ row.hasGrade ? '编辑' : '录入' }}
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <!-- 成绩录入弹窗 -->
    <GradeInputDialog
      v-model="inputDialogVisible"
      :grade-data="currentGrade"
      :course-code="courseCode"
      :course-name="courseName"
      :semester-id="semesterId"
      @success="handleInputSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, computed, watch, h } from "vue";
import { message } from "@/utils/message";
import { getStudentGradeInputList } from "@/api/score/grade-input";
import { PureTableBar } from "@/components/RePureTableBar";
import GradeInputDialog from "./GradeInputDialog.vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import EditPen from "~icons/ep/edit-pen";

import type { TableColumnList } from "@pureadmin/table";
import type { GradeInputVO } from "@/api/score/grade-input";

defineOptions({
  name: "GradeInputList"
});

// Props
interface Props {
  classCode: string;
  className: string;
  majorName: string;
  courseCode: string;
  courseName: string;
  semesterId: number;
  semesterName: string;
}

const props = withDefaults(defineProps<Props>(), {
  classCode: "",
  className: "",
  majorName: "",
  courseCode: "",
  courseName: "",
  semesterId: 0,
  semesterName: ""
});

// Emits
const emit = defineEmits<{
  back: [];
}>();

// 响应式数据
const loading = ref(true);
const tableRef = ref();

// 搜索表单
const form = reactive({
  studentName: "",
  studentId: ""
});

// 成绩数据
const gradeData = ref<GradeInputVO[]>([]);

// 弹窗控制
const inputDialogVisible = ref(false);
const currentGrade = ref<GradeInputVO | null>(null);

// 表格列配置
const columns: TableColumnList = [
  {
    label: "序号",
    type: "index",
    width: 70,
    align: "center"
  },
  {
    label: "学号",
    prop: "studentId",
    width: 120,
    align: "center"
  },
  {
    label: "姓名",
    prop: "studentName",
    width: 100,
    align: "center"
  },
  {
    label: "期末成绩",
    prop: "finalScore",
    width: 100,
    align: "center",
    cellRenderer: ({ row }) => {
      if (row.hasGrade && row.finalScore !== null && row.finalScore !== undefined) {
        return row.finalScore.toFixed(2);
      }
      return "-";
    }
  },
  {
    label: "绩点",
    prop: "gradePoint",
    width: 80,
    align: "center",
    cellRenderer: ({ row }) => {
      if (row.hasGrade && row.gradePoint !== null && row.gradePoint !== undefined) {
        return row.gradePoint.toFixed(2);
      }
      return "-";
    }
  },
  {
    label: "是否重修",
    prop: "isRetake",
    width: 100,
    align: "center",
    cellRenderer: ({ row }) => {
      if (row.hasGrade) {
        return row.isRetake ? "是" : "否";
      }
      return "-";
    }
  },
  {
    label: "录入状态",
    prop: "hasGrade",
    width: 100,
    align: "center",
    cellRenderer: ({ row }) => {
      return h("el-tag", {
        type: row.hasGrade ? "success" : "info"
      }, row.hasGrade ? "已录入" : "未录入");
    }
  },
  {
    label: "操作",
    fixed: "right",
    width: 120,
    slot: "operation"
  }
];

// 不再需要获取学期列表，直接使用传入的学期信息

// 获取成绩数据
const getGrades = async () => {
  if (!props.classCode || !props.courseCode || !props.semesterId) {
    return;
  }

  loading.value = true;
  try {
    const { data } = await getStudentGradeInputList(props.classCode, props.courseCode, props.semesterId);
    gradeData.value = data || [];
  } catch (error) {
    console.error("获取成绩数据失败:", error);
    message("获取成绩数据失败", { type: "error" });
  } finally {
    loading.value = false;
  }
};

// 搜索
const onSearch = () => {
  getGrades();
};

// 重置
const onReset = () => {
  form.studentName = "";
  form.studentId = "";
  getGrades();
};

// 返回
const handleBack = () => {
  emit('back');
};

// 编辑/录入成绩
const handleEdit = (row: GradeInputVO) => {
  currentGrade.value = row;
  inputDialogVisible.value = true;
};



// 录入成功回调
const handleInputSuccess = () => {
  message("操作成功", { type: "success" });
  getGrades();
};



// 组件挂载
onMounted(() => {
  getGrades();
});
</script>

<style lang="scss" scoped>
/* 覆盖全局main-content样式，让表格左右铺满 */
.main-content {
  margin: 10px 0 0 !important;
}
</style>

