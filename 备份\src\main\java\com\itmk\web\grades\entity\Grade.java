package com.itmk.web.grades.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("grades")
public class Grade {
    @TableId(type = IdType.AUTO)
    private Integer gradeId;       // 成绩ID
    private String studentId;      // 学生学号
    private String courseCode;     // 课程代码
    private Integer semesterId;    // 学期ID
    private Double grade;          // 成绩，可以为null表示无成绩
    private String remarks;        // 备注

    // 课程信息 - 这些字段不存在于数据库表中
    @TableField(exist = false)
    private String courseName;     // 课程名称
    @TableField(exist = false)
    private Double credits;        // 学分
    @TableField(exist = false)
    private Double gpa;            // 绩点

    // 学业成绩 - 这个字段不存在于数据库表中
    @TableField(exist = false)
    private Double calculateGpa; // 学业成绩
    
    // 学期名称 - 用于显示，不存储在数据库中
    @TableField(exist = false)
    private String semesterName;
}