package com.itmk.config;

import com.itmk.utils.LogUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * 安全配置类
 * 统一管理安全相关配置和异常处理
 *
 * 注意：JWT的核心类（JwtUtils、JwtInterceptor等）保留在 config.jwt 包中，
 * 这里主要提供安全相关的统一配置和异常处理
 *
 * <AUTHOR>
 * @since 2025-07-19
 */
@Configuration
public class SecurityConfig {

    private static final Logger logger = LoggerFactory.getLogger(SecurityConfig.class);

    // ==================== 异常处理配置 ====================

    /**
     * 全局异常处理器
     */
    @Bean
    public GlobalExceptionHandler globalExceptionHandler() {
        return new GlobalExceptionHandler();
    }

    /**
     * 全局异常处理器实现
     */
    public static class GlobalExceptionHandler implements HandlerExceptionResolver {

        private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

        @Override
        public ModelAndView resolveException(HttpServletRequest request, HttpServletResponse response,
                                           Object handler, Exception ex) {

            // 记录异常日志
            String requestURI = request.getRequestURI();
            String method = request.getMethod();
            LogUtils.logOperationError(logger, method, requestURI, ex.getMessage(), ex);

            // 设置响应
            response.setContentType("application/json;charset=UTF-8");
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);

            try (PrintWriter writer = response.getWriter()) {
                if (ex instanceof BusinessException) {
                    BusinessException businessException = (BusinessException) ex;
                    writer.write(String.format(
                        "{\"code\":%d,\"msg\": \"%s\",\"data\":null}",
                        businessException.getCode(),
                        businessException.getMessage()
                    ));
                } else if (ex instanceof IllegalArgumentException) {
                    writer.write(String.format(
                        "{\"code\":400,\"msg\": \"%s\",\"data\":null}",
                        ex.getMessage()
                    ));
                } else if (ex instanceof IllegalStateException) {
                    writer.write(String.format(
                        "{\"code\":409,\"msg\": \"%s\",\"data\":null}",
                        ex.getMessage()
                    ));
                } else {
                    writer.write("{\"code\":500,\"msg\":\"系统内部错误\",\"data\":null}");
                }
            } catch (IOException e) {
                logger.error("写入异常响应失败", e);
            }

            return new ModelAndView();
        }
    }

    // ==================== 业务异常类 ====================

    /**
     * 业务异常类
     * 用于处理业务逻辑中的异常情况
     */
    public static class BusinessException extends RuntimeException {

        private static final long serialVersionUID = 1L;

        private Integer code;

        public BusinessException(String message) {
            super(message);
            this.code = 400;
        }

        public BusinessException(Integer code, String message) {
            super(message);
            this.code = code;
        }

        public BusinessException(String message, Throwable cause) {
            super(message, cause);
            this.code = 400;
        }

        public BusinessException(Integer code, String message, Throwable cause) {
            super(message, cause);
            this.code = code;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }
    }

    // ==================== 安全配置管理 ====================

    /**
     * 安全配置管理器
     */
    @Bean
    public SecurityConfigManager securityConfigManager() {
        return new SecurityConfigManager();
    }

    /**
     * 安全配置管理器实现
     */
    public static class SecurityConfigManager {

        private static final Logger logger = LoggerFactory.getLogger(SecurityConfigManager.class);

        public SecurityConfigManager() {
            LogUtils.logSystemStart(logger, "安全配置管理器");

            // 记录安全配置信息
            LogUtils.logConfigLoaded(logger, "JWT认证", "启用");
            LogUtils.logConfigLoaded(logger, "全局异常处理", "启用");
            LogUtils.logConfigLoaded(logger, "安全增强", "启用");

            logger.info("安全配置初始化完成");
        }

        /**
         * 验证密码强度
         *
         * @param password 密码
         * @return 是否符合强度要求
         */
        public boolean validatePasswordStrength(String password) {
            if (password == null || password.length() < 8) {
                return false;
            }

            // 检查是否包含数字
            boolean hasDigit = password.chars().anyMatch(Character::isDigit);
            // 检查是否包含字母
            boolean hasLetter = password.chars().anyMatch(Character::isLetter);
            // 检查是否包含特殊字符
            boolean hasSpecial = password.chars().anyMatch(ch -> !Character.isLetterOrDigit(ch));

            return hasDigit && hasLetter && hasSpecial;
        }

        /**
         * 生成安全的随机密钥
         *
         * @param length 密钥长度
         * @return 随机密钥
         */
        public String generateSecureKey(int length) {
            String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
            StringBuilder key = new StringBuilder();

            for (int i = 0; i < length; i++) {
                int index = (int) (Math.random() * chars.length());
                key.append(chars.charAt(index));
            }

            return key.toString();
        }

        /**
         * 记录安全事件
         *
         * @param eventType 事件类型
         * @param description 事件描述
         * @param clientIP 客户端IP
         */
        public void logSecurityEvent(String eventType, String description, String clientIP) {
            logger.warn("安全事件 - 类型: {}, 描述: {}, 客户端IP: {}", eventType, description, clientIP);
        }
    }

    // ==================== 参数验证配置 ====================

    /**
     * 参数验证工具
     */
    @Bean
    public ParameterValidator parameterValidator() {
        return new ParameterValidator();
    }

    /**
     * 参数验证工具实现
     */
    public static class ParameterValidator {

        private static final Logger logger = LoggerFactory.getLogger(ParameterValidator.class);

        public ParameterValidator() {
            LogUtils.logSystemStart(logger, "参数验证工具");
        }

        /**
         * 验证手机号格式
         *
         * @param phone 手机号
         * @return 是否有效
         */
        public boolean isValidPhone(String phone) {
            if (phone == null || phone.trim().isEmpty()) {
                return false;
            }
            return phone.matches("^1[3-9]\\d{9}$");
        }

        /**
         * 验证邮箱格式
         *
         * @param email 邮箱
         * @return 是否有效
         */
        public boolean isValidEmail(String email) {
            if (email == null || email.trim().isEmpty()) {
                return false;
            }
            return email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
        }

        /**
         * 验证身份证号格式
         *
         * @param idCard 身份证号
         * @return 是否有效
         */
        public boolean isValidIdCard(String idCard) {
            if (idCard == null || idCard.trim().isEmpty()) {
                return false;
            }
            return idCard.matches("^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$");
        }

        /**
         * 验证学号格式
         *
         * @param studentId 学号
         * @return 是否有效
         */
        public boolean isValidStudentId(String studentId) {
            if (studentId == null || studentId.trim().isEmpty()) {
                return false;
            }
            // 假设学号格式为：年份(4位) + 专业代码(2位) + 序号(4位)
            return studentId.matches("^20\\d{8}$");
        }

        /**
         * 验证参数不为空
         *
         * @param value 参数值
         * @param fieldName 字段名
         * @throws BusinessException 如果参数为空
         */
        public void validateNotEmpty(String value, String fieldName) {
            if (value == null || value.trim().isEmpty()) {
                throw new BusinessException(400, fieldName + "不能为空");
            }
        }

        /**
         * 验证参数长度
         *
         * @param value 参数值
         * @param fieldName 字段名
         * @param maxLength 最大长度
         * @throws BusinessException 如果长度超限
         */
        public void validateLength(String value, String fieldName, int maxLength) {
            if (value != null && value.length() > maxLength) {
                throw new BusinessException(400, fieldName + "长度不能超过" + maxLength + "个字符");
            }
        }
    }
}
