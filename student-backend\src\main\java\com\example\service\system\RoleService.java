package com.example.service.system;

import com.example.common.PageResult;
import com.example.dto.system.RoleQueryDTO;
import com.example.vo.system.RoleVO;

import java.util.List;

/**
 * 角色服务接口
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
public interface RoleService {

    /**
     * 获取角色列表
     *
     * @param query 查询条件
     * @return 分页角色列表
     */
    PageResult<RoleVO> getRoleList(RoleQueryDTO query);

    /**
     * 获取所有角色列表
     *
     * @return 所有角色列表
     */
    List<RoleVO> getAllRoles();

    /**
     * 根据ID获取角色详情
     *
     * @param id 角色ID
     * @return 角色详情
     */
    RoleVO getRoleById(Integer id);

    /**
     * 新增角色
     *
     * @param roleVO 角色信息
     */
    void saveRole(RoleVO roleVO);

    /**
     * 更新角色
     *
     * @param roleVO 角色信息
     */
    void updateRole(RoleVO roleVO);

    /**
     * 删除角色
     *
     * @param roleId 角色ID
     */
    void deleteRole(Integer roleId);

    /**
     * 获取角色菜单权限
     *
     * @param roleId 角色ID
     * @return 菜单ID列表
     */
    List<Integer> getRoleMenuPermissions(Integer roleId);

    /**
     * 分配角色菜单权限
     *
     * @param roleId 角色ID
     * @param menuIds 菜单ID列表
     */
    void assignMenuPermissions(Integer roleId, List<Integer> menuIds);

    /**
     * 检查角色名称是否存在
     *
     * @param roleName 角色名称
     * @param excludeId 排除的角色ID
     * @return 是否存在
     */
    boolean isRoleNameExists(String roleName, Integer excludeId);

    /**
     * 检查角色编码是否存在
     *
     * @param roleCode 角色编码
     * @param excludeId 排除的角色ID
     * @return 是否存在
     */
    boolean isRoleCodeExists(String roleCode, Integer excludeId);

    /**
     * 切换角色状态
     *
     * @param roleId 角色ID
     * @param status 状态
     */
    void toggleRoleStatus(Integer roleId, Integer status);
}
