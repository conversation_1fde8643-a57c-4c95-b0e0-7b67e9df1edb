interface FormItemProps {
  /** 班级ID */
  id?: number;
  /** 班级代码 */
  classCode: string;
  /** 班级名称 */
  className: string;
  /** 所属学院代码 */
  collegeCode: string;
  /** 所属专业代码 */
  majorCode: string;
  /** 入学年份 */
  gradeYear: number;
  /** 学生人数 */
  studentCount?: number;
  /** 班主任工号 */
  headTeacherCode?: string;
  /** 学院选项 */
  collegeOptions: Record<string, unknown>[];
  /** 专业选项 */
  majorOptions: Record<string, unknown>[];
  /** 教师选项 */
  teacherOptions: Record<string, unknown>[];
}

interface FormProps {
  formInline: FormItemProps;
}

interface QueryFormProps {
  /** 班级代码 */
  classCode: string;
  /** 班级名称 */
  className: string;
  /** 所属专业代码 */
  majorCode: string;
  /** 入学年份 */
  gradeYear: number | null;
  /** 班主任工号 */
  headTeacherCode: string;
  /** 学院代码 */
  collegeCode: string;
}

export type { FormItemProps, FormProps, QueryFormProps };
