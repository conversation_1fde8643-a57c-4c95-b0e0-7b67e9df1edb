package com.example.controller.system;

import com.example.common.Result;
import com.example.service.system.MenuService;
import com.example.utils.JwtUtil;
import com.example.vo.system.MenuVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

import java.util.List;
import java.util.Map;

/**
 * 系统菜单管理控制器
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
@RestController
@RequestMapping("/api/system/menu")
@RequiredArgsConstructor
@Validated
@Tag(name = "系统菜单管理", description = "系统菜单管理相关接口")
public class SystemMenuController {

    private final MenuService menuService;
    private final JwtUtil jwtUtil;

    /**
     * 获取菜单列表（用于菜单管理页面）
     */
    @PostMapping("/list")
    @Operation(summary = "获取菜单列表", description = "获取系统菜单列表，用于菜单管理页面")
    public Result<List<MenuVO>> getMenuList(@RequestBody(required = false) Map<String, Object> params) {
        List<MenuVO> menuList = menuService.getMenuList();
        return Result.success("查询成功", menuList);
    }

    /**
     * 新增菜单
     */
    @PostMapping("/save")
    @Operation(summary = "新增菜单", description = "新增系统菜单")
    public Result<Void> saveMenu(@Valid @RequestBody MenuVO menuVO) {
        menuService.saveMenu(menuVO);
        return Result.success("新增菜单成功");
    }

    /**
     * 更新菜单
     */
    @PostMapping("/update")
    @Operation(summary = "更新菜单", description = "更新系统菜单")
    public Result<Void> updateMenu(@Valid @RequestBody MenuVO menuVO) {
        if (menuVO.getId() == null) {
            return Result.badRequest("菜单ID不能为空");
        }
        menuService.updateMenu(menuVO);
        return Result.success("修改菜单成功");
    }

    /**
     * 删除菜单
     */
    @PostMapping("/delete")
    @Operation(summary = "删除菜单", description = "删除系统菜单")
    public Result<Void> deleteMenu(@RequestBody Map<String, Object> params) {
        if (params == null || params.get("id") == null) {
            return Result.badRequest("菜单ID不能为空");
        }

        try {
            Integer menuId = Integer.valueOf(params.get("id").toString());
            menuService.deleteMenu(menuId);
            return Result.success("删除菜单成功");
        } catch (NumberFormatException e) {
            return Result.badRequest("菜单ID格式错误");
        }
    }

    /**
     * 切换菜单状态
     */
    @PostMapping("/toggle-status")
    @Operation(summary = "切换菜单状态", description = "启用或禁用菜单")
    public Result<Void> toggleMenuStatus(@RequestBody Map<String, Object> params) {
        if (params == null || params.get("menuId") == null || params.get("status") == null) {
            return Result.badRequest("菜单ID和状态不能为空");
        }

        try {
            Integer menuId = Integer.valueOf(params.get("menuId").toString());
            Integer status = Integer.valueOf(params.get("status").toString());
            menuService.toggleMenuStatus(menuId, status);
            String statusText = status == 1 ? "启用" : "禁用";
            return Result.success(statusText + "菜单成功");
        } catch (NumberFormatException e) {
            return Result.badRequest("参数格式错误");
        }
    }
}
