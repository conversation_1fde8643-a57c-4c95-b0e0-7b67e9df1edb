package com.itmk.web.sys_login.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 用户信息响应DTO
 * 用于统一返回不同类型用户的基本信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserInfoResponse {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名/学号/工号
     */
    private String username;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 用户类型 (0:学生, 1:教师, 2:管理员)
     */
    private String userType;

    /**
     * 性别
     */
    private String sex;

    /**
     * 电话
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 账户状态
     */
    private Boolean isEnabled;

    /**
     * 账户是否锁定
     */
    private Boolean isAccountNonLocked;

    /**
     * 角色信息（管理员使用）
     */
    private String roleName;

    /**
     * 班级ID（学生使用）
     */
    private Long classId;

    /**
     * 入学时间（学生使用）
     */
    private String intoTime;

    /**
     * 职位（教师使用）
     */
    private String position;

    /**
     * 部门（教师使用）
     */
    private String department;

    /**
     * 是否为管理员
     */
    private String isAdmin;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 用户权限列表
     */
    private String[] roles;
}
