package com.example.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.entity.system.SysRole;
import com.example.entity.system.SysMenu;
import com.example.dto.system.RoleQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统角色Mapper
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Mapper
public interface SysRoleMapper extends BaseMapper<SysRole> {

    /**
     * 查询角色列表
     *
     * @param page 分页参数
     * @param query 查询条件
     * @return 角色列表
     */
    IPage<SysRole> selectRoleList(IPage<SysRole> page, @Param("query") RoleQueryDTO query);

    /**
     * 查询所有启用的角色
     *
     * @return 角色列表
     */
    List<SysRole> selectAllActive();

    /**
     * 根据角色编码查询角色
     *
     * @param roleCode 角色编码
     * @return 角色信息
     */
    SysRole selectByRoleCode(@Param("roleCode") String roleCode);

    /**
     * 根据用户ID查询角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<SysRole> selectByUserId(@Param("userId") Integer userId);

    /**
     * 检查角色名称是否存在
     *
     * @param roleName 角色名称
     * @param excludeId 排除的角色ID
     * @return 是否存在
     */
    Boolean existsByRoleName(@Param("roleName") String roleName, @Param("excludeId") Integer excludeId);

    /**
     * 检查角色编码是否存在
     *
     * @param roleCode 角色编码
     * @param excludeId 排除的角色ID
     * @return 是否存在
     */
    Boolean existsByRoleCode(@Param("roleCode") String roleCode, @Param("excludeId") Integer excludeId);

    /**
     * 获取最大排序值
     *
     * @return 最大排序值
     */
    Integer getMaxSort();

    /**
     * 根据角色ID查询用户数量
     *
     * @param roleId 角色ID
     * @return 用户数量
     */
    Long countUsersByRoleId(@Param("roleId") Integer roleId);

    /**
     * 批量更新角色状态
     *
     * @param roleIds 角色ID列表
     * @param status 状态
     */
    void batchUpdateStatus(@Param("roleIds") List<Integer> roleIds, @Param("status") Integer status);

    /**
     * 查询角色权限（菜单ID列表）
     *
     * @param roleId 角色ID
     * @return 菜单ID列表
     */
    List<Integer> selectRoleMenuIds(@Param("roleId") Integer roleId);

    /**
     * 查询角色的菜单权限详情
     *
     * @param roleId 角色ID
     * @return 菜单列表
     */
    List<SysMenu> selectRoleMenus(@Param("roleId") Integer roleId);

    /**
     * 统计角色数量
     *
     * @return 角色数量
     */
    Long countRoles();

    /**
     * 查询角色详情
     *
     * @param roleId 角色ID
     * @return 角色信息
     */
    SysRole selectRoleDetail(@Param("roleId") Integer roleId);
}
