package com.example.mapper.student;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.dto.student.StudentsQueryDTO;
import com.example.entity.student.Students;
import com.example.vo.student.StudentsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 学生信息Mapper接口
 */
@Mapper
public interface StudentsMapper extends BaseMapper<Students> {

    /**
     * 分页查询学生列表
     *
     * @param page 分页对象
     * @param queryDTO 查询条件
     * @return 学生列表
     */
    IPage<StudentsVO> selectStudentsPage(Page<StudentsVO> page, @Param("query") StudentsQueryDTO queryDTO);

    /**
     * 根据班级代码查询学生列表
     *
     * @param classCode 班级代码
     * @return 学生列表
     */
    List<StudentsVO> selectStudentsByClassCode(@Param("classCode") String classCode);

    /**
     * 根据专业代码查询学生列表
     *
     * @param majorCode 专业代码
     * @return 学生列表
     */
    List<StudentsVO> selectStudentsByMajorCode(@Param("majorCode") String majorCode);

    /**
     * 检查学号是否存在
     *
     * @param studentId 学号
     * @param excludeId 排除的学生ID
     * @return 是否存在
     */
    boolean existsStudentId(@Param("studentId") String studentId, @Param("excludeId") Integer excludeId);

    /**
     * 根据班级代码统计学生人数
     *
     * @param classCode 班级代码
     * @return 学生人数
     */
    int countStudentsByClassCode(@Param("classCode") String classCode);

    /**
     * 根据班级代码获取班级ID
     *
     * @param classCode 班级代码
     * @return 班级ID
     */
    Integer getClassIdByClassCode(@Param("classCode") String classCode);
}
