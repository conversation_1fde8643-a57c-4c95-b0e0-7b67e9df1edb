import { ref, h } from "vue";
import { addDialog } from "@/components/ReDialog";
import { deviceDetection } from "@pureadmin/utils";

/**
 * 通用CRUD操作Hook
 */
export function useCrud<T = any>(
  formComponent: any,
  apis: {
    save: (data: T) => Promise<any>;
    update: (data: T) => Promise<any>;
    delete: (id: number) => Promise<any>;
    batchDelete?: (ids: number[]) => Promise<any>;
  },
  options: {
    entityName: string; // 实体名称，如"学生"、"班级"
    onSuccess?: () => void; // 操作成功后的回调
  }
) {
  const formRef = ref();

  /** 打开对话框 */
  function openDialog(title = "新增", row?: T, formProps = {}) {
    addDialog({
      title: `${title}${options.entityName}`,
      props: {
        formInline: {
          ...(row || {}),
          ...formProps
        }
      },
      width: "46%",
      draggable: true,
      fullscreen: deviceDetection(),
      fullscreenIcon: true,
      closeOnClickModal: false,
      contentRenderer: () => h(formComponent, { ref: formRef }),
      beforeSure: (done) => {
        const FormRef = formRef.value.getRef();
        const curData = formRef.value.getFormData() as T;

        function chores() {
          // 成功消息由后端统一处理，前端不再显示自定义消息
          done();
          options.onSuccess?.();
        }

        FormRef.validate(async (valid: boolean) => {
          if (valid) {
            try {
              let result: any;
              if (title === "新增") {
                result = await apis.save(curData);
              } else {
                result = await apis.update(curData);
              }

              if (result.success) {
                chores();
              }
              // 错误消息由后端统一处理，前端不再显示自定义消息
            } catch (error) {
              // 错误消息由后端统一处理，前端不再显示自定义消息
              console.error(`${title}失败:`, error);
            }
          }
        });
      }
    });
  }

  /** 单个删除 */
  async function handleDelete(row: any) {
    try {
      const result = await apis.delete(row.id);
      if (result.success) {
        // 成功消息由后端统一处理，前端不再显示自定义消息
        options.onSuccess?.();
      }
      // 错误消息由后端统一处理，前端不再显示自定义消息
    } catch (error) {
      // 错误消息由后端统一处理，前端不再显示自定义消息
      console.error("删除失败:", error);
    }
  }

  /** 批量删除 */
  async function onBatchDelete(tableRef: any) {
    if (!apis.batchDelete) {
      console.warn("不支持批量删除");
      return;
    }

    const curSelected = tableRef.value.getTableRef().getSelectionRows();
    const ids = curSelected.map((item: any) => item.id);

    try {
      const result = await apis.batchDelete(ids);
      if (result.success) {
        // 成功消息由后端统一处理，前端不再显示自定义消息
        tableRef.value.getTableRef().clearSelection();
        options.onSuccess?.();
      }
      // 错误消息由后端统一处理，前端不再显示自定义消息
    } catch (error) {
      // 错误消息由后端统一处理，前端不再显示自定义消息
      console.error("批量删除失败:", error);
    }
  }

  return {
    openDialog,
    handleDelete,
    onBatchDelete
  };
}
