<template>
  <el-dialog
    v-model="visible"
    title="多课程成绩导入"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="import-container">
      <!-- 课程信息展示 -->
      <el-form label-width="100px">
        <el-form-item label="班级信息">
          <el-tag type="info">{{ props.classCode }} - {{ props.className }}</el-tag>
        </el-form-item>

        <el-form-item label="选择学期">
          <el-select
            v-model="selectedSemesterId"
            placeholder="请选择学期"
            style="width: 100%"
            @change="handleSemesterChange"
          >
            <el-option
              label="全部学期"
              value=""
            />
            <el-option
              v-for="semester in availableSemesters"
              :key="semester.id"
              :label="semester.semesterName"
              :value="semester.id"
            />
          </el-select>
          <div class="form-tip">选择"全部学期"将显示班级的所有课程</div>
        </el-form-item>

        <el-form-item label="分配课程" v-loading="coursesLoading">
          <div class="course-selection">
            <el-checkbox-group v-model="selectedCourseCodes" v-loading="coursesLoading">
              <div v-for="course in availableCourses" :key="course.courseCode" class="course-item">
                <el-checkbox :value="course.courseCode">{{ course.courseName }}</el-checkbox>
              </div>
            </el-checkbox-group>
            <div v-if="availableCourses.length === 0 && !coursesLoading" class="no-courses">
              暂无可用课程
            </div>
          </div>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button
          type="primary"
          :icon="useRenderIcon(Download)"
          @click="handleDownloadTemplate"
          :loading="downloadLoading"
        >
          下载导入模板
        </el-button>
      </div>

      <!-- 文件上传 -->
      <el-divider content-position="left">上传成绩文件</el-divider>

      <el-upload
        ref="uploadRef"
        class="upload-demo"
        drag
        :auto-upload="false"
        :limit="1"
        accept=".xlsx,.xls"
        :on-change="handleFileChange"
        :on-exceed="handleExceed"
        :file-list="fileList"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            只能上传 xlsx/xls 文件，且不超过 10MB
          </div>
        </template>
      </el-upload>

      <!-- 导入按钮 -->
      <div class="import-actions" v-if="selectedFile">
        <el-button
          type="success"
          :icon="useRenderIcon(Upload)"
          @click="handleImport"
          :loading="importLoading"
        >
          开始导入
        </el-button>
      </div>

      <!-- 导入结果 -->
      <div v-if="importResult" class="import-result">
        <el-divider content-position="left">导入结果</el-divider>

        <el-alert
          :title="importResult.success ? '导入成功' : '导入失败'"
          :type="importResult.success ? 'success' : 'error'"
          :description="importResult.summary"
          show-icon
          :closable="false"
        />

        <div v-if="importResult.errorMessages && importResult.errorMessages.length > 0" class="error-details">
          <h4>错误详情：</h4>
          <ul>
            <li v-for="(error, index) in importResult.errorMessages" :key="index">
              {{ error }}
            </li>
          </ul>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { UploadFilled } from "@element-plus/icons-vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import Download from "~icons/ep/download";
import Upload from "~icons/ep/upload";

import { getAllSemesters } from "@/api/basic/semester";
import { getCoursesByClass, getCoursesByClassAndSemester } from "@/api/educational/classCourse";
import { downloadMultiCourseTemplate, importMultiCourseGrades } from "@/api/score/grade-input";

// Props
interface Props {
  modelValue: boolean;
  classCode?: string;
  className?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  classCode: "",
  className: ""
});

// Emits
const emit = defineEmits<{
  "update:modelValue": [value: boolean];
  success: [];
}>();

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit("update:modelValue", value)
});

const selectedSemesterId = ref<number | string>("");
const availableSemesters = ref([]);
const availableCourses = ref([]);
const selectedCourseCodes = ref<string[]>([]);
const coursesLoading = ref(false);
const downloadLoading = ref(false);
const importLoading = ref(false);
const selectedFile = ref<File | null>(null);
const fileList = ref([]);
const uploadRef = ref();
const importResult = ref(null);

// 监听弹窗显示状态
watch(visible, (newVisible) => {
  if (newVisible) {
    loadSemesters();
    // 默认选择"全部学期"并加载课程，首次打开时自动全选
    selectedSemesterId.value = "";
    loadCourses("", true);
    resetForm();
  }
});

// 加载学期列表
const loadSemesters = async () => {
  try {
    const response = await getAllSemesters();
    // 过滤掉 id 为 null 或 undefined 的学期数据
    availableSemesters.value = (response.data || []).filter(semester =>
      semester && semester.id != null && semester.id !== undefined
    );
  } catch (error) {
    console.error("加载学期列表失败:", error);
    ElMessage.error("加载学期列表失败");
  }
};

// 加载课程列表
const loadCourses = async (semesterId?: number | string, autoSelectAll: boolean = false) => {
  if (!props.classCode) {
    return;
  }

  coursesLoading.value = true;
  try {
    let response;
    if (semesterId === "" || semesterId === null || semesterId === undefined) {
      // 全部学期 - 获取班级的所有课程
      response = await getCoursesByClass(props.classCode);
    } else {
      // 指定学期 - 获取该学期的课程
      response = await getCoursesByClassAndSemester(props.classCode, Number(semesterId));
    }

    availableCourses.value = response.data || [];

    if (autoSelectAll) {
      // 首次打开时自动选中所有课程
      selectedCourseCodes.value = availableCourses.value.map(course => course.courseCode);
    } else {
      // 学期切换时，只保留仍然存在的已选课程
      const availableCourseCodes = availableCourses.value.map(course => course.courseCode);
      selectedCourseCodes.value = selectedCourseCodes.value.filter(code =>
        availableCourseCodes.includes(code)
      );
    }
  } catch (error) {
    console.error("加载课程列表失败:", error);
    ElMessage.error("加载课程列表失败");
    availableCourses.value = [];
    selectedCourseCodes.value = [];
  } finally {
    coursesLoading.value = false;
  }
};

// 学期变化处理
const handleSemesterChange = (semesterId: number | string) => {
  loadCourses(semesterId);
};

// 课程选择变化处理（复选框方式，已废弃）
const handleCourseSelectionChange = (selectedCodes: string[]) => {
  selectedCourseCodes.value = selectedCodes;
};

// 表格选择变化处理
const handleTableSelectionChange = (selection: any[]) => {
  selectedCourseCodes.value = selection.map(course => course.courseCode);
};

// 下载模板
const handleDownloadTemplate = async () => {
  if (selectedCourseCodes.value.length === 0) {
    ElMessage.error("请先选择课程");
    return;
  }

  downloadLoading.value = true;
  try {
    // 调用多课程模板下载API
    const response = await downloadMultiCourseTemplate(
      props.classCode,
      selectedCourseCodes.value,
      selectedSemesterId.value === "" ? undefined : Number(selectedSemesterId.value)
    );

    // 创建下载链接
    const blob = new Blob([response], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `多课程成绩导入模板_${props.className || props.classCode}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    ElMessage.success("模板下载成功");
  } catch (error) {
    console.error("下载模板失败:", error);
    ElMessage.error("下载模板失败");
  } finally {
    downloadLoading.value = false;
  }
};

// 文件选择
const handleFileChange = (file: any) => {
  selectedFile.value = file.raw;
  fileList.value = [file];
};

// 文件超出限制
const handleExceed = () => {
  ElMessage.warning("只能上传一个文件");
};

// 开始导入
const handleImport = async () => {
  if (!selectedFile.value) {
    ElMessage.error("请选择要导入的文件");
    return;
  }

  if (props.selectedCourses.length === 0) {
    ElMessage.error("请先选择课程");
    return;
  }

  try {
    await ElMessageBox.confirm(
      '确定要导入成绩吗？导入过程中请勿关闭页面。',
      '确认导入',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    importLoading.value = true;
    importResult.value = null;

    // 调用多课程导入API
    const result = await importMultiCourseGrades(
      selectedFile.value,
      props.classCode,
      selectedCourseCodes.value,
      selectedSemesterId.value === "" ? undefined : Number(selectedSemesterId.value)
    );

    importResult.value = result.data;

    if (result.data.success) {
      ElMessage.success("导入完成");
      if (result.data.successRows > 0) {
        emit("success");
      }
    } else {
      ElMessage.error("导入失败，请查看错误详情");
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error("导入失败:", error);
      ElMessage.error("导入失败");
    }
  } finally {
    importLoading.value = false;
  }
};

// 重置表单
const resetForm = () => {
  // 保持学期选择不重置，因为已经在watch中设置了默认值
  selectedCourseCodes.value = [];
  selectedFile.value = null;
  fileList.value = [];
  importResult.value = null;

  if (uploadRef.value) {
    uploadRef.value.clearFiles();
  }
};

// 关闭弹窗
const handleClose = () => {
  visible.value = false;
  resetForm();
};


</script>

<style scoped>
.import-container {
  padding: 20px 0;
}

.course-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.course-tag {
  margin: 2px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.course-selection {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
}

.course-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 8px;
}

.course-checkbox {
  margin-right: 0;
  margin-bottom: 8px;
}

.no-courses {
  text-align: center;
  color: #909399;
  padding: 20px;
}

.action-buttons {
  margin: 20px 0;
  text-align: center;
}

.import-actions {
  margin: 20px 0;
  text-align: center;
}

.import-result {
  margin-top: 20px;
}

.error-details {
  margin-top: 16px;
  padding: 16px;
  background-color: #fef0f0;
  border-radius: 4px;
}

.error-details h4 {
  margin: 0 0 8px 0;
  color: #f56c6c;
}

.error-details ul {
  margin: 0;
  padding-left: 20px;
}

.error-details li {
  color: #f56c6c;
  margin-bottom: 4px;
}
</style>
