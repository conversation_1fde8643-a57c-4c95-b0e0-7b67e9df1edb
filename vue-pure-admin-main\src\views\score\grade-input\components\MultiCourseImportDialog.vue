<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { UploadFilled, Download, Upload } from "@element-plus/icons-vue";
import type { UploadFile } from "element-plus";
import ReCol from "@/components/ReCol";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { importFormRules } from "../utils/rule";

// API导入
import { getAllSemesters } from "@/api/basic/semester";
import { getCoursesByClass, getCoursesByClassAndSemester } from "@/api/educational/classCourse";
import { downloadMultiCourseTemplate, importMultiCourseGrades } from "@/api/score/grade-input";

// Props
interface Props {
  modelValue: boolean;
  classCode: string;
  className: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  classCode: "",
  className: ""
});

// Emits
const emit = defineEmits<{
  "update:modelValue": [value: boolean];
  success: [];
}>();

// 表单数据
const formInline = ref({
  title: "成绩导入",
  classCode: props.classCode,
  className: props.className,
  selectedSemesterId: "",
  selectedCourseCodes: [] as string[],
  availableSemesters: [] as any[],
  availableCourses: [] as any[]
});

const ruleFormRef = ref();

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit("update:modelValue", value)
});

const coursesLoading = ref(false);
const downloadLoading = ref(false);
const importLoading = ref(false);
const selectedFile = ref<File | null>(null);
const fileList = ref<UploadFile[]>([]);
const uploadRef = ref();
const importResult = ref<any>(null);

// 加载学期列表
const loadSemesters = async () => {
  try {
    const response = await getAllSemesters();
    formInline.value.availableSemesters = (response.data || []).filter(semester =>
      semester && semester.id != null && semester.id !== undefined
    );
  } catch (error) {
    console.error("加载学期列表失败:", error);
    ElMessage.error("加载学期列表失败");
  }
};

// 加载课程列表
const loadCourses = async (semesterId?: number | string, autoSelectAll: boolean = false) => {
  if (!props.classCode) {
    return;
  }

  coursesLoading.value = true;
  try {
    let response;
    if (semesterId === "" || semesterId === null || semesterId === undefined) {
      response = await getCoursesByClass(props.classCode);
    } else {
      response = await getCoursesByClassAndSemester(props.classCode, Number(semesterId));
    }

    formInline.value.availableCourses = response.data || [];

    if (autoSelectAll) {
      formInline.value.selectedCourseCodes = formInline.value.availableCourses.map(course => course.courseCode);
    } else {
      const availableCourseCodes = formInline.value.availableCourses.map(course => course.courseCode);
      formInline.value.selectedCourseCodes = formInline.value.selectedCourseCodes.filter(code =>
        availableCourseCodes.includes(code)
      );
    }
  } catch (error) {
    console.error("加载课程列表失败:", error);
    ElMessage.error("加载课程列表失败");
    formInline.value.availableCourses = [];
    formInline.value.selectedCourseCodes = [];
  } finally {
    coursesLoading.value = false;
  }
};

// 学期变化处理
const handleSemesterChange = (semesterId: number | string) => {
  loadCourses(semesterId, true);
};

// 文件上传处理
const handleFileChange = (file: UploadFile) => {
  selectedFile.value = file.raw || null;
  fileList.value = [file];
  return false;
};

const handleExceed = () => {
  ElMessage.warning("只能上传一个文件");
};

// 下载模板
const handleDownloadTemplate = async () => {
  if (formInline.value.selectedCourseCodes.length === 0) {
    ElMessage.error("请先选择课程");
    return;
  }

  downloadLoading.value = true;
  try {
    const response = await downloadMultiCourseTemplate(
      props.classCode,
      formInline.value.selectedCourseCodes,
      formInline.value.selectedSemesterId === "" ? undefined : Number(formInline.value.selectedSemesterId)
    );

    const blob = new Blob([response], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = "课程成绩导入模板.xlsx";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    ElMessage.success("模板下载成功");
  } catch (error) {
    console.error("下载模板失败:", error);
    ElMessage.error("下载模板失败");
  } finally {
    downloadLoading.value = false;
  }
};

// 开始导入
const handleImport = async () => {
  if (!selectedFile.value) {
    ElMessage.error("请选择要导入的文件");
    return;
  }

  if (formInline.value.selectedCourseCodes.length === 0) {
    ElMessage.error("请先选择课程");
    return;
  }

  try {
    await ElMessageBox.confirm(
      '确定要导入成绩吗？导入过程中请勿关闭页面。',
      '确认导入',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    importLoading.value = true;
    importResult.value = null;

    const result = await importMultiCourseGrades(
      selectedFile.value,
      props.classCode,
      formInline.value.selectedCourseCodes,
      formInline.value.selectedSemesterId === "" ? undefined : Number(formInline.value.selectedSemesterId)
    );

    importResult.value = result.data;

    if (result.data.success) {
      ElMessage.success("导入完成");
      if (result.data.successRows > 0) {
        emit("success");
      }
    } else {
      ElMessage.error("导入失败，请查看错误详情");
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error("导入失败:", error);
      ElMessage.error("导入失败");
    }
  } finally {
    importLoading.value = false;
  }
};

// 重置表单
const resetForm = () => {
  formInline.value.selectedCourseCodes = [];
  selectedFile.value = null;
  fileList.value = [];
  importResult.value = null;

  if (uploadRef.value) {
    uploadRef.value.clearFiles();
  }
};

// 监听弹窗显示状态
watch(visible, (newVisible) => {
  if (newVisible) {
    loadSemesters();
    formInline.value.selectedSemesterId = "";
    loadCourses("", true);
    resetForm();
  }
});

// 监听props变化，更新表单数据
watch(() => [props.classCode, props.className], () => {
  formInline.value.classCode = props.classCode;
  formInline.value.className = props.className;
}, { immediate: true });

function getRef() {
  return ruleFormRef.value;
}

defineExpose({ getRef });
</script>

<template>
  <el-dialog
    v-model="visible"
    title="成绩导入"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
  >
    <el-form
      ref="ruleFormRef"
      :model="formInline"
      :rules="importFormRules"
      label-width="100px"
    >
      <el-row :gutter="30">
        <re-col :value="24">
          <el-form-item label="班级信息">
            <el-tag type="info">{{ props.classCode }} - {{ props.className }}</el-tag>
          </el-form-item>
        </re-col>

        <re-col :value="12" :xs="24" :sm="24">
          <el-form-item label="选择学期">
            <el-select
              v-model="formInline.selectedSemesterId"
              placeholder="请选择学期"
              clearable
              style="width: 100%"
              @change="handleSemesterChange"
            >
              <el-option label="全部学期" value="" />
              <el-option
                v-for="semester in formInline.availableSemesters"
                :key="semester.id"
                :label="semester.semesterName"
                :value="semester.id"
              />
            </el-select>
          </el-form-item>
        </re-col>

        <re-col :value="24">
          <el-form-item label="分配课程" prop="selectedCourseCodes" v-loading="coursesLoading">
            <div class="course-selection">
              <el-checkbox-group v-model="formInline.selectedCourseCodes">
                <div v-for="course in formInline.availableCourses" :key="course.courseCode" class="course-item">
                  <el-checkbox :value="course.courseCode">{{ course.courseName }}</el-checkbox>
                </div>
              </el-checkbox-group>
              <div v-if="formInline.availableCourses.length === 0 && !coursesLoading" class="no-courses">
                暂无可用课程
              </div>
            </div>
          </el-form-item>
        </re-col>

        <re-col :value="24">
          <el-form-item label="操作">
            <el-space>
              <el-button
                type="primary"
                :icon="useRenderIcon(Download)"
                @click="handleDownloadTemplate"
                :loading="downloadLoading"
              >
                下载导入模板
              </el-button>
              <el-button
                type="success"
                :icon="useRenderIcon(Upload)"
                @click="handleImport"
                :loading="importLoading"
                :disabled="!selectedFile"
              >
                开始导入
              </el-button>
            </el-space>
          </el-form-item>
        </re-col>

        <re-col :value="24">
          <el-form-item label="选择文件">
            <el-upload
              ref="uploadRef"
              :auto-upload="false"
              :show-file-list="true"
              :limit="1"
              accept=".xlsx,.xls"
              :on-change="handleFileChange"
              :on-exceed="handleExceed"
              :file-list="fileList"
              drag
              style="width: 100%"
            >
              <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
              <template #tip>
                <div class="el-upload__tip">
                  只能上传 .xlsx/.xls 文件，且不超过 10MB
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </re-col>

        <!-- 导入结果显示 -->
        <re-col v-if="importResult" :value="24">
          <el-form-item label="导入结果">
            <el-alert
              :title="`导入完成：总计${importResult.totalRows}条，成功${importResult.successRows}条，失败${importResult.failedRows}条`"
              :type="importResult.success ? 'success' : 'error'"
              :closable="false"
            >
              <div v-if="importResult.errorMessages && importResult.errorMessages.length > 0">
                <p><strong>错误信息：</strong></p>
                <ul>
                  <li v-for="(error, index) in importResult.errorMessages" :key="index">{{ error }}</li>
                </ul>
              </div>
            </el-alert>
          </el-form-item>
        </re-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>

<style scoped>
.course-selection {
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  padding: 10px;
}

.course-item {
  margin-bottom: 8px;
}

.course-item:last-child {
  margin-bottom: 0;
}

.no-courses {
  text-align: center;
  color: var(--el-text-color-secondary);
  padding: 20px;
}
</style>