#生产环境配置
server:
  port: 8080
  # 生产环境安全配置
  servlet:
    session:
      timeout: 30m
      cookie:
        secure: true
        http-only: true
        same-site: strict

#数据库连接配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************************************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:123456}
    # 生产环境连接池配置
    druid:
      initial-size: 10
      min-idle: 10
      max-active: 50
      max-wait: 30000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      max-evictable-idle-time-millis: 900000
      validation-query: SELECT 1
      validation-query-timeout: 3
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      # 连接泄漏检测
      remove-abandoned: true
      remove-abandoned-timeout: 1800
      log-abandoned: true
      # 性能监控
      filters: stat,wall,slf4j
      # 慢SQL记录
      filter:
        stat:
          enabled: true
          slow-sql-millis: 2000
          log-slow-sql: true

  # 关闭热加载
  devtools:
    restart:
      enabled: false
    livereload:
      enabled: false

  # 任务执行线程池配置
  task:
    execution:
      pool:
        core-size: 8
        max-size: 32
        queue-capacity: 200
        keep-alive: 60s
        thread-name-prefix: "async-task-"
        allow-core-thread-timeout: true
    scheduling:
      pool:
        size: 4
      thread-name-prefix: "scheduled-task-"

#mybatis plus配置
mybatis-plus:
  configuration:
    # 生产环境关闭SQL日志
    # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
    call-setters-on-nulls: true
    # 开启二级缓存
    cache-enabled: true
    # 延迟加载配置
    lazy-loading-enabled: true
    aggressive-lazy-loading: false
    # 本地缓存作用域设置为STATEMENT，避免脏读
    local-cache-scope: STATEMENT
    # 自动映射行为
    auto-mapping-behavior: PARTIAL
    auto-mapping-unknown-column-behavior: WARNING
    # 执行器类型，REUSE可以重用预处理语句
    default-executor-type: REUSE
    # 设置超时时间
    default-statement-timeout: 30
    # 设置获取数据的大小
    default-fetch-size: 100
  global-config:
    db-config:
      field-strategy: NOT_EMPTY
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      # 主键类型
      id-type: ASSIGN_ID
    # 启用SQL解析缓存
    sql-parser-cache: true

#jwt配置 - 生产环境
jwt:
  issuer: itmk-student-system-prod
  # 生产环境必须使用环境变量设置密钥
  secret: ${JWT_SECRET:please-change-this-secret-in-production-environment}
  # 生产环境建议更短的过期时间
  expiration: 15

#日志配置 - 生产环境
logging:
  level:
    root: WARN
    com.itmk: INFO
    # SQL日志级别
    com.itmk.web.*.mapper: DEBUG
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n'
  file:
    name: logs/application.log
    max-size: 100MB
    max-history: 30

# 应用自定义配置
app:
  # 监控配置
  performance:
    monitor:
      enabled: true
  # 缓存配置
  cache:
    enabled: true
    default-ttl: 1800  # 默认缓存时间30分钟
  # 安全配置
  security:
    password:
      # 密码加密强度
      strength: 12

# 安全配置
security:
  # 启用安全头
  headers:
    frame-options: DENY
    content-type-options: nosniff
    xss-protection: 1; mode=block

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: never
