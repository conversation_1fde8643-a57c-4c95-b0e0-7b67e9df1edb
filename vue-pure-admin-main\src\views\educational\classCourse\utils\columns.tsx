import { ElTag } from "element-plus";
import { ClassCourseVO } from "./types";



export function useColumns() {
  const columns = [
    {
      label: "勾选列",
      type: "selection",
      width: 55,
      align: "left"
    },
    {
      label: "序号",
      type: "index",
      width: 70
    },
    {
      label: "课程代码",
      prop: "courseCode",
      minWidth: 120
    },
    {
      label: "课程名称",
      prop: "courseName",
      minWidth: 150
    },
    {
      label: "学分",
      prop: "credits",
      width: 80
    },

    {
      label: "学期",
      prop: "semesterName",
      minWidth: 120
    },
    {
      label: "状态",
      prop: "isActive",
      width: 80,
      cellRenderer: ({ row }: { row: ClassCourseVO }) => (
        <ElTag
          type={row.isActive ? "success" : "danger"}
          size="small"
        >
          {row.isActive ? "启用" : "停用"}
        </ElTag>
      )
    },
    {
      label: "排序",
      prop: "sortOrder",
      width: 80
    },
    {
      label: "操作",
      fixed: "right",
      width: 220,
      slot: "operation"
    }
  ];

  return {
    columns
  };
}
