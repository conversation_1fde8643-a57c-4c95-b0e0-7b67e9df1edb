import { reactive, ref } from "vue";
import { type PaginationProps } from "@pureadmin/table";

/**
 * 通用表格管理Hook
 */
export function useTable<T = any, Q = any>(
  fetchApi: (params: Q) => Promise<any>,
  defaultQuery: Q
) {
  const queryForm = reactive<Q>(defaultQuery);
  const dataList = ref<T[]>([]);
  const loading = ref(true);
  const selectedNum = ref(0);
  const tableRef = ref();

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  });

  /** 搜索 */
  async function onSearch() {
    loading.value = true;
    try {
      const params = {
        ...queryForm,
        current: pagination.currentPage,
        size: pagination.pageSize
      };

      const result = await fetchApi(params);
      if (result.success) {
        dataList.value = result.data.records || [];
        pagination.total = result.data.total || 0;
      }
      // 错误消息由后端统一处理，前端不再显示自定义消息
    } catch (error) {
      // 错误消息由后端统一处理，前端不再显示自定义消息
      console.error("查询失败:", error);
    } finally {
      loading.value = false;
    }
  }

  /** 重置搜索 */
  function resetForm() {
    Object.assign(queryForm, defaultQuery);
    pagination.currentPage = 1;
    onSearch();
  }

  /** 分页大小改变 */
  function onSizeChange(val: number) {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    onSearch();
  }

  /** 当前页改变 */
  function onCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  /** 多选改变 */
  function handleSelectionChange(val: T[]) {
    selectedNum.value = val.length;
  }

  /** 取消选择 */
  function onSelectionCancel() {
    selectedNum.value = 0;
    tableRef.value.getTableRef().clearSelection();
  }

  return {
    queryForm,
    dataList,
    loading,
    selectedNum,
    tableRef,
    pagination,
    onSearch,
    resetForm,
    onSizeChange,
    onCurrentChange,
    handleSelectionChange,
    onSelectionCancel
  };
}
