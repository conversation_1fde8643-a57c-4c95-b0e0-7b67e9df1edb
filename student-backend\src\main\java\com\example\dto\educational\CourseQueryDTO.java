package com.example.dto.educational;

import com.example.dto.BaseQueryDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 课程查询DTO
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "课程查询条件")
public class CourseQueryDTO extends BaseQueryDTO {

    @Schema(description = "课程代码")
    private String courseCode;

    @Schema(description = "课程名称")
    private String courseName;

    @Schema(description = "课程类型")
    private String courseType;

    @Schema(description = "所属学院代码")
    private String collegeCode;
}
