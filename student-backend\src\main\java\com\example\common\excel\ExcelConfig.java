package com.example.common.excel;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.function.Function;

/**
 * Excel导入导出配置类
 * 用于定义导入导出的字段映射、验证规则等
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExcelConfig<T> {
    
    /**
     * 表头配置
     */
    private String[] headers;
    
    /**
     * 工作表名称
     */
    private String sheetName;
    
    /**
     * 模板名称（用于下载时的文件名）
     */
    private String templateName;
    
    /**
     * 实体类型
     */
    private Class<T> entityClass;
    
    /**
     * 数据提取器 - 将实体对象转换为Excel行数据
     */
    private Function<T, Object[]> dataExtractor;
    
    /**
     * 数据构建器 - 将Excel行数据转换为实体对象
     */
    private Function<Object[], T> dataBuilder;
    
    /**
     * 数据验证器 - 验证实体对象是否有效
     */
    private Function<T, ValidationResult> validator;
    
    /**
     * 是否包含序号列
     */
    private boolean includeIndex = false;
    
    /**
     * 序号列标题
     */
    private String indexHeader = "序号";
    
    /**
     * 获取完整的表头（包含序号列）
     */
    public String[] getFullHeaders() {
        if (!includeIndex) {
            return headers;
        }
        
        String[] fullHeaders = new String[headers.length + 1];
        fullHeaders[0] = indexHeader;
        System.arraycopy(headers, 0, fullHeaders, 1, headers.length);
        return fullHeaders;
    }
    
    /**
     * 获取完整的数据行（包含序号）
     */
    public Object[] getFullDataRow(T entity, int index) {
        Object[] data = dataExtractor.apply(entity);
        
        if (!includeIndex) {
            return data;
        }
        
        Object[] fullData = new Object[data.length + 1];
        fullData[0] = index;
        System.arraycopy(data, 0, fullData, 1, data.length);
        return fullData;
    }
    
    /**
     * 从完整行数据中提取实体数据（去除序号列）
     */
    public Object[] extractEntityData(Object[] fullRowData) {
        if (!includeIndex) {
            return fullRowData;
        }
        
        Object[] entityData = new Object[fullRowData.length - 1];
        System.arraycopy(fullRowData, 1, entityData, 0, entityData.length);
        return entityData;
    }
    
    /**
     * 验证结果类
     */
    @Data
    @AllArgsConstructor
    public static class ValidationResult {
        private boolean valid;
        private String errorMessage;
        
        public static ValidationResult success() {
            return new ValidationResult(true, null);
        }
        
        public static ValidationResult error(String message) {
            return new ValidationResult(false, message);
        }
    }
    
    /**
     * 创建简单配置的静态方法
     */
    public static <T> ExcelConfig<T> simple(String[] headers, String sheetName, String templateName,
                                           Class<T> entityClass,
                                           Function<T, Object[]> dataExtractor,
                                           Function<Object[], T> dataBuilder) {
        return new ExcelConfig<>(headers, sheetName, templateName, entityClass, 
                                dataExtractor, dataBuilder, null, false, "序号");
    }
    
    /**
     * 创建带验证的配置的静态方法
     */
    public static <T> ExcelConfig<T> withValidation(String[] headers, String sheetName, String templateName,
                                                   Class<T> entityClass,
                                                   Function<T, Object[]> dataExtractor,
                                                   Function<Object[], T> dataBuilder,
                                                   Function<T, ValidationResult> validator) {
        return new ExcelConfig<>(headers, sheetName, templateName, entityClass,
                                dataExtractor, dataBuilder, validator, false, "序号");
    }
    
    /**
     * 创建带序号的配置
     */
    public static <T> ExcelConfig<T> withIndex(String[] headers, String sheetName, String templateName,
                                              Class<T> entityClass,
                                              Function<T, Object[]> dataExtractor,
                                              Function<Object[], T> dataBuilder,
                                              Function<T, ValidationResult> validator) {
        return new ExcelConfig<>(headers, sheetName, templateName, entityClass,
                                dataExtractor, dataBuilder, validator, true, "序号");
    }
}
