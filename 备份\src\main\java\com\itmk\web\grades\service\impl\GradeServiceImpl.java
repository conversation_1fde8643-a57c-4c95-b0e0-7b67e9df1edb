package com.itmk.web.grades.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itmk.utils.PageUtils;
import com.itmk.web.common.entity.Semester;
import com.itmk.web.common.mapper.SemesterMapper;
import com.itmk.web.grades.entity.Grade;
import com.itmk.web.grades.entity.GradeParm;
import com.itmk.web.grades.mapper.GradeMapper;
import com.itmk.web.grades.service.GradeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
public class GradeServiceImpl extends ServiceImpl<GradeMapper, Grade> implements GradeService {

    @Autowired
    private SemesterMapper semesterMapper;

    @Override
    public IPage<Grade> getList(GradeParm parm) {
        // 参数检查，防止空指针异常
        if (parm == null) {
            parm = new GradeParm();
        }
        
        // 添加默认分页参数
        Long currentPage = parm.getCurrentPage() != null ? parm.getCurrentPage() : 1L;
        Long pageSize = parm.getPageSize() != null ? parm.getPageSize() : 10L;
        
        // 构造分页对象
        IPage<Grade> page = PageUtils.createPage(currentPage, pageSize);
        return this.baseMapper.getList(page, parm);
    }

    @Override
    public IPage<Grade> calculategpa(GradeParm parm) {
        // 构造分页对象
        IPage<Grade> page = PageUtils.createPage(parm.getCurrentPage(), parm.getPageSize());
        return this.baseMapper.calculategpa(page, parm);
    }
    
    @Override
    public List<Map<String, Object>> getStudentGpa(String semesterName) {
        // 如果提供了学期名称，先查询对应的学期ID
        Integer semesterId = null;
        if (semesterName != null && !semesterName.isEmpty()) {
            semesterId = semesterMapper.getIdByName(semesterName);
        }
        return this.baseMapper.getStudentGpa(null, semesterId);
    }
    
    @Override
    public List<Map<String, Object>> getStudentGpaById(Integer semesterId) {
        return this.baseMapper.getStudentGpa(null, semesterId);
    }
    
    @Override
    public List<Map<String, Object>> getStudentGpaByIds(List<Integer> semesterIds) {
        if (semesterIds == null || semesterIds.isEmpty()) {
            return this.baseMapper.getStudentGpa(null, null);
        }
        
        // 如果只有一个学期ID，调用单学期方法
        if (semesterIds.size() == 1) {
            return this.baseMapper.getStudentGpa(null, semesterIds.get(0));
        }
        
        // 获取多个学期的学生GPA
        return this.baseMapper.getStudentGpaByIds(semesterIds);
    }
    
    @Override
    @Transactional
    public void updateGradesSemesterId() {
        try {
            // 获取所有学期信息
            List<Semester> semesters = semesterMapper.selectList(null);
            if (semesters == null || semesters.isEmpty()) {
                return;
            }
            
            // 获取默认学期ID (使用第一个学期)
            Integer defaultSemesterId = semesters.get(0).getSemesterId();
            
            // 查询所有缺少学期ID的成绩记录
            QueryWrapper<Grade> queryNullSemester = new QueryWrapper<>();
            queryNullSemester.lambda().isNull(Grade::getSemesterId).or().eq(Grade::getSemesterId, 0);
            List<Grade> gradesWithoutSemester = this.baseMapper.selectList(queryNullSemester);
            
            if (gradesWithoutSemester.isEmpty()) {
                return;
            }
            
            // 按学期名称分组的统计
            Map<String, Integer> updateCountByName = new HashMap<>();
            int updatedByName = 0;
            int updatedByDefault = 0;
            
            // 遍历需要更新的成绩记录
            for (Grade grade : gradesWithoutSemester) {
                // 如果有学期名称，尝试根据名称设置学期ID
                if (grade.getSemesterName() != null && !grade.getSemesterName().isEmpty()) {
                    Integer semesterId = semesterMapper.getIdByName(grade.getSemesterName());
                    if (semesterId != null) {
                        grade.setSemesterId(semesterId);
                        updatedByName++;
                        
                        // 更新统计
                        updateCountByName.put(grade.getSemesterName(), 
                            updateCountByName.getOrDefault(grade.getSemesterName(), 0) + 1);
                    } else {
                        // 如果找不到对应的学期ID，使用默认值
                        grade.setSemesterId(defaultSemesterId);
                        updatedByDefault++;
                    }
                } else {
                    // 如果没有学期名称，直接使用默认值
                    grade.setSemesterId(defaultSemesterId);
                    updatedByDefault++;
                }
            }
            
            // 批量更新
            this.updateBatchById(gradesWithoutSemester);
        } catch (Exception e) {
            throw new RuntimeException("更新成绩表学期ID失败：" + e.getMessage());
        }
    }
}