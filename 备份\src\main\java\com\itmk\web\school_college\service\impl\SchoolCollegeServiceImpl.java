package com.itmk.web.school_college.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itmk.utils.PageUtils;
import com.itmk.web.school_college.entity.ListParm;
import com.itmk.web.school_college.entity.SchoolCollege;
import com.itmk.web.school_college.mapper.SchoolCollegeMapper;
import com.itmk.web.school_college.service.SchoolCollegeService;
import org.springframework.stereotype.Service;

@Service
public class SchoolCollegeServiceImpl extends ServiceImpl<SchoolCollegeMapper, SchoolCollege> implements SchoolCollegeService {
    @Override
    public IPage<SchoolCollege> getList(ListParm listParm) {
        //构造查询条件
        QueryWrapper<SchoolCollege> query = new QueryWrapper<>();
        if (listParm.getCollegeName() != null && !listParm.getCollegeName().isEmpty()) {
            query.lambda().like(SchoolCollege::getCollegeName, listParm.getCollegeName());
        }
        query.lambda().orderByAsc(SchoolCollege::getOrderNum);
        //构造分页对象
        IPage<SchoolCollege> page = PageUtils.createPage(listParm.getCurrentPage(), listParm.getPageSize());
        //查询
        return this.page(page, query);
    }
}