package com.itmk.config;

import com.itmk.utils.LogUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

/**
 * 定时任务配置类
 * 统一管理定时任务相关配置
 * 
 * <AUTHOR>
 * @since 2025-07-19
 */
@Configuration
@EnableScheduling
public class TaskConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(TaskConfig.class);
    
    /**
     * 定时任务线程池配置
     * 
     * @return 线程池任务调度器
     */
    @Bean("taskScheduler")
    public ThreadPoolTaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        
        // 核心线程数
        scheduler.setPoolSize(5);
        
        // 线程名前缀
        scheduler.setThreadNamePrefix("scheduled-task-");
        
        // 等待任务完成后再关闭线程池
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间（秒）
        scheduler.setAwaitTerminationSeconds(60);
        
        // 拒绝策略：由调用线程执行
        scheduler.setRejectedExecutionHandler((r, executor) -> {
            logger.warn("定时任务线程池已满，任务将由调用线程执行: {}", r.toString());
            r.run();
        });
        
        // 初始化
        scheduler.initialize();
        
        LogUtils.logSystemStart(logger, "定时任务线程池");
        
        return scheduler;
    }
    
    /**
     * 任务执行异常处理器
     */
    @Bean
    public TaskExceptionHandler taskExceptionHandler() {
        return new TaskExceptionHandler();
    }
    
    /**
     * 定时任务异常处理器
     */
    public static class TaskExceptionHandler {
        
        private static final Logger logger = LoggerFactory.getLogger(TaskExceptionHandler.class);
        
        /**
         * 处理定时任务异常
         * 
         * @param taskName 任务名称
         * @param exception 异常
         */
        public void handleTaskException(String taskName, Exception exception) {
            LogUtils.logTaskError(logger, taskName, "定时任务执行异常", exception);
            
            // 这里可以添加更多的异常处理逻辑，比如：
            // 1. 发送告警邮件
            // 2. 记录到监控系统
            // 3. 重试机制等
        }
        
        /**
         * 处理任务超时
         * 
         * @param taskName 任务名称
         * @param timeoutMs 超时时间（毫秒）
         */
        public void handleTaskTimeout(String taskName, long timeoutMs) {
            LogUtils.logTaskWarning(logger, taskName, 
                String.format("任务执行超时，超时时间：%dms", timeoutMs));
        }
    }
}
