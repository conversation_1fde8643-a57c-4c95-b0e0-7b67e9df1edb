package com.example.service.student.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.common.exception.BusinessException;
import com.example.dto.student.StudentsQueryDTO;
import com.example.entity.student.Students;
import com.example.mapper.student.StudentsMapper;
import com.example.service.student.StudentsService;
import com.example.service.basic.ClassesService;
import com.example.vo.student.StudentsVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 学生信息Service实现类
 */
@Service
public class StudentsServiceImpl extends ServiceImpl<StudentsMapper, Students> implements StudentsService {

    @Autowired
    private ClassesService classesService;

    @Override
    public IPage<StudentsVO> getStudentsPage(StudentsQueryDTO queryDTO) {
        Page<StudentsVO> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        return baseMapper.selectStudentsPage(page, queryDTO);
    }

    @Override
    public StudentsVO getStudentsById(Integer id) {
        if (id == null) {
            throw new BusinessException("学生ID不能为空");
        }

        Students students = getById(id);
        if (students == null) {
            throw new BusinessException("学生不存在");
        }

        StudentsVO studentsVO = new StudentsVO();
        BeanUtils.copyProperties(students, studentsVO);
        return studentsVO;
    }

    @Override
    public StudentsVO getStudentByStudentId(String studentId) {
        if (!StringUtils.hasText(studentId)) {
            throw new BusinessException("学号不能为空");
        }

        Students students = lambdaQuery()
                .eq(Students::getStudentId, studentId)
                .one();

        if (students == null) {
            return null;
        }

        StudentsVO studentsVO = new StudentsVO();
        BeanUtils.copyProperties(students, studentsVO);
        return studentsVO;
    }

    @Override
    public List<StudentsVO> getAllStudents() {
        StudentsQueryDTO queryDTO = new StudentsQueryDTO();
        queryDTO.setCurrent(1);
        queryDTO.setSize(Integer.MAX_VALUE);
        return getStudentsPage(queryDTO).getRecords();
    }

    @Override
    public List<StudentsVO> getStudentsByClassCode(String classCode) {
        if (!StringUtils.hasText(classCode)) {
            throw new BusinessException("班级代码不能为空");
        }
        return baseMapper.selectStudentsByClassCode(classCode);
    }

    @Override
    public List<StudentsVO> getStudentsByMajorCode(String majorCode) {
        if (!StringUtils.hasText(majorCode)) {
            throw new BusinessException("专业代码不能为空");
        }
        return baseMapper.selectStudentsByMajorCode(majorCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveStudents(StudentsVO studentsVO) {
        validateStudentsInfo(studentsVO, null);

        try {
            Students students = new Students();
            // 使用BeanUtils复制基本字段，忽略不存在的字段
            BeanUtils.copyProperties(studentsVO, students, "majorCode", "collegeCode", "majorName", "collegeName", "className");

            save(students);

            // 更新班级学生人数
            updateClassStudentCount(studentsVO.getClassCode());
        } catch (Exception e) {
            throw new BusinessException("新增学生失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStudents(StudentsVO studentsVO) {
        if (studentsVO.getId() == null) {
            throw new BusinessException("学生ID不能为空");
        }

        Students existingStudents = getById(studentsVO.getId());
        if (existingStudents == null) {
            throw new BusinessException("学生不存在");
        }

        validateStudentsInfo(studentsVO, studentsVO.getId());

        try {
            // 获取修改前的班级代码，用于比较班级是否变更
            String oldClassCode = existingStudents.getClassCode();

            Students students = new Students();
            // 使用BeanUtils复制基本字段，忽略不存在的字段
            BeanUtils.copyProperties(studentsVO, students, "majorCode", "collegeCode", "majorName", "collegeName", "className");

            updateById(students);

            // 更新相关班级的学生人数
            if (oldClassCode != null && !oldClassCode.equals(studentsVO.getClassCode())) {
                // 班级发生变更，需要更新两个班级的人数
                updateClassStudentCount(oldClassCode);
                updateClassStudentCount(studentsVO.getClassCode());
            } else {
                // 班级未变更，只更新当前班级人数（可能状态发生了变化）
                updateClassStudentCount(studentsVO.getClassCode());
            }
        } catch (Exception e) {
            throw new BusinessException("更新学生失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteStudents(Integer id) {
        if (id == null) {
            throw new BusinessException("学生ID不能为空");
        }

        Students students = getById(id);
        if (students == null) {
            throw new BusinessException("学生不存在");
        }

        try {
            // 获取班级代码，用于更新班级人数
            String classCode = students.getClassCode();

            removeById(id);

            // 更新班级学生人数
            updateClassStudentCount(classCode);
        } catch (Exception e) {
            throw new BusinessException("删除学生失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteStudents(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException("学生ID列表不能为空");
        }

        try {
            // 获取要删除的学生信息，用于更新班级人数
            List<Students> studentsToDelete = listByIds(ids);
            Set<String> classCodes = studentsToDelete.stream()
                    .map(Students::getClassCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            removeByIds(ids);

            // 更新相关班级的学生人数
            classCodes.forEach(this::updateClassStudentCount);
        } catch (Exception e) {
            throw new BusinessException("批量删除学生失败");
        }
    }

    /**
     * 验证学生信息
     */
    private void validateStudentsInfo(StudentsVO studentsVO, Integer excludeId) {
        if (!StringUtils.hasText(studentsVO.getStudentId())) {
            throw new BusinessException("学号不能为空");
        }

        if (!StringUtils.hasText(studentsVO.getName())) {
            throw new BusinessException("姓名不能为空");
        }

        if (!StringUtils.hasText(studentsVO.getClassCode())) {
            throw new BusinessException("所属班级不能为空");
        }

        // 检查学号唯一性
        if (isStudentIdExists(studentsVO.getStudentId(), excludeId)) {
            throw new BusinessException("学号已存在");
        }
    }

    /**
     * 检查学号是否存在
     */
    private boolean isStudentIdExists(String studentId, Integer excludeId) {
        if (!StringUtils.hasText(studentId)) {
            return false;
        }

        return baseMapper.existsStudentId(studentId, excludeId);
    }

    /**
     * 更新班级学生人数
     */
    private void updateClassStudentCount(String classCode) {
        if (!StringUtils.hasText(classCode)) {
            return;
        }

        try {
            // 统计该班级在校学生人数
            int studentCount = baseMapper.countStudentsByClassCode(classCode);

            // 根据班级代码查找班级ID
            Integer classId = baseMapper.getClassIdByClassCode(classCode);
            if (classId != null) {
                classesService.updateStudentCount(classId, studentCount);
            }
        } catch (Exception e) {
            // 记录日志但不影响主业务
            System.err.println("更新班级学生人数失败: " + e.getMessage());
        }
    }
}
