package com.itmk.web.common.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.itmk.utils.ResultUtils;
import com.itmk.utils.ResultVo;
import com.itmk.web.common.entity.CommParm;
import com.itmk.web.common.entity.Course;
import com.itmk.web.common.entity.Semester;
import com.itmk.web.common.service.CourseService;
import com.itmk.web.common.service.SemesterService;
import com.itmk.service.UniversalExcelService;
import com.itmk.service.BaseExcelService;
import com.itmk.utils.excel.ExcelConfigFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
@RequestMapping("/api/course")
public class CourseController {

    @Autowired
    private CourseService courseService;

    @Autowired
    private SemesterService semesterService;

    @Autowired
    private UniversalExcelService universalExcelService;

    // 添加课程
    @PostMapping("/add")
    public ResultVo add(@RequestBody Course course) {
        boolean save = courseService.save(course);
        if (save) {
            return ResultUtils.success("添加成功");
        }
        return ResultUtils.error("添加失败");
    }

    // 批量导入课程
    @PostMapping("/batchImport")
    public ResultVo batchImport(@RequestBody List<Course> courses) {
        if (courses == null || courses.isEmpty()) {
            return ResultUtils.error("导入数据不能为空");
        }

        boolean result = courseService.saveBatch(courses);
        if (result) {
            return ResultUtils.success("批量导入成功");
        }
        return ResultUtils.error("批量导入失败");
    }

    // 导入Excel文件
    @PostMapping("/import")
    public ResultVo importFile(@RequestParam("file") MultipartFile file) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                return ResultUtils.error("请选择要导入的文件");
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
                return ResultUtils.error("请上传Excel文件（.xlsx或.xls格式）");
            }

            // 导入Excel
            BaseExcelService.ImportResult<Course> importResult =
                universalExcelService.importExcel(file, ExcelConfigFactory.getCourseConfig());

            if (!importResult.isSuccess()) {
                return ResultUtils.error(importResult.getMessage());
            }

            // 保存数据前，先设置学期ID
            if (!importResult.getSuccessData().isEmpty()) {
                // 获取所有学期信息，用于学年学期查找
                List<Semester> allSemesters = semesterService.list();
                Map<String, Semester> semesterMap = new HashMap<>();
                for (Semester semester : allSemesters) {
                    String key = semester.getAcademicYear() + "_" + semester.getSemesterName();
                    semesterMap.put(key, semester);
                }

                // 为每个课程设置学期ID
                List<String> errorMessages = new ArrayList<>();
                for (Course course : importResult.getSuccessData()) {
                    if (course.getAcademicYear() != null && course.getSemesterName() != null) {
                        String semesterKey = course.getAcademicYear() + "_" + course.getSemesterName();
                        Semester semester = semesterMap.get(semesterKey);
                        if (semester != null) {
                            course.setSemesterId(semester.getSemesterId());
                        } else {
                            errorMessages.add("找不到学年为" + course.getAcademicYear() + "，学期为" + course.getSemesterName() + "的记录");
                        }
                    }
                }

                if (!errorMessages.isEmpty()) {
                    return ResultUtils.error("导入失败：" + String.join("；", errorMessages));
                }

                boolean success = courseService.saveBatch(importResult.getSuccessData());
                if (!success) {
                    return ResultUtils.error("保存失败");
                }
            }

            return ResultUtils.success(importResult.getMessage(), importResult);

        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtils.error("导入失败: " + e.getMessage());
        }
    }

    // 下载导入模板
    @GetMapping("/template")
    public ResponseEntity<byte[]> downloadTemplate() {
        try {
            // 使用专门的模板配置生成模板文件
            byte[] templateData = universalExcelService.generateTemplate(ExcelConfigFactory.getCourseTemplateConfig());

            // 生成文件名
            String fileName = "课程信息导入模板_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
            // 正确编码文件名，使用RFC 5987标准格式
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8")
                    .replaceAll("\\+", "%20"); // 将+号替换为%20

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            // 使用RFC 5987标准格式设置Content-Disposition
            headers.set("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
            headers.set("Cache-Control", "no-cache");

            return new ResponseEntity<>(templateData, headers, HttpStatus.OK);

        } catch (Exception e) {
            e.printStackTrace();
            String errorMessage = "模板下载失败: " + e.getMessage();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(errorMessage.getBytes());
        }
    }

    // 导出课程信息
    @GetMapping("/export")
    public ResponseEntity<byte[]> export(CommParm parm) {
        try {
            // 获取导出数据
            if (parm == null) parm = new CommParm();
            parm.setCurrentPage(1L);
            parm.setPageSize(1000L); // 一次导出最多1000条数据
            IPage<Course> courseList = courseService.getListWithSemester(parm);

            // 生成Excel文件
            byte[] excelData = universalExcelService.exportExcel(courseList.getRecords(), ExcelConfigFactory.getCourseConfig());

            // 生成文件名
            String fileName = "课程信息_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", encodedFileName);
            headers.set("Cache-Control", "no-cache");

            return new ResponseEntity<>(excelData, headers, HttpStatus.OK);

        } catch (Exception e) {
            e.printStackTrace();
            String errorMessage = "导出失败: " + e.getMessage();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(errorMessage.getBytes());
        }
    }

    // 更新课程
    @PutMapping("/update")
    public ResultVo edit(@RequestBody Course course) {
        try {
            // 首先查询原课程信息
            Course oldCourse = courseService.getById(course.getId());
            if (oldCourse == null) {
                return ResultUtils.error("课程不存在");
            }

            // 检查是否修改了课程代码
            if (!oldCourse.getCourseCode().equals(course.getCourseCode())) {
                // 由于外键约束，不允许直接修改课程代码
                return ResultUtils.error("不能修改课程代码，因为它被用于关联学生成绩");
            }

            // 执行更新操作
            boolean save = courseService.updateById(course);
            if (save) {
                return ResultUtils.success("更新成功");
            } else {
                return ResultUtils.error("更新失败");
            }
        } catch (Exception e) {
            // 捕获可能的外键约束异常
            String errorMsg = e.getMessage();
            if (errorMsg != null && errorMsg.contains("foreign key constraint")) {
                return ResultUtils.error("不能修改课程代码，因为它被用于关联学生成绩");
            } else {
                e.printStackTrace();
                return ResultUtils.error("更新失败: " + e.getMessage());
            }
        }
    }

    // 删除课程
    @DeleteMapping("/delete/{id}")
    public ResultVo delete(@PathVariable Integer id) {
        try {
            // 首先查询课程信息，获取课程代码
            Course course = courseService.getById(id);
            if (course == null) {
                return ResultUtils.error("课程不存在");
            }

            // 尝试删除课程
            boolean remove = courseService.removeById(id);
            if (remove) {
                return ResultUtils.success("删除成功");
            } else {
                return ResultUtils.error("删除失败");
            }
        } catch (Exception e) {
            // 捕获可能的外键约束异常
            String errorMsg = e.getMessage();
            if (errorMsg != null && errorMsg.contains("foreign key constraint")) {
                return ResultUtils.error("该课程已被学生选修，无法删除。请先移除相关的选课记录。");
            } else {
                e.printStackTrace();
                return ResultUtils.error("删除失败: " + e.getMessage());
            }
        }
    }

    // 查询所有课程
    @GetMapping("/list")
    public ResultVo getList(CommParm commParm) {
        IPage<Course> list = courseService.getListWithSemester(commParm);
        return ResultUtils.success("查询成功", list);
    }

    // 根据ID查询课程
    @GetMapping("/{id}")
    public Course getById(@PathVariable Integer id) {
        return courseService.getById(id);
    }
}