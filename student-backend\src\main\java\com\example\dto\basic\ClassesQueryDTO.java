package com.example.dto.basic;

import lombok.Data;

/**
 * 班级查询DTO
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Data
public class ClassesQueryDTO {

    /**
     * 班级代码（模糊查询）
     */
    private String classCode;

    /**
     * 班级名称（模糊查询）
     */
    private String className;

    /**
     * 所属专业代码
     */
    private String majorCode;

    /**
     * 入学年份
     */
    private Integer gradeYear;

    /**
     * 班主任工号
     */
    private String headTeacherCode;

    /**
     * 学院代码
     */
    private String collegeCode;

    /**
     * 当前页码
     */
    private Integer current = 1;

    /**
     * 每页大小
     */
    private Integer size = 10;

    /**
     * 排序字段
     */
    private String sortField;

    /**
     * 排序方向
     */
    private String sortOrder;
}
