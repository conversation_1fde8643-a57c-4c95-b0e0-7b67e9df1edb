package com.example.dto.basic;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 学年学期查询DTO
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Data
@Schema(description = "学年学期查询条件")
public class SemesterQueryDTO {

    @Schema(description = "当前页码", example = "1")
    private Integer current;

    @Schema(description = "每页大小", example = "10")
    private Integer size;

    @Schema(description = "学年")
    private String academicYear;

    @Schema(description = "学期号")
    private Integer semesterNumber;

    @Schema(description = "学期名称")
    private String semesterName;

    @Schema(description = "是否当前学期")
    private Boolean isCurrent;

    @Override
    public String toString() {
        return "SemesterQueryDTO{" +
                "current=" + current +
                ", size=" + size +
                ", academicYear='" + academicYear + '\'' +
                ", semesterNumber=" + semesterNumber +
                ", semesterName='" + semesterName + '\'' +
                ", isCurrent=" + isCurrent +
                '}';
    }
}
