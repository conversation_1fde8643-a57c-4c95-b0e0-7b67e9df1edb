package com.itmk.web.sys_menu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.itmk.utils.TreeBuilder;
import com.itmk.web.sys_menu.converter.MenuConverter;
import com.itmk.web.sys_menu.dto.MenuDTO;
import com.itmk.web.sys_menu.dto.MenuQueryDTO;
import com.itmk.web.sys_menu.dto.RouterDTO;
import com.itmk.web.sys_menu.entity.SysMenu;
import com.itmk.web.sys_menu.mapper.SysMenuMapper;
import com.itmk.web.sys_menu.service.SysMenuService;
import com.itmk.web.sys_role_menu.entity.SysRoleMenu;
import com.itmk.web.sys_role_menu.service.SysRoleMenuService;
import com.itmk.web.sys_role.entity.SysRole;
import com.itmk.web.sys_role.service.SysRoleService;
import com.itmk.config.jwt.UserContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.HashSet;
import java.util.ArrayList;

/**
 * 菜单服务实现类
 * 基于Spring Boot最佳实践优化
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements SysMenuService {

    private static final Logger logger = LoggerFactory.getLogger(SysMenuServiceImpl.class);

    @Autowired
    private MenuConverter menuConverter;



    @Autowired
    private SysRoleMenuService sysRoleMenuService;

    @Autowired
    private SysRoleService sysRoleService;
    /**
     * 获取菜单列表（缓存）
     */
    @Override
    @Cacheable(value = "menuCache", key = "'menuList'")
    public List<MenuDTO> getMenuList() {
        logger.info("获取菜单列表");

        QueryWrapper<SysMenu> query = new QueryWrapper<>();
        query.lambda().orderByAsc(SysMenu::getOrderNum);
        List<SysMenu> menuList = this.baseMapper.selectList(query);

        return TreeBuilder.buildMenuTree(menuList, 0L);
    }

    /**
     * 根据查询条件获取菜单列表
     */
    @Override
    public List<MenuDTO> getMenuList(MenuQueryDTO queryDTO) {
        logger.info("根据条件查询菜单列表: {}", queryDTO);

        QueryWrapper<SysMenu> query = buildQueryWrapper(queryDTO);
        List<SysMenu> menuList = this.baseMapper.selectList(query);

        if (queryDTO.getBuildTree()) {
            return TreeBuilder.buildMenuTree(menuList, 0L);
        } else {
            return menuConverter.toDTOList(menuList);
        }
    }

    /**
     * 获取上级菜单列表（缓存）
     */
    @Override
    @Cacheable(value = "menuCache", key = "'parentMenuList'")
    public List<MenuDTO> getParentMenuList() {
        logger.info("获取上级菜单列表");

        // 查询目录和菜单
        String[] types = {"0", "1"};
        QueryWrapper<SysMenu> query = new QueryWrapper<>();
        query.lambda().in(SysMenu::getType, Arrays.asList(types))
                     .orderByAsc(SysMenu::getOrderNum);
        List<SysMenu> menuList = this.baseMapper.selectList(query);

        // 添加顶级菜单选项
        SysMenu topMenu = new SysMenu();
        topMenu.setParentId(-1L);
        topMenu.setMenuId(0L);
        topMenu.setTitle("顶级菜单");
        topMenu.setOrderNum(0L);
        menuList.add(topMenu);

        return TreeBuilder.buildMenuTree(menuList, -1L);
    }

    /**
     * 根据用户ID获取菜单（缓存）
     */
    @Override
    @Cacheable(value = "userMenuCache", key = "#userId")
    public List<RouterDTO> getMenuByUserId(Long userId) {
        logger.info("获取用户菜单，用户ID: {}", userId);

        List<SysMenu> menuList = this.baseMapper.getMenuByUserId(userId);
        return TreeBuilder.buildRouterTree(menuList, 0L);
    }

    /**
     * 根据角色ID获取菜单
     */
    @Override
    public List<MenuDTO> getMenuByRoleId(Long roleId) {
        logger.info("获取角色菜单，角色ID: {}", roleId);

        List<SysMenu> menuList = this.baseMapper.getMenuByRoleId(roleId);
        return menuConverter.toDTOList(menuList);
    }

    /**
     * 根据学生ID获取菜单（缓存）
     */
    @Override
    @Cacheable(value = "studentMenuCache", key = "#studentId")
    public List<RouterDTO> getMenuByStudentId(Long studentId) {
        logger.info("获取学生菜单，学生ID: {}", studentId);

        List<SysMenu> menuList = this.baseMapper.getMenuByStuId(studentId);
        return TreeBuilder.buildRouterTree(menuList, 0L);
    }

    /**
     * 根据教师ID获取菜单（缓存）
     */
    @Override
    @Cacheable(value = "teacherMenuCache", key = "#teacherId")
    public List<RouterDTO> getMenuByTeacherId(Long teacherId) {
        logger.info("获取教师菜单，教师ID: {}", teacherId);

        List<SysMenu> menuList = this.baseMapper.getMenuByTeacherId(teacherId);
        return TreeBuilder.buildRouterTree(menuList, 0L);
    }

    /**
     * 创建菜单
     */
    @Override
    @CacheEvict(value = {"menuCache", "userMenuCache", "studentMenuCache", "teacherMenuCache"}, allEntries = true)
    public boolean createMenu(MenuDTO menuDTO) {
        logger.info("创建菜单: {}", menuDTO.getTitle());

        // 验证菜单数据
        validateMenuData(menuDTO);

        SysMenu entity = menuConverter.createEntity(menuDTO);
        return this.save(entity);
    }

    /**
     * 更新菜单
     */
    @Override
    @CacheEvict(value = {"menuCache", "userMenuCache", "studentMenuCache", "teacherMenuCache"}, allEntries = true)
    public boolean updateMenu(MenuDTO menuDTO) {
        logger.info("更新菜单: {}", menuDTO.getTitle());

        // 验证菜单数据
        validateMenuData(menuDTO);

        SysMenu entity = this.getById(menuDTO.getMenuId());
        if (entity == null) {
            throw new IllegalArgumentException("菜单不存在");
        }

        menuConverter.updateEntity(entity, menuDTO);
        return this.updateById(entity);
    }

    /**
     * 删除菜单
     */
    @Override
    @CacheEvict(value = {"menuCache", "userMenuCache", "studentMenuCache", "teacherMenuCache"}, allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMenu(Long menuId) {
        logger.info("删除菜单，ID: {}", menuId);

        // 权限检查：只有管理员可以删除菜单
        if (!hasMenuDeletePermission()) {
            throw new SecurityException("权限不足，只有管理员可以删除菜单");
        }

        // 检查是否有子菜单
        if (hasChildren(menuId)) {
            throw new IllegalStateException("该菜单存在下级菜单，不能删除");
        }

        // 检查是否有角色关联
        if (hasRoleAssociation(menuId)) {
            throw new IllegalStateException("该菜单已分配给角色，请先取消角色分配后再删除");
        }

        // 删除菜单
        boolean result = this.removeById(menuId);
        if (result) {
            logger.info("菜单删除成功，ID: {}", menuId);
        }
        return result;
    }

    /**
     * 检查菜单是否有子菜单
     */
    private boolean hasChildren(Long menuId) {
        QueryWrapper<SysMenu> query = new QueryWrapper<>();
        query.lambda().eq(SysMenu::getParentId, menuId);
        return this.count(query) > 0;
    }

    /**
     * 检查菜单是否有角色关联
     */
    private boolean hasRoleAssociation(Long menuId) {
        // 查询 sys_role_menu 表中是否存在该菜单的关联记录
        QueryWrapper<SysRoleMenu> query = new QueryWrapper<>();
        query.lambda().eq(SysRoleMenu::getMenuId, menuId);
        long count = sysRoleMenuService.count(query);
        return count > 0;
    }

    /**
     * 检查当前用户是否有菜单删除权限
     */
    private boolean hasMenuDeletePermission() {
        UserContext currentUser = UserContext.getCurrentUser();
        if (currentUser == null) {
            logger.warn("当前用户上下文为空，拒绝菜单删除操作");
            return false;
        }

        // 只有管理员（userType = "2"）可以删除菜单
        boolean isAdmin = UserContext.isAdmin();
        if (!isAdmin) {
            logger.warn("用户 {} (类型: {}) 尝试删除菜单，权限不足",
                currentUser.getUsername(), currentUser.getUserType());
        }

        return isAdmin;
    }

    /**
     * 强制删除菜单（级联删除相关数据）
     */
    @Override
    @CacheEvict(value = {"menuCache", "userMenuCache", "studentMenuCache", "teacherMenuCache"}, allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public boolean forceDeleteMenu(Long menuId) {
        logger.info("强制删除菜单，ID: {}", menuId);

        // 权限检查：只有管理员可以强制删除菜单
        if (!hasMenuDeletePermission()) {
            throw new SecurityException("权限不足，只有管理员可以强制删除菜单");
        }

        // 检查是否有子菜单
        if (hasChildren(menuId)) {
            throw new IllegalStateException("该菜单存在下级菜单，不能删除");
        }

        // 1. 先删除角色菜单关联
        QueryWrapper<SysRoleMenu> roleMenuQuery = new QueryWrapper<>();
        roleMenuQuery.lambda().eq(SysRoleMenu::getMenuId, menuId);
        sysRoleMenuService.remove(roleMenuQuery);
        logger.info("已清理菜单 {} 的角色关联", menuId);

        // 2. 删除菜单本身
        boolean result = this.removeById(menuId);
        if (result) {
            logger.info("菜单强制删除成功，ID: {}", menuId);
        }
        return result;
    }

    /**
     * 验证菜单数据
     */
    private void validateMenuData(MenuDTO menuDTO) {
        if (!StringUtils.hasText(menuDTO.getTitle())) {
            throw new IllegalArgumentException("菜单标题不能为空");
        }

        if (!StringUtils.hasText(menuDTO.getName())) {
            throw new IllegalArgumentException("路由名称不能为空");
        }

        if (!StringUtils.hasText(menuDTO.getPath())) {
            throw new IllegalArgumentException("路由路径不能为空");
        }

        // 检查路由名称是否重复
        QueryWrapper<SysMenu> query = new QueryWrapper<>();
        query.lambda().eq(SysMenu::getName, menuDTO.getName());
        if (menuDTO.getMenuId() != null) {
            query.lambda().ne(SysMenu::getMenuId, menuDTO.getMenuId());
        }

        if (this.count(query) > 0) {
            throw new IllegalArgumentException("路由名称已存在");
        }
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper<SysMenu> buildQueryWrapper(MenuQueryDTO queryDTO) {
        QueryWrapper<SysMenu> query = new QueryWrapper<>();

        if (StringUtils.hasText(queryDTO.getTitle())) {
            query.lambda().like(SysMenu::getTitle, queryDTO.getTitle());
        }

        if (StringUtils.hasText(queryDTO.getType())) {
            query.lambda().eq(SysMenu::getType, queryDTO.getType());
        }

        if (queryDTO.getParentId() != null) {
            query.lambda().eq(SysMenu::getParentId, queryDTO.getParentId());
        }

        // 排序
        if ("ASC".equalsIgnoreCase(queryDTO.getOrderDirection())) {
            query.lambda().orderByAsc(SysMenu::getOrderNum);
        } else {
            query.lambda().orderByDesc(SysMenu::getOrderNum);
        }

        return query;
    }

    /**
     * 根据用户ID获取原始菜单实体列表（用于角色分配）
     */
    @Override
    public List<SysMenu> getMenuEntitiesByUserId(Long userId) {
        logger.info("获取用户原始菜单实体，用户ID: {}", userId);
        return this.baseMapper.getMenuByUserId(userId);
    }

    /**
     * 根据角色名称获取菜单路由（缓存）
     */
    @Override
    @Cacheable(value = "roleMenuCache", key = "#roleName")
    public List<RouterDTO> getMenuByRoleName(String roleName) {
        logger.info("根据角色名称获取菜单，角色: {}", roleName);

        // 直接从数据库查询角色对应的菜单
        // 首先根据角色名称查找角色ID
        QueryWrapper<SysRole> roleQuery = new QueryWrapper<>();
        roleQuery.lambda().eq(SysRole::getRoleName, roleName);
        SysRole role = sysRoleService.getOne(roleQuery);

        if (role == null) {
            logger.warn("角色 {} 不存在", roleName);
            return Collections.emptyList();
        }

        // 根据角色ID获取菜单
        List<SysMenu> menuList = this.baseMapper.getMenuByRoleId(role.getRoleId());
        return TreeBuilder.buildRouterTree(menuList, 0L);
    }

    /**
     * 根据权限代码列表获取菜单路由（已移除权限字段，返回所有菜单）
     */
    @Override
    public List<RouterDTO> getMenuByPermissions(List<String> permissions) {
        logger.info("获取菜单路由（权限字段已移除）");

        // 查询所有菜单
        QueryWrapper<SysMenu> query = new QueryWrapper<>();
        query.lambda().orderByAsc(SysMenu::getOrderNum);

        List<SysMenu> menuList = this.baseMapper.selectList(query);

        return TreeBuilder.buildRouterTree(menuList, 0L);
    }


}
