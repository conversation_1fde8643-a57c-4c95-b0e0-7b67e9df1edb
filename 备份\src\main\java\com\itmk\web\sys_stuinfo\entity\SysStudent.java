package com.itmk.web.sys_stuinfo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * 系统学生实体类
 *
 * 用于详细的学生档案管理，包含完整的个人信息：
 * - 身份证号、银行卡号
 * - 家庭住址、户主信息
 * - 父母信息和联系方式
 * - 宿舍信息等
 *
 * 主要用于学生档案管理和详细信息记录
 * 对应数据库表：students
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("students") // 指定表名
public class SysStudent {
    private Integer id;

    @TableField("student_id") // 指定数据库字段名
    private String studentId; // 学号

    @TableField("name")
    private String name; // 姓名

    @TableField("gender")
    private String gender; // 性别（改为字符串类型，存储枚举值）

    @TableField("ethnicity")
    private String ethnicity; // 民族

    @TableField("political_status")
    private String politicalStatus; // 政治面貌

    @TableField("id_card")
    private String idCard; // 身份证号

    @TableField("bank_card")
    private String bankCard; // 银行卡号（建行）

    @TableField("phone")
    private String phone; // 本人电话

    @TableField("dormitory")
    private String dormitory; // 宿舍号

    @TableField("province")
    private String province; // 省

    @TableField("city")
    private String city; // 地（市）

    @TableField("district")
    private String district; // 县（区）

    @TableField("home_address")
    private String homeAddress; // 家庭住址

    @TableField("householder_name")
    private String householderName; // 户主姓名

    @TableField("householder_id_card")
    private String householderIdCard; // 户主身份证号

    @TableField("relationship")
    private String relationship; // 与本人关系

    @TableField("father_name")
    private String fatherName; // 父亲姓名

    @TableField("father_id_card")
    private String fatherIdCard; // 父亲身份证号

    @TableField("father_phone")
    private String fatherPhone; // 父亲电话

    @TableField("mother_name")
    private String motherName; // 母亲姓名

    @TableField("mother_id_card")
    private String motherIdCard; // 母亲身份证号

    @TableField("mother_phone")
    private String motherPhone; // 母亲电话

    @TableField("created_at")
    private Timestamp createdAt; // 创建时间

    @TableField("updated_at")
    private Timestamp updatedAt; // 更新时间

    // 性别枚举（改为字符串类型，存储枚举值）
    public enum Gender {
        男, 女
    }
}

