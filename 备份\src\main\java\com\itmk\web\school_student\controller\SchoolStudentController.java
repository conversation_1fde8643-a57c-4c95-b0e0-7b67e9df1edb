package com.itmk.web.school_student.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.itmk.utils.ResultUtils;
import com.itmk.utils.ResultVo;
import com.itmk.utils.excel.ExcelConfigFactory;
import com.itmk.service.BaseExcelService;
import com.itmk.service.UniversalExcelService;
import com.itmk.web.school_class.entity.SchoolClass;
import com.itmk.web.school_class.service.SchoolClassService;
import com.itmk.web.school_student.entity.SchoolStudent;
import com.itmk.web.school_student.entity.StuParm;
import com.itmk.web.school_student.service.SchoolStudentService;
import com.itmk.web.stu_role.entity.StuRole;
import com.itmk.web.stu_role.service.StuRoleService;
import com.itmk.web.sys_role.entity.SysRole;
import com.itmk.web.sys_role.service.SysRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;



@RestController
@RequestMapping("/api/student")
public class SchoolStudentController {
    @Autowired
    private SchoolStudentService schoolStudentService;
    @Autowired
    private SchoolClassService schoolClassService;
    @Autowired
    private SysRoleService sysRoleService;
    @Autowired
    private StuRoleService stuRoleService;
    @Autowired
    private UniversalExcelService universalExcelService;

    //新增
    @PostMapping
    public ResultVo add(@RequestBody SchoolStudent schoolStudent){
        // 加密密码
        schoolStudent.setPassword(DigestUtils.md5DigestAsHex(schoolStudent.getPassword().getBytes()));
        schoolStudentService.addStu(schoolStudent);
        return ResultUtils.success("新增成功");
    }

    //编辑
    @PutMapping
    public ResultVo edit(@RequestBody SchoolStudent schoolStudent){
        // 加密密码
        schoolStudent.setPassword(DigestUtils.md5DigestAsHex(schoolStudent.getPassword().getBytes()));
        schoolStudentService.editStu(schoolStudent);
        return ResultUtils.success("编辑成功");
    }

    //删除
    @DeleteMapping("/{stuId}")
    public ResultVo delete(@PathVariable("stuId") Long stuId){
        schoolStudentService.deleteStu(stuId);
        return ResultUtils.success("删除成功");
    }

    //根据id查询
    @GetMapping("/getById")
    public ResultVo getById(Long stuId){
        SchoolStudent stu = schoolStudentService.getStuById(stuId);
        return ResultUtils.success("查询成功",stu);
    }

    //查询列表
    @GetMapping("/list")
    public ResultVo getList(StuParm parm){
        IPage<SchoolStudent> list = schoolStudentService.getList(parm);
        return ResultUtils.success("查询成功",list);
    }

    //根据专业id查询班级列表
    @GetMapping("/getClassList")
    public ResultVo getClassList(Long majorId){
        QueryWrapper<SchoolClass> query = new QueryWrapper<>();
        query.lambda().eq(SchoolClass::getMajorId,majorId);
        List<SchoolClass> list = schoolClassService.list(query);
        return ResultUtils.success("查询成功",list);
    }

    // 查询学生角色
    @GetMapping("/getRoleList")
    public ResultVo getRoleList(){
       QueryWrapper<SysRole> query = new QueryWrapper<>();
       query.lambda().eq(SysRole::getRoleType,"1");
       List<SysRole> list = sysRoleService.list(query);
       return ResultUtils.success("查询成功",list);
    }
    // 根据学生id查询角色id
    @GetMapping("/getRoleId")
    public ResultVo getRoleId(Long stuId){
        QueryWrapper<StuRole> query = new QueryWrapper<>();
        query.lambda().eq(StuRole::getStuId,stuId);
        StuRole role = stuRoleService.getOne(query);
        return ResultUtils.success("查询成功",role);
    }

    /**
     * 导出学生信息
     */
    @GetMapping("/export")
    public ResponseEntity<byte[]> exportStudents(StuParm parm) {
        try {
            // 获取所有数据（不分页）
            StuParm exportParm = new StuParm();
            exportParm.setCurrentPage(1L);
            exportParm.setPageSize(10000L); // 设置一个大的页面大小
            exportParm.setStuName(parm.getStuName());
            exportParm.setClassId(parm.getClassId());

            IPage<SchoolStudent> page = schoolStudentService.getList(exportParm);
            List<SchoolStudent> studentList = page.getRecords();

            // 使用通用Excel服务导出
            byte[] excelData = universalExcelService.exportExcel(
                studentList,
                ExcelConfigFactory.getSchoolStudentConfig()
            );

            // 设置文件名
            String fileName = "学生信息_" + System.currentTimeMillis() + ".xlsx";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", encodedFileName);

            return ResponseEntity.ok()
                .headers(headers)
                .body(excelData);

        } catch (IOException e) {
            throw new RuntimeException("导出失败: " + e.getMessage(), e);
        }
    }

    /**
     * 下载导入模板
     */
    @GetMapping("/template")
    public ResponseEntity<byte[]> downloadTemplate() {
        try {
            // 生成模板
            byte[] templateData = universalExcelService.generateTemplate(
                ExcelConfigFactory.getSchoolStudentTemplateConfig()
            );

            // 设置文件名
            String fileName = "学生信息导入模板.xlsx";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", encodedFileName);

            return ResponseEntity.ok()
                .headers(headers)
                .body(templateData);

        } catch (IOException e) {
            throw new RuntimeException("生成模板失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导入学生信息
     */
    @PostMapping("/import")
    @Transactional
    public ResultVo importStudents(@RequestParam("file") MultipartFile file) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                return ResultUtils.error("请选择要导入的文件");
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
                return ResultUtils.error("请上传Excel文件（.xlsx或.xls格式）");
            }

            // 导入Excel
            BaseExcelService.ImportResult<SchoolStudent> importResult =
                universalExcelService.importExcel(file, ExcelConfigFactory.getSchoolStudentTemplateConfig());

            if (!importResult.isSuccess()) {
                return ResultUtils.error(importResult.getMessage());
            }

            List<SchoolStudent> successList = importResult.getSuccessData();
            List<BaseExcelService.ImportError> errorList = importResult.getErrors();

            // 保存成功的数据
            int successCount = 0;
            List<String> saveErrorList = new ArrayList<>();

            for (SchoolStudent student : successList) {
                try {
                    // 设置默认密码（加密）
                    if (student.getPassword() == null || student.getPassword().isEmpty()) {
                        student.setPassword(DigestUtils.md5DigestAsHex("123456".getBytes()));
                    } else {
                        student.setPassword(DigestUtils.md5DigestAsHex(student.getPassword().getBytes()));
                    }

                    // 保存学生信息
                    schoolStudentService.addStu(student);
                    successCount++;
                } catch (Exception e) {
                    saveErrorList.add("学号 " + student.getStudentId() + "：保存失败 - " + e.getMessage());
                }
            }

            // 构建返回消息
            StringBuilder message = new StringBuilder();
            message.append("导入完成！成功导入 ").append(successCount).append(" 条记录");

            // 合并验证错误和保存错误
            List<String> allErrors = new ArrayList<>();
            for (BaseExcelService.ImportError error : errorList) {
                allErrors.add("第" + error.getRowNumber() + "行：" + error.getErrorMessage());
            }
            allErrors.addAll(saveErrorList);

            if (!allErrors.isEmpty()) {
                message.append("，失败 ").append(allErrors.size()).append(" 条");
                message.append("。错误详情：").append(String.join("；", allErrors));
            }

            return ResultUtils.success(message.toString());

        } catch (Exception e) {
            return ResultUtils.error("导入失败：" + e.getMessage());
        }
    }

    //启用学生
    @PutMapping("/enable/{stuId}")
    public ResultVo enableStudent(@PathVariable("stuId") Long stuId){
        SchoolStudent student = schoolStudentService.getById(stuId);
        if(student == null){
            return ResultUtils.error("学生不存在!");
        }
        student.setEnabled(true);
        boolean result = schoolStudentService.updateById(student);
        if(result){
            return ResultUtils.success("启用学生成功!");
        }
        return ResultUtils.error("启用学生失败!");
    }

    //禁用学生
    @PutMapping("/disable/{stuId}")
    public ResultVo disableStudent(@PathVariable("stuId") Long stuId){
        SchoolStudent student = schoolStudentService.getById(stuId);
        if(student == null){
            return ResultUtils.error("学生不存在!");
        }
        student.setEnabled(false);
        boolean result = schoolStudentService.updateById(student);
        if(result){
            return ResultUtils.success("禁用学生成功!");
        }
        return ResultUtils.error("禁用学生失败!");
    }

    //锁定学生
    @PutMapping("/lock/{stuId}")
    public ResultVo lockStudent(@PathVariable("stuId") Long stuId){
        SchoolStudent student = schoolStudentService.getById(stuId);
        if(student == null){
            return ResultUtils.error("学生不存在!");
        }
        student.setAccountNonLocked(false);
        boolean result = schoolStudentService.updateById(student);
        if(result){
            return ResultUtils.success("锁定学生成功!");
        }
        return ResultUtils.error("锁定学生失败!");
    }

    //解锁学生
    @PutMapping("/unlock/{stuId}")
    public ResultVo unlockStudent(@PathVariable("stuId") Long stuId){
        SchoolStudent student = schoolStudentService.getById(stuId);
        if(student == null){
            return ResultUtils.error("学生不存在!");
        }
        student.setAccountNonLocked(true);
        boolean result = schoolStudentService.updateById(student);
        if(result){
            return ResultUtils.success("解锁学生成功!");
        }
        return ResultUtils.error("解锁学生失败!");
    }
}