package com.itmk.utils.excel;

import com.itmk.web.common.entity.Course;
import com.itmk.web.common.entity.Semester;
import com.itmk.web.sys_stuinfo.entity.SysStudent;
import com.itmk.web.school_student.entity.SchoolStudent;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * Excel配置工厂
 * 统一管理所有实体的Excel导入导出配置
 *
 * 注意：此类提供基本的Excel配置，具体的字段映射可能需要根据实际实体类结构调整
 */
public class ExcelConfigFactory {

    /**
     * 获取课程Excel配置
     */
    public static ExcelConfig<Course> getCourseConfig() {
        // 按照原先的导入表头格式：ID, 课程名称, 课程代码, 学年, 学期名称, 学分
        String[] headers = {"ID", "课程名称", "课程代码", "学年", "学期名称", "学分"};

        return ExcelConfig.withValidation(
            headers,
            "课程信息",
            // 数据提取器
            course -> new Object[]{
                course.getId(),
                course.getCourseName(),
                course.getCourseCode(),
                course.getAcademicYear(),
                course.getSemesterName(),
                course.getCredits()
            },
            // 数据构建器
            rowData -> {
                Course course = new Course();

                // ID (可选)
                if (rowData[0] != null && StringUtils.hasText(rowData[0].toString())) {
                    try {
                        course.setId(Integer.parseInt(rowData[0].toString()));
                    } catch (NumberFormatException e) {
                        // ID解析失败不影响导入，继续处理
                    }
                }

                // 课程名称 (必填)
                if (rowData[1] != null) {
                    course.setCourseName(rowData[1].toString().trim());
                }

                // 课程代码 (必填)
                if (rowData[2] != null) {
                    course.setCourseCode(rowData[2].toString().trim());
                }

                // 学年 (必填)
                if (rowData[3] != null) {
                    course.setAcademicYear(rowData[3].toString().trim());
                }

                // 学期名称 (必填)
                if (rowData[4] != null) {
                    course.setSemesterName(rowData[4].toString().trim());
                }

                // 学分 (必填)
                if (rowData[5] != null) {
                    try {
                        course.setCredits(Double.parseDouble(rowData[5].toString()));
                    } catch (NumberFormatException e) {
                        course.setCredits(0.0); // 默认值
                    }
                }

                return course;
            },
            // 验证器
            course -> {
                if (!StringUtils.hasText(course.getCourseName())) {
                    return ExcelConfig.ValidationResult.error("课程名称不能为空");
                }
                if (!StringUtils.hasText(course.getCourseCode())) {
                    return ExcelConfig.ValidationResult.error("课程代码不能为空");
                }
                if (!StringUtils.hasText(course.getAcademicYear())) {
                    return ExcelConfig.ValidationResult.error("学年不能为空");
                }
                if (!StringUtils.hasText(course.getSemesterName())) {
                    return ExcelConfig.ValidationResult.error("学期名称不能为空");
                }
                if (course.getCredits() == null || course.getCredits() <= 0) {
                    return ExcelConfig.ValidationResult.error("学分必须大于0");
                }
                return ExcelConfig.ValidationResult.success();
            }
        );
    }

    /**
     * 获取学期Excel配置
     */
    public static ExcelConfig<Semester> getSemesterConfig() {
        String[] headers = {"ID", "学期名称", "学年", "创建时间", "更新时间"};

        return ExcelConfig.withValidation(
            headers,
            "学期信息",
            // 数据提取器
            semester -> new Object[]{
                semester.getId(),
                semester.getSemesterName(),
                semester.getAcademicYear(),
                semester.getCreatedAt(),
                semester.getUpdatedAt()
            },
            // 数据构建器
            rowData -> {
                Semester semester = new Semester();

                // ID (可选)
                if (rowData[0] != null && StringUtils.hasText(rowData[0].toString())) {
                    try {
                        semester.setId(Integer.parseInt(rowData[0].toString()));
                    } catch (NumberFormatException e) {
                        // ID解析失败不影响导入
                    }
                }

                // 学期名称 (必填)
                if (rowData[1] != null) {
                    semester.setSemesterName(rowData[1].toString().trim());
                }

                // 学年 (必填)
                if (rowData[2] != null) {
                    semester.setAcademicYear(rowData[2].toString().trim());
                }

                // 设置创建时间和更新时间
                semester.setCreatedAt(LocalDateTime.now());
                semester.setUpdatedAt(LocalDateTime.now());

                return semester;
            },
            // 验证器
            semester -> {
                if (!StringUtils.hasText(semester.getSemesterName())) {
                    return ExcelConfig.ValidationResult.error("学期名称不能为空");
                }
                if (!StringUtils.hasText(semester.getAcademicYear())) {
                    return ExcelConfig.ValidationResult.error("学年不能为空");
                }
                return ExcelConfig.ValidationResult.success();
            }
        );
    }

    /**
     * 获取系统学生Excel配置
     */
    public static ExcelConfig<SysStudent> getSysStudentConfig() {
        String[] headers = {"ID", "学号", "姓名", "性别", "电话"};

        return ExcelConfig.withValidation(
            headers,
            "系统学生信息",
            // 数据提取器
            student -> new Object[]{
                student.getId(),
                student.getStudentId(),
                student.getName(),
                student.getGender(),
                student.getPhone()
            },
            // 数据构建器
            rowData -> {
                SysStudent student = new SysStudent();

                // ID (可选)
                if (rowData[0] != null && StringUtils.hasText(rowData[0].toString())) {
                    try {
                        student.setId(Integer.parseInt(rowData[0].toString()));
                    } catch (NumberFormatException e) {
                        // ID解析失败不影响导入
                    }
                }

                // 学号 (必填)
                if (rowData[1] != null) {
                    student.setStudentId(rowData[1].toString().trim());
                }

                // 姓名 (必填)
                if (rowData[2] != null) {
                    student.setName(rowData[2].toString().trim());
                }

                // 性别
                if (rowData[3] != null) {
                    student.setGender(rowData[3].toString().trim());
                }

                // 电话
                if (rowData[4] != null) {
                    student.setPhone(rowData[4].toString().trim());
                }

                return student;
            },
            // 验证器
            student -> {
                if (!StringUtils.hasText(student.getStudentId())) {
                    return ExcelConfig.ValidationResult.error("学号不能为空");
                }
                if (!StringUtils.hasText(student.getName())) {
                    return ExcelConfig.ValidationResult.error("姓名不能为空");
                }
                return ExcelConfig.ValidationResult.success();
            }
        );
    }

    /**
     * 获取学校学生Excel配置
     */
    public static ExcelConfig<SchoolStudent> getSchoolStudentConfig() {
        String[] headers = {"ID", "学号", "姓名", "性别", "电话", "班级ID"};

        return ExcelConfig.withValidation(
            headers,
            "学校学生信息",
            // 数据提取器
            student -> new Object[]{
                student.getStuId(),
                student.getStudentId(),
                student.getStuName(),
                student.getSex(),
                student.getPhone(),
                student.getClassId()
            },
            // 数据构建器
            rowData -> {
                SchoolStudent student = new SchoolStudent();

                // ID (可选)
                if (rowData[0] != null && StringUtils.hasText(rowData[0].toString())) {
                    try {
                        student.setStuId(Long.parseLong(rowData[0].toString()));
                    } catch (NumberFormatException e) {
                        // ID解析失败不影响导入
                    }
                }

                // 学号 (必填)
                if (rowData[1] != null) {
                    student.setStudentId(rowData[1].toString().trim());
                }

                // 姓名 (必填)
                if (rowData[2] != null) {
                    student.setStuName(rowData[2].toString().trim());
                }

                // 性别
                if (rowData[3] != null) {
                    student.setSex(rowData[3].toString().trim());
                }

                // 电话
                if (rowData[4] != null) {
                    student.setPhone(rowData[4].toString().trim());
                }

                // 班级ID
                if (rowData[5] != null) {
                    try {
                        student.setClassId(Long.parseLong(rowData[5].toString()));
                    } catch (NumberFormatException e) {
                        // 班级ID解析失败
                    }
                }

                return student;
            },
            // 验证器
            student -> {
                if (!StringUtils.hasText(student.getStudentId())) {
                    return ExcelConfig.ValidationResult.error("学号不能为空");
                }
                if (!StringUtils.hasText(student.getStuName())) {
                    return ExcelConfig.ValidationResult.error("姓名不能为空");
                }
                return ExcelConfig.ValidationResult.success();
            }
        );
    }

    /**
     * 获取课程模板配置（用于模板下载）
     */
    public static ExcelConfig<Course> getCourseTemplateConfig() {
        return getCourseConfig();
    }

    /**
     * 获取系统学生模板配置（用于模板下载）
     */
    public static ExcelConfig<SysStudent> getSysStudentTemplateConfig() {
        return getSysStudentConfig();
    }

    /**
     * 获取学校学生模板配置（用于模板下载）
     */
    public static ExcelConfig<SchoolStudent> getSchoolStudentTemplateConfig() {
        return getSchoolStudentConfig();
    }
}
