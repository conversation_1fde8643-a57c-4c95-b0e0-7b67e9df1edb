package com.example.vo.monitor;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 在线用户响应VO
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
public class OnlineUserVO {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 地理位置
     */
    private String location;

    /**
     * 浏览器
     */
    private String browser;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 登录时间
     */
    private LocalDateTime loginTime;

    /**
     * 最后访问时间
     */
    private LocalDateTime lastAccessTime;

    /**
     * 在线时长（分钟）
     */
    private Long onlineDuration;
}
