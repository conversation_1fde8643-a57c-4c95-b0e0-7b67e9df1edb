import { http } from "@/utils/http";
import type { QualityAssessmentQuery, QualityAssessmentRecord } from "@/views/score/quality-assessment/utils/types";

type Result = {
  success: boolean;
  data?: any;
};

type ResultTable = {
  success: boolean;
  data?: {
    /** 列表数据 */
    records: QualityAssessmentRecord[];
    /** 总条目数 */
    total: number;
    /** 当前页数 */
    current: number;
    /** 每页显示条目个数 */
    size: number;
  };
};

/** 获取基本素质测评列表 */
export const getQualityAssessmentList = (data?: QualityAssessmentQuery) => {
  return http.request<ResultTable>("post", "/quality-assessment/list", { data });
};

/** 新增基本素质测评记录 */
export const addQualityAssessment = (data?: QualityAssessmentRecord) => {
  return http.request<Result>("post", "/quality-assessment/add", { data });
};

/** 修改基本素质测评记录 */
export const updateQualityAssessment = (data?: QualityAssessmentRecord) => {
  return http.request<Result>("post", "/quality-assessment/update", { data });
};

/** 删除基本素质测评记录 */
export const deleteQualityAssessment = (evaluationId: number) => {
  return http.request<Result>("delete", `/quality-assessment/delete/${evaluationId}`);
};

/** 导出基本素质测评数据 */
export const exportQualityAssessment = (data?: QualityAssessmentQuery) => {
  return http.request<any>("post", "/quality-assessment/export", { 
    data,
    responseType: "blob"
  });
};

/** 下载基本素质测评导入模板 */
export const downloadQualityAssessmentTemplate = (data?: { classCode: string; semesterId: string }) => {
  return http.request<any>("post", "/quality-assessment/download-template", { 
    data,
    responseType: "blob"
  });
};

/** 导入基本素质测评数据 */
export const importQualityAssessment = (data: FormData) => {
  return http.request<Result>("post", "/quality-assessment/import", { 
    data,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};
