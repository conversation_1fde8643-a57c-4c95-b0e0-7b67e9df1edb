package com.example.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 缓存配置
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Configuration
@EnableCaching
public class CacheConfig {

    /**
     * 缓存管理器
     */
    @Bean
    public CacheManager cacheManager() {
        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager();

        // 预定义缓存名称
        cacheManager.setCacheNames(java.util.Arrays.asList(
            "menuList",
            "userMenuList",
            "asyncRoutes",
            "userRoles",
            "userPermissions",
            "deptList",
            "allDepts",
            "deptDetail",
            "userList",
            "allUsers",
            "userDetail",
            "userRoleIds",
            "userByUsername",
            "roleList",
            "allRoles",
            "roleDetail",
            "roleMenuPermissions",
            // 学院管理相关缓存
            "collegeList",
            "allColleges",
            "collegeDetail",
            // 专业管理相关缓存
            "majorList",
            "allMajors",
            "majorsByCollege",
            "majorDetail",
            // 学年学期管理相关缓存
            "semesterList",
            "allSemesters",
            "semestersByYear",
            "semesterDetail",
            "currentSemester",
            // 课程管理相关缓存
            "courseList",
            "coursesByMajor",
            "coursesByType",
            "coursesByCollege",
            "courseDetail",
            "courseByCode",
            // 班级课程分配相关缓存
            "classCourseList",
            "classCoursesByClass",
            "classCourseDetail"
        ));

        // 允许空值缓存
        cacheManager.setAllowNullValues(true);

        return cacheManager;
    }
}
