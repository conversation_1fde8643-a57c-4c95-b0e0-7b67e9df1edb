package com.example.service.system;

import com.example.dto.system.DeptQueryDTO;
import com.example.vo.system.DeptVO;

import java.util.List;

/**
 * 部门服务接口
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
public interface DeptService {

    /**
     * 获取部门列表（树形结构）
     *
     * @param query 查询条件
     * @return 部门树形列表
     */
    List<DeptVO> getDeptList(DeptQueryDTO query);

    /**
     * 获取所有部门列表
     *
     * @return 所有部门列表
     */
    List<DeptVO> getAllDepts();

    /**
     * 根据ID获取部门详情
     *
     * @param id 部门ID
     * @return 部门详情
     */
    DeptVO getDeptById(Integer id);

    /**
     * 新增部门
     *
     * @param deptVO 部门信息
     */
    void saveDept(DeptVO deptVO);

    /**
     * 更新部门
     *
     * @param deptVO 部门信息
     */
    void updateDept(DeptVO deptVO);

    /**
     * 删除部门
     *
     * @param deptId 部门ID
     */
    void deleteDept(Integer deptId);

    /**
     * 检查部门名称是否存在
     *
     * @param deptName 部门名称
     * @param excludeId 排除的部门ID
     * @return 是否存在
     */
    boolean isDeptNameExists(String deptName, Integer excludeId);

    /**
     * 检查部门编码是否存在
     *
     * @param deptCode 部门编码
     * @param excludeId 排除的部门ID
     * @return 是否存在
     */
    boolean isDeptCodeExists(String deptCode, Integer excludeId);

    /**
     * 切换部门状态
     *
     * @param deptId 部门ID
     * @param status 状态
     */
    void toggleDeptStatus(Integer deptId, Integer status);


}
