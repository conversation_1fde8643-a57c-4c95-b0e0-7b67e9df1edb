<template>
  <el-form
    ref="ruleFormRef"
    :model="newFormInline"
    :rules="classCourseFormRules"
    label-width="100px"
  >
    <el-form-item label="班级" prop="classCode">
      <el-select
        v-model="newFormInline.classCode"
        placeholder="请选择班级"
        clearable
        filterable
        class="w-full"
      >
        <el-option
          v-for="classItem in classOptions"
          :key="classItem.classCode"
          :label="classItem.className"
          :value="classItem.classCode"
        />
      </el-select>
    </el-form-item>

    <el-form-item label="课程" prop="courseCode">
      <el-select
        v-model="newFormInline.courseCode"
        placeholder="请选择课程"
        clearable
        filterable
        class="w-full"
      >
        <el-option
          v-for="course in courseOptions"
          :key="course.courseCode"
          :label="`${course.courseName} (${course.courseCode})`"
          :value="course.courseCode"
        />
      </el-select>
    </el-form-item>

    <el-form-item label="学期" prop="semesterId">
      <el-select
        v-model="newFormInline.semesterId"
        placeholder="请选择学期"
        clearable
        class="w-full"
      >
        <el-option
          v-for="semester in semesterOptions"
          :key="semester.id"
          :label="semester.semesterName"
          :value="semester.id"
        />
      </el-select>
    </el-form-item>



    <el-form-item label="状态" prop="isActive">
      <el-switch
        v-model="newFormInline.isActive"
        active-text="启用"
        inactive-text="停用"
      />
    </el-form-item>

    <el-form-item label="排序" prop="sortOrder">
      <el-input-number
        v-model="newFormInline.sortOrder"
        :min="0"
        :max="999"
        controls-position="right"
        class="w-full"
      />
    </el-form-item>

    <el-form-item label="备注" prop="remark">
      <el-input
        v-model="newFormInline.remark"
        type="textarea"
        :rows="3"
        placeholder="请输入备注信息"
        maxlength="500"
        show-word-limit
      />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, onMounted, withDefaults } from "vue";
import { classCourseFormRules } from "../utils/rule";
import { ClassCourseFormProps } from "../utils/types";
import { getAllClasses } from "@/api/basic/classes";
import { getAllCourses } from "@/api/educational/course";
import { getAllSemesters } from "@/api/basic/semester";

const props = withDefaults(defineProps<ClassCourseFormProps>(), {
  formInline: () => ({
    id: null,
    classCode: "",
    courseCode: "",
    semesterId: null,
    isActive: true,
    sortOrder: 0,
    remark: ""
  })
});

const ruleFormRef = ref();
const newFormInline = ref(props.formInline);

// 选项数据
const classOptions = ref([]);
const courseOptions = ref([]);
const semesterOptions = ref([]);

// 加载班级选项
async function loadClassOptions() {
  try {
    const response = await getAllClasses();
    if (response.success && response.data) {
      classOptions.value = response.data;
    }
  } catch (error) {
    console.error("加载班级选项失败:", error);
  }
}

// 加载课程选项
async function loadCourseOptions() {
  try {
    const response = await getAllCourses();
    if (response.success && response.data) {
      courseOptions.value = response.data;
    }
  } catch (error) {
    console.error("加载课程选项失败:", error);
  }
}

// 加载学期选项
async function loadSemesterOptions() {
  try {
    const response = await getAllSemesters();
    if (response.success && response.data) {
      semesterOptions.value = response.data;
    }
  } catch (error) {
    console.error("加载学期选项失败:", error);
  }
}

// 获取表单引用
function getRef() {
  return ruleFormRef.value;
}

// 组件挂载时加载数据
onMounted(async () => {
  await Promise.all([
    loadClassOptions(),
    loadCourseOptions(),
    loadSemesterOptions()
  ]);
});

defineExpose({ getRef });
</script>
