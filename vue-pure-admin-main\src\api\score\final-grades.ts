import { http } from "@/utils/http";
import type { PageResult } from "@/api/types";

// 成绩查询参数
export interface GradeQueryParams {
  current?: number;
  size?: number;
  studentId?: string;
  studentName?: string;
  classCode?: string;
  majorCode?: string;
  collegeCode?: string;
  courseCode?: string;
  courseName?: string;
  courseType?: string;
  semesterId?: number;
  minScore?: number;
  maxScore?: number;
  isRetake?: boolean;
  grade?: string;
}

// 成绩信息
export interface GradeVO {
  id?: number;
  studentId: string;
  studentName?: string;
  classCode?: string;
  className?: string;
  majorCode?: string;
  majorName?: string;
  collegeCode?: string;
  collegeName?: string;
  courseCode: string;
  courseName?: string;
  courseType?: string;
  credits?: number;
  semesterId: number;
  semesterName?: string;
  finalScore: number;
  gradePoint?: number;
  isRetake?: boolean;
  remarks?: string;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * 分页查询成绩列表
 */
export const getGradePage = (params: GradeQueryParams) => {
  return http.request<PageResult<GradeVO>>("post", "/api/score/grades/page", { data: params });
};

/**
 * 根据班级查询成绩列表
 */
export const getGradesByClass = (classCode: string, semesterId?: number) => {
  const params = semesterId ? { semesterId } : {};
  return http.request<GradeVO[]>("get", `/api/score/final/class/${classCode}`, { params });
};

/**
 * 根据学生查询成绩列表
 */
export const getGradesByStudent = (studentId: string, semesterId?: number) => {
  const params = semesterId ? { semesterId } : {};
  return http.request<GradeVO[]>("get", `/api/score/final/student/${studentId}`, { params });
};

// 横向成绩数据
export interface HorizontalGradeData {
  studentId: string;
  studentName?: string;
  classCode?: string;
  className?: string;
  academicScore?: number; // 学业成绩
  calculateGpa?: number; // 绩点
  totalCredits?: number;
  avgScore?: number;
  [courseCode: string]: any; // 动态课程成绩字段
}

/**
 * 获取横向期末成绩数据
 */
export const getHorizontalGrades = (classCode: string, semesterId?: number, pageNum = 1, pageSize = 10) => {
  return http.request<any>("get", "/api/score/final/horizontal", {
    params: { classCode, semesterId, pageNum, pageSize }
  });
};

/**
 * 导出单学期期末成绩Excel
 */
export const exportGrades = (classCode: string, semesterId?: number) => {
  return new Promise((resolve, reject) => {
    http.request<Blob>("get", `/api/score/final/export`, {
      params: { classCode, semesterId },
      responseType: "blob"
    }, {
      beforeResponseCallback: (response) => {
        // 返回完整的响应对象，包含data和headers
        resolve({
          data: response.data,
          headers: response.headers
        });
      }
    }).catch(reject);
  });
};

/**
 * 导出多学期合并期末成绩Excel
 */
export const exportMultipleSemestersGrades = (classCode: string, semesterIds: number[]) => {
  const params = new URLSearchParams();
  params.append('classCode', classCode);
  semesterIds.forEach(id => {
    params.append('semesterIds', id.toString());
  });

  return new Promise((resolve, reject) => {
    http.request<Blob>("get", `/api/score/final/export/multiple?${params.toString()}`, {
      responseType: "blob"
    }, {
      beforeResponseCallback: (response) => {
        // 返回完整的响应对象，包含data和headers
        resolve({
          data: response.data,
          headers: response.headers
        });
      }
    }).catch(reject);
  });
};
