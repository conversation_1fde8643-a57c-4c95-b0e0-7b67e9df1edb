package com.itmk.web.sys_menu.converter;

import com.itmk.web.sys_menu.dto.MenuDTO;
import com.itmk.web.sys_menu.entity.SysMenu;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 菜单实体与DTO转换器
 * 基于Spring Boot最佳实践设计
 */
@Component
public class MenuConverter {
    
    /**
     * 实体转DTO
     */
    public MenuDTO toDTO(SysMenu entity) {
        if (entity == null) {
            return null;
        }
        
        MenuDTO dto = new MenuDTO();
        BeanUtils.copyProperties(entity, dto);
        
        // 处理时间转换
        if (entity.getCreateTime() != null) {
            dto.setCreateTime(entity.getCreateTime().toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDateTime());
        }
        if (entity.getUpdateTime() != null) {
            dto.setUpdateTime(entity.getUpdateTime().toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDateTime());
        }
        
        return dto;
    }
    
    /**
     * DTO转实体
     */
    public SysMenu toEntity(MenuDTO dto) {
        if (dto == null) {
            return null;
        }
        
        SysMenu entity = new SysMenu();
        BeanUtils.copyProperties(dto, entity);
        
        // 处理时间转换
        if (dto.getCreateTime() != null) {
            entity.setCreateTime(Date.from(dto.getCreateTime()
                .atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (dto.getUpdateTime() != null) {
            entity.setUpdateTime(Date.from(dto.getUpdateTime()
                .atZone(ZoneId.systemDefault()).toInstant()));
        }
        
        return entity;
    }
    
    /**
     * 实体列表转DTO列表
     */
    public List<MenuDTO> toDTOList(List<SysMenu> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return new ArrayList<>();
        }
        
        return entities.stream()
            .map(this::toDTO)
            .collect(Collectors.toList());
    }
    
    /**
     * DTO列表转实体列表
     */
    public List<SysMenu> toEntityList(List<MenuDTO> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return new ArrayList<>();
        }
        
        return dtos.stream()
            .map(this::toEntity)
            .collect(Collectors.toList());
    }
    
    /**
     * 更新实体（用于编辑操作）
     */
    public void updateEntity(SysMenu entity, MenuDTO dto) {
        if (entity == null || dto == null) {
            return;
        }
        
        // 保留原有的ID和创建时间
        Long originalId = entity.getMenuId();
        Date originalCreateTime = entity.getCreateTime();
        
        BeanUtils.copyProperties(dto, entity);
        
        // 恢复原有的ID和创建时间
        entity.setMenuId(originalId);
        entity.setCreateTime(originalCreateTime);
        
        // 设置更新时间
        entity.setUpdateTime(new Date());
    }
    
    /**
     * 创建新实体（用于新增操作）
     */
    public SysMenu createEntity(MenuDTO dto) {
        if (dto == null) {
            return null;
        }
        
        SysMenu entity = toEntity(dto);
        
        // 设置创建时间和更新时间
        Date now = new Date();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        
        return entity;
    }
}
