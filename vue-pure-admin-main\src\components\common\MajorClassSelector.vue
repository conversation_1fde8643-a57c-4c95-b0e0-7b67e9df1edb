<template>
  <div class="major-class-selector">
    <!-- 专业选择 -->
    <div v-if="currentStep === 'major'" class="selector-container">
      <div class="header">
        <h3>选择专业</h3>
        <p class="subtitle">请选择要查看成绩的专业</p>
      </div>

      <!-- 搜索表单 -->
      <el-form :model="majorForm" class="search-form">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="学院">
              <el-select
                v-model="majorForm.collegeCode"
                placeholder="请选择学院"
                clearable
                @change="onMajorSearch"
              >
                <el-option
                  v-for="college in colleges"
                  :key="college.collegeCode"
                  :label="college.collegeName"
                  :value="college.collegeCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="专业名称">
              <el-input
                v-model="majorForm.majorName"
                placeholder="请输入专业名称"
                clearable
                @input="onMajorSearch"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-button type="primary" @click="onMajorSearch">
                <template #icon>
                  <IconifyIconOffline icon="ep:search" />
                </template>
                搜索
              </el-button>
              <el-button @click="resetMajorForm">
                <template #icon>
                  <IconifyIconOffline icon="ep:refresh" />
                </template>
                重置
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 专业列表 -->
      <PureTableBar title="专业列表" :columns="majorColumns" @refresh="onMajorSearch">
        <template v-slot="{ size, dynamicColumns }">
          <pure-table
            ref="majorTableRef"
            adaptive
            :adaptiveConfig="{ offsetBottom: 108 }"
            align-whole="center"
            table-layout="auto"
            :loading="majorLoading"
            :size="size"
            :data="majorList"
            :columns="dynamicColumns"
            :pagination="majorPagination"
            :paginationSmall="size === 'small'"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            @page-size-change="handleMajorSizeChange"
            @page-current-change="handleMajorCurrentChange"
          >
            <template #operation="{ row }">
              <el-button
                class="reset-margin"
                link
                type="primary"
                :size="size"
                @click="selectMajor(row)"
              >
                选择专业
              </el-button>
            </template>
          </pure-table>
        </template>
      </PureTableBar>
    </div>

    <!-- 班级选择 -->
    <div v-if="currentStep === 'class'" class="selector-container">
      <div class="header">
        <h3>选择班级</h3>
        <p class="subtitle">{{ selectedMajor?.majorName }} - 请选择要查看成绩的班级</p>
        <el-button @click="backToMajor" class="back-btn">
          <template #icon>
            <IconifyIconOffline icon="ep:arrow-left" />
          </template>
          返回专业选择
        </el-button>
      </div>

      <!-- 班级搜索表单 -->
      <el-form :model="classForm" class="search-form">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="入学年份">
              <el-select
                v-model="classForm.enrollmentYear"
                placeholder="请选择入学年份"
                clearable
                @change="onClassSearch"
              >
                <el-option
                  v-for="year in yearOptions"
                  :key="year.value"
                  :label="year.label"
                  :value="year.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="班级名称">
              <el-input
                v-model="classForm.className"
                placeholder="请输入班级名称"
                clearable
                @input="onClassSearch"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-button type="primary" @click="onClassSearch">
                <template #icon>
                  <IconifyIconOffline icon="ep:search" />
                </template>
                搜索
              </el-button>
              <el-button @click="resetClassForm">
                <template #icon>
                  <IconifyIconOffline icon="ep:refresh" />
                </template>
                重置
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 班级列表 -->
      <PureTableBar :title="`${selectedMajor?.majorName} - 班级列表`" :columns="classColumns" @refresh="onClassSearch">
        <template v-slot="{ size, dynamicColumns }">
          <pure-table
            ref="classTableRef"
            adaptive
            :adaptiveConfig="{ offsetBottom: 108 }"
            align-whole="center"
            table-layout="auto"
            :loading="classLoading"
            :size="size"
            :data="classList"
            :columns="dynamicColumns"
            :pagination="classPagination"
            :paginationSmall="size === 'small'"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            @page-size-change="handleClassSizeChange"
            @page-current-change="handleClassCurrentChange"
          >
            <template #operation="{ row }">
              <el-button
                class="reset-margin"
                link
                type="primary"
                :size="size"
                @click="selectClass(row)"
              >
                选择班级
              </el-button>
            </template>
          </pure-table>
        </template>
      </PureTableBar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, computed } from "vue";
import { IconifyIconOffline } from "@/components/ReIcon";
import { PureTableBar } from "@/components/RePureTableBar";
import { getMajorList } from "@/api/basic/major";
import { getCollegeList } from "@/api/basic/college";
import { getClassesByMajorCode } from "@/api/basic/classes";
import { getYearOptions } from "@/utils/yearOptions";
import type { MajorItem as MajorVO } from "@/api/basic/major";
import type { CollegeItem as CollegeVO } from "@/api/basic/college";
import type { ClassesVO as ClassVO } from "@/api/basic/classes";
import type { PaginationProps } from "@pureadmin/table";

// Props
interface Props {
  initialMajorCode?: string;
  initialClassCode?: string;
}

const props = withDefaults(defineProps<Props>(), {
  initialMajorCode: "",
  initialClassCode: ""
});

// Emits
const emit = defineEmits<{
  majorSelected: [major: { majorCode: string; majorName: string }];
  classSelected: [classInfo: {
    majorCode: string;
    majorName: string;
    classCode: string;
    className: string;
  }];
  selectionComplete: [selection: {
    major: { majorCode: string; majorName: string };
    class: { classCode: string; className: string };
  }];
}>();

// 当前步骤
const currentStep = ref<'major' | 'class'>('major');

// 选中的专业和班级
const selectedMajor = ref<{ majorCode: string; majorName: string } | null>(null);
const selectedClass = ref<{ classCode: string; className: string } | null>(null);

// 专业相关数据
const majorForm = reactive({
  collegeCode: "",
  majorName: ""
});

const majorList = ref<MajorVO[]>([]);
const colleges = ref<CollegeVO[]>([]);
const majorLoading = ref(false);
const majorTableRef = ref();

const majorPagination = reactive<PaginationProps>({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true
});

const majorColumns: TableColumnList = [
  {
    label: "专业代码",
    prop: "majorCode",
    minWidth: 120
  },
  {
    label: "专业名称",
    prop: "majorName",
    minWidth: 200
  },
  {
    label: "学院名称",
    prop: "collegeName",
    minWidth: 150
  },
  {
    label: "学制",
    prop: "duration",
    width: 80,
    align: "center"
  },
  {
    label: "操作",
    fixed: "right",
    width: 120,
    slot: "operation"
  }
];

// 班级相关数据
const classForm = reactive({
  enrollmentYear: "",
  className: ""
});

const classList = ref<ClassVO[]>([]);
const classLoading = ref(false);
const classTableRef = ref();
const yearOptions = getYearOptions();

const classPagination = reactive<PaginationProps>({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true
});

const classColumns: TableColumnList = [
  {
    label: "班级代码",
    prop: "classCode",
    minWidth: 120
  },
  {
    label: "班级名称",
    prop: "className",
    minWidth: 200
  },
  {
    label: "入学年份",
    prop: "enrollmentYear",
    width: 100,
    align: "center"
  },
  {
    label: "学生人数",
    prop: "studentCount",
    width: 100,
    align: "center"
  },
  {
    label: "操作",
    fixed: "right",
    width: 120,
    slot: "operation"
  }
];

// 获取学院列表
const getColleges = async () => {
  try {
    const { data } = await getCollegeList({
      current: 1,
      size: 1000
    });
    colleges.value = data?.records || [];
  } catch (error) {
    console.error("获取学院列表失败:", error);
  }
};

// 获取专业列表
const getMajors = async () => {
  try {
    majorLoading.value = true;
    const { data } = await getMajorList({
      current: majorPagination.currentPage,
      size: majorPagination.pageSize,
      collegeCode: majorForm.collegeCode,
      majorName: majorForm.majorName
    });

    majorList.value = data?.records || [];
    majorPagination.total = data?.total || 0;
  } catch (error) {
    console.error("获取专业列表失败:", error);
    majorList.value = [];
    majorPagination.total = 0;
  } finally {
    majorLoading.value = false;
  }
};

// 获取班级列表
const getClasses = async () => {
  if (!selectedMajor.value?.majorCode) return;

  try {
    classLoading.value = true;
    const { data } = await getClassesByMajorCode({
      majorCode: selectedMajor.value.majorCode,
      current: classPagination.currentPage,
      size: classPagination.pageSize,
      enrollmentYear: classForm.enrollmentYear,
      className: classForm.className
    });

    classList.value = data?.records || [];
    classPagination.total = data?.total || 0;
  } catch (error) {
    console.error("获取班级列表失败:", error);
    classList.value = [];
    classPagination.total = 0;
  } finally {
    classLoading.value = false;
  }
};

// 专业搜索
const onMajorSearch = () => {
  majorPagination.currentPage = 1;
  getMajors();
};

// 重置专业表单
const resetMajorForm = () => {
  majorForm.collegeCode = "";
  majorForm.majorName = "";
  onMajorSearch();
};

// 专业分页处理
const handleMajorSizeChange = (val: number) => {
  majorPagination.pageSize = val;
  getMajors();
};

const handleMajorCurrentChange = (val: number) => {
  majorPagination.currentPage = val;
  getMajors();
};

// 选择专业
const selectMajor = (major: MajorVO) => {
  selectedMajor.value = {
    majorCode: major.majorCode,
    majorName: major.majorName
  };

  // 重置班级相关数据
  classList.value = [];
  classPagination.currentPage = 1;
  classPagination.total = 0;
  resetClassForm();

  // 切换到班级选择步骤
  currentStep.value = 'class';

  // 触发专业选择事件
  emit('majorSelected', selectedMajor.value);

  // 获取班级列表
  getClasses();
};

// 返回专业选择
const backToMajor = () => {
  currentStep.value = 'major';
  selectedMajor.value = null;
  selectedClass.value = null;
};

// 班级搜索
const onClassSearch = () => {
  classPagination.currentPage = 1;
  getClasses();
};

// 重置班级表单
const resetClassForm = () => {
  classForm.enrollmentYear = "";
  classForm.className = "";
  if (currentStep.value === 'class') {
    onClassSearch();
  }
};

// 班级分页处理
const handleClassSizeChange = (val: number) => {
  classPagination.pageSize = val;
  getClasses();
};

const handleClassCurrentChange = (val: number) => {
  classPagination.currentPage = val;
  getClasses();
};

// 选择班级
const selectClass = (classInfo: ClassVO) => {
  selectedClass.value = {
    classCode: classInfo.classCode,
    className: classInfo.className
  };

  const classSelection = {
    majorCode: selectedMajor.value!.majorCode,
    majorName: selectedMajor.value!.majorName,
    classCode: classInfo.classCode,
    className: classInfo.className
  };

  // 触发班级选择事件
  emit('classSelected', classSelection);

  // 触发选择完成事件
  emit('selectionComplete', {
    major: selectedMajor.value!,
    class: selectedClass.value
  });
};

// 初始化
onMounted(() => {
  getColleges();
  getMajors();

  // 如果有初始专业代码，自动选择
  if (props.initialMajorCode) {
    // 这里可以根据初始专业代码查找并选择专业
    // 实现逻辑根据具体需求调整
  }
});
</script>

<style scoped lang="scss">
.major-class-selector {
  .selector-container {
    .header {
      margin-bottom: 20px;

      h3 {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }

      .subtitle {
        margin: 0 0 16px 0;
        color: var(--el-text-color-regular);
        font-size: 14px;
      }

      .back-btn {
        margin-bottom: 16px;
      }
    }

    .search-form {
      margin-bottom: 20px;

      :deep(.el-form-item) {
        margin-bottom: 12px;
      }
    }
  }
}
</style>
