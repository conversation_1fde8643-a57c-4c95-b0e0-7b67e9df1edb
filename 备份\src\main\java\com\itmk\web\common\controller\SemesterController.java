package com.itmk.web.common.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.itmk.utils.ResultUtils;
import com.itmk.utils.ResultVo;
import com.itmk.web.common.entity.CommParm;
import com.itmk.web.common.entity.Semester;
import com.itmk.web.common.service.SemesterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


@Api(tags = "学期管理")
@RestController
@RequestMapping("/api/semester")
public class SemesterController {

    @Autowired
    private SemesterService semesterService;

    @ApiOperation("获取学期列表")
    @GetMapping("/list")
    public ResultVo getList(@ApiParam("查询参数") CommParm commParm) {
        try {
            IPage<Semester> list = semesterService.getList(commParm);
            return ResultUtils.success("查询成功", list);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtils.error("查询学期列表失败: " + e.getMessage());
        }
    }



    @ApiOperation("获取所有学年列表")
    @GetMapping("/academic-years")
    public ResultVo getAcademicYears() {
        try {
            List<String> academicYears = semesterService.getAcademicYears();
            return ResultUtils.success("查询成功", academicYears);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtils.error("查询学年列表失败: " + e.getMessage());
        }
    }

    @ApiOperation("根据学年获取学期列表")
    @GetMapping("/by-academic-year")
    public ResultVo getSemestersByAcademicYear(@ApiParam("学年") @RequestParam String academicYear) {
        try {
            List<Semester> semesters = semesterService.getSemestersByAcademicYear(academicYear);
            return ResultUtils.success("查询成功", semesters);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtils.error("查询学期列表失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取当前学年和学期信息")
    @GetMapping("/current")
    public ResultVo getCurrentAcademicYearAndSemester() {
        try {
            Map<String, Object> currentInfo = semesterService.getCurrentAcademicYearAndSemester();
            return ResultUtils.success("查询成功", currentInfo);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtils.error("查询当前学年学期信息失败: " + e.getMessage());
        }
    }

    @ApiOperation("自动创建学期数据")
    @PostMapping("/auto-create")
    public ResultVo autoCreateSemesters() {
        try {
            semesterService.autoCreateSemesters();
            return ResultUtils.success("自动创建学期数据成功");
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtils.error("自动创建学期数据失败: " + e.getMessage());
        }
    }




}