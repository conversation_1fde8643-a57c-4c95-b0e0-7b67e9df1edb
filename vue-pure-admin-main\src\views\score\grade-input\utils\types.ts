import type { FormRules } from "element-plus";

/** 成绩录入表单项 */
export interface GradeFormItemProps {
  /** 表单标题 */
  title: string;
  /** 学生ID */
  studentId?: string;
  /** 学生姓名 */
  studentName?: string;
  /** 班级代码 */
  classCode?: string;
  /** 班级名称 */
  className?: string;
  /** 课程代码 */
  courseCode?: string;
  /** 课程名称 */
  courseName?: string;
  /** 学期ID */
  semesterId?: number;
  /** 学期名称 */
  semesterName?: string;
  /** 期末成绩 */
  finalScore?: number;
  /** 绩点 */
  gradePoint?: number;
  /** 是否重修 */
  isRetake?: boolean;
  /** 备注 */
  remarks?: string;
}

/** 成绩录入表单属性 */
export interface GradeFormProps {
  formInline: GradeFormItemProps;
}

/** 成绩导入表单项 */
export interface ImportFormItemProps {
  /** 表单标题 */
  title: string;
  /** 班级代码 */
  classCode?: string;
  /** 班级名称 */
  className?: string;
  /** 学期ID */
  semesterId?: number | string;
  /** 选择的课程代码列表 */
  selectedCourseCodes?: string[];
  /** 上传的文件 */
  file?: File;
}

/** 成绩导入表单属性 */
export interface ImportFormProps {
  formInline: ImportFormItemProps;
}

/** 课程选择项 */
export interface CourseOption {
  courseCode: string;
  courseName: string;
  semesterId: number;
  semesterName: string;
}

/** 学期选择项 */
export interface SemesterOption {
  id: number;
  semesterName: string;
}

/** 成绩查询表单 */
export interface SearchFormProps {
  /** 学生姓名 */
  studentName?: string;
  /** 学号 */
  studentId?: string;
  /** 班级代码 */
  classCode?: string;
  /** 课程代码 */
  courseCode?: string;
  /** 学期ID */
  semesterId?: number;
}

/** 成绩导入结果 */
export interface ImportResultProps {
  /** 是否成功 */
  success: boolean;
  /** 总行数 */
  totalRows: number;
  /** 成功行数 */
  successRows: number;
  /** 失败行数 */
  failedRows: number;
  /** 错误信息列表 */
  errorMessages: string[];
  /** 错误详情列表 */
  errorDetails: Array<{
    rowNumber: number;
    studentId: string;
    errorMessage: string;
  }>;
  /** 汇总信息 */
  summary: string;
}
