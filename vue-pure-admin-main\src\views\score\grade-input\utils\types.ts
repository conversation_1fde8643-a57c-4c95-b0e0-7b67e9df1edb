// 专业信息类型
export interface MajorInfo {
  majorCode: string;
  majorName: string;
}

// 班级信息类型
export interface ClassInfo {
  classCode: string;
  className: string;
  majorCode: string;
  majorName: string;
}

// 课程信息类型
export interface CourseInfo {
  courseCode: string;
  courseName: string;
  semesterId: number;
  semesterName: string;
  credits: number;
}

// 学生成绩信息类型
export interface StudentGradeInfo {
  id?: number;
  studentId: string;
  studentName: string;
  courseCode: string;
  courseName: string;
  semesterId: number;
  semesterName: string;
  finalScore?: number;
  gradePoint?: number;
  isRetake: boolean;
  remarks?: string;
  createdAt?: string;
  updatedAt?: string;
}

// 表单属性类型
export interface FormProps {
  formInline: {
    title: string;
    id?: number;
    studentId: string;
    studentName: string;
    courseCode: string;
    courseName: string;
    semesterId: number;
    semesterName: string;
    finalScore?: number;
    gradePoint?: number;
    isRetake: boolean;
    remarks?: string;
  };
}

// 导入结果类型
export interface ImportResultInfo {
  success: boolean;
  totalRows: number;
  successRows: number;
  failedRows: number;
  errorMessages: string[];
  errorDetails: Array<{
    rowNumber: number;
    studentId: string;
    errorMessage: string;
  }>;
}

// 导入表单属性类型
export interface ImportFormProps {
  formInline: {
    title: string;
    classCode: string;
    className: string;
    selectedSemesterId: string | number;
    selectedCourseCodes: string[];
    availableSemesters: Array<{
      id: number;
      semesterName: string;
    }>;
    availableCourses: Array<{
      courseCode: string;
      courseName: string;
      semesterId: number;
      semesterName: string;
    }>;
  };
}

// 搜索表单类型
export interface SearchFormData {
  majorCode?: string;
  classCode?: string;
  courseCode?: string;
  semesterId?: number;
  studentName?: string;
  studentId?: string;
}

// 面包屑导航类型
export interface BreadcrumbItem {
  title: string;
  disabled?: boolean;
}

// 视图状态类型
export type ViewType = 'majors' | 'classes' | 'courses' | 'grades';

// 当前选择状态类型
export interface CurrentSelection {
  major?: MajorInfo;
  class?: ClassInfo;
  course?: CourseInfo;
}
