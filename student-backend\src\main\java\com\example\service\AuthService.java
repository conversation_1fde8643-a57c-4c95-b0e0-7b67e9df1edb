package com.example.service;

import com.example.dto.auth.LoginRequest;
import com.example.vo.auth.LoginResponse;

/**
 * 认证服务接口
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public interface AuthService {

    /**
     * 用户登录
     *
     * @param loginRequest 登录请求
     * @return 登录响应
     */
    LoginResponse login(LoginRequest loginRequest);

    /**
     * 刷新token
     *
     * @param refreshToken 刷新token
     * @return 新的token信息
     */
    LoginResponse refreshToken(String refreshToken);
}
