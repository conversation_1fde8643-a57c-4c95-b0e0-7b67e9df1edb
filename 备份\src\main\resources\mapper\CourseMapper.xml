<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.itmk.web.common.mapper.CourseMapper">

    <!-- 获取课程列表，包含学期信息 -->
    <select id="getListWithSemester" resultType="com.itmk.web.common.entity.Course">
        SELECT
            c.*,
            s.semester_name AS semesterName,
            s.academic_year AS academicYear
        FROM
            courses c
        LEFT JOIN
            semesters s ON c.semester_id = s.semester_id
        <where>
            <if test="courseName != null and courseName != ''">
                AND c.course_name LIKE CONCAT('%', #{courseName}, '%')
            </if>
            <if test="academicYear != null and academicYear != ''">
                AND s.academic_year = #{academicYear}
            </if>
            <if test="semesterName != null and semesterName != ''">
                AND s.semester_name = #{semesterName}
            </if>
        </where>
        ORDER BY
            c.id ASC
    </select>

</mapper>