package com.example.controller.teacher;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.common.Result;
import com.example.dto.teacher.TeacherQueryDTO;
import com.example.service.teacher.TeacherService;
import com.example.vo.teacher.TeacherVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 教师管理控制器
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@RestController
@RequestMapping("/api/teacher")
@RequiredArgsConstructor
@Tag(name = "教师管理", description = "教师信息的增删改查")
public class TeacherController {

    private final TeacherService teacherService;

    /**
     * 分页查询教师列表
     */
    @PostMapping("/list")
    @Operation(summary = "分页查询教师列表", description = "根据条件分页查询教师信息")
    public Result<IPage<TeacherVO>> getTeacherList(@RequestBody TeacherQueryDTO queryDTO) {
        IPage<TeacherVO> result = teacherService.getTeacherList(queryDTO);
        return Result.success("查询成功", result);
    }

    /**
     * 获取所有教师列表
     */
    @GetMapping("/all")
    @Operation(summary = "获取所有教师列表", description = "获取所有教师信息，不分页")
    public Result<List<TeacherVO>> getAllTeachers() {
        List<TeacherVO> result = teacherService.getAllTeachers();
        return Result.success("查询成功", result);
    }

    /**
     * 根据学院代码获取教师列表
     */
    @GetMapping("/college/{collegeCode}")
    @Operation(summary = "根据学院代码获取教师列表", description = "获取指定学院的所有教师")
    public Result<List<TeacherVO>> getTeachersByCollegeCode(@PathVariable String collegeCode) {
        List<TeacherVO> result = teacherService.getTeachersByCollegeCode(collegeCode);
        return Result.success("查询成功", result);
    }

    /**
     * 根据ID获取教师详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID获取教师详情", description = "根据教师ID获取详细信息")
    public Result<TeacherVO> getTeacherById(@PathVariable Integer id) {
        TeacherVO result = teacherService.getTeacherById(id);
        return Result.success("查询成功", result);
    }

    /**
     * 根据教师工号获取教师信息
     */
    @GetMapping("/code/{teacherCode}")
    @Operation(summary = "根据教师工号获取教师信息", description = "根据教师工号获取教师信息")
    public Result<TeacherVO> getTeacherByCode(@PathVariable String teacherCode) {
        TeacherVO result = teacherService.getTeacherByCode(teacherCode);
        return Result.success("查询成功", result);
    }

    /**
     * 新增教师
     */
    @PostMapping("/save")
    @Operation(summary = "新增教师", description = "新增教师信息")
    public Result<Void> saveTeacher(@Validated @RequestBody TeacherVO teacherVO) {
        teacherService.saveTeacher(teacherVO);
        return Result.success("新增教师成功");
    }

    /**
     * 更新教师
     */
    @PostMapping("/update")
    @Operation(summary = "更新教师", description = "更新教师信息")
    public Result<Void> updateTeacher(@Validated @RequestBody TeacherVO teacherVO) {
        teacherService.updateTeacher(teacherVO);
        return Result.success("更新教师成功");
    }

    /**
     * 删除教师
     */
    @PostMapping("/delete")
    @Operation(summary = "删除教师", description = "根据ID删除教师")
    public Result<Void> deleteTeacher(@RequestBody @Parameter(description = "包含教师ID的请求体") java.util.Map<String, Integer> request) {
        Integer id = request.get("id");
        teacherService.deleteTeacher(id);
        return Result.success("删除教师成功");
    }

    /**
     * 检查教师工号是否存在
     */
    @GetMapping("/check/{teacherCode}")
    @Operation(summary = "检查教师工号是否存在", description = "检查教师工号是否已被使用")
    public Result<Boolean> checkTeacherCodeExists(@PathVariable String teacherCode, 
                                                  @RequestParam(required = false) Integer excludeId) {
        boolean exists = teacherService.checkTeacherCodeExists(teacherCode, excludeId);
        return Result.success("查询成功", exists);
    }
}
