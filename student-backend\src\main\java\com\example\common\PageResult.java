package com.example.common;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.List;

/**
 * 分页结果封装类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageResult<T> {

    /**
     * 列表数据
     */
    private List<T> list;

    /**
     * 总条目数
     */
    private Long total;

    /**
     * 每页显示条目个数
     */
    private Integer pageSize;

    /**
     * 当前页数
     */
    private Integer pageNum;

    /**
     * 总页数
     */
    private Long totalPages;

    /**
     * 构造方法
     */
    public PageResult(List<T> list, Long total, Integer pageSize, Integer pageNum) {
        this.list = list;
        this.total = total;
        this.pageSize = pageSize;
        this.pageNum = pageNum;
        this.totalPages = pageSize > 0 ? (total + pageSize - 1) / pageSize : 0;
    }

    /**
     * 从MyBatis-Plus的Page对象创建PageResult
     */
    public static <T> PageResult<T> of(com.baomidou.mybatisplus.extension.plugins.pagination.Page<T> page) {
        return PageResult.<T>builder()
            .list(page.getRecords())
            .total(page.getTotal())
            .pageSize((int) page.getSize())
            .pageNum((int) page.getCurrent())
            .totalPages(page.getPages())
            .build();
    }

    /**
     * 从MyBatis-Plus的Page对象创建PageResult，并转换数据类型
     */
    public static <T, R> PageResult<R> of(com.baomidou.mybatisplus.extension.plugins.pagination.Page<T> page, List<R> convertedList) {
        return PageResult.<R>builder()
            .list(convertedList)
            .total(page.getTotal())
            .pageSize((int) page.getSize())
            .pageNum((int) page.getCurrent())
            .totalPages(page.getPages())
            .build();
    }
}
