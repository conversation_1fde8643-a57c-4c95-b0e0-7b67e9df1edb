package com.example.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.example.dto.auth.LoginRequest;
import com.example.entity.system.SysUser;
import com.example.common.exception.BusinessException;
import com.example.mapper.system.SysUserMapper;
import com.example.service.AuthService;
import com.example.utils.JwtUtil;
import com.example.vo.auth.LoginResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * 认证服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

    private final SysUserMapper userMapper;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;

    @Override
    public LoginResponse login(LoginRequest loginRequest) {
        // 输入验证
        if (loginRequest == null || !StringUtils.hasText(loginRequest.getUsername())
            || !StringUtils.hasText(loginRequest.getPassword())) {
            throw new BusinessException("用户名和密码不能为空");
        }

        // 用户名长度限制
        if (loginRequest.getUsername().length() > 50) {
            throw new BusinessException("用户名长度不能超过50个字符");
        }

        try {
            // 查询用户
            LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysUser::getUsername, loginRequest.getUsername())
                       .eq(SysUser::getStatus, 1); // 只查询启用的用户
            SysUser user = userMapper.selectOne(queryWrapper);

            if (user == null) {
                throw new BusinessException("用户名或密码错误");
            }

            // 验证密码
            if (!passwordEncoder.matches(loginRequest.getPassword(), user.getPassword())) {
                throw new BusinessException("用户名或密码错误");
            }

            // 查询用户角色和权限
            List<String> roles = getUserRoles(user.getId());
            List<String> permissions = getUserPermissions(user.getId());

            // 生成token
            String accessToken = jwtUtil.generateToken(user.getUsername(), user.getId());
            String refreshToken = jwtUtil.generateRefreshToken(user.getUsername(), user.getId());
            Date expires = jwtUtil.getExpirationDateFromToken(accessToken);

            // 构建响应
            LoginResponse response = new LoginResponse();
            response.setAvatar(user.getAvatar());
            response.setUsername(user.getUsername());
            response.setNickname(user.getNickname());
            response.setRoles(roles);
            response.setPermissions(permissions);
            response.setAccessToken(accessToken);
            response.setRefreshToken(refreshToken);
            response.setExpires(expires);

            return response;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("用户登录失败: {}", loginRequest.getUsername(), e);
            throw new BusinessException("登录失败，请稍后重试");
        }
    }

    @Override
    public LoginResponse refreshToken(String refreshToken) {
        // 输入验证
        if (!StringUtils.hasText(refreshToken)) {
            throw new BusinessException("刷新token不能为空");
        }

        try {
            // 验证刷新token
            if (!jwtUtil.validateToken(refreshToken)) {
                throw new BusinessException("刷新token无效或已过期");
            }

            // 从token中获取用户信息
            String username = jwtUtil.getUsernameFromToken(refreshToken);
            Integer userId = jwtUtil.getUserIdFromToken(refreshToken);

            if (username == null || userId == null) {
                throw new BusinessException("刷新token格式错误");
            }

            // 查询用户
            SysUser user = userMapper.selectById(userId);
            if (user == null || user.getStatus() == 0) {
                throw new BusinessException("用户不存在或已被禁用");
            }

            // 生成新的token
            String newAccessToken = jwtUtil.generateToken(username, userId);
            String newRefreshToken = jwtUtil.generateRefreshToken(username, userId);
            Date expires = jwtUtil.getExpirationDateFromToken(newAccessToken);

            // 构建响应
            LoginResponse response = new LoginResponse();
            response.setAccessToken(newAccessToken);
            response.setRefreshToken(newRefreshToken);
            response.setExpires(expires);

            return response;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("刷新token失败", e);
            throw new BusinessException("刷新token失败");
        }
    }

    /**
     * 获取用户角色（带缓存）
     */
    @Cacheable(value = "userRoles", key = "#userId")
    private List<String> getUserRoles(Integer userId) {
        return userMapper.selectRoleCodesByUserId(userId);
    }

    /**
     * 获取用户权限（带缓存）
     */
    @Cacheable(value = "userPermissions", key = "#userId")
    private List<String> getUserPermissions(Integer userId) {
        return userMapper.selectPermissionsByUserId(userId);
    }
}
