import { http } from "@/utils/http";

export interface UserInfo {
  id?: number;
  username: string;
  nickname?: string;
  email?: string;
  phone?: string;
  avatar?: string;
  gender?: string;
  birthDate?: string;
  address?: string;
  roles?: string[];
  permissions?: string[];
  createTime?: string;
  updateTime?: string;
}

export interface LoginLogVO {
  id?: number;
  username: string;
  loginTime: string;
  loginIp: string;
  loginLocation?: string;
  browser?: string;
  os?: string;
  status: string;
  message?: string;
}

export interface LoginRequest {
  username: string;
  password: string;
  userType?: string;
}

export interface LoginResponse {
  token: string;
  refreshToken?: string;
  userInfo: UserInfo;
  permissions?: string[];
  roles?: string[];
}

export interface UserResult {
  success: boolean;
  data: LoginResponse;
  message?: string;
}

export interface RefreshTokenResult {
  success: boolean;
  data: {
    token: string;
    refreshToken?: string;
  };
  message?: string;
}

/** 用户登录 */
export const getLogin = (data: LoginRequest) => {
  return http.request<UserResult>("post", "/api/login", { data });
};

/** 刷新token */
export const refreshTokenApi = (data: { refreshToken: string }) => {
  return http.request<RefreshTokenResult>("post", "/api/refresh-token", { data });
};

/** 用户登出 */
export const logout = () => {
  return http.request("post", "/api/logout");
};

/** 获取当前用户信息 */
export const getMine = () => {
  return http.request<UserInfo>("get", "/api/user/mine");
};

/** 获取用户信息 */
export const getUserInfo = (params: { userId: number; userType: string }) => {
  return http.request<UserInfo>("get", "/api/getInfo", { params });
};

/** 获取用户菜单列表 */
export const getMenuList = (params: { userId: number; userType: string }) => {
  return http.request("get", "/api/getMenuList", { params });
};

/** 根据权限获取菜单 */
export const getMenuByPermissions = (permissions: string[]) => {
  return http.request("post", "/api/getMenuByPermissions", { data: permissions });
};

/** 根据权限字符串获取菜单 */
export const getMenuByPermissionString = (permissionString: string) => {
  return http.request("get", "/api/getMenuByPermissionString", {
    params: { permissionString }
  });
};

/** 获取所有角色 */
export const getAllRoles = () => {
  return http.request("get", "/api/getAllRoles");
};

/** 获取当前用户的登录日志 */
export const getMineLogs = (params?: any) => {
  return http.request<LoginLogVO[]>("get", "/api/user/mine/logs", { params });
};