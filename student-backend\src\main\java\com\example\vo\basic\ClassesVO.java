package com.example.vo.basic;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

import java.time.LocalDateTime;

/**
 * 班级响应VO
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Data
public class ClassesVO {

    /**
     * 班级ID
     */
    private Integer id;

    /**
     * 班级代码
     */
    @NotBlank(message = "班级代码不能为空")
    private String classCode;

    /**
     * 班级名称
     */
    @NotBlank(message = "班级名称不能为空")
    private String className;

    /**
     * 所属专业代码
     */
    @NotBlank(message = "所属专业不能为空")
    private String majorCode;

    /**
     * 入学年份
     */
    @NotNull(message = "入学年份不能为空")
    @Min(value = 2000, message = "入学年份不能小于2000年")
    @Max(value = 2100, message = "入学年份不能大于2100年")
    private Integer gradeYear;

    /**
     * 学生人数
     */
    private Integer studentCount;

    /**
     * 班主任工号
     */
    private String headTeacherCode;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    // 关联字段

    /**
     * 专业名称
     */
    private String majorName;

    /**
     * 学院代码
     */
    private String collegeCode;

    /**
     * 班主任姓名
     */
    private String headTeacherName;

    /**
     * 学院名称
     */
    private String collegeName;
}
