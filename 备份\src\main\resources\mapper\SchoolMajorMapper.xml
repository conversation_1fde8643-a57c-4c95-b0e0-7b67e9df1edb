<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.itmk.web.school_major.mapper.SchoolMajorMapper">
    <select id="getList" resultType="com.itmk.web.school_major.entity.SchoolMajor">
        select sm.*, sc.college_name 
        from school_major as sm
        left join school_college as sc on sm.college_id = sc.college_id
        where 1=1
        <if test="parm.collegeName != null and parm.collegeName !=''">
            and sc.college_name like concat('%',#{parm.collegeName},'%')
        </if>
        <if test="parm.majorName != null and parm.majorName !=''">
            and sm.major_name like concat('%',#{parm.majorName},'%')
        </if>
    </select>
</mapper>