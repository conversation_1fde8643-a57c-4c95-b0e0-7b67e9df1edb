package com.itmk.web.grades.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.itmk.web.grades.entity.Grade;
import com.itmk.web.grades.entity.GradeParm;

import java.util.List;
import java.util.Map;

public interface GradeService extends IService<Grade> {
    IPage<Grade> getList(GradeParm parm);
    IPage<Grade> calculategpa(GradeParm parm);
    
    // 获取学生学业成绩 - 按学期名称
    List<Map<String, Object>> getStudentGpa(String semesterName);
    
    // 获取学生学业成绩 - 按单个学期ID
    List<Map<String, Object>> getStudentGpaById(Integer semesterId);
    
    // 获取学生学业成绩 - 按多个学期ID
    List<Map<String, Object>> getStudentGpaByIds(List<Integer> semesterIds);
    
    // 更新成绩的学期ID
    void updateGradesSemesterId();
}