package com.itmk.web.quality_evaluation.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.itmk.config.jwt.UserContext;
import com.itmk.utils.BatchOperationUtils;
import com.itmk.utils.ResultUtils;
import com.itmk.utils.ResultVo;
import com.itmk.web.quality_evaluation.entity.QualityEvaluation;
import com.itmk.web.quality_evaluation.entity.QualityEvaluationParm;
import com.itmk.web.quality_evaluation.service.QualityEvaluationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 基本素质测评成绩Controller
 */
@RestController
@RequestMapping("/api/quality")
public class QualityEvaluationController {

    private static final Logger logger = LoggerFactory.getLogger(QualityEvaluationController.class);

    @Autowired
    private QualityEvaluationService qualityEvaluationService;

    /**
     * 分页查询基本素质测评成绩
     */
    @GetMapping("/list")
    public ResultVo getList(QualityEvaluationParm parm) {
        // 数据权限控制：如果是学生登录，只能查看自己的素质测评成绩
        if (UserContext.isStudent()) {
            String currentUsername = UserContext.getCurrentUsername();
            if (currentUsername != null) {
                // 设置查询条件为当前学生的学号
                if (parm == null) {
                    parm = new QualityEvaluationParm();
                }
                parm.setStudentId(currentUsername);
                parm.setStudentName(null); // 清除其他查询条件
            } else {
                return ResultUtils.error("无法获取当前用户信息");
            }
        }

        // 处理多选学期的情况
        if (parm.getEvaluationPeriods() != null && !parm.getEvaluationPeriods().isEmpty()) {
            for (String period : parm.getEvaluationPeriods()) {
                if (!qualityEvaluationService.isFirstSemester(period)) {
                String prevSemesterId = qualityEvaluationService.getPreviousSemesterId(period);
                if (prevSemesterId != null) {
                    parm.setPrevSemesterId(prevSemesterId);
                        break;
                    }
                }
            }
        } else if (parm.getEvaluationPeriod() != null && !parm.getEvaluationPeriod().isEmpty()) {
            // 单学期查询的情况
            if (!qualityEvaluationService.isFirstSemester(parm.getEvaluationPeriod())) {
                String prevSemesterId = qualityEvaluationService.getPreviousSemesterId(parm.getEvaluationPeriod());
                if (prevSemesterId != null) {
                    parm.setPrevSemesterId(prevSemesterId);
                }
            }
        }

        IPage<QualityEvaluation> list = qualityEvaluationService.getList(parm);
        return ResultUtils.success("查询成功", list);
    }

    /**
     * 新增基本素质测评成绩
     */
    @PostMapping
    public ResultVo add(@RequestBody QualityEvaluation qualityEvaluation) {
        try {
            // 数据验证
            String validationError = validateQualityEvaluation(qualityEvaluation);
            if (validationError != null) {
                return ResultUtils.error(validationError);
            }

            // 检查是否已存在相同学生和学期的记录
            QualityEvaluation existingRecord = qualityEvaluationService.getByStudentIdAndPeriod(
                qualityEvaluation.getStudentId(), qualityEvaluation.getEvaluationPeriod());
            if (existingRecord != null) {
                return ResultUtils.error("该学生在此学期已有评分记录，请使用修改功能");
            }

            // 设置创建时间和更新时间
            Date now = new Date();
            qualityEvaluation.setCreateTime(now);
            qualityEvaluation.setUpdateTime(now);

            // 确保第二学期数据一致性
            qualityEvaluationService.ensureSecondSemesterConsistency(qualityEvaluation);

            // 计算分数
            qualityEvaluationService.prepareScores(qualityEvaluation);

            boolean save = qualityEvaluationService.save(qualityEvaluation);
            return save ? ResultUtils.success("新增成功") : ResultUtils.error("新增失败");
        } catch (Exception e) {
            return ResultUtils.error("新增失败: " + e.getMessage());
        }
    }

    /**
     * 修改基本素质测评成绩
     */
    @PutMapping
    public ResultVo edit(@RequestBody QualityEvaluation qualityEvaluation) {
        try {
            // 数据验证
            String validationError = validateQualityEvaluation(qualityEvaluation);
            if (validationError != null) {
                return ResultUtils.error(validationError);
            }

            // 验证记录是否存在
            if (qualityEvaluation.getEvaluationId() == null) {
                return ResultUtils.error("记录ID不能为空");
            }

            QualityEvaluation existingRecord = qualityEvaluationService.getById(qualityEvaluation.getEvaluationId());
            if (existingRecord == null) {
                return ResultUtils.error("要修改的记录不存在");
            }

            // 设置更新时间
            qualityEvaluation.setUpdateTime(new Date());

            // 确保第二学期数据一致性
            qualityEvaluationService.ensureSecondSemesterConsistency(qualityEvaluation);

            // 计算分数
            qualityEvaluationService.prepareScores(qualityEvaluation);

            boolean update = qualityEvaluationService.updateById(qualityEvaluation);
            return update ? ResultUtils.success("修改成功") : ResultUtils.error("修改失败");
        } catch (Exception e) {
            return ResultUtils.error("修改失败: " + e.getMessage());
        }
    }

    /**
     * 删除基本素质测评成绩
     */
    @DeleteMapping("/{id}")
    public ResultVo delete(@PathVariable("id") Integer id) {
        boolean remove = qualityEvaluationService.removeById(id);
        return remove ? ResultUtils.success("删除成功") : ResultUtils.error("删除失败");
    }

    /**
     * 获取基本素质测评成绩详情
     */
    @GetMapping("/{id}")
    public ResultVo getById(@PathVariable("id") Integer id) {
        QualityEvaluation qualityEvaluation = qualityEvaluationService.getById(id);
        return ResultUtils.success("查询成功", qualityEvaluation);
    }

    /**
     * 导出基本素质测评成绩
     */
    @GetMapping("/export")
    public ResponseEntity<byte[]> exportExcel(QualityEvaluationParm parm) {
        try {
            // 查询数据
            parm.setCurrentPage(1L);
            parm.setPageSize(10000L); // 最多导出10000条数据
            
            // 如果没有指定学期，则导出所有学期数据
            if ((parm.getEvaluationPeriods() == null || parm.getEvaluationPeriods().isEmpty()) 
                && (parm.getEvaluationPeriod() == null || parm.getEvaluationPeriod().isEmpty())) {
                // 这是导出全部数据的情况，不需要特殊处理
            } else if (parm.getEvaluationPeriod() != null && !parm.getEvaluationPeriod().isEmpty()) {
                // 如果指定了单个学期，确保evaluationPeriods包含这个学期
                if (parm.getEvaluationPeriods() == null) {
                    parm.setEvaluationPeriods(new ArrayList<>());
                }
                if (!parm.getEvaluationPeriods().contains(parm.getEvaluationPeriod())) {
                    parm.getEvaluationPeriods().add(parm.getEvaluationPeriod());
                }
            }
            
            IPage<QualityEvaluation> page = qualityEvaluationService.getList(parm);
            List<QualityEvaluation> list = page.getRecords();
            
            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("基本素质测评成绩");
            
            // 创建标题行样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headerTitles = {"序号", "姓名", "学号", "宿舍号", "评分学期", "加分", "扣分", "加分说明", "扣分说明", "基础分", "总计"};
            for (int i = 0; i < headerTitles.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headerTitles[i]);
                cell.setCellStyle(headerStyle);
            }
            
            // 填充数据
            int rowNum = 1;
            for (QualityEvaluation item : list) {
                Row row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(rowNum - 1); // 序号
                row.createCell(1).setCellValue(item.getStudentName());
                row.createCell(2).setCellValue(item.getStudentId());
                row.createCell(3).setCellValue(item.getDormitoryNo());
                row.createCell(4).setCellValue(item.getEvaluationPeriod());
                row.createCell(5).setCellValue(item.getAddScore() != null ? item.getAddScore() : 0);
                row.createCell(6).setCellValue(item.getReduceScore() != null ? item.getReduceScore() : 0);
                row.createCell(7).setCellValue(item.getAddScoreRemark() != null ? item.getAddScoreRemark() : "");
                row.createCell(8).setCellValue(item.getReduceScoreRemark() != null ? item.getReduceScoreRemark() : "");
                row.createCell(9).setCellValue(item.getPeriodScore() != null ? item.getPeriodScore() : 0);
                row.createCell(10).setCellValue(item.getTotalScore() != null ? item.getTotalScore() : 0);
            }
            
            // 设置列宽
            sheet.setColumnWidth(0, 10 * 256);
            sheet.setColumnWidth(1, 15 * 256);
            sheet.setColumnWidth(2, 15 * 256);
            sheet.setColumnWidth(3, 15 * 256);
            sheet.setColumnWidth(4, 15 * 256);
            sheet.setColumnWidth(5, 10 * 256);
            sheet.setColumnWidth(6, 10 * 256);
            sheet.setColumnWidth(7, 40 * 256);
            sheet.setColumnWidth(8, 40 * 256);
            sheet.setColumnWidth(9, 15 * 256);
            sheet.setColumnWidth(10, 15 * 256);
            
            // 写入到ByteArrayOutputStream
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            workbook.close();
            
            // 设置HTTP响应头
            String fileName = "基本素质测评成绩_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", encodedFileName);
            
            // 返回Excel文件
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(outputStream.toByteArray());
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException("导出失败: " + e.getMessage());
        }
    }

    /**
     * 下载导入模板
     */
    @GetMapping("/template")
    public ResponseEntity<byte[]> downloadTemplate() {
        try {
            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("数据导入模板");
            
            // 设置列宽
            sheet.setColumnWidth(0, 20 * 256);  // 学号
            sheet.setColumnWidth(1, 20 * 256);  // 评分学期
            sheet.setColumnWidth(2, 40 * 256);  // 加分说明
            sheet.setColumnWidth(3, 40 * 256);  // 扣分说明
            
            // 创建样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            headerStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerFont.setFontHeightInPoints((short) 12);
            headerStyle.setFont(headerFont);
            
            CellStyle normalStyle = workbook.createCellStyle();
            normalStyle.setAlignment(HorizontalAlignment.LEFT);
            normalStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            normalStyle.setBorderTop(BorderStyle.THIN);
            normalStyle.setBorderBottom(BorderStyle.THIN);
            normalStyle.setBorderLeft(BorderStyle.THIN);
            normalStyle.setBorderRight(BorderStyle.THIN);
            normalStyle.setWrapText(true);
            
            // 添加说明行
            Row titleRow = sheet.createRow(0);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue("基本素质测评成绩导入模板");
            titleCell.setCellStyle(headerStyle);
            
            CellStyle instructionStyle = workbook.createCellStyle();
            instructionStyle.setWrapText(true);
            instructionStyle.setAlignment(HorizontalAlignment.LEFT);
            instructionStyle.setVerticalAlignment(VerticalAlignment.TOP);
            
            // 合并标题单元格
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 3));
            
            // 添加使用说明
            Row instructionRow1 = sheet.createRow(1);
            Cell instructionCell1 = instructionRow1.createCell(0);
            instructionCell1.setCellValue("使用说明：");
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 3));
            
            Row instructionRow2 = sheet.createRow(2);
            Cell instructionCell2 = instructionRow2.createCell(0);
            instructionCell2.setCellValue("1. 学号和评分学期为必填项，其他为选填项。");
            instructionCell2.setCellStyle(instructionStyle);
            sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 3));
            
            Row instructionRow3 = sheet.createRow(3);
            Cell instructionCell3 = instructionRow3.createCell(0);
            instructionCell3.setCellValue("2. 评分学期请填写如 '2023-2024-1' 或 '2023-2024-第一学期' 格式。");
            instructionCell3.setCellStyle(instructionStyle);
            sheet.addMergedRegion(new CellRangeAddress(3, 3, 0, 3));
            
            // 创建表头行（第5行）
            Row headerRow = sheet.createRow(4);
            
            // 创建表头单元格
            String[] headers = {"学号(必填)", "评分学期(必填)", "加分说明", "扣分说明"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }
            
            // 添加示例数据行
            Row exampleRow = sheet.createRow(5);
            Cell[] exampleCells = new Cell[4];
            for (int i = 0; i < 4; i++) {
                exampleCells[i] = exampleRow.createCell(i);
                exampleCells[i].setCellStyle(normalStyle);
            }
            exampleCells[0].setCellValue("202305480137");
            exampleCells[1].setCellValue("2023-2024-1");
            exampleCells[2].setCellValue("1.文明宿舍（自律委员会）（院级）*4  +8\n2.心理协会优秀志愿者（院级）  +3");
            exampleCells[3].setCellValue("1.迟到1次 -1\n2.旷课1次 -2\n3.迟到2次 -2*2");
            
            // 设置行高
            headerRow.setHeight((short) 600);
            exampleRow.setHeight((short) 1200);
            
            // 将工作簿转换为字节数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            workbook.write(baos);
            byte[] bytes = baos.toByteArray();
            baos.close();
            workbook.close();
            
            // 设置HTTP头
            HttpHeaders headers2 = new HttpHeaders();
            headers2.add("Content-Disposition", "attachment; filename=quality_evaluation_template.xlsx");
            headers2.add("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            headers2.add("Content-Length", String.valueOf(bytes.length));
            
            return new ResponseEntity<>(bytes, headers2, HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 导入基本素质测评成绩
     */
    @PostMapping("/import")
    public ResultVo importExcel(@RequestParam("file") MultipartFile file,
                               @RequestParam(value = "autoCalculate", required = false, defaultValue = "true") Boolean autoCalculate,
                               @RequestParam(value = "semesterMap", required = false) String semesterMapJson) {

        // 参数验证
        if (file.isEmpty()) {
            return ResultUtils.error("上传文件不能为空");
        }

        String fileName = file.getOriginalFilename();
        if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
            return ResultUtils.error("请上传Excel文件");
        }

        // 文件大小检查（限制10MB）
        if (file.getSize() > 10 * 1024 * 1024) {
            return ResultUtils.error("文件大小不能超过10MB");
        }

        long startTime = System.currentTimeMillis();
        logger.info("开始导入质量评估数据，文件名: {}, 大小: {} bytes", fileName, file.getSize());

        try {
            // 解析学期映射信息
            Map<String, String> semesterMap = parseSemesterMap(semesterMapJson);

            // 解析Excel文件
            Workbook workbook = createWorkbook(file, fileName);
            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null) {
                return ResultUtils.error("Excel文件中没有工作表");
            }
            
            // 固定表头位置和列顺序
            int headerRowIndex = 4; // 表头固定在第5行
            int studentIdIndex = 0; // 第1列为学号
            int evaluationPeriodIndex = 1; // 第2列为评分学期
            int addScoreRemarkIndex = 2; // 第3列为加分说明
            int reduceScoreRemarkIndex = 3; // 第4列为扣分说明
            
            // 验证表头
            Row headerRow = sheet.getRow(headerRowIndex);
            if (headerRow == null) {
                return ResultUtils.error("Excel文件格式错误，未找到第5行表头。请使用系统提供的导入模板");
            }
            
            // 检查表头列是否符合预期
            boolean isValidHeader = true;
            List<String> expectedHeaders = new ArrayList<>();
            List<String> actualHeaders = new ArrayList<>();
            
            // 预期的表头列名
            String[] expectedHeaderNames = {"学号(必填)", "评分学期(必填)", "加分说明", "扣分说明"};
            
            // 获取实际表头
            for (int i = 0; i < 4; i++) {
                Cell cell = headerRow.getCell(i);
                String headerValue = cell != null ? getCellValueAsString(cell).trim() : "";
                actualHeaders.add(headerValue);
                
                // 检查是否接近预期的表头名称
                if (i < expectedHeaderNames.length) {
                    expectedHeaders.add(expectedHeaderNames[i]);
                    
                    // 判断是否与预期相符
                    if (!headerValue.contains("学号") && i == 0 ||
                        !headerValue.contains("学期") && i == 1) {
                        isValidHeader = false;
                }
                }
            }
            
            if (!isValidHeader) {
                return ResultUtils.error("Excel文件格式错误，表头列不符合预期。预期表头: " + 
                                       String.join(", ", expectedHeaders) + "，实际表头: " + 
                                       String.join(", ", actualHeaders) + 
                                       "。请使用系统提供的导入模板");
            }
            
            // 读取数据行
            List<QualityEvaluation> importList = new ArrayList<>();
            Map<String, String> errorMap = new HashMap<>();
            
                // 从表头的下一行开始读取数据
                for (int i = headerRowIndex + 1; i <= sheet.getLastRowNum(); i++) {
                    Row row = sheet.getRow(i);
                    if (row == null) {
                        continue;
                    }
                    
                    // 检查该行是否全部为空
                boolean isEmpty = true;
                    for (int j = 0; j < 4; j++) {
                        Cell cell = row.getCell(j);
                        if (cell != null && !getCellValueAsString(cell).trim().isEmpty()) {
                            isEmpty = false;
                            break;
                        }
                    }
                    
                    if (isEmpty) {
                        continue;
                    }
                    
                    // 获取学号
                    Cell studentIdCell = row.getCell(studentIdIndex);
                    if (studentIdCell == null) {
                        continue;
                    }
                    
                    String studentId = getCellValueAsString(studentIdCell);
                    if (studentId == null || studentId.trim().isEmpty()) {
                        continue;
                    }
                    
                    // 清理学号，确保只包含数字
                    studentId = studentId.trim().replaceAll("[^0-9]", "");
                    if (studentId.isEmpty()) {
                        continue;
                    }
                    
                    // 获取评分学期
                    Cell evaluationPeriodCell = row.getCell(evaluationPeriodIndex);
                    if (evaluationPeriodCell == null) {
                        continue;
                    }
                    
                    String evaluationPeriodName = getCellValueAsString(evaluationPeriodCell);
                    if (evaluationPeriodName == null || evaluationPeriodName.trim().isEmpty()) {
                        errorMap.put("第" + (i + 1) + "行", "评分学期不能为空");
                        continue;
                    }
                    
                    // 将学期名称转换为学期ID
                String evaluationPeriod = qualityEvaluationService.resolveSemesterId(evaluationPeriodName, semesterMap);
                if (evaluationPeriod == null) {
                            errorMap.put("第" + (i + 1) + "行", "未找到学期名称 '" + evaluationPeriodName + "' 对应的学期ID");
                            continue;
                    }
                    
                // 获取加分说明和扣分说明
                    String addScoreRemark = "";
                String reduceScoreRemark = "";
                
                    if (addScoreRemarkIndex != -1) {
                        Cell addScoreRemarkCell = row.getCell(addScoreRemarkIndex);
                        if (addScoreRemarkCell != null) {
                            addScoreRemark = getCellValueAsString(addScoreRemarkCell);
                        }
                    }
                    
                    if (reduceScoreRemarkIndex != -1) {
                        Cell reduceScoreRemarkCell = row.getCell(reduceScoreRemarkIndex);
                        if (reduceScoreRemarkCell != null) {
                            reduceScoreRemark = getCellValueAsString(reduceScoreRemarkCell);
                        }
                    }
                    
                    // 创建记录对象
                    QualityEvaluation record = new QualityEvaluation();
                    record.setStudentId(studentId);
                    record.setEvaluationPeriod(evaluationPeriod);
                    record.setAddScoreRemark(addScoreRemark);
                    record.setReduceScoreRemark(reduceScoreRemark);
                    
                    // 检查是否已存在相同学生ID和学期的记录
                    QualityEvaluation existingRecord = qualityEvaluationService.getByStudentIdAndPeriod(studentId, evaluationPeriod);
                    if (existingRecord != null) {
                        record.setEvaluationId(existingRecord.getEvaluationId());
                        record.setCreateTime(existingRecord.getCreateTime());
                    }
                    
                // 设置或计算分数
                    if (autoCalculate) {
                    qualityEvaluationService.prepareScores(record);
                                } else {
                    // 不自动计算时的默认设置
                        record.setAddScore(0.0);
                        record.setReduceScore(0.0);
                    record.setPeriodScore(qualityEvaluationService.getBaseScore(record));
                        record.setTotalScore(record.getPeriodScore());
                    }
                    
                    // 设置创建时间和更新时间
                    Date now = new Date();
                if (record.getCreateTime() == null) {
                    record.setCreateTime(now);
                }
                    record.setUpdateTime(now);
                    
                    importList.add(record);
            }
            
            // 检查是否有错误
            if (!errorMap.isEmpty()) {
                StringBuilder errorMsg = new StringBuilder("导入过程中发现以下错误：\n");
                for (Map.Entry<String, String> entry : errorMap.entrySet()) {
                    errorMsg.append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
                }
                return ResultUtils.error(errorMsg.toString());
            }
            
            // 保存导入的记录
            if (importList.isEmpty()) {
                return ResultUtils.error("没有有效的数据可导入，请检查Excel文件格式和内容");
            }
            
                // 将记录分为需要更新的和需要新增的
                List<QualityEvaluation> toUpdate = new ArrayList<>();
                List<QualityEvaluation> toInsert = new ArrayList<>();
                
                for (QualityEvaluation record : importList) {
                    if (record.getEvaluationId() != null) {
                        toUpdate.add(record);
                    } else {
                        toInsert.add(record);
                    }
                }
                
                int updateCount = 0;
                int insertCount = 0;
                
                // 使用批量操作工具进行数据处理
                if (!toUpdate.isEmpty()) {
                    boolean updateSuccess = BatchOperationUtils.batchUpdate(qualityEvaluationService, toUpdate, 500);
                    if (updateSuccess) {
                        updateCount = toUpdate.size();
                        logger.info("批量更新成功: {} 条记录", updateCount);
                    }
                }

                if (!toInsert.isEmpty()) {
                    boolean insertSuccess = BatchOperationUtils.batchInsert(qualityEvaluationService, toInsert, 500);
                    if (insertSuccess) {
                        insertCount = toInsert.size();
                        logger.info("批量插入成功: {} 条记录", insertCount);
                    }
                }

            long endTime = System.currentTimeMillis();
            String resultMessage = String.format("导入成功: 新增 %d 条记录，更新 %d 条记录，耗时 %d ms",
                                                insertCount, updateCount, (endTime - startTime));
            logger.info(resultMessage);

            return ResultUtils.success(resultMessage);

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            logger.error("导入失败，耗时 {} ms，错误信息: {}", (endTime - startTime), e.getMessage(), e);
            return ResultUtils.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 解析学期映射信息
     */
    private Map<String, String> parseSemesterMap(String semesterMapJson) {
        Map<String, String> semesterMap = new HashMap<>();
        if (semesterMapJson != null && !semesterMapJson.isEmpty()) {
            try {
                semesterMap = new ObjectMapper().readValue(semesterMapJson, new TypeReference<Map<String, String>>() {});
            } catch (Exception e) {
                logger.warn("解析学期映射信息失败: {}", e.getMessage());
            }
        }
        return semesterMap;
    }

    /**
     * 创建工作簿对象
     */
    private Workbook createWorkbook(MultipartFile file, String fileName) throws IOException {
        if (fileName.endsWith(".xlsx")) {
            return new XSSFWorkbook(file.getInputStream());
        } else {
            return new HSSFWorkbook(file.getInputStream());
        }
    }

    /**
     * 获取单元格的字符串值
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }
        
        try {
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return new SimpleDateFormat("yyyy-MM-dd").format(cell.getDateCellValue());
                } else {
                    double value = cell.getNumericCellValue();
                        // 对于整数值，去掉小数点后的部分
                    if (value == Math.floor(value)) {
                        return String.valueOf((long)value);
                    } else {
                        return String.valueOf(value);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    try {
                        return cell.getStringCellValue();
                    } catch (Exception e) {
                        // 如果无法获取字符串值，尝试获取数值
                        try {
                            double value = cell.getNumericCellValue();
                            if (value == Math.floor(value)) {
                                return String.valueOf((long)value);
                            } else {
                                return String.valueOf(value);
                            }
                        } catch (Exception ex) {
                            // 如果也无法获取数值，返回公式本身
                            return cell.getCellFormula();
                        }
                    }
                case BLANK:
                    return "";
                case ERROR:
                    return "[错误值]";
            default:
                return "";
            }
        } catch (Exception e) {
            // 尝试将单元格转换为字符串
                return cell.toString();
        }
    }

    /**
     * 自动计算所有分数
     */
    @PostMapping("/calculate-scores")
    public ResultVo calculateScores() {
        try {
            int affectedPrevCount = qualityEvaluationService.updatePrevPeriodScore();
            int affectedPeriodCount = qualityEvaluationService.updatePeriodScoreFromPrev();

            return ResultUtils.success("自动计算完成：更新了" + affectedPrevCount + "条记录的上学期分数和" + affectedPeriodCount + "条记录的基础分");
        } catch (Exception e) {
            return ResultUtils.error("自动计算失败: " + e.getMessage());
        }
    }

    /**
     * 修复第二学期基础分数据
     * 确保第二学期的periodScore等于prevPeriodScore
     */
    @PostMapping("/fix-second-semester-base-scores")
    public ResultVo fixSecondSemesterBaseScores() {
        long startTime = System.currentTimeMillis();
        logger.info("开始执行第二学期基础分修复操作");

        try {
            // 先更新prevPeriodScore字段
            long step1Start = System.currentTimeMillis();
            int affectedPrevCount = qualityEvaluationService.updatePrevPeriodScore();
            long step1End = System.currentTimeMillis();
            logger.info("步骤1完成：更新了{}条记录的上学期分数，耗时{}ms", affectedPrevCount, (step1End - step1Start));

            // 再更新periodScore字段
            long step2Start = System.currentTimeMillis();
            int affectedPeriodCount = qualityEvaluationService.updatePeriodScoreFromPrev();
            long step2End = System.currentTimeMillis();
            logger.info("步骤2完成：更新了{}条记录的基础分，耗时{}ms", affectedPeriodCount, (step2End - step2Start));

            // 重新计算所有第二学期记录的总分
            long step3Start = System.currentTimeMillis();
            int recalculatedCount = qualityEvaluationService.recalculateSecondSemesterTotalScores();
            long step3End = System.currentTimeMillis();
            logger.info("步骤3完成：重新计算了{}条记录的总分，耗时{}ms", recalculatedCount, (step3End - step3Start));

            long totalTime = System.currentTimeMillis() - startTime;
            String resultMsg = String.format(
                "第二学期基础分修复完成：更新了%d条记录的上学期分数，%d条记录的基础分，重新计算了%d条记录的总分",
                affectedPrevCount, affectedPeriodCount, recalculatedCount);

            logger.info("第二学期基础分修复操作完成，总耗时{}ms", totalTime);
            return ResultUtils.success(resultMsg);
        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            logger.error("修复第二学期基础分失败，耗时{}ms，错误信息: {}", totalTime, e.getMessage(), e);
            return ResultUtils.error("修复第二学期基础分失败: " + e.getMessage());
        }
    }


    
    /**
     * 计算加分
     */
    @PostMapping("/calculate-add-score")
    public ResultVo calculateAddScore(@RequestBody Map<String, String> params) {
        try {
            String remark = params.get("remark");
            if (remark == null || remark.trim().isEmpty()) {
                return ResultUtils.success("计算完成", 0);
            }
            
            double score = qualityEvaluationService.calculateAddScore(remark);
            return ResultUtils.success("计算完成", score);
        } catch (Exception e) {
            return ResultUtils.error("计算加分失败: " + e.getMessage());
                        }
                    }
                    
    /**
     * 计算扣分
     */
    @PostMapping("/calculate-reduce-score")
    public ResultVo calculateReduceScore(@RequestBody Map<String, String> params) {
        try {
            String remark = params.get("remark");
            if (remark == null || remark.trim().isEmpty()) {
                return ResultUtils.success("计算完成", 0);
            }
            
            double score = qualityEvaluationService.calculateReduceScore(remark);
            return ResultUtils.success("计算完成", score);
                } catch (Exception e) {
            return ResultUtils.error("计算扣分失败: " + e.getMessage());
                }
            }
            
    /**
     * 计算总分
     */
    @PostMapping("/calculate-total-score")
    public ResultVo calculateTotalScore(@RequestBody QualityEvaluation qualityEvaluation) {
        try {
            if (qualityEvaluation == null) {
                return ResultUtils.error("参数不能为空");
            }
            
            qualityEvaluationService.calculateTotalScore(qualityEvaluation);
            return ResultUtils.success("计算完成", qualityEvaluation.getTotalScore());
        } catch (Exception e) {
            return ResultUtils.error("计算总分失败: " + e.getMessage());
        }
    }

    /**
     * 验证基本素质测评成绩数据
     * @param qualityEvaluation 基本素质测评成绩
     * @return 验证错误信息，null表示验证通过
     */
    private String validateQualityEvaluation(QualityEvaluation qualityEvaluation) {
        if (qualityEvaluation == null) {
            return "评分数据不能为空";
        }

        // 验证学生ID
        if (qualityEvaluation.getStudentId() == null || qualityEvaluation.getStudentId().trim().isEmpty()) {
            return "学生ID不能为空";
        }

        // 验证评分学期
        if (qualityEvaluation.getEvaluationPeriod() == null || qualityEvaluation.getEvaluationPeriod().trim().isEmpty()) {
            return "评分学期不能为空";
        }

        // 验证分数范围
        if (qualityEvaluation.getAddScore() != null && qualityEvaluation.getAddScore() < 0) {
            return "加分不能为负数";
        }

        if (qualityEvaluation.getReduceScore() != null && qualityEvaluation.getReduceScore() < 0) {
            return "扣分不能为负数";
        }

        if (qualityEvaluation.getPeriodScore() != null && qualityEvaluation.getPeriodScore() < 0) {
            return "基础分不能为负数";
        }

        // 验证加分和扣分的合理性
        if (qualityEvaluation.getAddScore() != null && qualityEvaluation.getAddScore() > 100) {
            return "加分不能超过100分";
        }

        if (qualityEvaluation.getReduceScore() != null && qualityEvaluation.getReduceScore() > 100) {
            return "扣分不能超过100分";
        }

        // 验证说明字段长度
        if (qualityEvaluation.getAddScoreRemark() != null && qualityEvaluation.getAddScoreRemark().length() > 500) {
            return "加分说明不能超过500个字符";
        }

        if (qualityEvaluation.getReduceScoreRemark() != null && qualityEvaluation.getReduceScoreRemark().length() > 500) {
            return "扣分说明不能超过500个字符";
        }

        return null; // 验证通过
    }
}