package com.example.vo.score;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 成绩录入VO
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@Schema(description = "成绩录入VO")
public class GradeInputVO {

    @Schema(description = "成绩ID")
    private Integer id;

    @Schema(description = "学号")
    private String studentId;

    @Schema(description = "学生姓名")
    private String studentName;

    @Schema(description = "班级代码")
    private String classCode;

    @Schema(description = "班级名称")
    private String className;

    @Schema(description = "课程代码")
    private String courseCode;

    @Schema(description = "课程名称")
    private String courseName;

    @Schema(description = "课程类型")
    private String courseType;

    @Schema(description = "学分")
    private BigDecimal credits;

    @Schema(description = "学期ID")
    private Integer semesterId;

    @Schema(description = "学期名称")
    private String semesterName;

    @Schema(description = "期末成绩")
    private BigDecimal finalScore;

    @Schema(description = "绩点")
    private BigDecimal gradePoint;

    @Schema(description = "是否重修")
    private Boolean isRetake;

    @Schema(description = "备注")
    private String remarks;

    @Schema(description = "是否已录入成绩")
    private Boolean hasGrade;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
}
