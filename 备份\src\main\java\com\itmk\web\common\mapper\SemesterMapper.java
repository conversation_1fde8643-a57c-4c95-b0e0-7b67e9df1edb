package com.itmk.web.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.itmk.web.common.entity.Semester;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface SemesterMapper extends BaseMapper<Semester> {
    
    @Select("SELECT semester_id FROM semesters WHERE semester_name = #{semesterName} LIMIT 1")
    Integer getIdByName(@Param("semesterName") String semesterName);
}