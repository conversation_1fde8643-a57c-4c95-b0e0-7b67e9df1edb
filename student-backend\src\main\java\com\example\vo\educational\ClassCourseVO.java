package com.example.vo.educational;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 班级课程分配VO
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@Schema(description = "班级课程分配信息")
public class ClassCourseVO {

    @Schema(description = "分配ID")
    private Integer id;

    @Schema(description = "班级代码", required = true)
    @NotBlank(message = "班级代码不能为空")
    @Size(max = 20, message = "班级代码长度不能超过20个字符")
    private String classCode;

    @Schema(description = "班级名称")
    private String className;

    @Schema(description = "专业代码")
    private String majorCode;

    @Schema(description = "专业名称")
    private String majorName;

    @Schema(description = "学院代码")
    private String collegeCode;

    @Schema(description = "学院名称")
    private String collegeName;

    @Schema(description = "课程代码", required = true)
    @NotBlank(message = "课程代码不能为空")
    @Size(max = 20, message = "课程代码长度不能超过20个字符")
    private String courseCode;

    @Schema(description = "课程名称")
    private String courseName;

    @Schema(description = "学分")
    private Double credits;

    @Schema(description = "课程类型")
    private String courseType;

    @Schema(description = "学期ID", required = true)
    @NotNull(message = "学期ID不能为空")
    private Integer semesterId;

    @Schema(description = "学期名称")
    private String semesterName;

    @Schema(description = "学年")
    private String academicYear;



    @Schema(description = "是否启用")
    private Boolean isActive;

    @Schema(description = "排序")
    private Integer sortOrder;

    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    @Schema(description = "创建人")
    private String createdBy;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新人")
    private String updatedBy;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
}
