package com.itmk.web.sys_stuinfo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.itmk.web.sys_stuinfo.entity.StuParm;
import com.itmk.web.sys_stuinfo.entity.SysStudent;

public interface SysStudentService extends IService<SysStudent> {
    IPage<SysStudent> list(StuParm parm);
    // 根据id查询
    SysStudent findById(Integer id);
    // 根据id删除
    boolean deleteById(Integer id);
    /**
     * 获取最大ID
     *
     * @return 最大ID
     */
    Integer getMaxId();
    /**
     * 检查学号和姓名是否重复
     *
     * @param studentId 学号
     * @param name      姓名
     * @param excludeId 排除的ID（用于编辑操作）
     * @return true：重复；false：不重复
     */
    boolean isDuplicate(String studentId, String name, Integer excludeId);

    /**
     * 根据学号查询学生
     *
     * @param studentId 学号
     * @return 学生信息，如不存在返回null
     */
    SysStudent getByStudentId(String studentId);
}