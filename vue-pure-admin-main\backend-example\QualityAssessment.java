package com.itmk.web.quality_assessment.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 基本素质测评实体类
 */
@Data
@TableName("quality_assessment")
public class QualityAssessment implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 评价ID
     */
    @TableId(value = "evaluation_id", type = IdType.AUTO)
    private Long evaluationId;

    /**
     * 学生学号
     */
    @TableField("student_id")
    private String studentId;

    /**
     * 学生姓名
     */
    @TableField("student_name")
    private String studentName;

    /**
     * 宿舍号
     */
    @TableField("dormitory_no")
    private String dormitoryNo;

    /**
     * 加分
     */
    @TableField("add_score")
    private Double addScore;

    /**
     * 扣分
     */
    @TableField("reduce_score")
    private Double reduceScore;

    /**
     * 加分说明
     */
    @TableField("add_score_remark")
    private String addScoreRemark;

    /**
     * 扣分说明
     */
    @TableField("reduce_score_remark")
    private String reduceScoreRemark;

    /**
     * 评分学期
     */
    @TableField("evaluation_period")
    private String evaluationPeriod;

    /**
     * 评分学期名称
     */
    @TableField("evaluation_period_name")
    private String evaluationPeriodName;

    /**
     * 学期名称
     */
    @TableField("semester_name")
    private String semesterName;

    /**
     * 基础分
     */
    @TableField("period_score")
    private Double periodScore;

    /**
     * 总得分
     */
    @TableField("total_score")
    private Double totalScore;

    /**
     * 班级ID
     */
    @TableField("class_id")
    private String classId;

    /**
     * 班级名称
     */
    @TableField("class_name")
    private String className;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
