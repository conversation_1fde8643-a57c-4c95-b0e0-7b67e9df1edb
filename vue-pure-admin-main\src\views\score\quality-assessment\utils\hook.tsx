import { ref, reactive, onMounted, h } from "vue";
import { type PaginationProps } from "@pureadmin/table";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { addDialog } from "@/components/ReDialog";
import { deviceDetection } from "@pureadmin/utils";

// 图标导入
import Delete from "@iconify-icons/ep/delete";
import EditPen from "@iconify-icons/ep/edit-pen";
import Search from "@iconify-icons/ep/search";
import Refresh from "@iconify-icons/ep/refresh";
import AddFill from "@iconify-icons/ri/add-circle-line";
import Upload from "@iconify-icons/ep/upload";
import Download from "@iconify-icons/ep/download";

// API导入
import {
  getQualityAssessmentList,
  addQualityAssessment,
  updateQualityAssessment,
  deleteQualityAssessment,
  exportQualityAssessment,
  downloadQualityAssessmentTemplate,
  importQualityAssessment
} from "@/api/score/quality-assessment";
import { getAllMajors } from "@/api/basic/major";
import { getClassesByMajor } from "@/api/basic/class";
import { getAllSemesters } from "@/api/basic/semester";

// 导入表单组件
import editForm from "../form/index.vue";
import importForm from "../form/import.vue";

export function useQualityAssessmentHook() {
  // 数据状态
  const loading = ref(true);
  const dataList = ref([]);
  const selectedNum = ref(0);

  // 分页配置
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  });

  // 搜索表单
  const searchForm = reactive({
    studentName: "",
    studentId: "",
    dormitoryNo: ""
  });

  // 选择状态
  const currentSelection = reactive({
    major: null as any,
    class: null as any,
    semester: null as any
  });

  // 数据源
  const majors = ref([]);
  const classes = ref([]);
  const courses = ref([]); // 这里存储学期数据

  // 当前视图
  const currentView = ref('assessment');

  // 表单引用
  const formRef = ref();
  const importFormRef = ref();

  // 表格列配置
  const columns: TableColumnList = [
    {
      label: "勾选列",
      type: "selection",
      width: 55,
      align: "left",
      hide: ({ checkList }: { checkList: string[] }) => !checkList.includes("勾选列")
    },
    {
      label: "序号",
      type: "index",
      width: 70,
      hide: ({ checkList }: { checkList: string[] }) => !checkList.includes("序号")
    },
    {
      label: "学生姓名",
      prop: "studentName",
      minWidth: 100,
      hide: ({ checkList }: { checkList: string[] }) => !checkList.includes("学生姓名")
    },
    {
      label: "学号",
      prop: "studentId",
      minWidth: 120,
      hide: ({ checkList }: { checkList: string[] }) => !checkList.includes("学号")
    },
    {
      label: "宿舍号",
      prop: "dormitoryNo",
      minWidth: 100,
      hide: ({ checkList }: { checkList: string[] }) => !checkList.includes("宿舍号")
    },
    {
      label: "评分学期",
      prop: "semesterName",
      minWidth: 120,
      hide: ({ checkList }: { checkList: string[] }) => !checkList.includes("评分学期")
    },
    {
      label: "基础分",
      prop: "periodScore",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <span>{row.periodScore || 0}</span>
      ),
      hide: ({ checkList }: { checkList: string[] }) => !checkList.includes("基础分")
    },
    {
      label: "加分",
      prop: "addScore",
      minWidth: 80,
      cellRenderer: ({ row }) => (
        <span style={{ color: row.addScore > 0 ? '#67c23a' : '#606266' }}>
          {row.addScore || 0}
        </span>
      ),
      hide: ({ checkList }: { checkList: string[] }) => !checkList.includes("加分")
    },
    {
      label: "扣分",
      prop: "reduceScore",
      minWidth: 80,
      cellRenderer: ({ row }) => (
        <span style={{ color: row.reduceScore > 0 ? '#f56c6c' : '#606266' }}>
          {row.reduceScore || 0}
        </span>
      ),
      hide: ({ checkList }: { checkList: string[] }) => !checkList.includes("扣分")
    },
    {
      label: "总得分",
      prop: "totalScore",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <span style={{ fontWeight: 'bold', color: '#409eff' }}>
          {row.totalScore || 0}
        </span>
      ),
      hide: ({ checkList }: { checkList: string[] }) => !checkList.includes("总得分")
    },
    {
      label: "加分说明",
      prop: "addScoreRemark",
      minWidth: 200,
      showOverflowTooltip: true,
      cellRenderer: ({ row }) => (
        <span>{row.addScoreRemark || '无'}</span>
      ),
      hide: ({ checkList }: { checkList: string[] }) => !checkList.includes("加分说明")
    },
    {
      label: "扣分说明",
      prop: "reduceScoreRemark",
      minWidth: 200,
      showOverflowTooltip: true,
      cellRenderer: ({ row }) => (
        <span>{row.reduceScoreRemark || '无'}</span>
      ),
      hide: ({ checkList }: { checkList: string[] }) => !checkList.includes("扣分说明")
    },
    {
      label: "操作",
      fixed: "right",
      width: 180,
      slot: "operation",
      hide: ({ checkList }: { checkList: string[] }) => !checkList.includes("操作")
    }
  ];

  // 加载专业数据
  const loadMajors = async () => {
    try {
      const response = await getAllMajors();
      majors.value = response.data || [];
    } catch (error) {
      console.error("加载专业数据失败:", error);
      ElMessage.error("加载专业数据失败");
    }
  };

  // 加载班级数据
  const loadClasses = async (majorId: string) => {
    try {
      const response = await getClassesByMajor(majorId);
      classes.value = response.data || [];
    } catch (error) {
      console.error("加载班级数据失败:", error);
      ElMessage.error("加载班级数据失败");
    }
  };

  // 加载学期数据
  const loadSemesters = async () => {
    try {
      const response = await getAllSemesters();
      courses.value = response.data || [];
    } catch (error) {
      console.error("加载学期数据失败:", error);
      ElMessage.error("加载学期数据失败");
    }
  };

  // 专业变化处理
  const handleMajorChange = (major: any) => {
    currentSelection.class = null;
    currentSelection.semester = null;
    classes.value = [];
    dataList.value = [];

    if (major) {
      loadClasses(major.majorId);
    }
  };

  // 班级变化处理
  const handleClassChange = (cls: any) => {
    currentSelection.semester = null;
    dataList.value = [];
  };

  // 学期变化处理
  const handleCourseChange = (semester: any) => {
    dataList.value = [];
  };

  // 加载成绩数据
  const loadGrades = async () => {
    if (!currentSelection.class || !currentSelection.semester) {
      ElMessage.warning("请先选择班级和学期");
      return;
    }

    loading.value = true;
    try {
      const params = {
        classId: currentSelection.class.classId,
        semesterId: currentSelection.semester.semesterId,
        currentPage: pagination.currentPage,
        pageSize: pagination.pageSize,
        ...searchForm
      };

      const response = await getQualityAssessmentList(params);
      dataList.value = response.data?.records || [];
      pagination.total = response.data?.total || 0;
    } catch (error) {
      console.error("加载成绩数据失败:", error);
      ElMessage.error("加载成绩数据失败");
    } finally {
      loading.value = false;
    }
  };

  // 搜索
  const onSearch = () => {
    pagination.currentPage = 1;
    loadGrades();
  };

  // 重置搜索
  const resetForm = () => {
    searchForm.studentName = "";
    searchForm.studentId = "";
    searchForm.dormitoryNo = "";
    onSearch();
  };

  // 分页处理
  const handleSizeChange = (val: number) => {
    pagination.pageSize = val;
    loadGrades();
  };

  const handleCurrentChange = (val: number) => {
    pagination.currentPage = val;
    loadGrades();
  };

  const handleSelectionChange = (selection: any[]) => {
    selectedNum.value = selection.length;
  };

  // 视图切换
  const handleViewChange = (view: string) => {
    currentView.value = view;
  };

  // 打开编辑对话框
  const openDialog = (title = "新增", row?: any) => {
    addDialog({
      title: `${title}基本素质测评记录`,
      props: {
        formInline: {
          title: `${title}基本素质测评记录`,
          evaluationId: row?.evaluationId ?? null,
          studentId: row?.studentId ?? "",
          studentName: row?.studentName ?? "",
          dormitoryNo: row?.dormitoryNo ?? "",
          addScore: row?.addScore ?? 0,
          reduceScore: row?.reduceScore ?? 0,
          addScoreRemark: row?.addScoreRemark ?? "",
          reduceScoreRemark: row?.reduceScoreRemark ?? "",
          evaluationPeriod: row?.evaluationPeriod ?? currentSelection.semester?.semesterId,
          evaluationPeriodName: row?.evaluationPeriodName ?? currentSelection.semester?.semesterName,
          periodScore: row?.periodScore ?? 60,
          totalScore: row?.totalScore ?? 60,
          classId: currentSelection.class?.classId,
          className: currentSelection.class?.className
        }
      },
      width: "80%",
      draggable: true,
      fullscreen: deviceDetection(),
      fullscreenIcon: true,
      closeOnClickModal: false,
      contentRenderer: () => h(editForm, {
        ref: formRef,
        formInline: {
          title: `${title}基本素质测评记录`,
          evaluationId: row?.evaluationId ?? null,
          studentId: row?.studentId ?? "",
          studentName: row?.studentName ?? "",
          dormitoryNo: row?.dormitoryNo ?? "",
          addScore: row?.addScore ?? 0,
          reduceScore: row?.reduceScore ?? 0,
          addScoreRemark: row?.addScoreRemark ?? "",
          reduceScoreRemark: row?.reduceScoreRemark ?? "",
          evaluationPeriod: row?.evaluationPeriod ?? currentSelection.semester?.semesterId,
          evaluationPeriodName: row?.evaluationPeriodName ?? currentSelection.semester?.semesterName,
          periodScore: row?.periodScore ?? 60,
          totalScore: row?.totalScore ?? 60,
          classId: currentSelection.class?.classId,
          className: currentSelection.class?.className
        },
        onSuccess: handleEditSuccess
      }),
      beforeSure: (done) => {
        const FormRef = formRef.value.getRef();
        const curData = formRef.value.getRef().formInline;

        FormRef.validate(async (valid) => {
          if (valid) {
            try {
              if (curData.evaluationId) {
                await updateQualityAssessment(curData);
                ElMessage.success("修改成功");
              } else {
                await addQualityAssessment(curData);
                ElMessage.success("新增成功");
              }
              done();
              loadGrades();
            } catch (error) {
              console.error("操作失败:", error);
              ElMessage.error("操作失败");
            }
          }
        });
      }
    });
  };

  // 编辑成功回调
  const handleEditSuccess = () => {
    loadGrades();
  };

  // 删除记录
  const handleDelete = async (row: any) => {
    try {
      await deleteQualityAssessment(row.evaluationId);
      ElMessage.success("删除成功");
      loadGrades();
    } catch (error) {
      console.error("删除失败:", error);
      ElMessage.error("删除失败");
    }
  };

  // 导出数据
  const handleExport = async () => {
    if (!currentSelection.class || !currentSelection.semester) {
      ElMessage.warning("请先选择班级和学期");
      return;
    }

    try {
      const params = {
        classId: currentSelection.class.classId,
        semesterId: currentSelection.semester.semesterId,
        ...searchForm
      };

      await exportQualityAssessment(params);
      ElMessage.success("导出成功");
    } catch (error) {
      console.error("导出失败:", error);
      ElMessage.error("导出失败");
    }
  };

  // 打开导入对话框
  const openImportDialog = () => {
    if (!currentSelection.class || !currentSelection.semester) {
      ElMessage.warning("请先选择班级和学期");
      return;
    }

    addDialog({
      title: "基本素质测评成绩导入",
      props: {
        formInline: {
          title: "基本素质测评成绩导入",
          classCode: currentSelection.class?.classCode ?? "",
          className: currentSelection.class?.className ?? "",
          semesterId: currentSelection.semester?.semesterId ?? "",
          semesterName: currentSelection.semester?.semesterName ?? "",
          selectedCourseCodes: [],
          availableSemesters: [],
          availableCourses: []
        }
      },
      width: "80%",
      draggable: true,
      fullscreen: deviceDetection(),
      fullscreenIcon: true,
      closeOnClickModal: false,
      contentRenderer: () => h(importForm, {
        ref: importFormRef,
        formInline: {
          title: "基本素质测评成绩导入",
          classCode: currentSelection.class?.classCode ?? "",
          className: currentSelection.class?.className ?? "",
          semesterId: currentSelection.semester?.semesterId ?? "",
          semesterName: currentSelection.semester?.semesterName ?? "",
          selectedCourseCodes: [],
          availableSemesters: [],
          availableCourses: []
        },
        onSuccess: handleImportSuccess
      }),
      beforeSure: (done) => {
        done();
      }
    });
  };

  // 导入成功回调
  const handleImportSuccess = () => {
    ElMessage.success("成绩导入成功");
    loadGrades();
  };

  // 初始化
  onMounted(() => {
    loadMajors();
    loadSemesters();
  });

  return {
    // 数据状态
    loading,
    dataList,
    pagination,
    selectedNum,

    // 搜索相关
    searchForm,
    onSearch,
    resetForm,

    // 表格相关
    columns,
    handleSizeChange,
    handleCurrentChange,
    handleSelectionChange,

    // 操作相关
    openDialog,
    handleDelete,
    handleExport,
    handleImport: openImportDialog,

    // 选择相关
    currentSelection,
    majors,
    classes,
    courses,
    handleMajorChange,
    handleClassChange,
    handleCourseChange,

    // 视图切换
    currentView,
    handleViewChange,

    // 成绩相关
    loadGrades,

    // 导入相关
    openImportDialog,
    handleImportSuccess
  };
}
