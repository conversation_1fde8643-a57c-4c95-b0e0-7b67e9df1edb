import { http } from "@/utils/http";

// 课程查询参数类型
export interface CourseQueryDTO {
  courseCode?: string;
  courseName?: string;
  courseType?: string;
  collegeCode?: string;
  majorCode?: string;
  current?: number;
  size?: number;
}

// 课程信息类型
export interface CourseVO {
  id?: number;
  courseCode: string;
  courseName: string;
  credits: number;
  courseNature: string; // 课程性质：必修、选修、实践、通识
  courseType?: string;
  majorCode?: string;
  majorName?: string;
  collegeCode?: string;
  collegeName?: string;
  description?: string;
  createdAt?: string;
  updatedAt?: string;
}

// 分页查询课程列表
export const getCoursePage = (data: CourseQueryDTO) => {
  return http.request<any>("post", "/api/educational/course/page", { data });
};

// 获取所有课程列表
export const getAllCourses = () => {
  return http.request<CourseVO[]>("get", "/api/educational/course/all");
};



// 根据课程类型获取课程列表
export const getCoursesByType = (courseType: string) => {
  return http.request<CourseVO[]>("get", `/api/educational/course/type/${courseType}`);
};

// 根据学院代码获取课程列表
export const getCoursesByCollegeCode = (collegeCode: string) => {
  return http.request<CourseVO[]>("get", `/api/educational/course/college/${collegeCode}`);
};

// 根据ID获取课程详情
export const getCourseById = (id: number) => {
  return http.request<CourseVO>("get", `/api/educational/course/${id}`);
};

// 新增课程
export const saveCourse = (data: CourseVO) => {
  return http.request<any>("post", "/api/educational/course", { data });
};

// 修改课程
export const updateCourse = (data: CourseVO) => {
  return http.request<any>("put", "/api/educational/course", { data });
};

// 删除课程
export const deleteCourse = (id: number) => {
  return http.request<any>("delete", `/api/educational/course/${id}`);
};

// 批量删除课程
export const batchDeleteCourses = (ids: number[]) => {
  return http.request<any>("delete", "/api/educational/course/batch", { data: ids });
};
