import dayjs from "dayjs";
import { message } from "@/utils/message";
import { type PaginationProps } from "@pureadmin/table";
import { type TableColumnList } from "@pureadmin/table";
import { reactive, ref, onMounted, toRaw } from "vue";
import {
  getSemesterList,
  type SemesterItem,
  type SemesterQueryDTO
} from "@/api/basic/semester";

export function useSemester() {
  const form = reactive<SemesterQueryDTO>({
    academicYear: "",
    semesterNumber: undefined,
    semesterName: "",
    isCurrent: undefined,
    current: 1,
    size: 10
  });

  const dataList = ref<SemesterItem[]>([]);
  const loading = ref(true);

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  });

  const columns: TableColumnList = [
    {
      label: "序号",
      type: "index",
      width: 70
    },
    {
      label: "学年",
      prop: "academicYear",
      minWidth: 120
    },
    {
      label: "学期号",
      prop: "semesterNumber",
      minWidth: 100,
      formatter: ({ semesterNumber }) => `${semesterNumber}`
    },
    {
      label: "学期名称",
      prop: "semesterName",
      minWidth: 200
    },
    {
      label: "开始日期",
      prop: "startDate",
      minWidth: 120,
      formatter: ({ startDate }) =>
        dayjs(startDate).format("YYYY-MM-DD")
    },
    {
      label: "结束日期",
      prop: "endDate",
      minWidth: 120,
      formatter: ({ endDate }) =>
        dayjs(endDate).format("YYYY-MM-DD")
    },
    {
      label: "当前学期",
      prop: "isCurrent",
      minWidth: 100,
      cellRenderer: ({ row, props }) => (
        <el-tag
          size={props.size}
          type={row.isCurrent ? "success" : "info"}
          effect="plain"
        >
          {row.isCurrent ? "是" : "否"}
        </el-tag>
      )
    },
    {
      label: "创建时间",
      minWidth: 180,
      prop: "createdAt",
      formatter: ({ createdAt }) =>
        dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss")
    },

  ];



  function handleSizeChange(val: number) {
    form.size = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    form.current = val;
    onSearch();
  }

  function handleSelectionChange(val: SemesterItem[]) {
    console.log("handleSelectionChange", val);
  }

  async function onSearch() {
    loading.value = true;
    try {
      const queryData = {
        ...toRaw(form),
        current: pagination.currentPage,
        size: pagination.pageSize
      };
      const response = await getSemesterList(queryData);

      if (response.success) {
        const result = response.data;
        dataList.value = result.list || [];
        pagination.total = result.total || 0;
        pagination.currentPage = result.pageNum || 1;
        pagination.pageSize = result.pageSize || 10;
      } else {
        message("查询失败", { type: "error" });
        dataList.value = [];
        pagination.total = 0;
      }
    } catch (error) {
      console.error("获取学期列表失败:", error);
      message("网络错误，请稍后重试", { type: "error" });
      dataList.value = [];
      pagination.total = 0;
    } finally {
      loading.value = false;
    }
  }

  const resetForm = (formEl: any) => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    handleSelectionChange
  };
}
