import { reactive } from "vue";
import type { FormRules } from "element-plus";

/** 自定义表单规则校验 */
export const formRules = reactive(<FormRules>{
  collegeCode: [
    { required: true, message: "请选择所属学院", trigger: "change" }
  ],
  majorCode: [
    { required: true, message: "专业代码为必填项", trigger: "blur" },
    { min: 2, max: 20, message: "专业代码长度应为2-20个字符", trigger: "blur" }
  ],
  majorName: [
    { required: true, message: "专业名称为必填项", trigger: "blur" },
    { min: 2, max: 100, message: "专业名称长度应为2-100个字符", trigger: "blur" }
  ],
  duration: [
    { required: true, message: "请选择学制年限", trigger: "change" }
  ],
  description: [
    { max: 500, message: "专业描述长度不能超过500个字符", trigger: "blur" }
  ]
});
