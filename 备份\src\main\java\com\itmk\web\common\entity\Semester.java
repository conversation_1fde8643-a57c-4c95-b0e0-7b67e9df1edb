package com.itmk.web.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("semesters")
public class Semester {
    @TableId(type = IdType.AUTO)
    private Integer semesterId;
    private String semesterName;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String academicYear;

    // 为兼容性添加的方法
    public Integer getId() {
        return semesterId;
    }

    public void setId(Integer id) {
        this.semesterId = id;
    }
}