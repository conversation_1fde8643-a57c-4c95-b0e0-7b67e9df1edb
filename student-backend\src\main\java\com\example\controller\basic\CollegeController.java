package com.example.controller.basic;

import com.example.common.PageResult;
import com.example.common.Result;
import com.example.dto.basic.CollegeQueryDTO;
import com.example.service.basic.CollegeService;
import com.example.vo.basic.CollegeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 学院管理控制器
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Slf4j
@RestController
@RequestMapping("/api/basic/college")
@RequiredArgsConstructor
@Validated
@Tag(name = "学院管理", description = "学院管理相关接口")
public class CollegeController {

    private final CollegeService collegeService;

    /**
     * 分页查询学院列表
     */
    @PostMapping("/list")
    @Operation(summary = "获取学院列表", description = "分页查询学院列表")
    public Result<PageResult<CollegeVO>> getCollegeList(@RequestBody(required = false) CollegeQueryDTO query) {
        PageResult<CollegeVO> collegeList = collegeService.getCollegeList(query);
        return Result.success("查询成功", collegeList);
    }

    /**
     * 获取所有学院列表（用于下拉选择）
     */
    @GetMapping("/all")
    @Operation(summary = "获取所有学院", description = "获取所有学院列表")
    public Result<List<CollegeVO>> getAllColleges() {
        List<CollegeVO> collegeList = collegeService.getAllColleges();
        return Result.success("查询成功", collegeList);
    }

    /**
     * 根据ID获取学院详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取学院详情", description = "根据ID获取学院详情")
    public Result<CollegeVO> getCollegeById(@PathVariable Integer id) {
        if (id == null) {
            return Result.badRequest("学院ID不能为空");
        }
        CollegeVO college = collegeService.getCollegeById(id);
        return Result.success("查询成功", college);
    }

    /**
     * 新增学院
     */
    @PostMapping("/save")
    @Operation(summary = "新增学院", description = "新增学院")
    public Result<Void> saveCollege(@Valid @RequestBody CollegeVO collegeVO) {
        collegeService.saveCollege(collegeVO);
        return Result.success("新增学院成功");
    }

    /**
     * 更新学院
     */
    @PostMapping("/update")
    @Operation(summary = "更新学院", description = "更新学院信息")
    public Result<Void> updateCollege(@Valid @RequestBody CollegeVO collegeVO) {
        collegeService.updateCollege(collegeVO);
        return Result.success("更新学院成功");
    }

    /**
     * 删除学院
     */
    @PostMapping("/delete")
    @Operation(summary = "删除学院", description = "删除学院")
    public Result<Void> deleteCollege(@RequestBody CollegeVO collegeVO) {
        if (collegeVO.getId() == null) {
            return Result.badRequest("学院ID不能为空");
        }
        collegeService.deleteCollege(collegeVO.getId());
        return Result.success("删除学院成功");
    }
}
