<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.system.SysMenuMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.entity.system.SysMenu">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="parent_id" property="parentId" jdbcType="INTEGER"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="path" property="path" jdbcType="VARCHAR"/>
        <result column="component" property="component" jdbcType="VARCHAR"/>
        <result column="menu_type" property="menuType" jdbcType="TINYINT"/>
        <result column="icon" property="icon" jdbcType="VARCHAR"/>
        <result column="rank" property="rank" jdbcType="INTEGER"/>
        <result column="redirect" property="redirect" jdbcType="VARCHAR"/>
        <result column="auths" property="auths" jdbcType="VARCHAR"/>
        <result column="show_link" property="showLink" jdbcType="TINYINT"/>
        <result column="keep_alive" property="keepAlive" jdbcType="TINYINT"/>
        <result column="frame_src" property="frameSrc" jdbcType="VARCHAR"/>
        <result column="frame_loading" property="frameLoading" jdbcType="TINYINT"/>
        <result column="hidden_tag" property="hiddenTag" jdbcType="TINYINT"/>
        <result column="fixed_tag" property="fixedTag" jdbcType="TINYINT"/>
        <result column="show_parent" property="showParent" jdbcType="TINYINT"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 通用SQL片段 -->
    <sql id="Base_Column_List">
        id, parent_id, title, `name`, `path`, component, menu_type, icon, `rank`, `redirect`, auths, show_link, keep_alive, frame_src, frame_loading, hidden_tag, fixed_tag, show_parent, status, create_time, update_time
    </sql>

    <!-- 查询所有菜单 -->
    <select id="selectAllMenus" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List"/>
        FROM sys_menu
        ORDER BY parent_id ASC, `rank` ASC, id ASC
    </select>

    <!-- 根据用户ID查询菜单 -->
    <select id="selectMenusByUserId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT DISTINCT m.id, m.parent_id, m.title, m.`name`, m.`path`, m.component, m.`redirect`,
               m.menu_type, m.icon, m.`rank`, m.auths, m.show_link, m.show_parent,
               m.keep_alive, m.fixed_tag, m.frame_src, m.status, m.create_time, m.update_time
        FROM sys_menu m
        INNER JOIN sys_role_menu rm ON m.id = rm.menu_id
        INNER JOIN sys_user_role ur ON rm.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND m.status = 1
        ORDER BY m.parent_id ASC, m.`rank` ASC
    </select>

    <!-- 根据角色ID查询菜单ID列表 -->
    <select id="selectMenuIdsByRoleId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT rm.menu_id
        FROM sys_role_menu rm
        INNER JOIN sys_menu m ON rm.menu_id = m.id
        WHERE rm.role_id = #{roleId}
          AND m.status = 1
        ORDER BY m.`rank` ASC
    </select>

    <!-- 查询菜单树（用于权限分配） -->
    <select id="selectMenuTree" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_menu
        WHERE status = 1
          AND menu_type IN (0, 1, 2)
        ORDER BY parent_id ASC, `rank` ASC
    </select>

    <!-- 根据父级ID查询子菜单 -->
    <select id="selectByParentId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_menu
        WHERE parent_id = #{parentId}
          AND status = 1
        ORDER BY `rank` ASC
    </select>

    <!-- 查询菜单详情 -->
    <select id="selectMenuDetail" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_menu
        WHERE id = #{menuId}
          AND status = 1
    </select>

    <!-- 检查菜单名称是否存在 -->
    <select id="existsByTitle" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM sys_menu
        WHERE title = #{title}
          AND status = 1
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查路由路径是否存在 -->
    <select id="existsByPath" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM sys_menu
        WHERE `path` = #{path}
          AND status = 1
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查路由名称是否存在 -->
    <select id="existsByName" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM sys_menu
        WHERE `name` = #{name}
          AND status = 1
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 根据父级ID查询子菜单数量 -->
    <select id="countByParentId" parameterType="java.lang.Integer" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM sys_menu
        WHERE parent_id = #{parentId}
          AND status = 1
    </select>

    <!-- 获取最大排序值 -->
    <select id="getMaxRank" resultType="java.lang.Integer">
        SELECT COALESCE(MAX(`rank`), 0)
        FROM sys_menu
        WHERE status = 1
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
    </select>

    <!-- 批量更新菜单状态 -->
    <update id="batchUpdateStatus">
        UPDATE sys_menu
        SET status = #{status},
            update_time = NOW()
        WHERE id IN
        <foreach collection="menuIds" item="menuId" open="(" separator="," close=")">
            #{menuId}
        </foreach>
    </update>

    <!-- 递归查询所有子菜单ID -->
    <select id="selectChildrenIds" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        WITH RECURSIVE menu_tree AS (
            SELECT id FROM sys_menu WHERE id = #{menuId} AND status = 1
            UNION ALL
            SELECT m.id FROM sys_menu m
            INNER JOIN menu_tree mt ON m.parent_id = mt.id
            WHERE m.status = 1
        )
        SELECT id FROM menu_tree WHERE id != #{menuId}
    </select>

    <!-- 专门的菜单更新方法，确保parent_id字段能够被正确更新（包括null值） -->
    <update id="updateMenuWithParentId" parameterType="com.example.entity.system.SysMenu">
        UPDATE sys_menu
        SET parent_id = #{parentId},
            title = #{title},
            `name` = #{name},
            `path` = #{path},
            component = #{component},
            redirect = #{redirect},
            menu_type = #{menuType},
            icon = #{icon},
            `rank` = #{rank},
            auths = #{auths},
            show_link = #{showLink},
            show_parent = #{showParent},
            keep_alive = #{keepAlive},
            fixed_tag = #{fixedTag},
            frame_src = #{frameSrc},
            frame_loading = #{frameLoading},
            hidden_tag = #{hiddenTag},
            status = #{status},
            update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>
