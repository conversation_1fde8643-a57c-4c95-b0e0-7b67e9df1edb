import { ref, h } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { addDialog } from "@/components/ReDialog";
import { message } from "@/utils/message";
import editForm from "../components/classCourseForm.vue";
import { ClassCourseFormInline } from "../utils/types";
import {
  saveClassCourse,
  updateClassCourse,
  deleteClassCourse,
  batchDeleteClassCourses
} from "@/api/educational/classCourse";

export function useCrud() {
  const formRef = ref();

  // 新增/编辑对话框
  function openDialog(title = "新增", row?: any) {
    addDialog({
      title: `${title}班级课程分配`,
      props: {
        formInline: {
          id: row?.id ?? null,
          classCode: row?.classCode ?? "",
          courseCode: row?.courseCode ?? "",
          semesterId: row?.semesterId ?? null,

          isActive: row?.isActive ?? true,
          sortOrder: row?.sortOrder ?? 0,
          remark: row?.remark ?? ""
        }
      },
      width: "46%",
      draggable: true,
      fullscreenIcon: true,
      closeOnClickModal: false,
      contentRenderer: () => h(editForm, { ref: formRef }),
      beforeSure: (done, { options }) => {
        const FormRef = formRef.value.getRef();
        const curData = options.props.formInline as ClassCourseFormInline;

        FormRef.validate(async (valid: boolean) => {
          if (valid) {
            try {
              // 调用保存或更新API
              if (curData.id) {
                await updateClassCourse(curData);
                message("更新成功", { type: "success" });
              } else {
                await saveClassCourse(curData);
                message("新增成功", { type: "success" });
              }
              done();
              // 这里可以通过回调函数刷新列表
            } catch (error) {
              console.error("操作失败:", error);
              message("操作失败", { type: "error" });
            }
          }
        });
      }
    });
  }

  // 删除确认
  async function handleDelete(row: any, onSuccess?: () => void) {
    try {
      await ElMessageBox.confirm(
        `确认删除班级课程分配 "${row.className} - ${row.courseName}" 吗？`,
        "系统提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      );

      // 调用删除API
      await deleteClassCourse(row.id);
      message("删除成功", { type: "success" });

      // 执行成功回调
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      if (error !== "cancel") {
        message("删除失败", { type: "error" });
      }
    }
  }

  // 批量删除
  async function handleBatchDelete(selectedRows: any[], onSuccess?: () => void) {
    if (selectedRows.length === 0) {
      message("请选择要删除的数据", { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        `确认删除选中的 ${selectedRows.length} 条数据吗？`,
        "系统提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      );

      // 调用批量删除API
      const ids = selectedRows.map(row => row.id);
      await batchDeleteClassCourses(ids);
      message("批量删除成功", { type: "success" });

      // 执行成功回调
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      if (error !== "cancel") {
        message("批量删除失败", { type: "error" });
      }
    }
  }

  return {
    formRef,
    openDialog,
    handleDelete,
    handleBatchDelete
  };
}
