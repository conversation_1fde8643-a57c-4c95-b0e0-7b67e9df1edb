import editForm from "../form.vue";
import { message } from "@/utils/message";
import { addDialog } from "@/components/ReDialog";
import { reactive, ref, onMounted, h } from "vue";
import type { FormItemProps, QueryFormProps } from "./types";
import type { PaginationProps } from "@pureadmin/table";
import { cloneDeep, deviceDetection } from "@pureadmin/utils";
import {
  getClassesPage,
  saveClasses,
  updateClasses,
  deleteClasses,
  batchDeleteClasses,
  type ClassesQueryDTO
} from "@/api/basic/classes";
import { getAllMajors } from "@/api/basic/major";
import { getAllTeachers } from "@/api/teacher/teachers";
import { getAllColleges } from "@/api/basic/college";
import { ElMessageBox } from "element-plus";
import { getCurrentYear } from "@/utils/yearOptions";

export function useClasses() {
  const formRef = ref();
  const tableRef = ref();
  const dataList = ref([]);
  const loading = ref(true);
  const selectedNum = ref(0);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  });
  const columns: TableColumnList = [
    {
      label: "勾选列",
      type: "selection",
      width: 55,
      align: "left"
    },
    {
      label: "序号",
      type: "index",
      width: 70
    },
    {
      label: "学院",
      prop: "collegeName",
      minWidth: 120
    },
    {
      label: "所属专业",
      prop: "majorName",
      minWidth: 150
    },
    {
      label: "班级代码",
      prop: "classCode",
      minWidth: 120
    },
    {
      label: "班级名称",
      prop: "className",
      minWidth: 150
    },
    {
      label: "入学年份",
      prop: "gradeYear",
      minWidth: 100
    },
    {
      label: "学生人数",
      prop: "studentCount",
      minWidth: 100
    },
    {
      label: "班主任",
      prop: "headTeacherName",
      minWidth: 120
    },
    {
      label: "创建时间",
      prop: "createdAt",
      minWidth: 180,
      formatter: ({ createdAt }) =>
        createdAt ? new Date(createdAt).toLocaleString() : ""
    },
    {
      label: "操作",
      fixed: "right",
      width: 180,
      slot: "operation"
    }
  ];

  // 查询表单
  const queryForm = reactive<QueryFormProps>({
    classCode: "",
    className: "",
    majorCode: "",
    gradeYear: null,
    headTeacherCode: "",
    collegeCode: ""
  });

  // 专业选项
  const majorOptions = ref([]);
  // 教师选项
  const teacherOptions = ref([]);
  // 学院选项
  const collegeOptions = ref([]);

  /** 搜索 */
  async function onSearch() {
    loading.value = true;
    try {
      const query: ClassesQueryDTO = {
        ...queryForm,
        gradeYear: queryForm.gradeYear || undefined,
        current: pagination.currentPage,
        size: pagination.pageSize
      };

      const result = await getClassesPage(query);
      if (result.success && result.data) {
        dataList.value = result.data.records || [];
        pagination.total = result.data.total || 0;
      } else {
        dataList.value = [];
        pagination.total = 0;
        message("查询班级列表失败", { type: "error" });
      }
    } catch (error) {
      message("查询班级列表失败", { type: "error" });
      dataList.value = [];
      pagination.total = 0;
    } finally {
      loading.value = false;
    }
  }

  /** 重置搜索表单 */
  function resetForm() {
    queryForm.collegeCode = "";
    queryForm.majorCode = "";
    queryForm.classCode = "";
    queryForm.className = "";
    queryForm.gradeYear = null;
    queryForm.headTeacherCode = "";
    onSearch();
  }

  /** 分页改变 */
  function onSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function onCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  /** 当CheckBox选择项发生变化时会触发该事件 */
  function handleSelectionChange(val: any[]) {
    selectedNum.value = val.length;
  }

  /** 取消选择 */
  function onSelectionCancel() {
    selectedNum.value = 0;
    // 用于多选表格，清空用户的选择
    tableRef.value.getTableRef().clearSelection();
  }

  /** 批量删除 */
  function onbatchDel() {
    // 返回当前选中的行
    const curSelected = tableRef.value.getTableRef().getSelectionRows();
    const ids = curSelected.map((item: any) => item.id);

    ElMessageBox.confirm(
      `确认要删除这 ${ids.length} 个班级吗?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        draggable: true
      }
    )
      .then(async () => {
        try {
          const result = await batchDeleteClasses(ids);
          if (result.success) {
            message("删除成功", { type: "success" });
            onSelectionCancel();
            onSearch();
          } else {
            message(result.message || "批量删除失败", { type: "error" });
          }
        } catch (error) {
          message("批量删除失败", { type: "error" });
        }
      })
      .catch(() => {});
  }

  /** 加载基础数据 */
  async function loadBaseData() {
    try {
      // 加载学院列表
      const collegesRes = await getAllColleges();
      if (collegesRes.success && collegesRes.data) {
        collegeOptions.value = collegesRes.data;
      } else {
        collegeOptions.value = [];
      }

      // 加载专业列表
      const majorsRes = await getAllMajors();
      if (majorsRes.success && majorsRes.data) {
        majorOptions.value = majorsRes.data;
      } else {
        majorOptions.value = [];
      }

      // 加载教师列表
      const teachersRes = await getAllTeachers();
      if (teachersRes.success && teachersRes.data) {
        teacherOptions.value = teachersRes.data;
      } else {
        teacherOptions.value = [];
      }
    } catch (error) {
      collegeOptions.value = [];
      majorOptions.value = [];
      teacherOptions.value = [];
    }
  }

  /** 打开新增/编辑弹窗 */
  function openDialog(title = "新增", row?: FormItemProps) {
    // 如果是编辑模式，需要根据专业代码找到对应的学院代码
    let collegeCode = "";
    if (row?.majorCode) {
      const major = majorOptions.value.find(m => m.majorCode === row.majorCode);
      collegeCode = major?.collegeCode || "";
    }

    addDialog({
      title: `${title}班级`,
      props: {
        formInline: {
          id: row?.id,
          classCode: row?.classCode ?? "",
          className: row?.className ?? "",
          collegeCode: collegeCode,
          majorCode: row?.majorCode ?? "",
          gradeYear: row?.gradeYear ?? getCurrentYear(),
          studentCount: row?.studentCount ?? 0,
          headTeacherCode: row?.headTeacherCode ?? "",
          collegeOptions: cloneDeep(collegeOptions.value),
          majorOptions: cloneDeep(majorOptions.value),
          teacherOptions: cloneDeep(teacherOptions.value)
        }
      },
      width: "46%",
      draggable: true,
      fullscreen: deviceDetection(),
      fullscreenIcon: true,
      closeOnClickModal: false,
      contentRenderer: () => h(editForm, { ref: formRef }),
      beforeSure: (done, { options }) => {
        const FormRef = formRef.value.getRef();
        const curData = options.props.formInline as FormItemProps;

        function chores() {
          done(); // 关闭弹窗
          onSearch(); // 刷新表格数据
        }

        FormRef.validate(async (valid: boolean) => {
          if (valid) {
            try {
              // 表单规则校验通过
              let result: any;
              if (title === "新增") {
                result = await saveClasses(curData);
                if (result.success) {
                  message("新增成功", { type: "success" });
                  chores();
                } else {
                  message(result.message || "新增失败", { type: "error" });
                }
              } else {
                result = await updateClasses(curData);
                if (result.success) {
                  message("修改成功", { type: "success" });
                  chores();
                } else {
                  message(result.message || "修改失败", { type: "error" });
                }
              }
            } catch (error) {
              message("操作失败", { type: "error" });
            }
          }
        });
      }
    });
  }

  /** 删除班级 */
  async function handleDelete(row: any) {
    ElMessageBox.confirm(
      `确认要删除班级"${row.className}"吗?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        draggable: true
      }
    )
      .then(async () => {
        try {
          const result = await deleteClasses(row.id);
          if (result.success) {
            message("删除成功", { type: "success" });
            onSearch();
          } else {
            message(result.message || "删除失败", { type: "error" });
          }
        } catch (error) {
          message("删除失败", { type: "error" });
        }
      })
      .catch(() => {});
  }

  onMounted(() => {
    loadBaseData().then(() => {
      onSearch();
    });
  });

  return {
    queryForm,
    loading,
    columns,
    dataList,
    pagination,
    selectedNum,
    majorOptions,
    teacherOptions,
    collegeOptions,
    tableRef,
    onSearch,
    resetForm,
    onbatchDel,
    openDialog,
    handleDelete,
    onSizeChange,
    onCurrentChange,
    onSelectionCancel,
    handleSelectionChange
  };
}
