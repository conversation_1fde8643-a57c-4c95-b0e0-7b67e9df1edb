package com.example.controller.student;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.common.Result;
import com.example.dto.student.StudentsQueryDTO;
import com.example.service.student.StudentsService;
import com.example.vo.student.StudentsVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 学生信息Controller
 */
@RestController
@RequestMapping("/api/student/students")
@Tag(name = "学生管理", description = "学生信息管理接口")
public class StudentsController {

    @Autowired
    private StudentsService studentsService;

    /**
     * 分页查询学生列表
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询学生列表", description = "根据条件分页查询学生信息")
    public Result<IPage<StudentsVO>> getStudentsPage(@RequestBody StudentsQueryDTO queryDTO) {
        try {
            IPage<StudentsVO> result = studentsService.getStudentsPage(queryDTO);
            return Result.success("查询成功", result);
        } catch (Exception e) {
            return Result.error("查询学生列表失败");
        }
    }

    /**
     * 根据ID查询学生详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "查询学生详情", description = "根据ID查询学生详细信息")
    public Result<StudentsVO> getStudentsById(@PathVariable Integer id) {
        try {
            StudentsVO result = studentsService.getStudentsById(id);
            return Result.success("查询成功", result);
        } catch (Exception e) {
            return Result.error("查询学生详情失败");
        }
    }

    /**
     * 查询所有学生列表
     */
    @GetMapping("/all")
    @Operation(summary = "查询所有学生", description = "查询所有学生信息")
    public Result<List<StudentsVO>> getAllStudents() {
        try {
            List<StudentsVO> result = studentsService.getAllStudents();
            return Result.success("查询成功", result);
        } catch (Exception e) {
            return Result.error("查询所有学生失败");
        }
    }

    /**
     * 根据班级代码查询学生列表
     */
    @GetMapping("/by-class/{classCode}")
    @Operation(summary = "根据班级查询学生", description = "根据班级代码查询学生列表")
    public Result<List<StudentsVO>> getStudentsByClassCode(@PathVariable String classCode) {
        try {
            List<StudentsVO> result = studentsService.getStudentsByClassCode(classCode);
            return Result.success("查询成功", result);
        } catch (Exception e) {
            return Result.error("查询学生失败");
        }
    }

    /**
     * 根据专业代码查询学生列表
     */
    @GetMapping("/by-major/{majorCode}")
    @Operation(summary = "根据专业查询学生", description = "根据专业代码查询学生列表")
    public Result<List<StudentsVO>> getStudentsByMajorCode(@PathVariable String majorCode) {
        try {
            List<StudentsVO> result = studentsService.getStudentsByMajorCode(majorCode);
            return Result.success("查询成功", result);
        } catch (Exception e) {
            return Result.error("查询学生失败");
        }
    }

    /**
     * 新增学生
     */
    @PostMapping
    @Operation(summary = "新增学生", description = "新增学生信息")
    public Result<Void> saveStudents(@RequestBody StudentsVO studentsVO) {
        try {
            studentsService.saveStudents(studentsVO);
            return Result.success("新增成功");
        } catch (Exception e) {
            return Result.error("新增学生失败: " + e.getMessage());
        }
    }

    /**
     * 更新学生
     */
    @PutMapping
    @Operation(summary = "更新学生", description = "更新学生信息")
    public Result<Void> updateStudents(@RequestBody StudentsVO studentsVO) {
        try {
            studentsService.updateStudents(studentsVO);
            return Result.success("更新成功");
        } catch (Exception e) {
            return Result.error("更新学生失败: " + e.getMessage());
        }
    }

    /**
     * 删除学生
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除学生", description = "根据ID删除学生")
    public Result<Void> deleteStudents(@PathVariable Integer id) {
        try {
            studentsService.deleteStudents(id);
            return Result.success("删除成功");
        } catch (Exception e) {
            return Result.error("删除学生失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除学生
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除学生", description = "批量删除学生信息")
    public Result<Void> batchDeleteStudents(@RequestBody List<Integer> ids) {
        try {
            studentsService.batchDeleteStudents(ids);
            return Result.success("批量删除成功");
        } catch (Exception e) {
            return Result.error("批量删除学生失败: " + e.getMessage());
        }
    }
}
