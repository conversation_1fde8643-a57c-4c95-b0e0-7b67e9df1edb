import { reactive } from "vue";
import type { FormRules } from "element-plus";

/** 自定义表单规则校验 */
export const formRules = reactive<FormRules>({
  courseCode: [
    { required: true, message: "请输入课程代码", trigger: "blur" },
    { min: 2, max: 20, message: "课程代码长度应在2-20个字符之间", trigger: "blur" }
  ],
  courseName: [
    { required: true, message: "请输入课程名称", trigger: "blur" },
    { min: 2, max: 100, message: "课程名称长度应在2-100个字符之间", trigger: "blur" }
  ],
  credits: [
    { required: true, message: "请输入学分", trigger: "blur" },
    { type: "number", min: 0.5, max: 10, message: "学分应在0.5-10之间", trigger: "blur" }
  ],
  courseType: [
    { required: true, message: "请选择课程类型", trigger: "change" }
  ],
  description: [
    { max: 1000, message: "课程描述长度不能超过1000个字符", trigger: "blur" }
  ]
});
