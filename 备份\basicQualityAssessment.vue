<template>
  <el-main>
    <!-- 搜索表单组件 -->
    <SearchForm
      v-model="searchModel"
      :fields="searchFields"
      @search="handleSearch"
      @reset="handleReset"
      @refresh="handleRefresh"
    >
      <!-- 自定义操作按钮插槽 -->
      <template slot="actions">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
        >
          新增
        </el-button>
        <ImportExport
          :export-api="exportQualityApi"
          :import-api="importQualityApi"
          :template-api="downloadQualityTemplateApi"
          :export-params="getExportParams()"
          :export-file-name="getExportFileName()"
          :template-file-name="templateFilename"
          :import-dialog-title="'导入基本素质测评成绩'"
          @import-success="handleImportSuccess"
          @export-success="handleExportSuccess"
        />
      </template>
    </SearchForm>

    <!-- 基本素质测评表格 -->
    <ElDataTable
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      :pagination="pagination"
      @row-action="handleTableAction"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!-- 新增/编辑弹框 -->
    <SysDialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      :smart-resize="true"
      @close="onClose"
      @confirm="onConfirm"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px" class="quality-form">
        <el-row :gutter="15">
          <!-- 左侧：学生信息和评分信息 -->
          <el-col :span="10">
            <!-- 学生基本信息 -->
            <div class="form-section-title">学生基本信息</div>

            <el-form-item label="学号" prop="studentId">
              <el-select v-model="form.studentId" placeholder="请选择学生学号" filterable style="width: 100%" @change="getStudentInfo">
                <el-option
                  v-for="student in studentOptions"
                  :key="student.studentId"
                  :label="student.studentId"
                  :value="student.studentId"
                >
                  <span>{{ student.studentId }}</span>
                  <span style="float: right; color: #8492a6; font-size: 12px">{{ student.name }}</span>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="姓名">
              <el-input v-model="form.studentName" placeholder="自动获取" :disabled="true" />
            </el-form-item>

            <el-form-item label="宿舍号">
              <el-input v-model="form.dormitoryNo" placeholder="自动获取" :disabled="true" />
            </el-form-item>

            <el-form-item label="评分学期" prop="evaluationPeriodName">
              <el-select v-model="form.evaluationPeriodName" placeholder="请选择评分学期" style="width: 100%" filterable>
                <el-option
                  v-for="item in semesterOptions"
                  :key="item.semesterId"
                  :label="item.semesterName"
                  :value="item.semesterName"
                >
                  <span>{{ item.semesterName }}</span>
                  <span v-if="item.academicYear" style="float: right; color: #8492a6; font-size: 12px">{{ item.academicYear }}</span>
                </el-option>
              </el-select>
            </el-form-item>

            <!-- 得分信息 -->
            <div class="form-section-title">得分信息</div>
            <div class="score-calculation-info">
              <div class="calculation-formula">
                所有分数由系统根据说明内容自动计算，无需手动输入
              </div>
            </div>

            <el-form-item label="基础分">
              <el-input-number v-model="form.periodScore" :precision="2" :step="1" :min="0" :max="200" style="width: 100%" />
              <div class="remark-hint">系统自动设置：第一学期60分，第二学期为上学期总分</div>
            </el-form-item>

            <el-form-item label="加分">
              <el-input-number v-model="form.addScore" :precision="2" :step="1" :min="0" :max="100" style="width: 100%" disabled />
              <div class="remark-hint">系统根据加分说明自动计算</div>
            </el-form-item>

            <el-form-item label="扣分">
              <el-input-number v-model="form.reduceScore" :precision="2" :step="1" :min="0" :max="100" style="width: 100%" disabled />
              <div class="remark-hint">系统根据扣分说明自动计算</div>
            </el-form-item>

            <el-form-item label="总得分">
              <el-input-number v-model="form.totalScore" :precision="2" :step="1" :min="0" :max="200" style="width: 100%" disabled />
              <div class="remark-hint">系统自动计算：基础分 + 加分 - 扣分</div>
            </el-form-item>
          </el-col>

          <!-- 右侧：加分扣分说明 -->
          <el-col :span="14">
            <div class="form-section-title">加分扣分详情</div>
            <el-form-item label="加分说明" prop="addScoreRemark">
              <el-input v-model="form.addScoreRemark" type="textarea" :rows="4" placeholder="请输入加分说明，格式如：参加志愿活动 +1" />
            </el-form-item>
            <el-form-item label="扣分说明" prop="reduceScoreRemark">
              <el-input v-model="form.reduceScoreRemark" type="textarea" :rows="4" placeholder="请输入扣分说明，格式如：迟到 -1" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </SysDialog>
  </el-main>
</template>

<script>
import {
  getQualityListApi,
  addQualityApi,
  editQualityApi,
  deleteQualityApi,
  exportQualityApi,
  importQualityApi,
  downloadQualityTemplateApi
} from '@/api/quality'
import { getAcademicYearsApi, getSemestersByAcademicYearApi } from '@/api/term'
import { getStudentByIdApi, getStudentListApi } from '@/api/student'
import { mapGetters } from 'vuex'

export default {
  name: 'QualityEvaluation',
  data() {
    return {
      loading: false,
      // 搜索表单配置
      searchFields: [
        {
          prop: 'studentName',
          label: '学生姓名',
          type: 'select',
          placeholder: '请选择学生姓名',
          options: [],
          filterable: true,
          clearable: true
        },
        {
          prop: 'studentId',
          label: '学生学号',
          type: 'select',
          placeholder: '请选择学生学号',
          options: [],
          filterable: true,
          clearable: true
        },
        {
          prop: 'dormitoryNo',
          label: '宿舍号',
          type: 'select',
          placeholder: '请选择宿舍号',
          options: [],
          filterable: true,
          clearable: true
        },
        {
          type: 'academic-year-semester',
          prop: 'academicYearSemester',
          label: '学年学期',
          placeholder: '请选择学年或学年学期',
          options: []
        }
      ],

      // 搜索模型
      searchModel: {
        studentName: '',
        studentId: '',
        dormitoryNo: '',
        academicYearSemester: []
      },
      // 表格数据
      tableData: [],
      // 分页配置
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      // 表格列配置
      tableColumns: [],
      // 对话框
      dialogTitle: '',
      dialogVisible: false,
      // 表单数据
      form: {
        evaluationId: null,
        studentId: '',
        studentName: '',
        dormitoryNo: '',
        addScore: 0,
        reduceScore: 0,
        addScoreRemark: '',
        reduceScoreRemark: '',
        evaluationPeriod: '',
        evaluationPeriodName: '',
        periodScore: 0,
        totalScore: 0
      },
      // 表单验证规则
      rules: {
        studentId: [
          { required: true, message: '请输入学生学号', trigger: 'blur' }
        ],
        evaluationPeriodName: [
          { required: true, message: '请选择评分学期', trigger: 'change' }
        ]
      },
      // 学期选项（用于学期名称转换）
      semesterOptions: [],
      // 学生选项
      studentOptions: [],
      // 宿舍选项
      dormitoryOptions: [],
      // 导入模板文件名
      templateFilename: '基本素质测评成绩导入模板.xlsx'
    }
  },
  computed: {
    ...mapGetters(['userType', 'name']),
    // 判断当前用户是否为学生
    isStudent() {
      return this.userType === '0'
    },
    // 导出API函数，供模板使用
    exportQualityApi() {
      return exportQualityApi
    },
    // 导入API函数，供模板使用
    importQualityApi() {
      return importQualityApi
    },
    // 下载模板API函数，供模板使用
    downloadQualityTemplateApi() {
      return downloadQualityTemplateApi
    }
  },

  async created() {
    this.initTableColumns()
    this.getStudentOptions()
    await this.loadAllSemesterOptions()
    // 不在这里调用handleSearch，等待SearchForm设置默认值后自动触发搜索
  },
  methods: {
    // 初始化表格列配置
    initTableColumns() {
      this.tableColumns = [
        { type: 'index', label: '序号', width: 60, align: 'center' },
        { prop: 'studentName', label: '姓名', minWidth: 100, align: 'left' },
        { prop: 'studentId', label: '学号', minWidth: 120, align: 'left' },
        { prop: 'dormitoryNo', label: '宿舍号', minWidth: 100, align: 'left' },
        {
          prop: 'semesterName',
          label: '评分学期',
          minWidth: 150,
          align: 'left'
        },
        { prop: 'addScore', label: '加分', minWidth: 80, align: 'center' },
        { prop: 'reduceScore', label: '扣分', minWidth: 80, align: 'center' },
        {
          prop: 'addScoreRemark',
          label: '加分说明',
          minWidth: 200,
          align: 'left',
          showOverflowTooltip: true,
          formatter: (row) => row.addScoreRemark || '无'
        },
        {
          prop: 'reduceScoreRemark',
          label: '扣分说明',
          minWidth: 200,
          align: 'left',
          showOverflowTooltip: true,
          formatter: (row) => row.reduceScoreRemark || '无'
        },
        {
          prop: 'periodScore',
          label: '基础分',
          minWidth: 120,
          align: 'center',
          headerTooltip: '第一学期基础分为60分，第二学期基础分为上一学期总分',
          formatter: (row) => row.periodScore || 0
        },
        {
          prop: 'totalScore',
          label: '最终总分',
          minWidth: 120,
          align: 'center',
          headerTooltip: '基础分+加分-扣分的结果'
        },
                {
          type: 'actions',
          label: '操作',
          fixed: 'right',
          actions: [
            {
              key: 'edit',
              label: '编辑',
              type: 'primary',
              size: 'small'
            },
            {
              key: 'delete',
              label: '删除',
              type: 'danger',
              size: 'small'
            }
          ]
        }
      ]
    },

    // 获取列表数据
    getList() {
      this.loading = true

      const params = {
        studentName: this.searchModel.studentName,
        studentId: this.searchModel.studentId,
        dormitoryNo: this.searchModel.dormitoryNo,
        currentPage: this.pagination.currentPage,
        pageSize: this.pagination.pageSize
      }

      // 解析学年学期级联选择器的值
      if (this.searchModel.academicYearSemester && this.searchModel.academicYearSemester.length > 0) {
        params.academicYear = this.searchModel.academicYearSemester[0]

        // 如果选择了具体学期，将学期ID作为评分学期参数
        if (this.searchModel.academicYearSemester.length === 2) {
          params.semesterId = this.searchModel.academicYearSemester[1]
          params.evaluationPeriod = this.searchModel.academicYearSemester[1]
        }
      }

      this.fetchData(params)
    },
    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1
      this.getList()
    },

    // 重置搜索
    handleReset() {
      this.searchModel = {
        studentName: '',
        studentId: '',
        dormitoryNo: '',
        academicYearSemester: []
      }
      this.pagination.currentPage = 1
      this.getList()
    },

    // 搜索表单刷新
    handleRefresh() {
      this.pagination.currentPage = 1
      this.getList()
    },

    // 获取导出参数
    getExportParams() {
      const params = {
        studentName: this.searchModel.studentName,
        studentId: this.searchModel.studentId,
        dormitoryNo: this.searchModel.dormitoryNo
      }

      // 解析学年学期级联选择器的值
      if (this.searchModel.academicYearSemester && this.searchModel.academicYearSemester.length > 0) {
        params.academicYear = this.searchModel.academicYearSemester[0]

        // 如果选择了具体学期，将学期ID作为评分学期参数
        if (this.searchModel.academicYearSemester.length === 2) {
          params.semesterId = this.searchModel.academicYearSemester[1]
          params.evaluationPeriod = this.searchModel.academicYearSemester[1]
        }
      }

      return params
    },

    // 获取导出文件名
    getExportFileName() {
      let year = '全部'
      if (this.searchModel.academicYearSemester && this.searchModel.academicYearSemester.length > 0) {
        year = this.searchModel.academicYearSemester[0]
      }
      return `基本素质测评成绩_${year}.xlsx`
    },

    // 导入成功回调
    handleImportSuccess() {
      this.pagination.currentPage = 1
      this.getList()
    },

    // 导出成功回调
    handleExportSuccess() {
      // 导出成功后的处理
    },

    // 表格操作
    handleTableAction({ key, record }) {
      switch (key) {
        case 'edit':
          this.handleEdit(record)
          break
        case 'delete':
          this.handleDelete(record)
          break
      }
    },
    // 获取数据的通用方法
    fetchData(params) {
      this.loading = true
      getQualityListApi(params).then(response => {
        if (response && response.code === 200) {
          this.tableData = response.data.records || []

          // 处理学期名称显示
          this.tableData.forEach(item => {
            if (item.evaluationPeriod) {
              const semesterName = this.getSemesterName(item.evaluationPeriod)
              item.semesterName = semesterName
            }
          })

          this.pagination.total = response.data.total || 0
        } else {
          this.$message({ type: 'error', message: response?.msg || '获取数据失败' })
          this.tableData = []
          this.pagination.total = 0
        }
        this.loading = false
      }).catch(error => {
        this.$message({ type: 'error', message: '获取列表数据失败: ' + (error.message || '服务器错误') })
        this.tableData = []
        this.pagination.total = 0
        this.loading = false
      })
    },
    // 处理分页大小变化
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.currentPage = 1
      this.getList()
    },
    // 处理当前页变化
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.getList()
    },
    // 新增
    handleAdd() {
      this.dialogTitle = '新增基本素质测评成绩'
      this.dialogVisible = true
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.resetFields()
        }
        this.form = {
          evaluationId: null,
          studentId: '',
          studentName: '',
          dormitoryNo: '',
          addScore: 0,
          reduceScore: 0,
          addScoreRemark: '',
          reduceScoreRemark: '',
          evaluationPeriod: '',
          evaluationPeriodName: '',
          periodScore: 60,
          totalScore: 60
        }
      })
    },
    // 编辑
    async handleEdit(row) {
      this.dialogTitle = '编辑基本素质测评成绩'
      this.dialogVisible = true

      this.$nextTick(async() => {
        if (this.$refs.form) {
          this.$refs.form.resetFields()
        }

        // 复制行数据到表单
        this.form = Object.assign({}, row)

        // 设置学期名称，用于显示
        if (this.form.evaluationPeriod) {
          const semesterName = this.getSemesterName(this.form.evaluationPeriod)
          this.form.evaluationPeriodName = semesterName
        }

        // 通过API获取完整的学生信息
        if (this.form.studentId) {
          try {
            const response = await getStudentByIdApi(this.form.studentId)
            if (response && response.code === 200 && response.data) {
              this.form.studentName = response.data.name || ''
              this.form.dormitoryNo = response.data.dormitory || ''
            }
          } catch (error) {
            // 获取学生信息失败，使用表格中的数据作为备选
            this.form.studentName = row.studentName || ''
            this.form.dormitoryNo = row.dormitoryNo || ''
          }
        }

        // 确保数值字段有默认值
        this.form.addScore = this.form.addScore || 0
        this.form.reduceScore = this.form.reduceScore || 0
        this.form.periodScore = this.form.periodScore || 0
        this.form.totalScore = this.form.totalScore || 0
      })
    },
    // 删除
    handleDelete(row) {
      this.$confirm('确认删除该记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteQualityApi(row.evaluationId).then(response => {
          this.$message({ type: 'success', message: '删除成功' })

          // 如果当前页只有一条数据且不是第一页，则返回上一页
          if (this.tableData.length === 1 && this.pagination.currentPage > 1) {
            this.pagination.currentPage -= 1
          }

          this.getList()
        }).catch(error => {
          this.$message({ type: 'error', message: '删除失败: ' + (error.message || '服务器错误') })
        })
      }).catch(() => {})
    },
    // 弹窗关闭
    onClose() {
      // 弹窗会自动关闭，这里可以做清理工作
    },

    // 弹窗确认
    onConfirm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '正在保存...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })

          // 根据学期名称找到对应的学期ID
          if (this.form.evaluationPeriodName) {
            const semester = this.semesterOptions.find(item => item.semesterName === this.form.evaluationPeriodName)
            if (semester) {
              this.form.evaluationPeriod = semester.semesterId
            }
          }

          this.submitFormData(loading)
        }
      })
    },

    // 提交表单数据
    submitFormData(loading) {
      if (this.form.evaluationId) {
        // 编辑
        editQualityApi(this.form).then(response => {
          loading.close()
          this.$message({ type: 'success', message: '修改成功' })
          this.dialogVisible = false // 手动关闭弹窗
          this.getList()
        }).catch(error => {
          loading.close()
          this.$message({ type: 'error', message: '修改失败: ' + (error.message || '服务器错误') })
        })
      } else {
        // 新增
        addQualityApi(this.form).then(response => {
          loading.close()
          this.$message({ type: 'success', message: '新增成功' })
          this.dialogVisible = false // 手动关闭弹窗
          this.pagination.currentPage = 1
          this.getList()
        }).catch(error => {
          loading.close()
          this.$message({ type: 'error', message: '新增失败: ' + (error.message || '服务器错误') })
        })
      }
    },
    // 处理导出
    async handleExport() {
      try {
        const params = this.getExportParams()

        const loading = this.$loading({
          lock: true,
          text: '正在导出...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        await exportQualityApi(params)
        loading.close()
        this.$message({ type: 'success', message: '导出成功' })
      } catch (error) {
        this.$message({ type: 'error', message: '导出失败: ' + (error.message || '服务器错误') })
      }
    },

    // 处理导入
    handleImport() {
      // 创建文件输入元素
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = '.xlsx,.xls'
      input.onchange = async(event) => {
        const file = event.target.files[0]
        if (!file) return

        const formData = new FormData()
        formData.append('file', file)

        const loading = this.$loading({
          lock: true,
          text: '正在导入...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        try {
          await importQualityApi(formData)
          loading.close()
          this.$message({ type: 'success', message: '导入成功' })
          this.pagination.currentPage = 1
          this.getList()
        } catch (error) {
          loading.close()
          this.$message({ type: 'error', message: '导入失败: ' + (error.message || '服务器错误') })
        }
      }
      input.click()
    },

    // 加载所有学期选项（用于表单选择）
    async loadAllSemesterOptions() {
      try {
        const academicYearsRes = await getAcademicYearsApi()
        if (academicYearsRes && academicYearsRes.code === 200) {
          const academicYears = academicYearsRes.data || []
          const allSemesters = []

          for (const year of academicYears) {
            try {
              const semestersRes = await getSemestersByAcademicYearApi(year)
              if (semestersRes && semestersRes.code === 200) {
                allSemesters.push(...(semestersRes.data || []))
              }
            } catch (error) {
              // 获取学期数据失败，跳过该学年
            }
          }

          this.semesterOptions = allSemesters.map(item => ({
            semesterId: item.semesterId,
            semesterName: item.semesterName,
            academicYear: item.academicYear
          }))

          this.semesterOptions.sort((a, b) => b.semesterId - a.semesterId)
        }
      } catch (error) {
        // 加载学期选项失败，保持空数组
      }
    },

    // 获取学生选项
    getStudentOptions() {
      getStudentListApi({ currentPage: 1, pageSize: 1000 }).then(response => {
        if (response && response.code === 200 && response.data && response.data.records) {
          this.studentOptions = response.data.records

          // 更新学生姓名搜索字段选项
          const studentNameField = this.searchFields.find(field => field.prop === 'studentName')
          if (studentNameField) {
            studentNameField.options = this.studentOptions.map(student => ({
              label: student.name,
              value: student.name
            }))
          }

          // 更新学生学号搜索字段选项
          const studentIdField = this.searchFields.find(field => field.prop === 'studentId')
          if (studentIdField) {
            studentIdField.options = this.studentOptions.map(student => ({
              label: student.studentId,
              value: student.studentId
            }))
          }

          // 提取宿舍号选项
          const dormitories = [...new Set(response.data.records
            .map(student => student.dormitory)
            .filter(dormitory => dormitory && dormitory.trim() !== ''))]
          this.dormitoryOptions = dormitories.sort()

          // 更新宿舍号搜索字段选项
          const dormitoryField = this.searchFields.find(field => field.prop === 'dormitoryNo')
          if (dormitoryField) {
            dormitoryField.options = this.dormitoryOptions.map(dormitory => ({
              label: dormitory,
              value: dormitory
            }))
          }
        }
      }).catch(error => {
        // 获取学生数据失败，显示错误消息
        this.$message({ type: 'error', message: '获取学生数据失败' })
      })
    },
    // 获取学期名称
    getSemesterName(semesterId) {
      if (!semesterId) return ''

      // 尝试从学期选项中查找
      const semester = this.semesterOptions.find(item => String(item.semesterId) === String(semesterId))
      if (semester) {
        return semester.semesterName
      }

      // 如果在当前选项中找不到，返回未知学期
      return '未知学期'
    },
    // 注意：学期判断逻辑已移至后端处理，前端无需复杂判断
    // 获取学生信息
    getStudentInfo() {
      // 如果学号为空，则不进行查询
      if (!this.form.studentId || this.form.studentId.trim() === '') {
        this.form.studentName = ''
        this.form.dormitoryNo = ''
        return
      }

      // 从studentOptions中查找学生信息
      const student = this.studentOptions.find(s => s.studentId === this.form.studentId)
      if (student) {
        this.form.studentName = student.name || ''
        this.form.dormitoryNo = student.dormitory || ''
      } else {
        // 如果在选项中找不到，则调用API获取
        getStudentByIdApi(this.form.studentId).then(response => {
          if (response && response.code === 200 && response.data) {
            this.form.studentName = response.data.name || ''
            this.form.dormitoryNo = response.data.dormitory || ''
          } else {
            this.form.studentName = ''
            this.form.dormitoryNo = ''
          }
        }).catch(error => {
          // 获取学生信息失败，清空相关字段
          this.form.studentName = ''
          this.form.dormitoryNo = ''
        })
      }
    },
    // 刷新列表数据
    refreshList() {
      this.pagination.currentPage = 1
      this.getList()
    }
  }
}
</script>

<style scoped>
/* 表格样式已移至全局样式 */
</style>
