package com.example.dto.student;

import lombok.Data;

/**
 * 学生信息查询DTO类
 */
@Data
public class StudentsQueryDTO {

    /**
     * 学号
     */
    private String studentId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别
     */
    private String gender;

    /**
     * 所属班级代码
     */
    private String classCode;

    /**
     * 所属专业代码
     */
    private String majorCode;

    /**
     * 所属学院代码
     */
    private String collegeCode;

    /**
     * 学籍状态
     */
    private String status;

    /**
     * 入学年份
     */
    private Integer enrollmentYear;

    /**
     * 当前页码
     */
    private Integer current = 1;

    /**
     * 每页大小
     */
    private Integer size = 10;

    /**
     * 排序字段
     */
    private String sortField;

    /**
     * 排序方向
     */
    private String sortOrder;
}
