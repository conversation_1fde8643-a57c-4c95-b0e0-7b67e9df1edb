package com.itmk.web.sys_stuinfo.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.itmk.utils.ResultUtils;
import com.itmk.utils.ResultVo;
import com.itmk.web.sys_stuinfo.entity.StuParm;
import com.itmk.web.sys_stuinfo.entity.SysStudent;
import com.itmk.web.sys_stuinfo.service.SysStudentService;
import com.itmk.service.UniversalExcelService;
import com.itmk.service.BaseExcelService;
import com.itmk.utils.excel.ExcelConfigFactory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.springframework.http.HttpStatus;

@RestController
@RequestMapping("/api/stuinfo")
public class SysStudentController {

    @Autowired
    private SysStudentService sysStudentService;

    @Autowired
    private UniversalExcelService universalExcelService;

    /**
     * 获取学生列表（分页）
     *
     * @param parm 分页参数
     * @return 分页结果
     */
    @GetMapping("/list")
    public ResultVo getList(StuParm parm) {
        IPage<SysStudent> list = sysStudentService.list(parm);
        return ResultUtils.success("查询成功", list);
    }

    /**
     * 添加学生信息
     *
     * @param sysStudent 学生信息
     * @return 操作结果
     */
    @PostMapping
    @Transactional
    public ResultVo addUser(@RequestBody SysStudent sysStudent) {
        // 查询当前最大 ID
        Integer maxId = sysStudentService.getMaxId();
        int newId = (maxId != null) ? maxId + 1 : 1;
        sysStudent.setId(newId);

        // 检查学号是否重复
        if (sysStudentService.isDuplicate(sysStudent.getStudentId(), sysStudent.getName(), null)) {
            return ResultUtils.error("学号重复，请重新输入");
        }

        // 设置创建时间
        sysStudent.setCreatedAt(new Timestamp(System.currentTimeMillis()));

        // 存入数据库
        boolean save = sysStudentService.save(sysStudent);
        if (save) {
            return ResultUtils.success("添加学生信息成功");
        } else {
            return ResultUtils.error("添加学生信息失败");
        }
    }

    /**
     * 编辑学生信息
     *
     * @param sysStudent 学生信息
     * @return 操作结果
     */
    @PutMapping
    @Transactional
    public ResultVo editUser(@RequestBody SysStudent sysStudent) {
        // 检查学号是否重复（排除当前编辑的记录）
        if (sysStudentService.isDuplicate(sysStudent.getStudentId(), sysStudent.getName(), sysStudent.getId())) {
            return ResultUtils.error("学号或姓名重复，请重新输入");
        }

        // 设置更新时间
        sysStudent.setUpdatedAt(new Timestamp(System.currentTimeMillis()));

        // 更新数据库
        boolean update = sysStudentService.updateById(sysStudent);
        if (update) {
            return ResultUtils.success("编辑学生信息成功");
        } else {
            return ResultUtils.error("编辑学生信息失败");
        }
    }

    /**
     * 根据ID获取学生信息
     *
     * @param id 学生ID
     * @return 学生信息
     */
    @GetMapping("/{id}")
    public ResultVo getById(@PathVariable Integer id) {
        SysStudent student = sysStudentService.getById(id);
        if (student != null) {
            return ResultUtils.success("查询成功", student);
        } else {
            return ResultUtils.error("学生信息不存在");
        }
    }

    /**
     * 根据ID删除学生信息
     *
     * @param id 学生ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @Transactional
    public ResultVo delete(@PathVariable("id") Integer id) {
        boolean remove = sysStudentService.removeById(id);
        if (remove) {
            return ResultUtils.success("删除学生信息成功");
        } else {
            return ResultUtils.error("删除学生信息失败");
        }
    }
    /**
     * 导出学生信息
     *
     * @return Excel文件
     */
    @GetMapping("/export")
    public ResponseEntity<byte[]> exportStudents() {
        try {
            // 查询所有学生数据
            List<SysStudent> students = sysStudentService.list();

            // 生成Excel文件
            byte[] excelData = universalExcelService.exportExcel(students,
                ExcelConfigFactory.getSysStudentConfig());

            // 生成文件名
            String fileName = "学生信息_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", encodedFileName);
            headers.set("Cache-Control", "no-cache");

            return new ResponseEntity<>(excelData, headers, HttpStatus.OK);

        } catch (Exception e) {
            e.printStackTrace();
            String errorMessage = "导出失败: " + e.getMessage();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(errorMessage.getBytes());
        }
    }

    /**
     * 导入学生信息
     *
     * @param file Excel文件
     * @return 操作结果
     */
    @PostMapping("/import")
    @Transactional
    public ResultVo importStudents(@RequestParam("file") MultipartFile file) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                return ResultUtils.error("请选择要导入的文件");
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
                return ResultUtils.error("请上传Excel文件（.xlsx或.xls格式）");
            }

            // 导入Excel
            BaseExcelService.ImportResult<SysStudent> importResult =
                universalExcelService.importExcel(file, ExcelConfigFactory.getSysStudentTemplateConfig());

            if (!importResult.isSuccess()) {
                return ResultUtils.error(importResult.getMessage());
            }

            // 处理导入的数据 - 区分新增和更新
            List<SysStudent> newStudents = new ArrayList<>();
            List<SysStudent> updateStudents = new ArrayList<>();
            int newCount = 0;
            int updateCount = 0;

            for (SysStudent student : importResult.getSuccessData()) {
                // 根据学号查找是否存在此学生
                SysStudent existingStudent = sysStudentService.getByStudentId(student.getStudentId());

                if (existingStudent != null) {
                    // 学生已存在，更新记录
                    student.setId(existingStudent.getId());
                    student.setCreatedAt(existingStudent.getCreatedAt());
                    student.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
                    updateStudents.add(student);
                    updateCount++;
                } else {
                    // 学生不存在，新增记录
                    Integer maxId = sysStudentService.getMaxId();
                    int newId = (maxId != null) ? maxId + 1 : 1;
                    student.setId(newId);
                    student.setCreatedAt(new Timestamp(System.currentTimeMillis()));
                    newStudents.add(student);
                    newCount++;
                }
            }

            // 批量保存
            if (!newStudents.isEmpty()) {
                sysStudentService.saveBatch(newStudents);
            }
            if (!updateStudents.isEmpty()) {
                sysStudentService.updateBatchById(updateStudents);
            }

            // 构建结果消息
            String resultMessage = String.format("导入完成：新增 %d 条，更新 %d 条", newCount, updateCount);
            if (!importResult.getErrors().isEmpty()) {
                resultMessage += "，失败 " + importResult.getErrors().size() + " 条";
            }

            return ResultUtils.success(resultMessage, importResult);

        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtils.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 下载导入模板
     *
     * @return Excel模板文件
     */
    @GetMapping("/template")
    public ResponseEntity<byte[]> downloadTemplate() {
        try {
            // 使用专门的模板配置生成模板文件
            byte[] templateData = universalExcelService.generateTemplate(ExcelConfigFactory.getSysStudentTemplateConfig());

            // 生成文件名
            String fileName = "学生信息导入模板_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
            // 正确编码文件名，使用RFC 5987标准格式
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8")
                    .replaceAll("\\+", "%20"); // 将+号替换为%20

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            // 使用RFC 5987标准格式设置Content-Disposition
            headers.set("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
            headers.set("Cache-Control", "no-cache");

            return new ResponseEntity<>(templateData, headers, HttpStatus.OK);

        } catch (Exception e) {
            e.printStackTrace();
            String errorMessage = "模板下载失败: " + e.getMessage();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(errorMessage.getBytes());
        }
    }

    /**
     * 根据学号查询学生信息
     */
    @GetMapping("/getByStudentId")
    public ResultVo getByStudentId(@RequestParam("studentId") String studentId) {
        if (studentId == null || studentId.trim().isEmpty()) {
            return ResultUtils.error("学号不能为空");
        }

        SysStudent student = sysStudentService.getByStudentId(studentId);
        if (student == null) {
            return ResultUtils.error("未找到该学生信息");
        }

        return ResultUtils.success("查询成功", student);
    }
}