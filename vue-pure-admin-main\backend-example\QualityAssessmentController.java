package com.itmk.web.quality_assessment.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.itmk.web.quality_assessment.entity.QualityAssessment;
import com.itmk.web.quality_assessment.service.QualityAssessmentService;
import com.itmk.utils.ResultUtils;
import com.itmk.utils.ResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 基本素质测评控制器
 */
@RestController
@RequestMapping("/quality-assessment")
public class QualityAssessmentController {

    @Autowired
    private QualityAssessmentService qualityAssessmentService;

    /**
     * 分页查询基本素质测评列表
     */
    @PostMapping("/list")
    public ResultVo getList(@RequestBody QualityAssessmentQuery query) {
        // 构建分页对象
        IPage<QualityAssessment> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        
        // 查询数据
        IPage<QualityAssessment> list = qualityAssessmentService.getList(page, query);
        
        return ResultUtils.success("查询成功", list);
    }

    /**
     * 新增基本素质测评记录
     */
    @PostMapping("/add")
    public ResultVo add(@RequestBody QualityAssessment qualityAssessment) {
        // 计算总分
        calculateTotalScore(qualityAssessment);
        
        boolean save = qualityAssessmentService.save(qualityAssessment);
        if (save) {
            return ResultUtils.success("新增成功");
        }
        return ResultUtils.error("新增失败");
    }

    /**
     * 修改基本素质测评记录
     */
    @PostMapping("/update")
    public ResultVo update(@RequestBody QualityAssessment qualityAssessment) {
        // 计算总分
        calculateTotalScore(qualityAssessment);
        
        boolean update = qualityAssessmentService.updateById(qualityAssessment);
        if (update) {
            return ResultUtils.success("修改成功");
        }
        return ResultUtils.error("修改失败");
    }

    /**
     * 删除基本素质测评记录
     */
    @DeleteMapping("/delete/{evaluationId}")
    public ResultVo delete(@PathVariable("evaluationId") Long evaluationId) {
        boolean remove = qualityAssessmentService.removeById(evaluationId);
        if (remove) {
            return ResultUtils.success("删除成功");
        }
        return ResultUtils.error("删除失败");
    }

    /**
     * 导出基本素质测评数据
     */
    @PostMapping("/export")
    public void export(@RequestBody QualityAssessmentQuery query, HttpServletResponse response) {
        try {
            qualityAssessmentService.exportData(query, response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 下载导入模板
     */
    @PostMapping("/download-template")
    public void downloadTemplate(@RequestBody TemplateQuery query, HttpServletResponse response) {
        try {
            qualityAssessmentService.downloadTemplate(query.getClassCode(), query.getSemesterId(), response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入基本素质测评数据
     */
    @PostMapping("/import")
    public ResultVo importData(@RequestParam("file") MultipartFile file,
                              @RequestParam("classCode") String classCode,
                              @RequestParam("semesterId") String semesterId) {
        try {
            ImportResult result = qualityAssessmentService.importData(file, classCode, semesterId);
            return ResultUtils.success("导入完成", result);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtils.error("导入失败：" + e.getMessage());
        }
    }

    /**
     * 计算总分
     */
    private void calculateTotalScore(QualityAssessment qualityAssessment) {
        Double periodScore = qualityAssessment.getPeriodScore() != null ? qualityAssessment.getPeriodScore() : 0.0;
        Double addScore = qualityAssessment.getAddScore() != null ? qualityAssessment.getAddScore() : 0.0;
        Double reduceScore = qualityAssessment.getReduceScore() != null ? qualityAssessment.getReduceScore() : 0.0;
        
        Double totalScore = periodScore + addScore - reduceScore;
        qualityAssessment.setTotalScore(totalScore);
    }

    /**
     * 查询参数类
     */
    public static class QualityAssessmentQuery {
        private String classId;
        private String semesterId;
        private String studentName;
        private String studentId;
        private String dormitoryNo;
        private Integer currentPage = 1;
        private Integer pageSize = 10;

        // getter and setter methods...
        public String getClassId() { return classId; }
        public void setClassId(String classId) { this.classId = classId; }
        
        public String getSemesterId() { return semesterId; }
        public void setSemesterId(String semesterId) { this.semesterId = semesterId; }
        
        public String getStudentName() { return studentName; }
        public void setStudentName(String studentName) { this.studentName = studentName; }
        
        public String getStudentId() { return studentId; }
        public void setStudentId(String studentId) { this.studentId = studentId; }
        
        public String getDormitoryNo() { return dormitoryNo; }
        public void setDormitoryNo(String dormitoryNo) { this.dormitoryNo = dormitoryNo; }
        
        public Integer getCurrentPage() { return currentPage; }
        public void setCurrentPage(Integer currentPage) { this.currentPage = currentPage; }
        
        public Integer getPageSize() { return pageSize; }
        public void setPageSize(Integer pageSize) { this.pageSize = pageSize; }
    }

    /**
     * 模板查询参数类
     */
    public static class TemplateQuery {
        private String classCode;
        private String semesterId;

        public String getClassCode() { return classCode; }
        public void setClassCode(String classCode) { this.classCode = classCode; }
        
        public String getSemesterId() { return semesterId; }
        public void setSemesterId(String semesterId) { this.semesterId = semesterId; }
    }

    /**
     * 导入结果类
     */
    public static class ImportResult {
        private boolean success;
        private int totalRows;
        private int successRows;
        private int failedRows;
        private List<String> errorMessages;

        // getter and setter methods...
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public int getTotalRows() { return totalRows; }
        public void setTotalRows(int totalRows) { this.totalRows = totalRows; }
        
        public int getSuccessRows() { return successRows; }
        public void setSuccessRows(int successRows) { this.successRows = successRows; }
        
        public int getFailedRows() { return failedRows; }
        public void setFailedRows(int failedRows) { this.failedRows = failedRows; }
        
        public List<String> getErrorMessages() { return errorMessages; }
        public void setErrorMessages(List<String> errorMessages) { this.errorMessages = errorMessages; }
    }
}
