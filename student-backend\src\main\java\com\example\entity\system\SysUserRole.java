package com.example.entity.system;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 用户角色关联实体
 * 注意：此表使用复合主键(user_id, role_id)，不使用单独的自增ID
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("sys_user_role")
public class SysUserRole {

    /**
     * 用户ID（复合主键之一）
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 角色ID（复合主键之一）
     */
    @TableField("role_id")
    private Integer roleId;
}
