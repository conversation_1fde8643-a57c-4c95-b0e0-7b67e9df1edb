<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.itmk.web.school_college.mapper.SchoolCollegeMapper">
    <!-- 查询所有学院 -->
    <select id="selectList" resultType="com.itmk.web.school_college.entity.SchoolCollege">
        select * from school_college
    </select>

    <!-- 根据ID查询学院 -->
    <select id="selectById" resultType="com.itmk.web.school_college.entity.SchoolCollege">
        select * from school_college where college_id = #{collegeId}
    </select>

    <!-- 插入学院 -->
    <insert id="insertCollege" parameterType="com.itmk.web.school_college.entity.SchoolCollege">
        insert into school_college (college_name, order_num, create_time)
        values (#{collegeName}, #{orderNum}, #{createTime})
    </insert>

    <!-- 更新学院 -->
    <update id="updateCollege" parameterType="com.itmk.web.school_college.entity.SchoolCollege">
        update school_college
        set college_name = #{collegeName},
            order_num = #{orderNum},
            create_time = #{createTime}
        where college_id = #{collegeId}
    </update>

    <!-- 删除学院 -->
    <delete id="deleteCollege" parameterType="Long">
        delete from school_college where college_id = #{collegeId}
    </delete>

    <!-- 分页查询学院 -->
    <select id="getList" resultType="com.itmk.web.school_college.entity.SchoolCollege">
        select * from school_college
        where 1=1
        <if test="parm.collegeName != null and parm.collegeName !=''">
            and college_name like concat('%',#{parm.collegeName},'%')
        </if>
        order by order_num asc
    </select>
</mapper>