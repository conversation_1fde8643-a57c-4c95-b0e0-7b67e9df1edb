package com.example.entity.basic;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 学年学期实体类
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("semesters")
public class Semester {

    /**
     * 学期ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 学年，如2023-2024
     */
    @TableField("academic_year")
    private String academicYear;

    /**
     * 学期号，1或2
     */
    @TableField("semester_number")
    private Integer semesterNumber;

    /**
     * 学期名称
     */
    @TableField("semester_name")
    private String semesterName;

    /**
     * 开始日期
     */
    @TableField("start_date")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @TableField("end_date")
    private LocalDate endDate;

    /**
     * 是否当前学期
     */
    @TableField("is_current")
    private Boolean isCurrent;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
