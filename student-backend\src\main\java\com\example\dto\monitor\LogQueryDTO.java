package com.example.dto.monitor;

import lombok.Data;

/**
 * 日志查询DTO
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
public class LogQueryDTO {

    /**
     * 页码
     */
    private Integer page = 1;

    /**
     * 每页大小
     */
    private Integer size = 10;

    /**
     * 日志类型：1登录日志、2操作日志、3系统日志
     */
    private Integer logType;

    /**
     * 操作用户
     */
    private String username;

    /**
     * 操作描述
     */
    private String operation;

    /**
     * 状态：0失败、1成功
     */
    private Integer status;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 排序字段
     */
    private String orderBy = "create_time";

    /**
     * 排序方向：asc、desc
     */
    private String orderDirection = "desc";
}
