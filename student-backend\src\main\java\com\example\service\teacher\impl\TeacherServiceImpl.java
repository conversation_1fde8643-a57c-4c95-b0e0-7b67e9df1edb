package com.example.service.teacher.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.dto.teacher.TeacherQueryDTO;
import com.example.entity.teacher.Teacher;
import com.example.mapper.teacher.TeacherMapper;
import com.example.service.teacher.TeacherService;
import com.example.vo.teacher.TeacherVO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 教师服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Service
@RequiredArgsConstructor
public class TeacherServiceImpl implements TeacherService {

    private final TeacherMapper teacherMapper;

    @Override
    public IPage<TeacherVO> getTeacherList(TeacherQueryDTO queryDTO) {
        // 设置默认分页参数
        if (queryDTO.getCurrent() == null || queryDTO.getCurrent() <= 0) {
            queryDTO.setCurrent(1);
        }
        if (queryDTO.getSize() == null || queryDTO.getSize() <= 0) {
            queryDTO.setSize(10);
        }

        Page<TeacherVO> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        return teacherMapper.selectTeacherPage(page, queryDTO);
    }

    @Override
    public List<TeacherVO> getAllTeachers() {
        return teacherMapper.selectAllTeachers();
    }

    @Override
    public List<TeacherVO> getTeachersByCollegeCode(String collegeCode) {
        return teacherMapper.selectByCollegeCode(collegeCode);
    }

    @Override
    public TeacherVO getTeacherById(Integer id) {
        return teacherMapper.selectTeacherById(id);
    }

    @Override
    public TeacherVO getTeacherByCode(String teacherCode) {
        return teacherMapper.selectByTeacherCode(teacherCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveTeacher(TeacherVO teacherVO) {
        // 检查教师工号是否已存在
        if (checkTeacherCodeExists(teacherVO.getTeacherCode(), null)) {
            throw new RuntimeException("教师工号已存在");
        }

        Teacher teacher = new Teacher();
        BeanUtils.copyProperties(teacherVO, teacher);
        teacher.setCreatedAt(LocalDateTime.now());
        teacher.setUpdatedAt(LocalDateTime.now());

        teacherMapper.insert(teacher);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTeacher(TeacherVO teacherVO) {
        // 检查教师工号是否已存在（排除当前记录）
        if (checkTeacherCodeExists(teacherVO.getTeacherCode(), teacherVO.getId())) {
            throw new RuntimeException("教师工号已存在");
        }

        Teacher teacher = new Teacher();
        BeanUtils.copyProperties(teacherVO, teacher);
        teacher.setUpdatedAt(LocalDateTime.now());

        teacherMapper.updateById(teacher);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTeacher(Integer id) {
        teacherMapper.deleteById(id);
    }

    @Override
    public boolean checkTeacherCodeExists(String teacherCode, Integer excludeId) {
        return teacherMapper.checkTeacherCodeExists(teacherCode, excludeId) > 0;
    }
}
