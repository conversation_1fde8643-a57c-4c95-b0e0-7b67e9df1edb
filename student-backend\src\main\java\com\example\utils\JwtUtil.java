package com.example.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.auth0.jwt.interfaces.JWTVerifier;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * JWT工具类
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
@Component
public class JwtUtil {

    @Value("${jwt.secret:student-system-secret-key}")
    private String secret;

    @Value("${jwt.expiration:60}")
    private Long expiration;

    @Value("${jwt.issuer:student-system}")
    private String issuer;

    /**
     * 生成访问令牌
     *
     * @param username 用户名
     * @param userId   用户ID
     * @return 访问令牌
     */
    public String generateToken(String username, Integer userId) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration * 60 * 1000);

        return JWT.create()
                .withIssuer(issuer)
                .withSubject(username)
                .withClaim("userId", userId)
                .withClaim("username", username)
                .withIssuedAt(now)
                .withExpiresAt(expiryDate)
                .sign(Algorithm.HMAC256(secret));
    }

    /**
     * 生成刷新令牌
     *
     * @param username 用户名
     * @param userId   用户ID
     * @return 刷新令牌
     */
    public String generateRefreshToken(String username, Integer userId) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration * 60 * 1000 * 24 * 7); // 7天

        return JWT.create()
                .withIssuer(issuer)
                .withSubject(username)
                .withClaim("userId", userId)
                .withClaim("username", username)
                .withClaim("type", "refresh")
                .withIssuedAt(now)
                .withExpiresAt(expiryDate)
                .sign(Algorithm.HMAC256(secret));
    }

    /**
     * 验证令牌
     *
     * @param token 令牌
     * @return 是否有效
     */
    public boolean validateToken(String token) {
        try {
            JWTVerifier verifier = JWT.require(Algorithm.HMAC256(secret))
                    .withIssuer(issuer)
                    .build();
            verifier.verify(token);
            return true;
        } catch (JWTVerificationException e) {
            log.error("JWT验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 从令牌中获取用户名
     *
     * @param token 令牌
     * @return 用户名
     */
    public String getUsernameFromToken(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim("username").asString();
        } catch (Exception e) {
            log.error("从token获取用户名失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从令牌中获取用户ID
     *
     * @param token 令牌
     * @return 用户ID
     */
    public Integer getUserIdFromToken(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim("userId").asInt();
        } catch (Exception e) {
            log.error("从token获取用户ID失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从令牌中获取过期时间
     *
     * @param token 令牌
     * @return 过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getExpiresAt();
        } catch (Exception e) {
            log.error("从token获取过期时间失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 判断令牌是否过期
     *
     * @param token 令牌
     * @return 是否过期
     */
    public boolean isTokenExpired(String token) {
        Date expiration = getExpirationDateFromToken(token);
        return expiration != null && expiration.before(new Date());
    }

    /**
     * 刷新令牌
     *
     * @param token 原令牌
     * @return 新令牌
     */
    public String refreshToken(String token) {
        try {
            String username = getUsernameFromToken(token);
            Integer userId = getUserIdFromToken(token);
            if (username != null && userId != null) {
                return generateToken(username, userId);
            }
        } catch (Exception e) {
            log.error("刷新token失败: {}", e.getMessage());
        }
        return null;
    }
}
