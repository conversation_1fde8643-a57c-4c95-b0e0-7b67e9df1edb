package com.example.mapper.score;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.dto.score.GradeQueryDTO;
import com.example.entity.score.Grade;
import com.example.vo.educational.CourseVO;
import com.example.vo.score.GradeVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 期末成绩数据访问层
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Mapper
public interface FinalGradeMapper extends BaseMapper<Grade> {

    /**
     * 分页查询期末成绩列表
     */
    IPage<GradeVO> selectGradePage(Page<GradeVO> page, @Param("query") GradeQueryDTO query);

    /**
     * 根据班级查询期末成绩列表
     */
    List<GradeVO> selectGradesByClass(@Param("classCode") String classCode,
                                      @Param("semesterId") Integer semesterId);

    /**
     * 根据学生查询期末成绩列表
     */
    List<GradeVO> selectGradesByStudent(@Param("studentId") String studentId,
                                        @Param("semesterId") Integer semesterId);

    /**
     * 获取班级课程列表
     */
    List<CourseVO> selectCoursesByClassAndSemester(@Param("classCode") String classCode,
                                                   @Param("semesterId") Integer semesterId);

    /**
     * 获取学生基本信息和统计数据
     */
    List<Map<String, Object>> selectStudentsWithGrades(@Param("classCode") String classCode,
                                                        @Param("semesterId") Integer semesterId,
                                                        @Param("pageSize") Integer pageSize,
                                                        @Param("offset") Integer offset);

    /**
     * 获取学生成绩数据
     */
    List<Map<String, Object>> selectGradesByStudentAndClass(@Param("classCode") String classCode,
                                                             @Param("semesterId") Integer semesterId);

    /**
     * 获取班级学生总数
     */
    Integer selectStudentCountByClass(@Param("classCode") String classCode);

    /**
     * 计算学业成绩
     */
    List<Grade> calculateGpa(@Param("parm") Map<String, Object> parm);

    /**
     * 获取学生成绩绩点，用于横向展示
     */
    List<Map<String, Object>> getStudentGpa(@Param("semesterId") Integer semesterId);

    /**
     * 获取多个学期的学生成绩绩点
     */
    List<Map<String, Object>> getStudentGpaByIds(@Param("semesterIds") List<Integer> semesterIds);

    /**
     * 获取班级学生的学业成绩和绩点
     */
    List<Map<String, Object>> getStudentAcademicScoreAndGpa(@Param("classCode") String classCode,
                                                             @Param("semesterId") Integer semesterId);

    /**
     * 根据班级代码获取班级名称
     */
    String getClassNameByCode(@Param("classCode") String classCode);

    /**
     * 根据学期ID获取学期名称
     */
    String getSemesterNameById(@Param("semesterId") Integer semesterId);
}
