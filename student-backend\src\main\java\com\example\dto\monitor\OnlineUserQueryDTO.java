package com.example.dto.monitor;

import lombok.Data;

/**
 * 在线用户查询DTO
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
public class OnlineUserQueryDTO {

    /**
     * 页码
     */
    private Integer page = 1;

    /**
     * 每页大小
     */
    private Integer size = 10;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 排序字段
     */
    private String orderBy = "login_time";

    /**
     * 排序方向：asc、desc
     */
    private String orderDirection = "desc";
}
