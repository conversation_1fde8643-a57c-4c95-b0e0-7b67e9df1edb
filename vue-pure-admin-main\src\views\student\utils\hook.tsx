import dayjs from "dayjs";
import editForm from "../form.vue";
import { message } from "@/utils/message";
import { addDialog } from "@/components/ReDialog";
import { type PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h, toRaw } from "vue";
import { QueryFormProps } from "./types";
import {
  getStudentsPage,
  saveStudents,
  updateStudents,
  deleteStudents,
  type StudentsVO
} from "@/api/student/students";
import { getAllColleges, type CollegeItem } from "@/api/basic/college";
import { getAllMajors, type MajorItem } from "@/api/basic/major";
import { getClassesByMajorCode, type ClassesVO } from "@/api/basic/classes";

export function useStudent() {
  const form = reactive<QueryFormProps>({
    studentId: "",
    name: "",
    gender: "",
    collegeCode: "",
    majorCode: "",
    classCode: "",
    status: "",
    enrollmentDate: "",
    current: 1,
    size: 10
  });

  const dataList = ref<StudentsVO[]>([]);
  const loading = ref(true);
  const collegeOptions = ref<CollegeItem[]>([]);
  const majorOptions = ref<MajorItem[]>([]);
  const classOptions = ref<ClassesVO[]>([]);

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  });

  const columns = [
    {
      label: "序号",
      type: "index",
      width: 70
    },
    {
      label: "学号",
      prop: "studentId",
      minWidth: 120
    },
    {
      label: "姓名",
      prop: "name",
      minWidth: 100
    },
    {
      label: "性别",
      prop: "gender",
      width: 80
    },
    {
      label: "联系电话",
      prop: "phone",
      minWidth: 120
    },
    {
      label: "邮箱",
      prop: "email",
      minWidth: 150
    },
    {
      label: "所属学院",
      prop: "collegeName",
      minWidth: 120
    },
    {
      label: "所属专业",
      prop: "majorName",
      minWidth: 120
    },
    {
      label: "所属班级",
      prop: "className",
      minWidth: 120
    },
    {
      label: "入学日期",
      prop: "enrollmentDate",
      width: 120,
      formatter: ({ enrollmentDate }) => {
        if (!enrollmentDate) return "";
        // 格式化为YYYY-MM-DD
        return enrollmentDate.split('T')[0];
      }
    },
    {
      label: "学籍状态",
      prop: "status",
      width: 100,
      cellRenderer: ({ row, props }) => (
        <el-tag
          size={props.size}
          type={getStatusTagType(row.status)}
          effect="plain"
        >
          {row.status}
        </el-tag>
      )
    },
    {
      label: "创建时间",
      prop: "createdAt",
      minWidth: 180,
      formatter: ({ createdAt }) =>
        dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "操作",
      fixed: "right",
      width: 180,
      slot: "operation"
    }
  ];

  function getStatusTagType(status: string) {
    switch (status) {
      case "在读":
        return "success";
      case "休学":
        return "warning";
      case "退学":
        return "danger";
      case "毕业":
        return "info";
      default:
        return "info"; // 默认使用info类型而不是空字符串
    }
  }

  function handleSelectionChange(_val: StudentsVO[]) {
    // 处理选择变化
  }

  async function onSearch() {
    loading.value = true;
    try {
      const searchParams = {
        ...toRaw(form)
      };
      const response = await getStudentsPage(searchParams);
      if (response.success && response.data) {
        dataList.value = response.data.records || [];
        pagination.total = response.data.total || 0;
        pagination.currentPage = response.data.current || 1;
        pagination.pageSize = response.data.size || 10;
      } else {
        message(response.message, { type: "error" });
      }
    } catch (error) {
      message("网络错误，请稍后重试", { type: "error" });
    } finally {
      loading.value = false;
    }
  }

  const resetForm = (formEl: any) => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  // 加载学院选项
  async function loadCollegeOptions() {
    try {
      const response = await getAllColleges();
      if (response.success && response.data) {
        collegeOptions.value = response.data;
      }
    } catch (error) {
      // 静默处理错误
    }
  }

  // 专业变化处理
  async function onMajorChange(majorCode: string) {
    form.classCode = ""; // 清空班级选择
    classOptions.value = []; // 清空班级选项

    if (!majorCode) return;

    try {
      const response = await getClassesByMajorCode(majorCode);
      if (response.success && response.data) {
        classOptions.value = response.data;
      }
    } catch (error) {
      // 静默处理错误
    }
  }

  function openDialog(title = "新增", row?: StudentsVO) {
    const formRef = ref();

    addDialog({
      title: `${title}学生`,
      props: {
        formInline: {
          id: row?.id ?? null,
          studentId: row?.studentId ?? "",
          name: row?.name ?? "",
          gender: row?.gender ?? "",
          birthDate: row?.birthDate ?? "",
          phone: row?.phone ?? "",
          email: row?.email ?? "",
          collegeCode: row?.collegeCode ?? "",
          majorCode: row?.majorCode ?? "",
          classCode: row?.classCode ?? "",
          enrollmentDate: row?.enrollmentDate ?? "",
          status: row?.status ?? "在读"
        }
      },
      width: "46%",
      draggable: true,
      fullscreenIcon: true,
      closeOnClickModal: false,
      contentRenderer: () => h(editForm, { ref: formRef }),
      beforeSure: (done, { options }) => {
        const FormRef = formRef.value?.getRef();
        const curData = options.props.formInline as any;

        function chores(successMessage: string) {
          message(successMessage, { type: "success" });
          done(); // 关闭弹框
          onSearch(); // 刷新表格数据
        }

        FormRef?.validate((valid: boolean) => {
          if (valid) {
            // 表单规则校验通过
            if (title === "新增") {
              saveStudents(curData).then((res: any) => {
                if (res.success) {
                  chores(res.message);
                } else {
                  message(res.message, { type: "error" });
                }
              }).catch(() => {
                message("网络错误，请稍后重试", { type: "error" });
              });
            } else {
              updateStudents(curData).then((res: any) => {
                if (res.success) {
                  chores(res.message);
                } else {
                  message(res.message, { type: "error" });
                }
              }).catch(() => {
                message("网络错误，请稍后重试", { type: "error" });
              });
            }
          }
        });
      }
    });
  }

  async function handleDelete(row: StudentsVO) {
    try {
      const res = await deleteStudents(row.id);
      if (res.success) {
        message(res.message, { type: "success" });
        onSearch();
      } else {
        message(res.message, { type: "error" });
      }
    } catch (error) {
      message("删除失败", { type: "error" });
    }
  }

  function handleSizeChange(val: number) {
    form.size = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    form.current = val;
    onSearch();
  }

  onMounted(() => {
    onSearch();
    loadCollegeOptions();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    collegeOptions,
    majorOptions,
    classOptions,
    onSearch,
    resetForm,
    onMajorChange,
    openDialog,
    handleDelete,
    handleSizeChange,
    handleCurrentChange,
    handleSelectionChange
  };
}
