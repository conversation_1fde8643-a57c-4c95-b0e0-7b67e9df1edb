package com.itmk.task;

import com.itmk.utils.TaskExecutionUtils;
import com.itmk.web.common.service.SemesterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 学期自动创建定时任务
 * 在每学期快要结束时创建下一学期的数据
 *
 * 执行策略：
 * - 第一学期结束前（1月15日）创建第二学期
 * - 第二学期结束前（7月15日）创建下一学年第一学期
 * - 新学年开始时（8月1日）确保数据完整
 * - 第二学期开始时（2月1日）确保数据完整
 */
@Component
public class SemesterAutoCreateTask {

    private static final Logger logger = LoggerFactory.getLogger(SemesterAutoCreateTask.class);

    @Autowired
    private SemesterService semesterService;

    /**
     * 第一学期结束前创建下一学期
     * 每年1月15日凌晨2点执行（第一学期8月-1月，在1月15日提前创建第二学期）
     * cron表达式：0 0 2 15 1 ?
     */
    @Scheduled(cron = "0 0 2 15 1 ?")
    public void createSecondSemester() {
        TaskExecutionUtils.executeTask(
            "创建第二学期数据",
            "第一学期即将结束，开始创建第二学期数据",
            () -> semesterService.forceAutoCreateSemesters(),
            logger
        );
    }

    /**
     * 第二学期结束前创建下一学年第一学期
     * 每年7月15日凌晨2点执行（第二学期2月-7月，在7月15日提前创建下一学年第一学期）
     * cron表达式：0 0 2 15 7 ?
     */
    @Scheduled(cron = "0 0 2 15 7 ?")
    public void createNextYearFirstSemester() {
        TaskExecutionUtils.executeTask(
            "创建下一学年第一学期数据",
            "第二学期即将结束，开始创建下一学年第一学期数据",
            () -> semesterService.forceAutoCreateSemesters(),
            logger
        );
    }

    /**
     * 新学年开始时确保数据完整
     * 每年8月1日凌晨3点执行（新学年开始，确保当前学年数据完整）
     * cron表达式：0 0 3 1 8 ?
     */
    @Scheduled(cron = "0 0 3 1 8 ?")
    public void ensureNewAcademicYearData() {
        TaskExecutionUtils.executeTask(
            "新学年数据完整性检查",
            "新学年开始，确保学期数据完整",
            () -> semesterService.forceAutoCreateSemesters(),
            logger
        );
    }

    /**
     * 新学期开始时确保数据完整
     * 每年2月1日凌晨3点执行（第二学期开始，确保当前学期数据完整）
     * cron表达式：0 0 3 1 2 ?
     */
    @Scheduled(cron = "0 0 3 1 2 ?")
    public void ensureSecondSemesterData() {
        TaskExecutionUtils.executeTask(
            "第二学期数据完整性检查",
            "第二学期开始，确保学期数据完整",
            () -> semesterService.forceAutoCreateSemesters(),
            logger
        );
    }


}
