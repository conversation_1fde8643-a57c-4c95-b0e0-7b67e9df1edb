package com.itmk.utils.excel;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.function.Function;

/**
 * Excel操作工具类
 * 提供通用的Excel读写、样式设置等功能
 */
public class ExcelUtils {

    /**
     * 创建表头样式
     */
    public static CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();

        // 设置背景色
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置对齐方式
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置字体
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 12);
        style.setFont(font);

        return style;
    }

    /**
     * 创建数据样式
     */
    public static CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置对齐方式
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置字体
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 11);
        style.setFont(font);

        return style;
    }

    /**
     * 设置单元格值
     */
    public static void setCellValue(Cell cell, Object value, CellStyle style) {
        if (cell == null) {
            return;
        }

        if (style != null) {
            cell.setCellStyle(style);
        }

        if (value == null) {
            cell.setCellValue("");
            return;
        }

        if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Number) {
            cell.setCellValue(((Number) value).doubleValue());
        } else if (value instanceof Date) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            cell.setCellValue(sdf.format((Date) value));
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else {
            cell.setCellValue(value.toString());
        }
    }

    /**
     * 获取单元格值作为字符串
     */
    public static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    return sdf.format(cell.getDateCellValue());
                } else {
                    // 处理数字，避免科学计数法
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == (long) numericValue) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return cell.getStringCellValue().trim();
                } catch (Exception e) {
                    try {
                        return String.valueOf(cell.getNumericCellValue());
                    } catch (Exception e2) {
                        return "";
                    }
                }
            case BLANK:
            default:
                return "";
        }
    }

    /**
     * 自动调整列宽
     */
    public static void autoSizeColumns(Sheet sheet, int columnCount) {
        for (int i = 0; i < columnCount; i++) {
            sheet.autoSizeColumn(i);
            // 设置最大宽度，避免列宽过大
            int columnWidth = sheet.getColumnWidth(i);
            if (columnWidth > 15000) { // 大约75个字符宽度
                sheet.setColumnWidth(i, 15000);
            }
            // 设置最小宽度
            if (columnWidth < 2000) { // 大约10个字符宽度
                sheet.setColumnWidth(i, 2000);
            }
        }
    }

    /**
     * 验证Excel文件格式
     */
    public static boolean isValidExcelFile(String fileName) {
        if (!StringUtils.hasText(fileName)) {
            return false;
        }

        String lowerCaseFileName = fileName.toLowerCase();
        return lowerCaseFileName.endsWith(".xlsx") || lowerCaseFileName.endsWith(".xls");
    }

    /**
     * 创建Excel模板
     */
    public static byte[] createTemplate(String[] headers, String sheetName) throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet(sheetName);

        // 创建表头样式
        CellStyle headerStyle = createHeaderStyle(workbook);

        // 创建表头行
        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            setCellValue(cell, headers[i], headerStyle);
        }

        // 自动调整列宽
        autoSizeColumns(sheet, headers.length);

        // 转换为字节数组
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            workbook.write(out);
            workbook.close();
            return out.toByteArray();
        }
    }

    /**
     * 通用Excel导出方法
     */
    public static <T> byte[] exportToExcel(List<T> dataList, String[] headers, String sheetName,
                                          Function<T, Object[]> dataExtractor) throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet(sheetName);

        // 创建样式
        CellStyle headerStyle = createHeaderStyle(workbook);
        CellStyle dataStyle = createDataStyle(workbook);

        // 创建表头
        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            setCellValue(cell, headers[i], headerStyle);
        }

        // 填充数据
        for (int i = 0; i < dataList.size(); i++) {
            Row row = sheet.createRow(i + 1);
            Object[] rowData = dataExtractor.apply(dataList.get(i));

            for (int j = 0; j < rowData.length && j < headers.length; j++) {
                Cell cell = row.createCell(j);
                setCellValue(cell, rowData[j], dataStyle);
            }
        }

        // 自动调整列宽
        autoSizeColumns(sheet, headers.length);

        // 转换为字节数组
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            workbook.write(out);
            workbook.close();
            return out.toByteArray();
        }
    }

    /**
     * 从输入流读取Excel数据
     */
    public static <T> List<T> importFromExcel(InputStream inputStream, String[] expectedHeaders,
                                            Function<Object[], T> dataBuilder) throws IOException {
        // 这里可以实现通用的Excel导入逻辑
        // 由于代码较长，建议使用ExcelConfig配合BaseExcelService来实现
        throw new UnsupportedOperationException("请使用BaseExcelService或UniversalExcelService进行Excel导入");
    }

    /**
     * 验证Excel表头
     */
    public static boolean validateHeaders(Row headerRow, String[] expectedHeaders) {
        if (headerRow == null || expectedHeaders == null) {
            return false;
        }

        if (headerRow.getLastCellNum() < expectedHeaders.length) {
            return false;
        }

        for (int i = 0; i < expectedHeaders.length; i++) {
            Cell cell = headerRow.getCell(i);
            String cellValue = getCellValueAsString(cell);
            if (!expectedHeaders[i].equals(cellValue)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查行是否为空
     */
    public static boolean isEmptyRow(Row row) {
        if (row == null) {
            return true;
        }

        for (int i = 0; i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                String cellValue = getCellValueAsString(cell);
                if (StringUtils.hasText(cellValue)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 获取工作表的有效数据行数（排除空行）
     */
    public static int getValidRowCount(Sheet sheet) {
        if (sheet == null) {
            return 0;
        }

        int count = 0;
        int lastRowNum = sheet.getLastRowNum();

        for (int i = 1; i <= lastRowNum; i++) { // 从第二行开始（跳过表头）
            Row row = sheet.getRow(i);
            if (!isEmptyRow(row)) {
                count++;
            }
        }

        return count;
    }

    /**
     * 创建错误信息样式
     */
    public static CellStyle createErrorStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();

        // 设置背景色为红色
        style.setFillForegroundColor(IndexedColors.LIGHT_ORANGE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置字体颜色为红色
        Font font = workbook.createFont();
        font.setColor(IndexedColors.RED.getIndex());
        style.setFont(font);

        return style;
    }
}
