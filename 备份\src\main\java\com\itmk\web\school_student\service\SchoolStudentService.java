package com.itmk.web.school_student.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.itmk.web.school_student.entity.SchoolStudent;
import com.itmk.web.school_student.entity.StuParm;


public interface SchoolStudentService extends IService<SchoolStudent> {
    //新增
    void addStu(SchoolStudent schoolStudent);
    //编辑
    void editStu(SchoolStudent schoolStudent);
    //根据用户id查询用户信息
    SchoolStudent getStuById(Long stuId);
    //列表
    IPage<SchoolStudent> getList(StuParm parm);
    //删除
    void deleteStu(Long stuId);
}
