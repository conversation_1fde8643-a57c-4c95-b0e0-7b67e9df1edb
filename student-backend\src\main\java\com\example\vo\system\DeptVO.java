package com.example.vo.system;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 部门响应VO
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
public class DeptVO {

    /**
     * 部门ID
     */
    private Integer id;

    /**
     * 父部门ID
     */
    private Integer parentId;

    /**
     * 部门名称
     */
    @NotBlank(message = "部门名称不能为空")
    private String deptName;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 负责人
     */
    private String leader;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 状态：0禁用、1启用
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 子部门列表
     */
    private List<DeptVO> children;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
