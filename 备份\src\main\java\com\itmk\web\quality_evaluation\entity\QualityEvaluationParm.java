package com.itmk.web.quality_evaluation.entity;

import lombok.Data;
import java.util.List;

/**
 * 基本素质测评成绩查询参数
 */
@Data
public class QualityEvaluationParm {
    
    /**
     * 当前页码
     */
    private Long currentPage;
    
    /**
     * 每页显示条数
     */
    private Long pageSize;
    
    /**
     * 学生ID
     */
    private String studentId;
    
    /**
     * 学生姓名
     */
    private String studentName;
    
    /**
     * 宿舍号
     */
    private String dormitoryNo;
    
    /**
     * 评分学期ID
     */
    private String evaluationPeriod;
    
    /**
     * 评分学期ID列表（用于多选查询）
     */
    private List<String> evaluationPeriods;
    
    /**
     * 上一学期ID（用于第二学期时查询上一学期的分数）
     */
    private String prevSemesterId;
} 