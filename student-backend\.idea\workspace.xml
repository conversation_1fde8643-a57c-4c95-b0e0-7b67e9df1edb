<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="03c861cf-3353-4dc1-91f4-5f679a2dd7d3" name="更改" comment="第一版">
      <change beforePath="$PROJECT_DIR$/.idea/sqldialects.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/sqldialects.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
      <option value="module" />
      <option value="repository" />
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\Exploitation\maven\apache-maven-3.8.1" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\Exploitation\maven\apache-maven-3.8.1\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2zrBmtOJQsXPhu9kpyK6koS6vVN" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;HTTP 请求.generated-requests | #8.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ExcelFrameworkTest.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.GradeInputServiceIntegrationTest.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.UniversalExcelServiceTest.executor&quot;: &quot;Run&quot;,
    &quot;Maven.student-management-system [install].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.StudentBackendApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.StudentManagementApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.StudentMangerApplication.executor&quot;: &quot;Run&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/项目/student/student-backend&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
    &quot;数据库脚本.quick_optimize_grades.sql.executor&quot;: &quot;Run&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ],
    &quot;RunConfigurationTargetLRU&quot;: [
      &quot;cab7755e-7150-4471-a9b5-274e8b1663b6/schema/\&quot;students\&quot;&quot;,
      &quot;cab7755e-7150-4471-a9b5-274e8b1663b6&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\项目\student\student-backend" />
      <recent name="E:\项目\student\student-backend\src\main\java\com\itmk\dto\system" />
      <recent name="E:\项目\student\student-backend\sql" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\项目\student\student-backend" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="StudentManagementApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="student-management-system" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.StudentManagementApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.example.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.StudentManagementApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="03c861cf-3353-4dc1-91f4-5f679a2dd7d3" name="更改" comment="" />
      <created>1740283200139</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1740283200139</updated>
      <workItem from="1740283202038" duration="41000" />
      <workItem from="1741758783044" duration="366000" />
      <workItem from="1752479474449" duration="15122000" />
      <workItem from="1752555416359" duration="3782000" />
      <workItem from="1752566058738" duration="74000" />
      <workItem from="1752566621075" duration="1114000" />
      <workItem from="1752575843783" duration="46000" />
      <workItem from="1752577466683" duration="1197000" />
      <workItem from="1752579805627" duration="68000" />
      <workItem from="1752580860798" duration="10407000" />
      <workItem from="1752595966516" duration="2000" />
      <workItem from="1752626991764" duration="5000" />
      <workItem from="1752652233345" duration="2238000" />
      <workItem from="1752679565441" duration="3947000" />
      <workItem from="1752730793416" duration="14457000" />
      <workItem from="1752807317113" duration="10011000" />
      <workItem from="1752840721134" duration="2071000" />
      <workItem from="1752893223379" duration="529000" />
      <workItem from="1752894375507" duration="104000" />
      <workItem from="1752894489804" duration="4812000" />
      <workItem from="1752899395736" duration="2171000" />
      <workItem from="1752901579693" duration="8614000" />
      <workItem from="1752910330882" duration="2064000" />
      <workItem from="1752913131165" duration="23440000" />
      <workItem from="1752976037918" duration="6960000" />
      <workItem from="1752984510612" duration="632000" />
      <workItem from="1752985373239" duration="28455000" />
      <workItem from="1753057566777" duration="27734000" />
      <workItem from="1753147244615" duration="31063000" />
      <workItem from="1753197421558" duration="245000" />
      <workItem from="1753197683308" duration="268000" />
      <workItem from="1753197965185" duration="725000" />
      <workItem from="1753198700724" duration="6006000" />
      <workItem from="1753204800121" duration="754000" />
      <workItem from="1753205571030" duration="1609000" />
      <workItem from="1753234304864" duration="2167000" />
      <workItem from="1753241640893" duration="100000" />
      <workItem from="1753241752077" duration="22509000" />
      <workItem from="1753324169146" duration="676000" />
      <workItem from="1753325376957" duration="43000" />
      <workItem from="1753325526778" duration="3643000" />
      <workItem from="1753339123816" duration="7612000" />
      <workItem from="1753349565051" duration="2608000" />
      <workItem from="1753354708604" duration="7566000" />
      <workItem from="1753365820207" duration="4232000" />
      <workItem from="1753373520916" duration="1220000" />
      <workItem from="1753375411688" duration="816000" />
      <workItem from="1753378169797" duration="147000" />
      <workItem from="1753407375699" duration="979000" />
      <workItem from="1753410319472" duration="160000" />
      <workItem from="1753419607748" duration="11667000" />
      <workItem from="1753436941129" duration="20982000" />
      <workItem from="1753497501087" duration="30525000" />
      <workItem from="1753583405201" duration="15946000" />
      <workItem from="1753609622801" duration="15000" />
      <workItem from="1753609819822" duration="11277000" />
      <workItem from="1753632924507" duration="611000" />
      <workItem from="1753658916758" duration="12332000" />
      <workItem from="1753683937213" duration="2174000" />
      <workItem from="1753695242349" duration="2754000" />
      <workItem from="1753705118439" duration="9013000" />
      <workItem from="1753749737730" duration="18232000" />
      <workItem from="1753781092717" duration="874000" />
      <workItem from="1753783356983" duration="1469000" />
      <workItem from="1753784849769" duration="20001000" />
      <workItem from="1753807342306" duration="720000" />
      <workItem from="1753853950963" duration="3008000" />
      <workItem from="1753858473363" duration="13237000" />
      <workItem from="1753879658970" duration="982000" />
      <workItem from="1753883187309" duration="938000" />
      <workItem from="1753887470237" duration="5413000" />
      <workItem from="1753931808046" duration="644000" />
      <workItem from="1753943586381" duration="15381000" />
      <workItem from="1754018079964" duration="8544000" />
    </task>
    <task id="LOCAL-00001" summary="第一版">
      <option name="closed" value="true" />
      <created>1753198346358</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753198346358</updated>
    </task>
    <task id="LOCAL-00002" summary="第一版">
      <option name="closed" value="true" />
      <created>1753198567640</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753198567640</updated>
    </task>
    <option name="localTasksCounter" value="3" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="CUSTOM_BOOLEAN_PROPERTIES">
                <map>
                  <entry key="Show.Git.Branches" value="true" />
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <ignored-roots>
      <path value="$PROJECT_DIR$" />
    </ignored-roots>
    <MESSAGE value="第一版" />
    <option name="LAST_COMMIT_MESSAGE" value="第一版" />
  </component>
</project>