package com.itmk.utils;

import org.slf4j.Logger;

import java.util.concurrent.Callable;
import java.util.function.Supplier;

/**
 * 任务执行工具类
 * 提供统一的任务执行模板，包含标准的异常处理和日志记录
 * 
 * <AUTHOR>
 * @since 2025-07-19
 */
public class TaskExecutionUtils {
    
    /**
     * 执行无返回值的任务（基础版本）
     * 
     * @param taskName 任务名称
     * @param task 要执行的任务
     * @param logger 日志记录器
     */
    public static void executeTask(String taskName, Runnable task, Logger logger) {
        try {
            LogUtils.logTaskStart(logger, taskName);
            task.run();
            LogUtils.logTaskSuccess(logger, taskName);
        } catch (Exception e) {
            LogUtils.logTaskError(logger, taskName, e.getMessage(), e);
        }
    }
    
    /**
     * 执行无返回值的任务（带描述）
     * 
     * @param taskName 任务名称
     * @param description 任务描述
     * @param task 要执行的任务
     * @param logger 日志记录器
     */
    public static void executeTask(String taskName, String description, Runnable task, Logger logger) {
        try {
            LogUtils.logTaskStart(logger, taskName, description);
            task.run();
            LogUtils.logTaskSuccess(logger, taskName);
        } catch (Exception e) {
            LogUtils.logTaskError(logger, taskName, e.getMessage(), e);
        }
    }
    
    /**
     * 执行有返回值的任务
     * 
     * @param taskName 任务名称
     * @param task 要执行的任务
     * @param logger 日志记录器
     * @param <T> 返回值类型
     * @return 任务执行结果，如果执行失败返回null
     */
    public static <T> T executeTask(String taskName, Callable<T> task, Logger logger) {
        try {
            LogUtils.logTaskStart(logger, taskName);
            T result = task.call();
            LogUtils.logTaskSuccess(logger, taskName);
            return result;
        } catch (Exception e) {
            LogUtils.logTaskError(logger, taskName, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 执行有返回值的任务（带描述）
     * 
     * @param taskName 任务名称
     * @param description 任务描述
     * @param task 要执行的任务
     * @param logger 日志记录器
     * @param <T> 返回值类型
     * @return 任务执行结果，如果执行失败返回null
     */
    public static <T> T executeTask(String taskName, String description, Callable<T> task, Logger logger) {
        try {
            LogUtils.logTaskStart(logger, taskName, description);
            T result = task.call();
            LogUtils.logTaskSuccess(logger, taskName);
            return result;
        } catch (Exception e) {
            LogUtils.logTaskError(logger, taskName, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 执行有返回值的任务（使用Supplier）
     * 
     * @param taskName 任务名称
     * @param task 要执行的任务
     * @param logger 日志记录器
     * @param <T> 返回值类型
     * @return 任务执行结果，如果执行失败返回null
     */
    public static <T> T executeTask(String taskName, Supplier<T> task, Logger logger) {
        try {
            LogUtils.logTaskStart(logger, taskName);
            T result = task.get();
            LogUtils.logTaskSuccess(logger, taskName);
            return result;
        } catch (Exception e) {
            LogUtils.logTaskError(logger, taskName, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 执行任务并返回执行结果对象
     * 
     * @param taskName 任务名称
     * @param task 要执行的任务
     * @param logger 日志记录器
     * @return 任务执行结果对象
     */
    public static TaskResult executeTaskWithResult(String taskName, Runnable task, Logger logger) {
        long startTime = System.currentTimeMillis();
        try {
            LogUtils.logTaskStart(logger, taskName);
            task.run();
            long executionTime = System.currentTimeMillis() - startTime;
            LogUtils.logTaskSuccess(logger, taskName);
            LogUtils.logPerformance(logger, taskName, executionTime);
            return TaskResult.success(taskName, executionTime);
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            LogUtils.logTaskError(logger, taskName, e.getMessage(), e);
            return TaskResult.failure(taskName, executionTime, e);
        }
    }
    
    /**
     * 执行有返回值的任务并返回执行结果对象
     * 
     * @param taskName 任务名称
     * @param task 要执行的任务
     * @param logger 日志记录器
     * @param <T> 返回值类型
     * @return 任务执行结果对象
     */
    public static <T> TaskResult<T> executeTaskWithResult(String taskName, Callable<T> task, Logger logger) {
        long startTime = System.currentTimeMillis();
        try {
            LogUtils.logTaskStart(logger, taskName);
            T result = task.call();
            long executionTime = System.currentTimeMillis() - startTime;
            LogUtils.logTaskSuccess(logger, taskName);
            LogUtils.logPerformance(logger, taskName, executionTime);
            return TaskResult.success(taskName, executionTime, result);
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            LogUtils.logTaskError(logger, taskName, e.getMessage(), e);
            return TaskResult.failure(taskName, executionTime, e);
        }
    }
    
    /**
     * 安全执行任务（不抛出异常）
     * 
     * @param taskName 任务名称
     * @param task 要执行的任务
     * @param logger 日志记录器
     * @return 是否执行成功
     */
    public static boolean safeExecuteTask(String taskName, Runnable task, Logger logger) {
        try {
            LogUtils.logTaskStart(logger, taskName);
            task.run();
            LogUtils.logTaskSuccess(logger, taskName);
            return true;
        } catch (Exception e) {
            LogUtils.logTaskError(logger, taskName, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 重试执行任务
     * 
     * @param taskName 任务名称
     * @param task 要执行的任务
     * @param logger 日志记录器
     * @param maxRetries 最大重试次数
     * @param retryDelayMs 重试间隔（毫秒）
     * @return 是否执行成功
     */
    public static boolean executeTaskWithRetry(String taskName, Runnable task, Logger logger, 
                                             int maxRetries, long retryDelayMs) {
        int attempts = 0;
        while (attempts <= maxRetries) {
            try {
                if (attempts == 0) {
                    LogUtils.logTaskStart(logger, taskName);
                } else {
                    logger.info("{} {} 第{}次重试...", DateTimeUtils.getLogTimeString(), taskName, attempts);
                }
                
                task.run();
                LogUtils.logTaskSuccess(logger, taskName);
                return true;
            } catch (Exception e) {
                attempts++;
                if (attempts > maxRetries) {
                    LogUtils.logTaskError(logger, taskName, 
                        String.format("重试%d次后仍然失败: %s", maxRetries, e.getMessage()), e);
                    return false;
                } else {
                    logger.warn("{} {} 第{}次执行失败，将在{}ms后重试: {}", 
                        DateTimeUtils.getLogTimeString(), taskName, attempts, retryDelayMs, e.getMessage());
                    try {
                        Thread.sleep(retryDelayMs);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        LogUtils.logTaskError(logger, taskName, "任务重试被中断", ie);
                        return false;
                    }
                }
            }
        }
        return false;
    }
    
    /**
     * 任务执行结果类
     */
    public static class TaskResult<T> {
        private final String taskName;
        private final boolean success;
        private final long executionTime;
        private final T result;
        private final Exception exception;
        
        private TaskResult(String taskName, boolean success, long executionTime, T result, Exception exception) {
            this.taskName = taskName;
            this.success = success;
            this.executionTime = executionTime;
            this.result = result;
            this.exception = exception;
        }
        
        public static TaskResult<Void> success(String taskName, long executionTime) {
            return new TaskResult<>(taskName, true, executionTime, null, null);
        }
        
        public static <T> TaskResult<T> success(String taskName, long executionTime, T result) {
            return new TaskResult<>(taskName, true, executionTime, result, null);
        }
        
        public static <T> TaskResult<T> failure(String taskName, long executionTime, Exception exception) {
            return new TaskResult<>(taskName, false, executionTime, null, exception);
        }
        
        // Getters
        public String getTaskName() { return taskName; }
        public boolean isSuccess() { return success; }
        public long getExecutionTime() { return executionTime; }
        public T getResult() { return result; }
        public Exception getException() { return exception; }
    }
}
