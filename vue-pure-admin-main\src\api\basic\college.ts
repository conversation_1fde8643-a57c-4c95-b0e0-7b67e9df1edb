import { http } from "@/utils/http";

export interface CollegeItem {
  id?: number;
  collegeCode: string;
  collegeName: string;
  description?: string;
  createTime?: string;
  updateTime?: string;
}

export interface CollegeQueryDTO {
  collegeName?: string;
  collegeCode?: string;
  current?: number;
  size?: number;
}

export interface CollegePageVO {
  records: CollegeItem[];
  total: number;
  current: number;
  size: number;
}

/** 获取学院分页列表 */
export const getCollegeList = (data?: CollegeQueryDTO) => {
  return http.request<CollegePageVO>("post", "/api/basic/college/list", { data });
};

/** 获取所有学院列表 */
export const getAllColleges = () => {
  return http.request<CollegeItem[]>("get", "/api/basic/college/all");
};

/** 保存学院 */
export const saveCollege = (data: CollegeItem) => {
  return http.request("post", "/api/basic/college/save", { data });
};

/** 更新学院 */
export const updateCollege = (data: CollegeItem) => {
  return http.request("post", "/api/basic/college/update", { data });
};

/** 删除学院 */
export const deleteCollege = (data: { id: number }) => {
  return http.request("post", "/api/basic/college/delete", { data });
};

/** 批量删除学院 */
export const batchDeleteColleges = (ids: number[]) => {
  return http.request("post", "/api/basic/college/batch-delete", { data: ids });
};