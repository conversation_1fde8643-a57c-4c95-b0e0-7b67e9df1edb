package com.example.dto.score;

import com.example.dto.BaseQueryDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 成绩查询DTO
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "成绩查询条件")
public class GradeQueryDTO extends BaseQueryDTO {

    @Schema(description = "学号")
    private String studentId;

    @Schema(description = "学生姓名")
    private String studentName;

    @Schema(description = "班级代码")
    private String classCode;

    @Schema(description = "专业代码")
    private String majorCode;

    @Schema(description = "学院代码")
    private String collegeCode;

    @Schema(description = "课程代码")
    private String courseCode;

    @Schema(description = "课程名称")
    private String courseName;

    @Schema(description = "课程类型")
    private String courseType;

    @Schema(description = "学期ID")
    private Integer semesterId;

    @Schema(description = "最低成绩")
    private BigDecimal minScore;

    @Schema(description = "最高成绩")
    private BigDecimal maxScore;

    @Schema(description = "是否重修")
    private Boolean isRetake;

    @Schema(description = "年级")
    private String grade;
}
