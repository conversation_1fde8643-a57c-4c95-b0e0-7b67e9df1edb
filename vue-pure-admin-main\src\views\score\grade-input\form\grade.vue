<script setup lang="ts">
import { ref, computed } from "vue";
import ReCol from "@/components/ReCol";
import { gradeFormRules } from "../utils/rule";
import type { GradeFormProps } from "../utils/types";

const props = withDefaults(defineProps<GradeFormProps>(), {
  formInline: () => ({
    title: "新增",
    studentId: "",
    studentName: "",
    classCode: "",
    courseCode: "",
    courseName: "",
    semesterId: undefined,
    semesterName: "",
    finalScore: undefined,
    gradePoint: undefined,
    isRetake: false,
    remarks: ""
  })
});

const ruleFormRef = ref();
const newFormInline = ref(props.formInline);

// 计算绩点
const calculatedGradePoint = computed(() => {
  const score = newFormInline.value.finalScore;
  if (score === null || score === undefined || score < 0 || score > 100) {
    return 0;
  }
  if (score < 60) {
    return 0;
  }
  const gradePoint = (score / 10) - 5;
  return Math.max(0, Math.min(5, Number(gradePoint.toFixed(2))));
});

// 监听成绩变化，自动计算绩点
function handleScoreChange() {
  newFormInline.value.gradePoint = calculatedGradePoint.value;
}

function getRef() {
  return ruleFormRef.value;
}

defineExpose({ getRef });
</script>

<template>
  <el-form
    ref="ruleFormRef"
    :model="newFormInline"
    :rules="gradeFormRules"
    label-width="100px"
  >
    <el-row :gutter="30">
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="学号" prop="studentId">
          <el-input
            v-model="newFormInline.studentId"
            clearable
            placeholder="请输入学号"
            :disabled="newFormInline.title === '修改'"
          />
        </el-form-item>
      </re-col>

      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="学生姓名" prop="studentName">
          <el-input
            v-model="newFormInline.studentName"
            clearable
            placeholder="请输入学生姓名"
            :disabled="newFormInline.title === '修改'"
          />
        </el-form-item>
      </re-col>

      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="课程代码" prop="courseCode">
          <el-input
            v-model="newFormInline.courseCode"
            clearable
            placeholder="请输入课程代码"
            :disabled="newFormInline.title === '修改'"
          />
        </el-form-item>
      </re-col>

      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="课程名称">
          <el-input
            v-model="newFormInline.courseName"
            clearable
            placeholder="请输入课程名称"
            :disabled="newFormInline.title === '修改'"
          />
        </el-form-item>
      </re-col>

      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="学期" prop="semesterId">
          <el-input
            v-model="newFormInline.semesterName"
            placeholder="学期信息"
            :disabled="true"
          />
        </el-form-item>
      </re-col>

      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="期末成绩" prop="finalScore">
          <el-input-number
            v-model="newFormInline.finalScore"
            :min="0"
            :max="100"
            :precision="1"
            placeholder="请输入期末成绩"
            style="width: 100%"
            @change="handleScoreChange"
          />
        </el-form-item>
      </re-col>

      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="绩点">
          <el-input-number
            v-model="newFormInline.gradePoint"
            :min="0"
            :max="5"
            :precision="2"
            placeholder="自动计算"
            style="width: 100%"
            :disabled="true"
          />
        </el-form-item>
      </re-col>

      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="是否重修">
          <el-switch
            v-model="newFormInline.isRetake"
            :active-value="true"
            :inactive-value="false"
            active-text="是"
            inactive-text="否"
          />
        </el-form-item>
      </re-col>

      <re-col :value="24">
        <el-form-item label="备注">
          <el-input
            v-model="newFormInline.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </re-col>
    </el-row>
  </el-form>
</template>
