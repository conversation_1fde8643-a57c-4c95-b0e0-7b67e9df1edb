// 学期数据项类型（用于显示）
interface SemesterItem {
  /** 学期ID */
  id?: number;
  /** 学年 */
  academicYear: string;
  /** 学期号 */
  semesterNumber: number;
  /** 学期名称 */
  semesterName: string;
  /** 开始日期 */
  startDate: string;
  /** 结束日期 */
  endDate: string;
  /** 是否当前学期 */
  isCurrent: boolean;
}

// 查询表单类型
interface QueryFormProps {
  /** 学年 */
  academicYear: string;
  /** 学期号 */
  semesterNumber: number | null;
  /** 学期名称 */
  semesterName: string;
  /** 是否当前学期 */
  isCurrent: boolean | null;
  /** 当前页码 */
  current?: number;
  /** 每页大小 */
  size?: number;
}

export type { SemesterItem, QueryFormProps };
