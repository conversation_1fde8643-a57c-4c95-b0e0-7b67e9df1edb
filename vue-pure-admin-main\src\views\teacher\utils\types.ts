/** 教师查询参数 */
export interface TeacherQueryParams {
  current?: number;
  size?: number;
  teacherCode?: string;
  name?: string;
  gender?: string;
  collegeCode?: string;
  title?: string;
  status?: string;
}

/** 教师信息 */
export interface TeacherItem {
  id?: number;
  teacherCode: string;
  name: string;
  gender: string;
  birthDate?: string;
  phone?: string;
  email?: string;
  collegeCode: string;
  collegeName?: string;
  title?: string;
  hireDate?: string;
  status?: string;
  createdAt?: string;
  updatedAt?: string;
}

/** 教师表单属性 */
export interface TeacherFormProps {
  formInline: TeacherItem;
}

/** 性别选项 */
export const GENDER_OPTIONS = [
  { label: "男", value: "男" },
  { label: "女", value: "女" }
];

/** 职称选项 */
export const TITLE_OPTIONS = [
  { label: "教授", value: "教授" },
  { label: "副教授", value: "副教授" },
  { label: "讲师", value: "讲师" },
  { label: "助教", value: "助教" }
];

/** 状态选项 */
export const STATUS_OPTIONS = [
  { label: "在职", value: "在职" },
  { label: "离职", value: "离职" },
  { label: "退休", value: "退休" }
];

/** 职称标签类型映射 */
export const TITLE_TAG_TYPE_MAP = {
  "教授": "danger",
  "副教授": "warning",
  "讲师": "primary",
  "助教": "info"
} as const;

/** 状态标签类型映射 */
export const STATUS_TAG_TYPE_MAP = {
  "在职": "success",
  "离职": "danger",
  "退休": "warning"
} as const;
