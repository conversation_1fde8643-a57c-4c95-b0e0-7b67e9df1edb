!(function (c) {
  var t,
    e,
    o,
    a,
    n,
    l,
    i =
      '<svg><symbol id="icon-full-screen-hs" viewBox="0 0 1024 1024"><path d="M960.605 932.345v-240.231c0.045-7.2-2.723-14.445-8.213-19.98-11.115-11.047-29.002-11.047-40.071 0-5.535 5.535-8.303 12.757-8.303 19.98v171.923l-351.99-352.035 351.99-352.013v171.855c0 7.245 2.767 14.49 8.303 20.002 11.070 11.070 28.957 11.070 40.071 0 5.49-5.511 8.257-12.78 8.213-20.002v-240.187c0.045-7.223-2.723-14.468-8.213-20.003-5.58-5.511-12.803-8.279-20.025-8.302h-240.233c-7.222 0-14.467 2.79-19.98 8.302-11.115 11.049-11.115 28.957 0 40.050 5.511 5.535 12.735 8.302 19.98 8.279h171.9l-352.013 352.013-352.012-352.035h171.855c7.268 0.022 14.49-2.745 20.025-8.279 11.070-11.047 11.070-29.002 0-40.050-5.49-5.511-12.758-8.279-20.025-8.303h-240.187c-7.268 0-14.513 2.79-20.025 8.303-5.513 5.558-8.279 12.803-8.279 20.048v240.165c0 7.245 2.79 14.512 8.279 20.002 11.070 11.070 28.98 11.070 40.028 0 5.513-5.511 8.279-12.713 8.279-20.002v-171.855l352.058 352.012-352.035 352.035v-171.922c0-7.2-2.745-14.445-8.279-19.98-11.070-11.047-29.002-11.047-40.028 0-5.558 5.535-8.279 12.757-8.279 19.98v240.231c0 7.223 2.79 14.468 8.279 20.048 5.535 5.468 12.757 8.279 20.025 8.279h240.188c7.268 0 14.49-2.745 20.025-8.279 11.070-11.047 11.070-29.002 0-40.050-5.535-5.535-12.78-8.257-20.025-8.257h-171.877l352.012-352.035 352.013 352.035h-171.9c-7.222 0-14.467 2.768-19.98 8.257-11.115 11.049-11.115 29.002 0 40.050 5.511 5.468 12.735 8.279 19.98 8.279h240.255c7.2 0 14.445-2.813 20.025-8.279 5.467-5.602 8.19-12.825 8.19-20.048z"  ></path></symbol><symbol id="icon-watch-hs" viewBox="0 0 1024 1024"><path d="M511.08 630.42c0.39-0.09 0.8-0.14 1.2-0.2h-0.52zM651 490.5c0 0.2-0.09 0.4-0.13 0.59s0 0.14 0 0.2c0.03-0.29 0.07-0.53 0.13-0.79zM471.17 630.42l-0.84-0.18h-0.31c0.38 0.03 0.77 0.1 1.15 0.18zM331.49 449v0.36c0.08-0.51 0.16-1 0.23-1.52-0.12 0.37-0.19 0.78-0.23 1.16zM468.88 630.1l1.14 0.11c-0.83-0.13-1.67-0.23-2.5-0.36a10 10 0 0 0 1.36 0.25zM331.57 492.78a11.92 11.92 0 0 0 0.24 1.36c-0.13-0.87-0.24-1.76-0.38-2.64 0.05 0.43 0.09 0.86 0.14 1.28zM331.25 490.5c0.07 0.33 0.13 0.66 0.18 1v-0.23zM331.25 450.58c0.05-0.26 0.1-0.5 0.15-0.73s0-0.34 0.05-0.51c-0.06 0.42-0.12 0.84-0.2 1.24zM650.84 491.41v-0.12c-0.07 0.41-0.13 0.83-0.19 1.24 0.09-0.4 0.15-0.8 0.19-1.12z"  ></path><path d="M512 65C264.66 65 65 264.66 65 512s199.66 447 447 447 447-199.66 447-447S759.34 65 512 65z m213.38 684.87c-8.76 9.55-25 8.88-33.91 0l-12.3-12.3q-42.64-42.63-85.28-85.28a200 200 0 0 1-59.3 22.52 214 214 0 0 1-131.48-14.95c-37.52-16.92-69.56-46.36-90.86-81.46-22-36.33-32.73-80.15-29.4-122.54a213.12 213.12 0 0 1 48.3-119.42c54.37-66.3 150.16-92.22 230.58-62.43a214.42 214.42 0 0 1 100.68 77.16c24.62 34.5 37.4 77.11 37.56 119.37a235.67 235.67 0 0 1-2.92 34.06c-6.88 45.84-30.52 87.73-64.1 118.92l5.81 5.81L725.38 716c9.48 9.43 8.79 24.3 0 33.87z"  ></path><path d="M635.31 542.1c1.2-2.36 2.34-4.74 3.44-7.15 0.28-0.61 0.54-1.22 0.81-1.83 1.5-3.86 3-7.72 4.25-11.67a183.83 183.83 0 0 0 6.56-26.9c0.11-0.66 0.2-1.34 0.29-2-0.35 1.85-0.91 3.8 0.21-1.44 0.24-2.18 0.45-4.37 0.61-6.56 0.35-4.66 0.51-9.32 0.52-14s-0.17-9.33-0.52-14q-0.26-3.45-0.64-6.88v-0.08c-1-6-0.2-1.67 0 0 0 0.31 0.11 0.63 0.17 1-0.16-0.89-0.27-1.8-0.41-2.7a182.65 182.65 0 0 0-6.6-27.62c-1.38-4.27-3-8.41-4.59-12.61-0.32-0.71-0.63-1.42-1-2.13-0.92-2-1.87-4-2.87-6a184.55 184.55 0 0 0-13.85-23.12c-1.11-1.58-7-8.66-1.71-2.52-1.42-1.65-2.72-3.42-4.12-5.09q-4.2-5-8.75-9.73c-6.1-6.32-12.78-11.79-19.44-17.49 6.14 5.26-0.94-0.6-2.52-1.71-1.79-1.26-3.6-2.48-5.43-3.68q-5.52-3.59-11.27-6.78-6.07-3.36-12.38-6.26c-0.75-0.35-1.51-0.68-2.26-1-1.64-0.62-3.25-1.3-4.9-1.9a181.3 181.3 0 0 0-27.13-7.74c-2.67-0.55-5.36-1-8.05-1.46-0.9-0.14-1.81-0.25-2.71-0.41l1 0.17c1.67 0.2 6 1 0 0H512q-6.56-0.75-13.15-1a188.34 188.34 0 0 0-28.4 1c-1.1 0.2-2.25 0.31-3.34 0.49-2.24 0.37-4.48 0.78-6.7 1.24q-6.63 1.35-13.14 3.18c-4.29 1.21-8.55 2.58-12.75 4.1-2 0.72-4 1.51-6 2.28l-1.84 0.82a185.92 185.92 0 0 0-24.25 13.32q-3.3 2.16-6.51 4.44c-0.74 0.53-3.82 2.89-3.93 2.93-0.71 0.57-1.41 1.14-2.1 1.72q-2.52 2.1-5 4.27a186.27 186.27 0 0 0-18.65 19.25c-1.32 1.58-6.2 9-2 2.31-1.16 1.83-2.62 3.53-3.86 5.3q-3.78 5.37-7.17 11T346.94 399q-1.5 3-2.89 5.95c-0.86 1.86-3.23 8.91-0.78 1.56-3 8.94-6.46 17.52-8.69 26.72q-1.68 6.94-2.82 14c0 0.21-0.05 0.42-0.08 0.62 0.29-1.51 0.67-2.55-0.28 2-0.2 1.77-0.38 3.54-0.53 5.32a188.09 188.09 0 0 0-0.11 29.36c0.17 2.25 0.41 4.49 0.65 6.74 0.8 3.86 0.63 3.81 0.4 2.87l0.06 0.41q1.06 6.38 2.56 12.66a200.32 200.32 0 0 0 8.27 26c0.46 1 0.89 2 1.35 3q1.4 3 2.89 6 3 5.86 6.39 11.53 3.56 5.89 7.54 11.54c1.13 1.59 2.44 3.11 3.49 4.76-4.09-6.45-0.21-0.31 1.12 1.3a185.34 185.34 0 0 0 19 19.82q2.92 2.63 5.95 5.13c1.62 1.33 7.76 5.22 1.31 1.12 3.49 2.22 6.72 4.91 10.18 7.19a184.79 184.79 0 0 0 23.59 13.12c1 0.48 2.06 0.93 3.09 1.4 2 0.76 3.94 1.54 5.92 2.26 4.2 1.52 8.46 2.89 12.75 4.1s8.72 2.29 13.14 3.18c2.22 0.46 4.46 0.87 6.7 1.24l0.41 0.06c-0.93-0.22-1-0.39 2.81 0.39a200.31 200.31 0 0 0 27.82 1q6.83-0.25 13.61-1c4.64-1 3.6-0.58 2.08-0.28l0.62-0.09c2-0.33 4-0.69 6-1.08a183.29 183.29 0 0 0 27.22-7.55c2.26-0.81 4.49-1.87 6.76-2.64 0.75-0.34 1.51-0.67 2.25-1q5.7-2.64 11.2-5.66c7.94-4.37 15-9.82 22.58-14.65-6.69 4.25 0.74-0.64 2.31-2s3.32-2.83 4.95-4.29q4.86-4.38 9.4-9.08 4.79-5 9.17-10.23c1.26-1.51 2.43-3.1 3.7-4.59-5.06 5.91-0.2 0.17 1-1.45a185.86 185.86 0 0 0 14.31-23.66z"  ></path><path d="M512.68 630.18l1.16-0.19-1.56 0.23z"  ></path></symbol><symbol id="icon-download-hs" viewBox="0 0 1024 1024"><path d="M200 834h632v88a8 8 0 0 1-8 8H192v-88a8 8 0 0 1 8-8z m239-262.037V185c0-13.255 10.745-24 24-24h104v401.473l141.997-137.148c9.534-9.209 24.728-8.945 33.936 0.59l0.004 0.003 72.205 74.799-304.959 294.546c-9.534 9.208-24.728 8.944-33.936-0.59l-0.003-0.004-71.859-74.44-0.063 0.062-205.587-212.927c-9.206-9.534-8.941-24.724 0.59-33.932l74.782-72.246L439 571.963z"  ></path></symbol><symbol id="icon-enlarge-hs" viewBox="0 0 1024 1024"><path d="M945.159 867.61l-206.543-206.538c49.578-63.772 79.108-143.903 79.108-230.953 0-207.97-168.58-376.547-376.639-376.547-207.97 0-376.547 168.577-376.547 376.547 0 208.038 168.577 376.614 376.547 376.614 87.059 0 167.197-29.532 230.973-79.108l206.543 206.544c9.171 9.17 21.227 13.802 33.278 13.802s24.106-4.629 33.28-13.802c18.431-18.43 18.431-48.215 0-66.559v0zM158.701 430.119c0-155.737 126.65-282.39 282.39-282.39 155.826 0 282.477 126.65 282.477 282.39 0 155.805-126.65 282.458-282.477 282.458-155.739 0-282.39-126.65-282.39-282.458v0z"  ></path><path d="M579.708 389.853h-98.352v-98.352c0-22.272-17.991-40.268-40.268-40.352-22.185 0.086-40.267 18.078-40.267 40.352v98.352h-98.352c-22.272 0-40.268 17.991-40.268 40.179 0 22.272 17.991 40.352 40.268 40.352h98.352v98.352c0 22.252 18.080 40.246 40.267 40.333 22.274-0.086 40.268-18.080 40.268-40.333v-98.352h98.352c22.272 0 40.355-18.079 40.267-40.352 0-22.187-17.991-40.179-40.267-40.179v0z"  ></path></symbol><symbol id="icon-previous-hs" viewBox="0 0 1024 1024"><path d="M814.933333 482.133333C716.8 384 597.333333 328.533333 460.8 315.733333V128c0-17.066667-8.533333-34.133333-25.6-38.4-17.066667-8.533333-34.133333-4.266667-46.933333 8.533333L29.866667 473.6c-17.066667 17.066667-17.066667 42.666667 0 59.733333l358.4 392.533334c8.533333 8.533333 21.333333 12.8 34.133333 12.8 4.266667 0 8.533333 0 17.066667-4.266667 17.066667-4.266667 25.6-21.333333 25.6-38.4v-204.8c68.266667-8.533333 128-4.266667 192 12.8 76.8 21.333333 170.666667 93.866667 273.066666 213.333333 12.8 12.8 29.866667 21.333333 51.2 12.8 17.066667-8.533333 29.866667-25.6 25.6-42.666666-21.333333-162.133333-85.333333-298.666667-192-405.333334z"  ></path></symbol><symbol id="icon-zoom-out-hs" viewBox="0 0 1024 1024"><path d="M951.643 877.547l-210.337-210.335c50.487-64.946 80.56-146.547 80.56-235.199 0-211.793-171.681-383.472-383.563-383.472-211.792 0-383.471 171.679-383.471 383.472 0 211.862 171.679 383.538 383.471 383.538 88.661 0 170.271-30.075 235.218-80.56l210.337 210.339c9.34 9.339 21.614 14.055 33.89 14.055s24.551-4.716 33.892-14.055c18.77-18.77 18.77-49.101 0-67.781v0zM150.725 432.011c0-158.601 128.978-287.58 287.58-287.58 158.691 0 287.671 128.978 287.671 287.58 0 158.668-128.978 287.651-287.671 287.651-158.601 0-287.58-128.978-287.58-287.651v0z"  ></path><path d="M397.297 391.004h-100.16c-22.683 0-41.008 18.324-41.008 40.919 0 22.683 18.324 41.094 41.008 41.094h282.333c22.683 0 41.095-18.412 41.007-41.094 0-22.595-18.324-40.919-41.007-40.919v0h-182.173z"  ></path></symbol><symbol id="icon-next-step-hs" viewBox="0 0 1024 1024"><path d="M209.066667 482.133333c98.133333-98.133333 213.333333-153.6 349.866666-166.4V128c0-17.066667 8.533333-34.133333 25.6-38.4 17.066667-8.533333 34.133333-4.266667 46.933334 8.533333l358.4 375.466667c17.066667 17.066667 17.066667 42.666667 0 59.733333l-358.4 392.533334c-8.533333 8.533333-21.333333 12.8-29.866667 12.8-4.266667 0-8.533333 0-17.066667-4.266667-17.066667-4.266667-25.6-21.333333-25.6-38.4v-204.8c-68.266667-8.533333-128-4.266667-192 12.8-76.8 21.333333-170.666667 93.866667-273.066666 213.333333-8.533333 17.066667-29.866667 21.333333-46.933334 12.8-17.066667-8.533333-29.866667-25.6-25.6-42.666666 17.066667-162.133333 81.066667-298.666667 187.733334-405.333334z"  ></path></symbol></svg>',
    s = (s = document.getElementsByTagName("script"))[
      s.length - 1
    ].getAttribute("data-injectcss");
  if (s && !c.__iconfont__svg__cssinject__) {
    c.__iconfont__svg__cssinject__ = !0;
    try {
      document.write(
        "<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>"
      );
    } catch (c) {
      console && console.log(c);
    }
  }
  function h() {
    n || ((n = !0), o());
  }
  ((t = function () {
    var c, t, e, o;
    (((o = document.createElement("div")).innerHTML = i),
      (i = null),
      (e = o.getElementsByTagName("svg")[0]) &&
        ((e.style.position = "absolute"),
        (e.style.width = 0),
        (e.style.height = 0),
        (e.style.overflow = "hidden"),
        (c = e),
        (t = document.body).firstChild
          ? ((o = c), (e = t.firstChild).parentNode.insertBefore(o, e))
          : t.appendChild(c)));
  }),
    document.addEventListener
      ? ~["complete", "loaded", "interactive"].indexOf(document.readyState)
        ? setTimeout(t, 0)
        : ((e = function () {
            (document.removeEventListener("DOMContentLoaded", e, !1), t());
          }),
          document.addEventListener("DOMContentLoaded", e, !1))
      : document.attachEvent &&
        ((o = t),
        (a = c.document),
        (n = !1),
        (l = function () {
          try {
            a.documentElement.doScroll("left");
          } catch (c) {
            return void setTimeout(l, 50);
          }
          h();
        })(),
        (a.onreadystatechange = function () {
          "complete" == a.readyState && ((a.onreadystatechange = null), h());
        })));
})(window);
