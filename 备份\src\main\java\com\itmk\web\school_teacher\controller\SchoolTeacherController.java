package com.itmk.web.school_teacher.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.itmk.utils.ResultUtils;
import com.itmk.utils.ResultVo;
import com.itmk.web.school_teacher.entity.SchoolTeacher;
import com.itmk.web.school_teacher.entity.TeacherListParm;
import com.itmk.web.school_teacher.service.SchoolTeacherService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/teacher")
public class SchoolTeacherController {
    @Autowired
    private SchoolTeacherService schoolTeacherService;

    //新增
    @PostMapping
    public ResultVo add(@RequestBody SchoolTeacher schoolTeacher){
        boolean save = schoolTeacherService.save(schoolTeacher);
        if(save){
            return ResultUtils.success("新增教师成功!");
        }
        return ResultUtils.error("新增教师失败!");
    }

    //编辑
    @PutMapping
    public ResultVo edit(@RequestBody SchoolTeacher schoolTeacher){
        boolean save = schoolTeacherService.updateById(schoolTeacher);
        if(save){
            return ResultUtils.success("编辑教师成功!");
        }
        return ResultUtils.error("编辑教师失败!");
    }

    //删除
    @DeleteMapping("/{teacherId}")
    public ResultVo delete(@PathVariable("teacherId") Long teacherId){
        boolean save = schoolTeacherService.removeById(teacherId);
        if(save){
            return ResultUtils.success("删除教师成功!");
        }
        return ResultUtils.error("删除教师失败!");
    }

    //列表
    @GetMapping("/list")
    public ResultVo getList(TeacherListParm teacherListParm){
        //直接调用service层的getList方法
        IPage<SchoolTeacher> list = schoolTeacherService.getList(teacherListParm);
        return ResultUtils.success("查询成功", list);
    }
}