package com.example.service.basic.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.common.exception.BusinessException;
import com.example.common.PageResult;
import com.example.dto.basic.CollegeQueryDTO;
import com.example.entity.basic.College;
import com.example.mapper.basic.CollegeMapper;
import com.example.service.basic.CollegeService;
import com.example.vo.basic.CollegeVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 学院服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CollegeServiceImpl implements CollegeService {

    private final CollegeMapper collegeMapper;

    @Override
    @Cacheable(value = "collegeList", key = "'list_' + (#query?.collegeCode ?: 'all') + '_' + (#query?.collegeName ?: 'all') + '_' + (#query?.current ?: 1) + '_' + (#query?.size ?: 10)")
    public PageResult<CollegeVO> getCollegeList(CollegeQueryDTO query) {
        if (query == null) {
            query = new CollegeQueryDTO();
        }

        // 设置默认分页参数
        if (query.getCurrent() == null || query.getCurrent() <= 0) {
            query.setCurrent(1);
        }
        if (query.getSize() == null || query.getSize() <= 0) {
            query.setSize(10);
        }

        IPage<College> page = new Page<>(query.getCurrent(), query.getSize());
        IPage<College> collegePage = collegeMapper.selectCollegePage(page, query);

        List<CollegeVO> collegeVOList = collegePage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return PageResult.<CollegeVO>builder()
                .list(collegeVOList)
                .total(collegePage.getTotal())
                .pageNum(query.getCurrent())
                .pageSize(query.getSize())
                .build();
    }

    @Override
    @Cacheable(value = "allColleges")
    public List<CollegeVO> getAllColleges() {
        List<College> colleges = collegeMapper.selectAllColleges();
        return colleges.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "collegeDetail", key = "#id")
    public CollegeVO getCollegeById(Integer id) {
        if (id == null) {
            throw new BusinessException("学院ID不能为空");
        }

        College college = collegeMapper.selectById(id);
        if (college == null) {
            throw new BusinessException("学院不存在");
        }

        return convertToVO(college);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"collegeList", "allColleges", "collegeDetail"}, allEntries = true)
    public void saveCollege(CollegeVO collegeVO) {
        if (collegeVO == null) {
            throw new BusinessException("学院信息不能为空");
        }

        // 验证学院信息
        validateCollegeInfo(collegeVO, null);

        try {
            College college = new College();
            copyVOToEntity(collegeVO, college);

            college.setCreatedAt(LocalDateTime.now());
            college.setUpdatedAt(LocalDateTime.now());

            collegeMapper.insert(college);

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("新增学院失败: {}", collegeVO.getCollegeName(), e);
            throw new BusinessException("新增学院失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"collegeList", "allColleges", "collegeDetail"}, allEntries = true)
    public void updateCollege(CollegeVO collegeVO) {
        if (collegeVO == null || collegeVO.getId() == null) {
            throw new BusinessException("学院信息不能为空");
        }

        // 检查学院是否存在
        College existingCollege = collegeMapper.selectById(collegeVO.getId());
        if (existingCollege == null) {
            throw new BusinessException("学院不存在");
        }

        // 验证学院信息
        validateCollegeInfo(collegeVO, collegeVO.getId());

        try {
            College college = new College();
            copyVOToEntity(collegeVO, college);
            college.setId(collegeVO.getId());
            college.setUpdatedAt(LocalDateTime.now());

            collegeMapper.updateById(college);

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新学院失败: {}", collegeVO.getCollegeName(), e);
            throw new BusinessException("更新学院失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"collegeList", "allColleges", "collegeDetail"}, allEntries = true)
    public void deleteCollege(Integer id) {
        if (id == null) {
            throw new BusinessException("学院ID不能为空");
        }

        College college = collegeMapper.selectById(id);
        if (college == null) {
            throw new BusinessException("学院不存在");
        }

        try {
            collegeMapper.deleteById(id);
        } catch (Exception e) {
            log.error("删除学院失败: {}", college.getCollegeName(), e);
            throw new BusinessException("删除学院失败，可能存在关联数据");
        }
    }

    /**
     * 验证学院信息
     */
    private void validateCollegeInfo(CollegeVO collegeVO, Integer excludeId) {
        if (!StringUtils.hasText(collegeVO.getCollegeCode())) {
            throw new BusinessException("学院代码不能为空");
        }
        if (!StringUtils.hasText(collegeVO.getCollegeName())) {
            throw new BusinessException("学院名称不能为空");
        }

        // 检查学院代码是否重复
        Boolean codeExists = collegeMapper.existsByCollegeCode(collegeVO.getCollegeCode(), excludeId);
        if (Boolean.TRUE.equals(codeExists)) {
            throw new BusinessException("学院代码已存在");
        }

        // 检查学院名称是否重复
        Boolean nameExists = collegeMapper.existsByCollegeName(collegeVO.getCollegeName(), excludeId);
        if (Boolean.TRUE.equals(nameExists)) {
            throw new BusinessException("学院名称已存在");
        }
    }

    /**
     * 实体转VO
     */
    private CollegeVO convertToVO(College college) {
        if (college == null) {
            return null;
        }
        CollegeVO vo = new CollegeVO();
        BeanUtils.copyProperties(college, vo);
        return vo;
    }

    /**
     * VO转实体
     */
    private void copyVOToEntity(CollegeVO vo, College entity) {
        if (vo == null || entity == null) {
            return;
        }
        BeanUtils.copyProperties(vo, entity);
    }
}
