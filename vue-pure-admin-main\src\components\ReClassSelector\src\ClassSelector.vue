<template>
  <div class="main">
    <!-- 搜索区域 -->
    <el-form
      ref="formRef"
      :inline="true"
      :model="queryForm"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
    >
      <el-form-item label="所属学院：" prop="collegeCode">
        <el-select
          v-model="queryForm.collegeCode"
          placeholder="请选择学院"
          clearable
          class="!w-[200px]"
          @change="onCollegeChange"
        >
          <el-option
            v-for="college in collegeOptions"
            :key="college.collegeCode"
            :label="college.collegeName"
            :value="college.collegeCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所属专业：" prop="majorCode">
        <el-select
          v-model="queryForm.majorCode"
          placeholder="请选择专业"
          clearable
          filterable
          class="!w-[200px]"
        >
          <el-option
            v-for="major in filteredMajorOptions"
            :key="major.majorCode"
            :label="major.majorName"
            :value="major.majorCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="班级代码：" prop="classCode">
        <el-input
          v-model="queryForm.classCode"
          placeholder="请输入班级代码"
          clearable
          class="!w-[200px]"
        />
      </el-form-item>
      <el-form-item label="班级名称：" prop="className">
        <el-input
          v-model="queryForm.className"
          placeholder="请输入班级名称"
          clearable
          class="!w-[180px]"
        />
      </el-form-item>
      <el-form-item label="入学年份：" prop="gradeYear">
        <el-select
          v-model="queryForm.gradeYear"
          placeholder="请选择年份"
          clearable
          class="!w-[120px]"
        >
          <el-option
            v-for="year in yearOptions"
            :key="year.value"
            :label="year.label"
            :value="year.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon(Search)"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon(Refresh)" @click="resetForm(formRef)">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格区域 -->
    <PureTableBar :title="tableTitle" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <slot name="buttons"></slot>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          border
          align-whole="center"
          showOverflowTooltip
          table-layout="auto"
          :loading="loading"
          :size="size"
          adaptive
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small' ? true : false"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="onSizeChange"
          @page-current-change="onCurrentChange"
        >
          <template #operation="{ row }">
            <slot name="operation" :row="row" :size="size">
              <el-button
                class="reset-margin"
                link
                type="primary"
                :size="size"
                :icon="useRenderIcon('ep:view')"
                @click="handleSelect(row)"
              >
                查看学生
              </el-button>
            </slot>
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useClassSelector } from "./hooks";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { getYearOptionsWithLabel } from "@/utils/yearOptions";

import Search from "~icons/ep/search";
import Refresh from "~icons/ep/refresh";
import View from "~icons/ep/view";

interface Props {
  /** 表格标题 */
  title?: string;
}

const props = withDefaults(defineProps<Props>(), {
  title: "班级列表"
});

const emit = defineEmits<{
  /** 选择班级事件 */
  select: [classRow: any];
}>();

const formRef = ref();

const {
  queryForm,
  loading,
  columns,
  dataList,
  pagination,
  collegeOptions,
  majorOptions,
  onSearch,
  resetForm,
  onCollegeChange,
  onSizeChange,
  onCurrentChange,
  handleSelectionChange
} = useClassSelector();

// 计算表格标题
const tableTitle = computed(() => props.title);

// 根据选择的学院筛选专业
const filteredMajorOptions = computed(() => {
  if (!queryForm.collegeCode) {
    return majorOptions.value;
  }
  return majorOptions.value.filter(
    major => major.collegeCode === queryForm.collegeCode
  );
});

// 获取年份选项
const yearOptions = getYearOptionsWithLabel();

// 处理选择班级
function handleSelect(row: any) {
  emit('select', row);
}
</script>
