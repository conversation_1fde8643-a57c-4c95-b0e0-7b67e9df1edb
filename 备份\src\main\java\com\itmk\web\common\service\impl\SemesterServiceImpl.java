package com.itmk.web.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itmk.utils.PageUtils;
import com.itmk.web.common.entity.CommParm;
import com.itmk.web.common.entity.Semester;
import com.itmk.web.common.mapper.SemesterMapper;
import com.itmk.web.common.service.SemesterService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;
import java.util.Map;
import java.util.HashMap;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;

@Service
public class SemesterServiceImpl extends ServiceImpl<SemesterMapper, Semester> implements SemesterService {

    // 缓存上次检查时间，避免频繁检查
    private static volatile long lastCheckTime = 0;
    private static final long CHECK_INTERVAL = 5 * 60 * 1000; // 5分钟检查间隔
    @Override
    public IPage<Semester> getList(CommParm commParm) {
        // 构造查询条件
        QueryWrapper<Semester> query = new QueryWrapper<>();
        if (commParm.getAcademicYear() != null && !commParm.getAcademicYear().isEmpty()) {
            // 使用semesterName字段进行模糊查询，查找包含学年的记录
            query.lambda().like(Semester::getSemesterName, commParm.getAcademicYear());
        }
        if (commParm.getSemesterName() != null && !commParm.getSemesterName().isEmpty()) {
            query.lambda().like(Semester::getSemesterName, commParm.getSemesterName());
        }
        // 按semesterId升序排序
        query.lambda().orderByAsc(Semester::getSemesterId);
        //构造分页对象
        IPage<Semester> page = PageUtils.createPage(commParm.getCurrentPage(), commParm.getPageSize());
        return this.page(page, query);
    }

    @Override
    public List<String> getAcademicYears() {
        // 查询所有学期
        List<Semester> semesters = this.list();

        // 提取学年信息并去重
        return semesters.stream()
                .map(Semester::getAcademicYear)
                .filter(academicYear -> academicYear != null && !academicYear.trim().isEmpty())
                .distinct()
                .sorted((a, b) -> b.compareTo(a)) // 按降序排序，最新学年在前
                .collect(Collectors.toList());
    }

    @Override
    public List<Semester> getSemestersByAcademicYear(String academicYear) {
        QueryWrapper<Semester> query = new QueryWrapper<>();

        // 先尝试通过academicYear字段精确查询
        query.lambda().eq(Semester::getAcademicYear, academicYear);
        List<Semester> semesters = this.list(query);

        // 如果通过academicYear字段没有找到，则通过semesterName模糊查询
        if (semesters.isEmpty()) {
            query = new QueryWrapper<>();
            query.lambda().like(Semester::getSemesterName, academicYear);
            semesters = this.list(query);
        }

        // 按semesterId升序排序
        return semesters.stream()
                .sorted((a, b) -> a.getSemesterId().compareTo(b.getSemesterId()))
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> getCurrentAcademicYearAndSemester() {
        Map<String, Object> result = new HashMap<>();

        // 获取当前日期
        LocalDate now = LocalDate.now();

        // 计算当前学年
        String currentAcademicYear = calculateCurrentAcademicYear(now);

        // 计算当前学期序号（1或2）
        int currentSemesterNumber = calculateCurrentSemesterNumber(now);

        // 获取所有学年列表
        List<String> allAcademicYears = getAcademicYears();

        // 如果计算出的学年不在数据库中，使用最新的学年
        if (!allAcademicYears.contains(currentAcademicYear) && !allAcademicYears.isEmpty()) {
            currentAcademicYear = allAcademicYears.get(0); // 第一个是最新的学年
        }

        // 获取当前学年的所有学期
        List<Semester> currentYearSemesters = getSemestersByAcademicYear(currentAcademicYear);

        // 查找当前学期
        Semester currentSemester = null;
        if (!currentYearSemesters.isEmpty()) {
            // 根据当前学年和学期序号构建学期名称
            String expectedSemesterName = currentAcademicYear + "-" + currentSemesterNumber;

            // 查找匹配的学期
            for (Semester semester : currentYearSemesters) {
                if (expectedSemesterName.equals(semester.getSemesterName())) {
                    currentSemester = semester;
                    break;
                }
            }

            // 如果没有找到对应的学期，使用第一个学期作为默认
            if (currentSemester == null) {
                currentSemester = currentYearSemesters.get(0);
            }
        }

        // 构建返回结果
        result.put("currentAcademicYear", currentAcademicYear);
        result.put("currentSemester", currentSemester);
        result.put("currentSemesterNumber", currentSemesterNumber);
        result.put("allAcademicYears", allAcademicYears);
        result.put("currentYearSemesters", currentYearSemesters);

        return result;
    }

    /**
     * 计算当前学年
     * 学年规则：8月1日-次年7月31日为一个学年
     */
    private String calculateCurrentAcademicYear(LocalDate date) {
        int year = date.getYear();
        Month month = date.getMonth();

        if (month.getValue() >= 8) {
            // 8月-12月，属于当年-次年学年
            return year + "-" + (year + 1);
        } else {
            // 1月-7月，属于上年-当年学年
            return (year - 1) + "-" + year;
        }
    }

    /**
     * 计算当前学期序号
     * 学期规则：8月1日-1月31日为第一学期，2月1日-7月31日为第二学期
     */
    private int calculateCurrentSemesterNumber(LocalDate date) {
        Month month = date.getMonth();

        if (month.getValue() >= 8 || month.getValue() <= 1) {
            // 8月-1月为第一学期
            return 1;
        } else {
            // 2月-7月为第二学期
            return 2;
        }
    }

    @Override
    public void autoCreateSemesters() {
        // 检查是否需要执行（避免频繁检查）
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastCheckTime < CHECK_INTERVAL) {
            return; // 距离上次检查不足5分钟，跳过
        }

        // 执行实际的学期创建逻辑
        doAutoCreateSemesters();

        // 更新最后检查时间
        lastCheckTime = currentTime;
    }

    /**
     * 强制执行学期创建（忽略时间间隔限制）
     * 供定时任务使用
     */
    public void forceAutoCreateSemesters() {
        doAutoCreateSemesters();
        lastCheckTime = System.currentTimeMillis();
    }

    /**
     * 实际执行学期创建的逻辑
     */
    private void doAutoCreateSemesters() {
        try {
            LocalDate now = LocalDate.now();
            String currentAcademicYear = calculateCurrentAcademicYear(now);
            int currentSemesterNumber = calculateCurrentSemesterNumber(now);

            // 检查当前学年的学期是否存在，如果不存在则创建
            createSemesterIfNotExists(currentAcademicYear, 1);
            createSemesterIfNotExists(currentAcademicYear, 2);

            // 根据当前时间决定是否需要创建下一学年的学期
            if (currentSemesterNumber == 2) {
                // 如果当前是第二学期，准备创建下一学年的第一学期
                String nextAcademicYear = getNextAcademicYear(currentAcademicYear);
                createSemesterIfNotExists(nextAcademicYear, 1);
            }

            // 也创建上一学年的学期（如果不存在）
            String previousAcademicYear = getPreviousAcademicYear(currentAcademicYear);
            createSemesterIfNotExists(previousAcademicYear, 1);
            createSemesterIfNotExists(previousAcademicYear, 2);

        } catch (Exception e) {
            throw e; // 重新抛出异常，让调用者知道失败了
        }
    }

    /**
     * 如果学期不存在则创建
     */
    private void createSemesterIfNotExists(String academicYear, int semesterNumber) {
        String semesterName = academicYear + "-" + semesterNumber;

        try {
            // 检查是否已存在
            QueryWrapper<Semester> query = new QueryWrapper<>();
            query.lambda().eq(Semester::getAcademicYear, academicYear)
                         .eq(Semester::getSemesterName, semesterName);

            if (this.count(query) == 0) {
                // 不存在则创建
                Semester semester = new Semester();
                // 不设置semesterId，让数据库自动生成
                semester.setAcademicYear(academicYear);
                semester.setSemesterName(semesterName);
                semester.setCreatedAt(LocalDateTime.now());
                semester.setUpdatedAt(LocalDateTime.now());

                this.save(semester);
            }
        } catch (Exception e) {
            // 静默处理异常，避免影响其他学期的创建
        }
    }

    /**
     * 获取下一学年
     */
    private String getNextAcademicYear(String currentAcademicYear) {
        String[] years = currentAcademicYear.split("-");
        if (years.length == 2) {
            try {
                int startYear = Integer.parseInt(years[0]);
                int endYear = Integer.parseInt(years[1]);
                return (startYear + 1) + "-" + (endYear + 1);
            } catch (NumberFormatException e) {
                // 解析失败，返回默认值
            }
        }
        return currentAcademicYear;
    }

    /**
     * 获取上一学年
     */
    private String getPreviousAcademicYear(String currentAcademicYear) {
        String[] years = currentAcademicYear.split("-");
        if (years.length == 2) {
            try {
                int startYear = Integer.parseInt(years[0]);
                int endYear = Integer.parseInt(years[1]);
                return (startYear - 1) + "-" + (endYear - 1);
            } catch (NumberFormatException e) {
                // 解析失败，返回默认值
            }
        }
        return currentAcademicYear;
    }

}