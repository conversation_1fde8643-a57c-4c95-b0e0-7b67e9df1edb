package com.itmk.web.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itmk.utils.PageUtils;
import com.itmk.web.common.entity.CommParm;
import com.itmk.web.common.entity.Course;
import com.itmk.web.common.mapper.CourseMapper;
import com.itmk.web.common.mapper.SemesterMapper;
import com.itmk.web.common.service.CourseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CourseServiceImpl extends ServiceImpl<CourseMapper, Course> implements CourseService {
    @Autowired
    private SemesterMapper semesterMapper;
    
    @Override
    public Integer getSemesterIdByName(String semesterName) {
        if (semesterName == null || semesterName.trim().isEmpty()) {
            return null;
        }
        return semesterMapper.getIdByName(semesterName);
    }
    
    @Override
    public IPage<Course> getList(CommParm commParm) {
        // 构造查询条件
        QueryWrapper<Course> query = new QueryWrapper<>();
        if (commParm.getCourseName() != null && !commParm.getCourseName().isEmpty()) {
            query.lambda().like(Course::getCourseName, commParm.getCourseName());
        }
        query.lambda().orderByAsc(Course::getId);
        //构造分页对象
        IPage<Course> page = PageUtils.createPage(commParm.getCurrentPage(), commParm.getPageSize());
        return this.page(page, query);
    }
    
    @Override
    public IPage<Course> getListWithSemester(CommParm commParm) {
        // 构造分页对象
        IPage<Course> page = PageUtils.createPage(commParm.getCurrentPage(), commParm.getPageSize());
        // 调用Mapper中的关联查询方法，传递所有筛选参数
        return this.baseMapper.getListWithSemester(page, commParm.getCourseName(),
                                                  commParm.getAcademicYear(),
                                                  commParm.getSemesterName());
    }
}