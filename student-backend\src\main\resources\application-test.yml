#端口号配置
server:
  port: 8080

# JWT配置
jwt:
  secret: itmk-student-management-system-jwt-secret-key-2025
  expiration: 60  # token过期时间（分钟）
  issuer: itmk-student-system
#数据库连接配置
spring:
  # HTTP编码配置
  http:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  # 消息编码配置
  messages:
    encoding: UTF-8
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************
    username: root
    password: 123456
    # Druid连接池配置
    druid:
      initial-size: 10
      min-idle: 10
      max-active: 50
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      # 开启监控统计
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: admin
        login-password: admin123
      # 开启Web监控
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      # 开启SQL监控
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 1000
        wall:
          enabled: true
          config:
            multi-statement-allow: true
  # 事务管理配置
  transaction:
    rollback-on-commit-failure: true
  # 配置热加载
  devtools:
    restart:
      enabled: true  # 设置开启热部署
      additional-paths: src/main/java  # 重启目录
      exclude: WEB-INF/**
    livereload:
      enabled: true  # 页面热部署

#mybatis plus配置
mybatis-plus:
  # 指定mapper xml文件位置
  mapper-locations: classpath*:mapper/**/*.xml
  # 指定实体类包路径
  type-aliases-package: com.example.entity
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 设置当字段为null时依然更新该字段
    call-setters-on-nulls: true
    # 本地缓存作用域设置为SESSION，与事务保持一致
    local-cache-scope: SESSION
    # 自动映射行为
    auto-mapping-behavior: PARTIAL
    # 执行器类型，SIMPLE适合事务管理
    default-executor-type: SIMPLE
    # 设置超时时间
    default-statement-timeout: 30
    # 设置获取数据的大小
    default-fetch-size: 100
  global-config:
    db-config:
      #配置mybatis plus 在更新时包括null值字段
      field-strategy: IGNORED
      # 主键类型 - 使用数据库自增
      id-type: AUTO





# Knife4j 美化配置
knife4j:
  enable: true
  setting:
    language: zh_cn
    enable-version: true
    enable-reload-cache-parameter: true
    enable-after-script: true
    enable-filter-multipart-api-method-type: POST
    enable-filter-multipart-apis: true
    enable-request-cache: true
    enable-host: false
    enable-host-text: localhost:8080
    enable-home-custom: true
    home-custom-location: classpath:markdown/home.md
    enable-search: true
    enable-footer: false
    enable-footer-custom: true
    footer-custom-content: 学生管理系统 API 文档
    enable-dynamic-parameter: true
    enable-debug: true
    enable-open-api: false
    enable-group: true
  cors: true
  production: false

#日志配置
logging:
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n'
  level:
    root: INFO
    com.example: INFO
    # MyBatis相关日志级别调整
    org.apache.ibatis: WARN
    org.mybatis: WARN
    com.baomidou.mybatisplus: WARN
    # SQL日志保持DEBUG级别以便调试
    com.example.mapper: DEBUG

