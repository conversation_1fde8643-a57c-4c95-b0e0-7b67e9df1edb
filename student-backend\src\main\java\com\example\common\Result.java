package com.example.common;

import lombok.Data;

/**
 * 统一响应结果
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
public class Result<T> {

    // ========== 状态码常量 ==========

    /** 成功 */
    public static final int SUCCESS = 200;

    /** 参数错误 */
    public static final int BAD_REQUEST = 400;

    /** 未授权 */
    public static final int UNAUTHORIZED = 401;

    /** 禁止访问 */
    public static final int FORBIDDEN = 403;

    /** 资源不存在 */
    public static final int NOT_FOUND = 404;

    /** 请求方法不允许 */
    public static final int METHOD_NOT_ALLOWED = 405;

    /** 服务器内部错误 */
    public static final int INTERNAL_SERVER_ERROR = 500;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 时间戳
     */
    private Long timestamp;

    public Result() {
        this.timestamp = System.currentTimeMillis();
    }

    public Result(Boolean success, Integer code, String message, T data) {
        this();
        this.success = success;
        this.code = code;
        this.message = message;
        this.data = data;
    }

    // ========== 成功响应方法 ==========

    /**
     * 成功响应
     */
    public static <T> Result<T> success() {
        return new Result<>(true, SUCCESS, "操作成功", null);
    }

    /**
     * 成功响应带数据
     */
    public static <T> Result<T> success(T data) {
        return new Result<>(true, SUCCESS, "操作成功", data);
    }

    /**
     * 成功响应带消息
     */
    public static <T> Result<T> success(String message) {
        return new Result<>(true, SUCCESS, message, null);
    }

    /**
     * 成功响应带消息和数据
     */
    public static <T> Result<T> success(String message, T data) {
        return new Result<>(true, SUCCESS, message, data);
    }

    // ========== 失败响应方法 ==========

    /**
     * 失败响应带消息
     */
    public static <T> Result<T> error(String message) {
        return new Result<>(false, INTERNAL_SERVER_ERROR, message, null);
    }

    /**
     * 失败响应带码和消息
     */
    public static <T> Result<T> error(Integer code, String message) {
        return new Result<>(false, code, message, null);
    }

    // ========== 参数错误响应方法 ==========

    /**
     * 参数错误响应带消息
     */
    public static <T> Result<T> badRequest(String message) {
        return new Result<>(false, BAD_REQUEST, message, null);
    }

    // ========== 未授权响应方法 ==========

    /**
     * 未授权响应带消息
     */
    public static <T> Result<T> unauthorized(String message) {
        return new Result<>(false, UNAUTHORIZED, message, null);
    }

    // ========== 禁止访问响应方法 ==========

    /**
     * 禁止访问响应带消息
     */
    public static <T> Result<T> forbidden(String message) {
        return new Result<>(false, FORBIDDEN, message, null);
    }

    // ========== 资源不存在响应方法 ==========

    /**
     * 资源不存在响应带消息
     */
    public static <T> Result<T> notFound(String message) {
        return new Result<>(false, NOT_FOUND, message, null);
    }

    // ========== 请求方法不允许响应方法 ==========

    /**
     * 请求方法不允许响应带消息
     */
    public static <T> Result<T> methodNotAllowed(String message) {
        return new Result<>(false, METHOD_NOT_ALLOWED, message, null);
    }

    // ========== 自定义响应方法 ==========

    /**
     * 自定义响应
     */
    public static <T> Result<T> result(Boolean success, Integer code, String message, T data) {
        return new Result<>(success, code, message, data);
    }
}
