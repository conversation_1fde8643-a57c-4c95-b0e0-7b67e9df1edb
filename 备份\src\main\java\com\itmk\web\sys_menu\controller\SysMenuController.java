package com.itmk.web.sys_menu.controller;

import com.itmk.utils.ResultUtils;
import com.itmk.utils.ResultVo;
import com.itmk.web.sys_menu.dto.MenuDTO;
import com.itmk.web.sys_menu.dto.MenuQueryDTO;
import com.itmk.web.sys_menu.dto.RouterDTO;
import com.itmk.web.sys_menu.service.SysMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 菜单管理控制器
 * 基于Spring Boot最佳实践优化
 */
@Api(tags = "菜单管理")
@RestController
@RequestMapping("/api/menu")
@Validated
public class SysMenuController {

    private static final Logger logger = LoggerFactory.getLogger(SysMenuController.class);

    @Autowired
    private SysMenuService sysMenuService;

    /**
     * 创建菜单
     */
    @ApiOperation("创建菜单")
    @PostMapping
    public ResultVo createMenu(@Valid @RequestBody MenuDTO menuDTO) {
        try {
            logger.info("创建菜单请求: {}", menuDTO.getTitle());
            boolean success = sysMenuService.createMenu(menuDTO);
            if (success) {
                return ResultUtils.success("菜单创建成功");
            }
            return ResultUtils.error("菜单创建失败");
        } catch (IllegalArgumentException e) {
            logger.warn("菜单创建参数错误: {}", e.getMessage());
            return ResultUtils.error(e.getMessage());
        } catch (Exception e) {
            logger.error("菜单创建异常", e);
            return ResultUtils.error("菜单创建失败：" + e.getMessage());
        }
    }

    /**
     * 更新菜单
     */
    @ApiOperation("更新菜单")
    @PutMapping
    public ResultVo updateMenu(@Valid @RequestBody MenuDTO menuDTO) {
        try {
            logger.info("更新菜单请求: {}", menuDTO.getTitle());
            boolean success = sysMenuService.updateMenu(menuDTO);
            if (success) {
                return ResultUtils.success("菜单更新成功");
            }
            return ResultUtils.error("菜单更新失败");
        } catch (IllegalArgumentException e) {
            logger.warn("菜单更新参数错误: {}", e.getMessage());
            return ResultUtils.error(e.getMessage());
        } catch (Exception e) {
            logger.error("菜单更新异常", e);
            return ResultUtils.error("菜单更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除菜单
     */
    @ApiOperation("删除菜单")
    @DeleteMapping("/{menuId}")
    public ResultVo deleteMenu(@ApiParam("菜单ID") @PathVariable("menuId") @NotNull Long menuId) {
        try {
            logger.info("删除菜单请求，ID: {}", menuId);
            boolean success = sysMenuService.deleteMenu(menuId);
            if (success) {
                return ResultUtils.success("菜单删除成功");
            }
            return ResultUtils.error("菜单删除失败");
        } catch (SecurityException e) {
            logger.warn("菜单删除权限错误: {}", e.getMessage());
            return ResultUtils.error(e.getMessage());
        } catch (IllegalStateException e) {
            logger.warn("菜单删除状态错误: {}", e.getMessage());
            return ResultUtils.error(e.getMessage());
        } catch (Exception e) {
            logger.error("菜单删除异常", e);
            return ResultUtils.error("菜单删除失败：" + e.getMessage());
        }
    }

    /**
     * 获取菜单列表
     */
    @ApiOperation("获取菜单列表")
    @GetMapping
    public ResultVo getMenuList() {
        try {
            logger.info("获取菜单列表请求");
            List<MenuDTO> menuList = sysMenuService.getMenuList();
            return ResultUtils.success("查询成功", menuList);
        } catch (Exception e) {
            logger.error("获取菜单列表异常", e);
            return ResultUtils.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据条件查询菜单列表
     */
    @ApiOperation("根据条件查询菜单列表")
    @PostMapping("/search")
    public ResultVo searchMenuList(@Valid @RequestBody MenuQueryDTO queryDTO) {
        try {
            logger.info("条件查询菜单列表请求: {}", queryDTO);
            List<MenuDTO> menuList = sysMenuService.getMenuList(queryDTO);
            return ResultUtils.success("查询成功", menuList);
        } catch (Exception e) {
            logger.error("条件查询菜单列表异常", e);
            return ResultUtils.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取上级菜单列表
     */
    @ApiOperation("获取上级菜单列表")
    @GetMapping("/parent")
    public ResultVo getParentMenuList() {
        try {
            logger.info("获取上级菜单列表请求");
            List<MenuDTO> parentMenuList = sysMenuService.getParentMenuList();
            return ResultUtils.success("查询成功", parentMenuList);
        } catch (Exception e) {
            logger.error("获取上级菜单列表异常", e);
            return ResultUtils.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据用户ID获取菜单路由
     */
    @ApiOperation("根据用户ID获取菜单路由")
    @GetMapping("/user/{userId}")
    public ResultVo getMenuByUserId(@ApiParam("用户ID") @PathVariable("userId") @NotNull Long userId) {
        try {
            logger.info("获取用户菜单路由请求，用户ID: {}", userId);
            List<RouterDTO> routerList = sysMenuService.getMenuByUserId(userId);
            return ResultUtils.success("查询成功", routerList);
        } catch (Exception e) {
            logger.error("获取用户菜单路由异常", e);
            return ResultUtils.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据学生ID获取菜单路由
     */
    @ApiOperation("根据学生ID获取菜单路由")
    @GetMapping("/student/{studentId}")
    public ResultVo getMenuByStudentId(@ApiParam("学生ID") @PathVariable("studentId") @NotNull Long studentId) {
        try {
            logger.info("获取学生菜单路由请求，学生ID: {}", studentId);
            List<RouterDTO> routerList = sysMenuService.getMenuByStudentId(studentId);
            return ResultUtils.success("查询成功", routerList);
        } catch (Exception e) {
            logger.error("获取学生菜单路由异常", e);
            return ResultUtils.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据教师ID获取菜单路由
     */
    @ApiOperation("根据教师ID获取菜单路由")
    @GetMapping("/teacher/{teacherId}")
    public ResultVo getMenuByTeacherId(@ApiParam("教师ID") @PathVariable("teacherId") @NotNull Long teacherId) {
        try {
            logger.info("获取教师菜单路由请求，教师ID: {}", teacherId);
            List<RouterDTO> routerList = sysMenuService.getMenuByTeacherId(teacherId);
            return ResultUtils.success("查询成功", routerList);
        } catch (Exception e) {
            logger.error("获取教师菜单路由异常", e);
            return ResultUtils.error("查询失败：" + e.getMessage());
        }
    }
}
