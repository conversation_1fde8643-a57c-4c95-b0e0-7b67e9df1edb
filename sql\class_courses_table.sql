-- ========================================
-- 班级课程分配表
-- 创建时间：2025-07-28
-- ========================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建班级课程分配表
DROP TABLE IF EXISTS `class_courses`;
CREATE TABLE `class_courses` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '分配ID',
  `class_code` varchar(20) NOT NULL COMMENT '班级代码',
  `course_code` varchar(20) NOT NULL COMMENT '课程代码',
  `semester_id` int NOT NULL COMMENT '学期ID',

  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用（1-启用，0-停用）',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `remark` text COMMENT '备注',
  `created_by` varchar(50) COMMENT '创建人',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(50) COMMENT '更新人',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_class_course_semester` (`class_code`, `course_code`, `semester_id`),
  KEY `idx_class_code` (`class_code`),
  KEY `idx_course_code` (`course_code`),
  KEY `idx_semester_id` (`semester_id`),
  KEY `idx_is_active` (`is_active`),
  CONSTRAINT `fk_class_courses_class` FOREIGN KEY (`class_code`) REFERENCES `classes` (`class_code`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_class_courses_course` FOREIGN KEY (`course_code`) REFERENCES `courses` (`course_code`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_class_courses_semester` FOREIGN KEY (`semester_id`) REFERENCES `semesters` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='班级课程分配表';

-- 插入示例数据
INSERT INTO `class_courses` (`class_code`, `course_code`, `semester_id`, `sort_order`, `created_by`) VALUES
-- 2023054801班第一学期课程
('2023054801', 'GDSXB', 1, 1, 'system'),
('2023054801', 'DXWYB', 1, 2, 'system'),
('2023054801', 'JSJWLJS', 1, 3, 'system'),
('2023054801', 'SJKYL', 1, 4, 'system'),
('2023054801', 'RJGC', 1, 5, 'system'),
-- 2023054801班第二学期课程
('2023054801', 'XXTJG', 2, 1, 'system'),
('2023054801', 'GLJJX', 2, 2, 'system'),
('2023054801', 'XTFX', 2, 3, 'system'),
-- 2023054802班第一学期课程
('2023054802', 'GDSXB', 1, 1, 'system'),
('2023054802', 'DXWYB', 1, 2, 'system'),
('2023054802', 'JSJWLJS', 1, 3, 'system'),
('2023054802', 'SJKYL', 1, 4, 'system'),
('2023054802', 'RJGC', 1, 5, 'system'),
-- 2023054803班第一学期课程
('2023054803', 'GDSXB', 1, 1, 'system'),
('2023054803', 'DXWYB', 1, 2, 'system'),
('2023054803', 'JSJWLJS', 1, 3, 'system'),
('2023054803', 'SJKYL', 1, 4, 'system'),
('2023054803', 'RJGC', 1, 5, 'system');

SET FOREIGN_KEY_CHECKS = 1;

-- 显示完成信息
SELECT 'Class Courses Table Created Successfully!' as status;
