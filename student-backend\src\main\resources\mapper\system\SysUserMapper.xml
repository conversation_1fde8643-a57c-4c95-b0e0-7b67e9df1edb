<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.system.SysUserMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.entity.system.SysUser">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="nickname" property="nickname" jdbcType="VARCHAR"/>
        <result column="avatar" property="avatar" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="gender" property="gender" jdbcType="TINYINT"/>
        <result column="dept_id" property="deptId" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="last_login_time" property="lastLoginTime" jdbcType="TIMESTAMP"/>
        <result column="last_login_ip" property="lastLoginIp" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 用户详情结果映射（包含部门信息） -->
    <resultMap id="UserDetailResultMap" type="com.example.entity.system.SysUser" extends="BaseResultMap">
        <result column="dept_name" property="deptName" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 通用SQL片段 -->
    <sql id="Base_Column_List">
        id, username, password, nickname, avatar, email, phone, gender,
        dept_id, status, remark, last_login_time, last_login_ip, create_time, update_time
    </sql>

    <!-- 用户详情SQL片段（包含部门信息） -->
    <sql id="User_Detail_Column_List">
        u.id, u.username, u.password, u.nickname, u.avatar, u.email, u.phone, u.gender,
        u.dept_id, u.status, u.remark, u.last_login_time, u.last_login_ip,
        u.create_time, u.update_time, d.dept_name
    </sql>

    <!-- 根据用户ID查询角色编码列表 -->
    <select id="selectRoleCodesByUserId" parameterType="java.lang.Integer" resultType="java.lang.String">
        SELECT r.role_code
        FROM sys_role r
        INNER JOIN sys_user_role ur ON r.id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND r.status = 1
        ORDER BY r.sort ASC
    </select>

    <!-- 根据用户ID查询权限列表 -->
    <select id="selectPermissionsByUserId" parameterType="java.lang.Integer" resultType="java.lang.String">
        SELECT DISTINCT m.auths
        FROM sys_menu m
        INNER JOIN sys_role_menu rm ON m.id = rm.menu_id
        INNER JOIN sys_user_role ur ON rm.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND m.status = 1
          AND m.auths IS NOT NULL
          AND m.auths != ''
    </select>

    <!-- 分页查询用户列表 -->
    <select id="selectUserPage" resultMap="UserDetailResultMap">
        SELECT
            <include refid="User_Detail_Column_List"/>
        FROM sys_user u
        LEFT JOIN sys_dept d ON u.dept_id = d.id AND d.status = 1
        <where>
            u.status != 0
            <if test="query.status != null">
                AND u.status = #{query.status}
            </if>
            <if test="query.deptId != null">
                AND u.dept_id = #{query.deptId}
            </if>
            <if test="query.gender != null">
                AND u.gender = #{query.gender}
            </if>
            <if test="query.username != null and query.username != ''">
                AND u.username LIKE CONCAT('%', #{query.username}, '%')
            </if>
            <if test="query.nickname != null and query.nickname != ''">
                AND u.nickname LIKE CONCAT('%', #{query.nickname}, '%')
            </if>
            <if test="query.email != null and query.email != ''">
                AND u.email = #{query.email}
            </if>
            <if test="query.phone != null and query.phone != ''">
                AND u.phone = #{query.phone}
            </if>
        </where>
        ORDER BY u.status DESC, u.create_time DESC
    </select>

    <!-- 根据用户名查询用户（用于登录验证） -->
    <select id="selectByUsername" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_user
        WHERE username = #{username}
          AND status = 1
    </select>

    <!-- 检查用户名是否存在 -->
    <select id="existsByUsername" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM sys_user
        WHERE username = #{username}
          AND status = 1
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查邮箱是否存在 -->
    <select id="existsByEmail" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM sys_user
        WHERE email = #{email}
          AND status = 1
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查手机号是否存在 -->
    <select id="existsByPhone" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM sys_user
        WHERE phone = #{phone}
          AND status = 1
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 根据部门ID查询用户数量 -->
    <select id="countByDeptId" parameterType="java.lang.Integer" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM sys_user
        WHERE dept_id = #{deptId}
          AND status = 1
    </select>

    <!-- 更新用户最后登录信息 -->
    <update id="updateLastLoginInfo">
        UPDATE sys_user
        SET last_login_time = #{lastLoginTime},
            last_login_ip = #{lastLoginIp},
            update_time = NOW()
        WHERE id = #{userId}
    </update>

    <!-- 批量更新用户状态 -->
    <update id="batchUpdateStatus">
        UPDATE sys_user
        SET status = #{status},
            update_time = NOW()
        WHERE id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </update>

    <!-- 重置用户密码 -->
    <update id="resetPassword">
        UPDATE sys_user
        SET password = #{newPassword},
            update_time = NOW()
        WHERE id = #{userId}
    </update>

</mapper>
