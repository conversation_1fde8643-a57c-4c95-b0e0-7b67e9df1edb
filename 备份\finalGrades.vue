<template>
  <el-main>
    <!-- 搜索表单 -->
    <SearchForm
      v-model="searchModel"
      :fields="searchFields"
      @search="handleSearch"
      @reset="handleReset"
      @refresh="handleRefresh"
    >
      <!-- 自定义操作按钮插槽 -->
      <template v-if="!isStudent" slot="actions">
        <ImportExport
          :export-api="exportGradesApi"
          :import-api="importGradesApi"
          :template-api="getGradeTemplateApi"
          :export-params="getExportParams()"
          :export-file-name="getExportFileName()"
          :template-file-name="'成绩导入模板.xlsx'"
          :import-dialog-title="'导入成绩'"
          @import-success="handleImportSuccess"
          @export-success="handleExportSuccess"
        />
      </template>
    </SearchForm>

    <!-- 成绩表格 -->
    <ElDataTable
      :data="horizontalData.students"
      :columns="dynamicTableColumns"
      :loading="loading"
      :pagination="pagination"
      @row-action="handleRowAction"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
      <!-- 动态课程列插槽 -->
      <template v-for="course in horizontalData.courses" :slot="course" slot-scope="{ text, record }">
        <span :key="course">{{ record[course] !== null && record[course] !== undefined ? Number(record[course]).toFixed(2) : '-' }}</span>
      </template>

      <!-- 平均分插槽 -->
      <template slot="average" slot-scope="{ text, record }">
        <span>{{ record.average !== null && record.average !== undefined ? record.average.toFixed(2) : '-' }}</span>
      </template>

      <!-- 学业成绩插槽 -->
      <template slot="academicScore" slot-scope="{ text, record }">
        <span>{{ record.academicScore !== null && record.academicScore !== undefined ? record.academicScore.toFixed(2) : '-' }}</span>
      </template>

      <!-- 绩点插槽 -->
      <template slot="calculateGpa" slot-scope="{ text, record }">
        <span>{{ record.calculateGpa !== null && record.calculateGpa !== undefined ? record.calculateGpa.toFixed(2) : '-' }}</span>
      </template>
    </ElDataTable>

    <!-- 编辑成绩对话框 -->
    <SysDialog
      title="编辑学生成绩"
      :visible.sync="editGradeDialogVisible"
      :smart-resize="true"
      :close-on-click-modal="false"
      confirm-button-text="保存"
      cancel-button-text="取消"
      :loading="savingGrades"
      @close="onEditGradeClose"
      @confirm="onEditGradeConfirm"
    >
        <div v-if="currentStudent">
          <div class="student-info">
            <p><strong>学号：</strong>{{ currentStudent.studentId }}</p>
            <p><strong>姓名：</strong>{{ currentStudent.studentName }}</p>
          </div>

          <div class="grade-form-container">
            <div class="grade-grid">
              <div v-for="(course, index) in editGradeList" :key="index" class="grade-item">
                <div class="course-name-code">
                  <span class="course-name">{{ course.courseName }}</span>
                  <span class="course-code">({{ course.courseCode }})</span>
                </div>
                <div class="grade-controls">
                  <el-input
                    :value="getSemesterName(course.semesterId)"
                    placeholder="学期"
                    size="small"
                    class="semester-select"
                    readonly
                    disabled
                  >
                    <template slot="suffix">
                      <i class="el-icon-info" title="学期由课程决定，不可修改" />
                    </template>
                  </el-input>
                  <div class="grade-input-group">
                    <template v-if="course.grade !== null">
                      <el-input-number
                        v-model="course.grade"
                        :min="0"
                        :max="100"
                        :precision="2"
                        :step="0.1"
                        size="small"
                        :controls="false"
                        class="grade-input"
                      />
                    </template>
                    <template v-else>
                      <el-input
                        placeholder="无成绩"
                        size="small"
                        class="grade-input no-grade"
                        readonly
                      />
                    </template>
                    <el-button
                      v-if="course.grade !== null"
                      type="text"
                      icon="el-icon-close"
                      class="grade-action-btn clear-btn"
                      title="清除成绩"
                      @click="clearGrade(course)"
                    />
                    <el-button
                      v-else
                      type="text"
                      icon="el-icon-plus"
                      class="grade-action-btn add-btn"
                      title="添加成绩"
                      @click="addGrade(course)"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
    </SysDialog>
  </el-main>
</template>

<script>
import { getHorizontalGradesApi, editGradeApi, exportGradesApi, getGradeTemplateApi, importGradesApi } from '@/api/grades'
import { getAcademicYearsApi, getSemestersByAcademicYearApi } from '@/api/term'
import { getCourseListApi } from '@/api/course'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      loading: false,

      // 搜索表单配置
      searchFields: [
        {
          type: 'academic-year-semester',
          prop: 'academicYearSemester',
          label: '学年学期',
          placeholder: '请选择学年学期',
          options: []
        }
      ],

      // 搜索模型
      searchModel: {
        academicYearSemester: []
      },

      // 表格配置 - 动态生成
      tableColumns: [],

      // 分页配置
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },

      // 数据
      termList: [],
      horizontalData: {
        courses: [],
        courseInfos: [],
        students: []
      },

      // 编辑成绩相关
      editGradeDialogVisible: false,
      currentStudent: null,
      editGradeList: [],
      savingGrades: false,

      // API 方法
      exportGradesApi,
      importGradesApi,
      getGradeTemplateApi
    }
  },
  computed: {
    ...mapGetters(['userType', 'name', 'username']),
    // 判断当前用户是否为学生
    isStudent() {
      return this.userType === '0'
    },

    // 动态生成表格列配置
    dynamicTableColumns() {
      const columns = [
        { type: 'index', label: '序号', width: 60, fixed: 'left' },
        { prop: 'studentId', label: '学号', width: 120, fixed: 'left' },
        { prop: 'studentName', label: '姓名', width: 100, fixed: 'left' }
      ]

      // 添加动态课程列
      this.horizontalData.courses.forEach(course => {
        columns.push({
          prop: course,
          label: this.getCourseNameByCode(course),
          width: 130,
          slot: course
        })
      })

      // 添加统计列
      columns.push(
        { prop: 'average', label: '平均分', width: 100, slot: 'average' },
        { prop: 'academicScore', label: '学业成绩', width: 100, slot: 'academicScore' },
        { prop: 'calculateGpa', label: '绩点', width: 100, slot: 'calculateGpa' }
      )

      // 如果不是学生，添加操作列
      if (!this.isStudent) {
        columns.push({
          type: 'actions',
          label: '操作',
          width: 120,
          fixed: 'right',
          actions: [
            {
              key: 'edit',
              label: '编辑',
              type: 'primary',
              size: 'mini'
            }
          ]
        })
      }

      return columns
    }
  },
  created() {
    // SearchForm 组件会自动处理学年学期选项的加载
    // 加载完整的学期列表用于学期名称显示
    this.loadTermList()
    // 这里只需要更新成绩表学期ID（如果需要的话）
    this.updateGradesSemesterId()
  },
  methods: {
    // 搜索表单事件
    handleSearch(model) {
      this.searchModel = { ...model }
      this.pagination.currentPage = 1
      this.getHorizontalGrades()
    },

    handleReset(model) {
      this.searchModel = { ...model }
      this.pagination.currentPage = 1
      this.getHorizontalGrades()
    },

    handleRefresh() {
      this.pagination.currentPage = 1
      this.getHorizontalGrades()
    },

    // 表格事件
    handleRowAction({ key, row }) {
      if (key === 'edit') {
        this.handleEditGrade(row)
      }
    },

    // 分页事件
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.currentPage = 1
      this.getHorizontalGrades()
    },

    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.getHorizontalGrades()
    },

    // 导入导出事件
    getExportParams() {
      const params = {}

      // 解析学年学期级联选择器的值
      if (this.searchModel.academicYearSemester && this.searchModel.academicYearSemester.length > 0) {
        params.academicYear = this.searchModel.academicYearSemester[0]

        // 如果选择了具体学期
        if (this.searchModel.academicYearSemester.length === 2) {
          params.semesterId = this.searchModel.academicYearSemester[1]
        }
      }

      return params
    },

    getExportFileName() {
      let year = '全部'
      if (this.searchModel.academicYearSemester && this.searchModel.academicYearSemester.length > 0) {
        year = this.searchModel.academicYearSemester[0]
      }
      return `成绩信息_${year}.xlsx`
    },

    handleImportSuccess() {
      this.getHorizontalGrades()
    },

    handleExportSuccess() {
      // 导出成功后的处理
    },

    // 获取课程名称
    getCourseNameByCode(courseCode) {
      const courseInfo = this.horizontalData.courseInfos.find(info => info.code === courseCode)
      return courseInfo ? courseInfo.name : courseCode
    },

    // 获取学期名称
    getSemesterName(semesterId) {
      if (!semesterId) return '未知学期'
      const semester = this.termList.find(term =>
        (term.id === semesterId) || (term.semesterId === semesterId)
      )
      return semester ? semester.semesterName : `学期${semesterId}`
    },
    // 清除成绩
    clearGrade(course) {
      // 确保将成绩设置为null而不是0或undefined
      this.$set(course, 'grade', null)
    },

    // 添加成绩
    addGrade(course) {
      // 设置初始成绩为0，而不是null
      this.$set(course, 'grade', 0)
    },

    // 获取横向成绩数据
    async getHorizontalGrades() {
      this.loading = true
      try {
        // 构建参数对象
        const params = {
          currentPage: this.pagination.currentPage,
          pageSize: this.pagination.pageSize
        }

        // 如果是学生用户，添加学生ID限制
        if (this.isStudent) {
          params.studentId = this.username // 使用当前登录用户的学号
        }

        // 解析学年学期级联选择器的值
        if (this.searchModel.academicYearSemester && this.searchModel.academicYearSemester.length > 0) {
          params.academicYear = this.searchModel.academicYearSemester[0]

          // 如果选择了具体学期
          if (this.searchModel.academicYearSemester.length === 2) {
            params.semesterId = this.searchModel.academicYearSemester[1]
          }
        } else {
          // 如果没有选择学年学期，提示用户
          this.$message({ type: 'warning', message: '请先选择学年学期' })
          this.loading = false
          return
        }

        const res = await getHorizontalGradesApi(params)
        if (res && res.code === 200) {
          this.horizontalData = res.data
          this.pagination.total = res.data.total || 0
        } else {
          this.$message({ type: 'error', message: res.msg || '获取成绩数据失败' })
        }
      } catch (error) {
        this.$message({ type: 'error', message: '获取成绩数据出错' })
      } finally {
        this.loading = false
      }
    },

    // 更新成绩表学期ID
    async updateGradesSemesterId() {
      try {
        // 该API已废弃，直接返回成功
        const res = { code: 200, msg: '更新成功' }
        if (res && res.code === 200) {
          return res
        } else {
          return Promise.reject(new Error('更新学期ID失败'))
        }
      } catch (error) {
        return Promise.reject(error)
      }
    },

    // 处理单击编辑成绩
    async handleEditGrade(student) {
      try {
        this.currentStudent = student
        this.editGradeList = []

        // 确保已加载学期列表
        if (this.termList.length === 0) {
          await this.loadTermList()
        }

        // 获取当前选择的学期ID作为默认值
        let defaultSemesterId = null
        if (this.searchModel.semesterIds && this.searchModel.semesterIds.length > 0) {
          defaultSemesterId = this.searchModel.semesterIds[0]
        } else if (this.termList.length > 0) {
          // 如果没有选择学期，则使用第一个可用学期
          defaultSemesterId = this.termList[0].id || this.termList[0].semesterId
        }

        // 收集需要处理的课程代码
        const coursesToProcess = []
        for (const courseCode of this.horizontalData.courses) {
          if (student[courseCode] !== undefined) {
            coursesToProcess.push(courseCode)
          }
        }

        if (coursesToProcess.length === 0) {
          this.$message({ type: 'warning', message: '该学生没有成绩记录' })
          return
        }

        // 查询所有课程信息以获取课程的正确学期ID
        const courseSemesterMap = {}
        try {
          // 查询课程列表获取课程的学期信息
          const courseResponse = await getCourseListApi({ currentPage: 1, pageSize: 1000 })
          if (courseResponse && courseResponse.data && courseResponse.data.records) {
            // 构建课程代码到学期ID的映射
            courseResponse.data.records.forEach(course => {
              if (course.courseCode && course.semesterId) {
                courseSemesterMap[course.courseCode] = course.semesterId
              }
            })
          }
        } catch (error) {
          // 获取课程学期信息失败，继续处理
        }

        // 处理每个课程
        // eslint-disable-next-line require-atomic-updates
        for (const courseCode of coursesToProcess) {
          // 查找课程信息
          const courseInfo = this.horizontalData.courseInfos.find(info => info.code === courseCode)

          // 确定学期ID：优先使用从数据库获取的，其次使用默认值
          const semesterId = courseSemesterMap[courseCode] || defaultSemesterId

          this.editGradeList.push({
            courseCode: courseCode,
            courseName: courseInfo ? courseInfo.name : courseCode,
            semesterId: semesterId,
            grade: student[courseCode], // 保持原始值，可能为null
            studentId: student.studentId
          })
        }

        this.editGradeDialogVisible = true
      } catch (error) {
        this.$message({ type: 'error', message: '准备编辑数据失败: ' + (error.message || '未知错误') })
      }
    },

    // 加载完整学期列表（仅用于编辑和导入功能，需要完整的学期数据来匹配）
    async loadTermList() {
      try {
        // 先获取所有学年
        const academicYearsRes = await getAcademicYearsApi()
        if (academicYearsRes && academicYearsRes.code === 200) {
          const academicYears = academicYearsRes.data || []

          // 获取所有学年的学期数据
          const allSemesters = []
          for (const year of academicYears) {
            try {
              const semestersRes = await getSemestersByAcademicYearApi(year)
              if (semestersRes && semestersRes.code === 200) {
                allSemesters.push(...(semestersRes.data || []))
              }
            } catch (error) {
              // 获取学年学期数据失败，跳过该学年
            }
          }

          // 确保学期数据中有id和semesterId属性
          this.termList = allSemesters.map(term => {
            const normalizedTerm = { ...term }

            // 确保id属性存在
            if (!normalizedTerm.id && normalizedTerm.semesterId) {
              normalizedTerm.id = normalizedTerm.semesterId
            }

            // 确保semesterId属性存在
            if (!normalizedTerm.semesterId && normalizedTerm.id) {
              normalizedTerm.semesterId = normalizedTerm.id
            }

            return normalizedTerm
          })

          // 按semesterId排序（降序，最新学期在前）
          this.termList.sort((a, b) => {
            const idA = a.semesterId || a.id || 0
            const idB = b.semesterId || b.id || 0
            return idB - idA
          })
        } else {
          this.$message({ type: 'error', message: '获取学年列表失败' })
        }
      } catch (error) {
        this.$message({ type: 'error', message: '加载学期列表失败' })
      }
    },

    // 编辑成绩对话框关闭
    onEditGradeClose() {
      // 弹窗会自动关闭
      this.currentStudent = null
      this.editGradeList = []
    },

    // 编辑成绩对话框确认
    async onEditGradeConfirm() {
      if (!this.editGradeList.length) {
        this.$message({ type: 'warning', message: '没有可保存的成绩' })
        return
      }

      // 确保学期列表已加载
      if (this.termList.length === 0) {
        try {
          await this.loadTermList()
        } catch (error) {
          this.$message({ type: 'error', message: '无法加载学期列表，请刷新页面重试' })
          return
        }
      }

      // 获取默认学期ID
      let defaultSemesterId = null
      if (this.searchModel.semesterIds && this.searchModel.semesterIds.length > 0) {
        defaultSemesterId = this.searchModel.semesterIds[0]
      } else if (this.termList.length > 0) {
        defaultSemesterId = this.termList[0].id || this.termList[0].semesterId
      } else {
        this.$message({ type: 'error', message: '没有可用的学期信息，无法保存成绩' })
        return
      }

      try {
        this.savingGrades = true

        // 成功和失败计数
        let successCount = 0
        let failureCount = 0
        const errorDetails = []

        // 先验证所有成绩项是否都有有效的学期ID
        const invalidItems = this.editGradeList.filter(item => !item.semesterId)
        if (invalidItems.length > 0) {
          // 为缺少学期ID的记录设置默认学期ID
          invalidItems.forEach(item => {
            item.semesterId = defaultSemesterId
          })
        }

        // 逐个保存成绩
        for (const gradeItem of this.editGradeList) {
          // 构建保存参数，允许成绩为null
          const params = {
            studentId: gradeItem.studentId,
            courseCode: gradeItem.courseCode,
            semesterId: gradeItem.semesterId || defaultSemesterId,
            grade: gradeItem.grade // 可以是null
          }

          try {
            // 调用API保存成绩
            const response = await editGradeApi(params)
            if (response && response.code === 200) {
              successCount++
            } else {
              failureCount++
              const errorMsg = response ? response.msg : '未知错误'
              errorDetails.push(`课程 ${params.courseCode}: ${errorMsg}`)
            }
          } catch (error) {
            failureCount++
            errorDetails.push(`课程 ${gradeItem.courseCode}: ${error.message || '未知错误'}`)
          }
        }

        if (failureCount > 0) {
          let errorMessage = `成绩保存部分完成：成功 ${successCount} 条，失败 ${failureCount} 条`
          if (errorDetails.length > 0) {
            // 最多显示3条错误详情
            const detailsToShow = errorDetails.slice(0, 3)
            errorMessage += `\n错误详情: ${detailsToShow.join('; ')}`
            if (errorDetails.length > 3) {
              errorMessage += `...等${errorDetails.length - 3}条`
            }
          }
          this.$message({ type: 'warning', message: errorMessage })
        } else {
          this.$message({ type: 'success', message: `成绩保存成功，共保存 ${successCount} 条记录` })
        }

        this.editGradeDialogVisible = false // 手动关闭弹窗
        this.onEditGradeClose()
        // 刷新成绩数据
        this.getHorizontalGrades()
      } catch (error) {
        this.$message({ type: 'error', message: '保存成绩失败: ' + (error.message || '未知错误') })
      } finally {
        this.savingGrades = false
      }
    }
  }
}
</script>

<style scoped>

/* 学生信息样式 */
.student-info {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.student-info p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #606266;
}

.student-info p:last-child {
  margin-bottom: 0;
}

/* 成绩表单容器样式 */
.grade-form-container {
  max-height: 500px;
  overflow-y: auto;
  padding: 10px 0;
}

/* 成绩网格布局 */
.grade-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  padding: 0 10px;
}

/* 成绩项样式 */
.grade-item {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
  background-color: #fafafa;
  transition: all 0.2s ease;
  min-height: 120px;
  display: flex;
  flex-direction: column;
}

.grade-item:hover {
  border-color: #c6e2ff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 课程名称区域 */
.course-name-code {
  margin-bottom: 12px;
  flex-shrink: 0;
}

.course-name {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
  display: block;
  line-height: 1.4;
}

.course-code {
  color: #909399;
  font-size: 12px;
  margin-top: 2px;
  display: block;
}

/* 成绩控制区域 */
.grade-controls {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 学期选择器 */
.semester-select {
  width: 100%;
}

/* 成绩输入组 */
.grade-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.grade-input {
  flex: 1;
  min-width: 0;
}

.grade-input.no-grade {
  text-align: center;
}

/* 操作按钮 */
.grade-action-btn {
  padding: 0;
  margin: 0;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  flex-shrink: 0;
}

.clear-btn {
  color: #f56c6c;
}

.clear-btn:hover {
  color: #f56c6c;
  background-color: #fef0f0;
}

.add-btn {
  color: #67c23a;
}

.add-btn:hover {
  color: #67c23a;
  background-color: #f0f9ff;
}

/* 表格样式已移至全局样式 */
</style>
