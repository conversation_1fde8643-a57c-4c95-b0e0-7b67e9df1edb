-- 基本素质测评表
CREATE TABLE `quality_assessment` (
  `evaluation_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `student_id` varchar(20) NOT NULL COMMENT '学生学号',
  `student_name` varchar(50) NOT NULL COMMENT '学生姓名',
  `dormitory_no` varchar(20) DEFAULT NULL COMMENT '宿舍号',
  `add_score` decimal(5,2) DEFAULT '0.00' COMMENT '加分',
  `reduce_score` decimal(5,2) DEFAULT '0.00' COMMENT '扣分',
  `add_score_remark` text COMMENT '加分说明',
  `reduce_score_remark` text COMMENT '扣分说明',
  `evaluation_period` varchar(20) NOT NULL COMMENT '评分学期',
  `evaluation_period_name` varchar(50) NOT NULL COMMENT '评分学期名称',
  `semester_name` varchar(50) NOT NULL COMMENT '学期名称',
  `period_score` decimal(5,2) DEFAULT '60.00' COMMENT '基础分',
  `total_score` decimal(5,2) DEFAULT '60.00' COMMENT '总得分',
  `class_id` varchar(20) NOT NULL COMMENT '班级ID',
  `class_name` varchar(50) NOT NULL COMMENT '班级名称',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`evaluation_id`),
  UNIQUE KEY `uk_student_period` (`student_id`, `evaluation_period`) COMMENT '学生在同一学期只能有一条记录',
  KEY `idx_class_period` (`class_id`, `evaluation_period`) COMMENT '班级学期索引',
  KEY `idx_student_id` (`student_id`) COMMENT '学生学号索引',
  KEY `idx_dormitory_no` (`dormitory_no`) COMMENT '宿舍号索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='基本素质测评表';

-- 插入示例数据
INSERT INTO `quality_assessment` (
  `student_id`, `student_name`, `dormitory_no`, `add_score`, `reduce_score`, 
  `add_score_remark`, `reduce_score_remark`, `evaluation_period`, `evaluation_period_name`, 
  `semester_name`, `period_score`, `total_score`, `class_id`, `class_name`
) VALUES 
('2021001001', '张三', '101-201', 5.00, 2.00, '参加志愿活动 +3，获得奖学金 +2', '迟到 -1，违纪 -1', '1', '2023-2024学年第一学期', '2023-2024学年第一学期', 60.00, 63.00, 'CS2021001', '计算机科学与技术2021-1班'),
('2021001002', '李四', '101-202', 3.00, 1.00, '参加社团活动 +3', '缺课 -1', '1', '2023-2024学年第一学期', '2023-2024学年第一学期', 60.00, 62.00, 'CS2021001', '计算机科学与技术2021-1班'),
('2021001003', '王五', '101-203', 2.00, 0.00, '参加比赛 +2', '', '1', '2023-2024学年第一学期', '2023-2024学年第一学期', 60.00, 62.00, 'CS2021001', '计算机科学与技术2021-1班'),
('2021001004', '赵六', '101-204', 1.00, 3.00, '参加讲座 +1', '违反宿舍管理规定 -3', '1', '2023-2024学年第一学期', '2023-2024学年第一学期', 60.00, 58.00, 'CS2021001', '计算机科学与技术2021-1班'),
('2021001005', '钱七', '101-205', 4.00, 0.00, '优秀学生干部 +4', '', '1', '2023-2024学年第一学期', '2023-2024学年第一学期', 60.00, 64.00, 'CS2021001', '计算机科学与技术2021-1班');

-- 创建视图：基本素质测评统计
CREATE VIEW `v_quality_assessment_stats` AS
SELECT 
  `class_id`,
  `class_name`,
  `evaluation_period`,
  `evaluation_period_name`,
  COUNT(*) as `total_students`,
  AVG(`total_score`) as `avg_score`,
  MAX(`total_score`) as `max_score`,
  MIN(`total_score`) as `min_score`,
  SUM(CASE WHEN `total_score` >= 80 THEN 1 ELSE 0 END) as `excellent_count`,
  SUM(CASE WHEN `total_score` >= 60 AND `total_score` < 80 THEN 1 ELSE 0 END) as `good_count`,
  SUM(CASE WHEN `total_score` < 60 THEN 1 ELSE 0 END) as `poor_count`
FROM `quality_assessment`
GROUP BY `class_id`, `class_name`, `evaluation_period`, `evaluation_period_name`;

-- 创建索引优化查询性能
CREATE INDEX `idx_create_time` ON `quality_assessment` (`create_time`);
CREATE INDEX `idx_total_score` ON `quality_assessment` (`total_score`);
CREATE INDEX `idx_class_period_score` ON `quality_assessment` (`class_id`, `evaluation_period`, `total_score`);
