<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.basic.CollegeMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.entity.basic.College">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="college_code" property="collegeCode" jdbcType="VARCHAR"/>
        <result column="college_name" property="collegeName" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, college_code, college_name, description, created_at, updated_at
    </sql>

    <!-- 分页查询学院列表 -->
    <select id="selectCollegePage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM colleges
        <where>
            <if test="query.collegeCode != null and query.collegeCode != ''">
                AND college_code LIKE CONCAT('%', #{query.collegeCode}, '%')
            </if>
            <if test="query.collegeName != null and query.collegeName != ''">
                AND college_name LIKE CONCAT('%', #{query.collegeName}, '%')
            </if>
        </where>
        ORDER BY
        <if test="query.sortField == 'collegeCode'">college_code</if>
        <if test="query.sortField == 'createdAt'">created_at</if>
        <if test="query.sortField != 'collegeCode' and query.sortField != 'createdAt'">CONVERT(college_name USING gbk)</if>
        <choose>
            <when test="query.sortOrder == 'desc'">DESC</when>
            <otherwise>ASC</otherwise>
        </choose>
    </select>

    <!-- 查询所有学院 -->
    <select id="selectAllColleges" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM colleges
        ORDER BY college_code
    </select>

    <!-- 根据学院代码查询学院 -->
    <select id="selectByCollegeCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM colleges
        WHERE college_code = #{collegeCode}
    </select>

    <!-- 检查学院代码是否存在 -->
    <select id="existsByCollegeCode" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM colleges
        WHERE college_code = #{collegeCode}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查学院名称是否存在 -->
    <select id="existsByCollegeName" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM colleges
        WHERE college_name = #{collegeName}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

</mapper>
