package com.example.dto.system;

import lombok.Data;

/**
 * 角色查询DTO
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
public class RoleQueryDTO {

    /**
     * 页码
     */
    private Integer page = 1;

    /**
     * 每页大小
     */
    private Integer size = 10;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 状态：0禁用、1启用
     */
    private Integer status;

    /**
     * 创建时间开始
     */
    private String createTimeStart;

    /**
     * 创建时间结束
     */
    private String createTimeEnd;

    /**
     * 排序字段
     */
    private String orderBy = "create_time";

    /**
     * 排序方向：asc、desc
     */
    private String orderDirection = "desc";
}
