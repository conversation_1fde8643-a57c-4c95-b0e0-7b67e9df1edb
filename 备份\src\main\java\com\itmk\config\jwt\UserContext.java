package com.itmk.config.jwt;

import lombok.Data;

/**
 * 用户上下文信息
 */
@Data
public class UserContext {
    private Long userId;
    private String username;
    private String userType; // 0:学生 1:教师 2:管理员

    // 线程本地变量，存储当前登录用户信息
    private static final ThreadLocal<UserContext> USER_CONTEXT = new ThreadLocal<>();

    /**
     * 设置当前用户信息
     */
    public static void setCurrentUser(UserContext userContext) {
        USER_CONTEXT.set(userContext);
    }

    /**
     * 获取当前用户信息
     */
    public static UserContext getCurrentUser() {
        return USER_CONTEXT.get();
    }

    /**
     * 获取当前用户ID
     */
    public static Long getCurrentUserId() {
        UserContext userContext = getCurrentUser();
        return userContext != null ? userContext.getUserId() : null;
    }

    /**
     * 获取当前用户名
     */
    public static String getCurrentUsername() {
        UserContext userContext = getCurrentUser();
        return userContext != null ? userContext.getUsername() : null;
    }

    /**
     * 获取当前用户类型
     */
    public static String getCurrentUserType() {
        UserContext userContext = getCurrentUser();
        return userContext != null ? userContext.getUserType() : null;
    }

    /**
     * 判断当前用户是否为学生
     */
    public static boolean isStudent() {
        String userType = getCurrentUserType();
        return "0".equals(userType);
    }

    /**
     * 判断当前用户是否为教师
     */
    public static boolean isTeacher() {
        String userType = getCurrentUserType();
        return "1".equals(userType);
    }

    /**
     * 判断当前用户是否为管理员
     */
    public static boolean isAdmin() {
        String userType = getCurrentUserType();
        return "2".equals(userType);
    }

    /**
     * 清除当前用户上下文
     */
    public static void clear() {
        USER_CONTEXT.remove();
    }

    // Setter methods for compatibility
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }
}
