package com.itmk.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 日志工具类
 * 提供统一的日志输出方法，特别是针对定时任务和业务操作的日志记录
 * 
 * <AUTHOR>
 * @since 2025-07-19
 */
public class LogUtils {
    
    /**
     * 记录任务开始执行的日志
     * 
     * @param logger 日志记录器
     * @param taskName 任务名称
     */
    public static void logTaskStart(Logger logger, String taskName) {
        logger.info("{} {} 开始执行...", DateTimeUtils.getLogTimeString(), taskName);
    }
    
    /**
     * 记录任务开始执行的日志（带描述）
     * 
     * @param logger 日志记录器
     * @param taskName 任务名称
     * @param description 任务描述
     */
    public static void logTaskStart(Logger logger, String taskName, String description) {
        logger.info("{} {} 开始执行：{}", DateTimeUtils.getLogTimeString(), taskName, description);
    }
    
    /**
     * 记录任务成功完成的日志
     * 
     * @param logger 日志记录器
     * @param taskName 任务名称
     */
    public static void logTaskSuccess(Logger logger, String taskName) {
        logger.info("{} {} 执行完成", DateTimeUtils.getLogTimeString(), taskName);
    }
    
    /**
     * 记录任务成功完成的日志（带消息）
     * 
     * @param logger 日志记录器
     * @param taskName 任务名称
     * @param message 成功消息
     */
    public static void logTaskSuccess(Logger logger, String taskName, String message) {
        logger.info("{} {} 执行完成：{}", DateTimeUtils.getLogTimeString(), taskName, message);
    }
    
    /**
     * 记录任务执行失败的日志
     * 
     * @param logger 日志记录器
     * @param taskName 任务名称
     * @param errorMessage 错误消息
     */
    public static void logTaskError(Logger logger, String taskName, String errorMessage) {
        logger.error("{} {} 执行失败：{}", DateTimeUtils.getLogTimeString(), taskName, errorMessage);
    }
    
    /**
     * 记录任务执行失败的日志（带异常）
     * 
     * @param logger 日志记录器
     * @param taskName 任务名称
     * @param errorMessage 错误消息
     * @param exception 异常对象
     */
    public static void logTaskError(Logger logger, String taskName, String errorMessage, Exception exception) {
        logger.error("{} {} 执行失败：{}", DateTimeUtils.getLogTimeString(), taskName, errorMessage, exception);
    }
    
    /**
     * 记录任务执行警告的日志
     *
     * @param logger 日志记录器
     * @param taskName 任务名称
     * @param warningMessage 警告消息
     */
    public static void logTaskWarning(Logger logger, String taskName, String warningMessage) {
        logger.warn("{} {} 执行警告：{}", DateTimeUtils.getLogTimeString(), taskName, warningMessage);
    }

    /**
     * 记录任务跳过执行的日志
     *
     * @param logger 日志记录器
     * @param taskName 任务名称
     * @param reason 跳过原因
     */
    public static void logTaskSkip(Logger logger, String taskName, String reason) {
        logger.info("{} {} 跳过执行：{}", DateTimeUtils.getLogTimeString(), taskName, reason);
    }
    
    /**
     * 记录业务操作开始的日志
     * 
     * @param logger 日志记录器
     * @param operation 操作名称
     * @param target 操作目标
     */
    public static void logOperationStart(Logger logger, String operation, String target) {
        logger.info("{} 开始执行 {} 操作，目标：{}", DateTimeUtils.getLogTimeString(), operation, target);
    }
    
    /**
     * 记录业务操作成功的日志
     * 
     * @param logger 日志记录器
     * @param operation 操作名称
     * @param target 操作目标
     * @param result 操作结果
     */
    public static void logOperationSuccess(Logger logger, String operation, String target, String result) {
        logger.info("{} {} 操作成功，目标：{}，结果：{}", 
            DateTimeUtils.getLogTimeString(), operation, target, result);
    }
    
    /**
     * 记录业务操作失败的日志
     * 
     * @param logger 日志记录器
     * @param operation 操作名称
     * @param target 操作目标
     * @param errorMessage 错误消息
     */
    public static void logOperationError(Logger logger, String operation, String target, String errorMessage) {
        logger.error("{} {} 操作失败，目标：{}，错误：{}", 
            DateTimeUtils.getLogTimeString(), operation, target, errorMessage);
    }
    
    /**
     * 记录业务操作失败的日志（带异常）
     * 
     * @param logger 日志记录器
     * @param operation 操作名称
     * @param target 操作目标
     * @param errorMessage 错误消息
     * @param exception 异常对象
     */
    public static void logOperationError(Logger logger, String operation, String target, 
                                       String errorMessage, Exception exception) {
        logger.error("{} {} 操作失败，目标：{}，错误：{}", 
            DateTimeUtils.getLogTimeString(), operation, target, errorMessage, exception);
    }
    
    /**
     * 记录性能监控日志
     * 
     * @param logger 日志记录器
     * @param methodName 方法名称
     * @param executionTime 执行时间（毫秒）
     */
    public static void logPerformance(Logger logger, String methodName, long executionTime) {
        if (executionTime > 2000) { // 超过2秒记录为警告
            logger.warn("{} 方法 {} 执行时间较长：{}ms", 
                DateTimeUtils.getLogTimeString(), methodName, executionTime);
        } else if (executionTime > 1000) { // 超过1秒记录为信息
            logger.info("{} 方法 {} 执行时间：{}ms", 
                DateTimeUtils.getLogTimeString(), methodName, executionTime);
        } else {
            logger.debug("{} 方法 {} 执行时间：{}ms", 
                DateTimeUtils.getLogTimeString(), methodName, executionTime);
        }
    }
    
    /**
     * 记录数据统计日志
     * 
     * @param logger 日志记录器
     * @param operation 操作名称
     * @param successCount 成功数量
     * @param errorCount 失败数量
     */
    public static void logDataStatistics(Logger logger, String operation, int successCount, int errorCount) {
        logger.info("{} {} 操作完成，成功：{}条，失败：{}条", 
            DateTimeUtils.getLogTimeString(), operation, successCount, errorCount);
    }
    
    /**
     * 记录数据统计日志（带总数）
     * 
     * @param logger 日志记录器
     * @param operation 操作名称
     * @param totalCount 总数量
     * @param successCount 成功数量
     * @param errorCount 失败数量
     */
    public static void logDataStatistics(Logger logger, String operation, int totalCount, 
                                       int successCount, int errorCount) {
        logger.info("{} {} 操作完成，总计：{}条，成功：{}条，失败：{}条", 
            DateTimeUtils.getLogTimeString(), operation, totalCount, successCount, errorCount);
    }
    
    /**
     * 获取指定类的Logger实例
     * 
     * @param clazz 类对象
     * @return Logger实例
     */
    public static Logger getLogger(Class<?> clazz) {
        return LoggerFactory.getLogger(clazz);
    }
    
    /**
     * 获取指定名称的Logger实例
     * 
     * @param name Logger名称
     * @return Logger实例
     */
    public static Logger getLogger(String name) {
        return LoggerFactory.getLogger(name);
    }
    
    /**
     * 记录系统启动日志
     * 
     * @param logger 日志记录器
     * @param componentName 组件名称
     */
    public static void logSystemStart(Logger logger, String componentName) {
        logger.info("{} 系统组件 {} 启动完成", DateTimeUtils.getLogTimeString(), componentName);
    }
    
    /**
     * 记录系统关闭日志
     * 
     * @param logger 日志记录器
     * @param componentName 组件名称
     */
    public static void logSystemShutdown(Logger logger, String componentName) {
        logger.info("{} 系统组件 {} 正在关闭", DateTimeUtils.getLogTimeString(), componentName);
    }
    
    /**
     * 记录配置加载日志
     * 
     * @param logger 日志记录器
     * @param configName 配置名称
     * @param configValue 配置值
     */
    public static void logConfigLoaded(Logger logger, String configName, Object configValue) {
        logger.debug("{} 配置加载：{} = {}", DateTimeUtils.getLogTimeString(), configName, configValue);
    }
}
