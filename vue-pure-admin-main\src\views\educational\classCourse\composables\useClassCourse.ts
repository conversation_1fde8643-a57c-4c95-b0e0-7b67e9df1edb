import { ref, onMounted } from "vue";
import { useTable } from "./useTable";
import { useCrud } from "./useCrud";
import {
  getClassCoursePage,
  saveClassCourse,
  updateClassCourse,
  deleteClassCourse,
  batchDeleteClassCourses
} from "@/api/educational/classCourse";
import { getAllSemesters } from "@/api/basic/semester";
import { getAllClasses } from "@/api/basic/classes";
import { getAllCourses } from "@/api/educational/course";
import { ClassCourseVO } from "../utils/types";

export function useClassCourse() {
  // 基础数据选项
  const semesterOptions = ref([]);
  const classOptions = ref([]);
  const courseOptions = ref([]);

  // 表格管理
  const {
    tableRef,
    loading,
    selectedNum,
    pagination,
    queryForm,
    handleSizeChange,
    handleCurrentChange,
    handleSelectionChange,
    resetQueryForm
  } = useTable();

  // CRUD操作
  const {
    formRef,
    openDialog,
    handleDelete,
    handleBatchDelete
  } = useCrud();

  // 数据列表
  const dataList = ref<ClassCourseVO[]>([]);

  // 搜索
  async function onSearch() {
    loading.value = true;
    try {
      const params = {
        current: pagination.currentPage,
        size: pagination.pageSize,
        classCode: queryForm.classCode,
        courseCode: queryForm.courseCode,
        courseName: queryForm.courseName,

        semesterId: queryForm.semesterId,
        collegeCode: queryForm.collegeCode,
        majorCode: queryForm.majorCode
      };

      const response = await getClassCoursePage(params);

      if (response.success) {
        dataList.value = response.data.records || [];
        pagination.total = response.data.total || 0;
      } else {
        dataList.value = [];
        pagination.total = 0;
        console.error("获取数据失败:", response.message);
      }
    } catch (error) {
      console.error("获取数据失败:", error);
      dataList.value = [];
      pagination.total = 0;
    } finally {
      loading.value = false;
    }
  }

  // 重置表单
  function resetForm(formEl: any) {
    if (!formEl) return;
    formEl.resetFields();
    resetQueryForm();
    onSearch();
  }

  // 加载基础数据选项
  async function loadOptions() {
    try {
      // 并行加载所有选项数据
      const [semesterRes, classRes, courseRes] = await Promise.all([
        getAllSemesters(),
        getAllClasses(),
        getAllCourses()
      ]);

      // 加载学期选项
      if (semesterRes.success) {
        semesterOptions.value = semesterRes.data || [];
      }

      // 加载班级选项
      if (classRes.success) {
        classOptions.value = classRes.data || [];
      }

      // 加载课程选项
      if (courseRes.success) {
        courseOptions.value = courseRes.data || [];
      }
    } catch (error) {
      console.error("加载选项数据失败:", error);
    }
  }

  // 删除操作
  function onDelete(row: ClassCourseVO) {
    handleDelete(row, onSearch);
  }

  // 批量删除操作
  function onBatchDelete() {
    const selectedRows = tableRef.value?.getSelectionRows() || [];
    if (selectedRows.length === 0) {
      console.log("请选择要删除的数据");
      return;
    }
    handleBatchDelete(selectedRows, onSearch);
  }

  // 导出
  function onExport() {
    // 这里可以调用导出API
    console.log("导出功能开发中");
  }

  // 组件挂载时加载数据
  onMounted(() => {
    loadOptions();
    onSearch();
  });

  return {
    // 数据
    dataList,
    semesterOptions,
    classOptions,
    courseOptions,

    // 表格相关
    tableRef,
    loading,
    selectedNum,
    pagination,
    queryForm,

    // 方法
    onSearch,
    resetForm,
    openDialog,
    onDelete,
    onBatchDelete,
    onExport,
    handleSizeChange,
    handleCurrentChange,
    handleSelectionChange
  };
}
