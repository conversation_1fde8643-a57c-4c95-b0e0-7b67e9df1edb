import type { FormRules } from "element-plus";

/** 教师表单验证规则 */
export const teacherFormRules: FormRules = {
  teacherCode: [
    { required: true, message: "教师工号为必填项", trigger: "blur" },
    { min: 2, max: 20, message: "教师工号长度应在2-20个字符之间", trigger: "blur" },
    { pattern: /^[A-Za-z0-9]+$/, message: "教师工号只能包含字母和数字", trigger: "blur" }
  ],
  name: [
    { required: true, message: "姓名为必填项", trigger: "blur" },
    { min: 2, max: 50, message: "姓名长度应在2-50个字符之间", trigger: "blur" },
    { pattern: /^[\u4e00-\u9fa5a-zA-Z\s]+$/, message: "姓名只能包含中文、英文和空格", trigger: "blur" }
  ],
  gender: [
    { required: true, message: "性别为必填项", trigger: "change" }
  ],
  collegeCode: [
    { required: true, message: "所属学院为必填项", trigger: "change" }
  ],
  phone: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号码",
      trigger: "blur"
    }
  ],
  email: [
    {
      type: "email",
      message: "请输入正确的邮箱地址",
      trigger: "blur"
    }
  ],
  birthDate: [
    {
      validator: (rule, value, callback) => {
        if (value) {
          const birthDate = new Date(value);
          const today = new Date();
          const age = today.getFullYear() - birthDate.getFullYear();
          
          if (birthDate > today) {
            callback(new Error("出生日期不能大于当前日期"));
          } else if (age > 100) {
            callback(new Error("年龄不能超过100岁"));
          } else if (age < 18) {
            callback(new Error("年龄不能小于18岁"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  hireDate: [
    {
      validator: (rule, value, callback) => {
        if (value) {
          const hireDate = new Date(value);
          const today = new Date();
          
          if (hireDate > today) {
            callback(new Error("入职日期不能大于当前日期"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  title: [
    { max: 50, message: "职称长度不能超过50个字符", trigger: "blur" }
  ]
};
