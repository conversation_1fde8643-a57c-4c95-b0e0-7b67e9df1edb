package com.itmk.utils;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.function.Function;

/**
 * 批量操作工具类
 * 提供批量数据库操作方法
 * 
 * <AUTHOR>
 * @since 2025-07-19
 */
public class BatchOperationUtils {
    
    /**
     * 默认批次大小
     */
    private static final int DEFAULT_BATCH_SIZE = 1000;
    
    /**
     * 批量插入数据
     * 
     * @param service 服务接口
     * @param dataList 数据列表
     * @param batchSize 批次大小
     * @param <T> 实体类型
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public static <T> boolean batchInsert(IService<T> service, Collection<T> dataList, int batchSize) {
        if (dataList == null || dataList.isEmpty()) {
            return true;
        }
        
        try {
            return service.saveBatch(dataList, batchSize);
        } catch (Exception e) {
            throw new RuntimeException("批量插入失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 批量插入数据（使用默认批次大小）
     */
    @Transactional(rollbackFor = Exception.class)
    public static <T> boolean batchInsert(IService<T> service, Collection<T> dataList) {
        return batchInsert(service, dataList, DEFAULT_BATCH_SIZE);
    }
    
    /**
     * 批量更新数据
     * 
     * @param service 服务接口
     * @param dataList 数据列表
     * @param batchSize 批次大小
     * @param <T> 实体类型
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public static <T> boolean batchUpdate(IService<T> service, Collection<T> dataList, int batchSize) {
        if (dataList == null || dataList.isEmpty()) {
            return true;
        }
        
        try {
            return service.updateBatchById(dataList, batchSize);
        } catch (Exception e) {
            throw new RuntimeException("批量更新失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 批量更新数据（使用默认批次大小）
     */
    @Transactional(rollbackFor = Exception.class)
    public static <T> boolean batchUpdate(IService<T> service, Collection<T> dataList) {
        return batchUpdate(service, dataList, DEFAULT_BATCH_SIZE);
    }
    
    /**
     * 分批处理数据
     * 
     * @param dataList 数据列表
     * @param batchSize 批次大小
     * @param processor 处理函数
     * @param <T> 数据类型
     * @param <R> 返回类型
     */
    public static <T, R> void processBatch(List<T> dataList, int batchSize, Function<List<T>, R> processor) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }
        
        int total = dataList.size();
        int processed = 0;
        
        while (processed < total) {
            int endIndex = Math.min(processed + batchSize, total);
            List<T> batch = dataList.subList(processed, endIndex);
            
            try {
                processor.apply(batch);
            } catch (Exception e) {
                throw new RuntimeException("批次处理失败: " + e.getMessage(), e);
            }
            
            processed = endIndex;
        }
    }
    
    /**
     * 分批处理数据（使用默认批次大小）
     */
    public static <T, R> void processBatch(List<T> dataList, Function<List<T>, R> processor) {
        processBatch(dataList, DEFAULT_BATCH_SIZE, processor);
    }
    
    /**
     * 获取默认批次大小
     */
    public static int getDefaultBatchSize() {
        return DEFAULT_BATCH_SIZE;
    }
}
