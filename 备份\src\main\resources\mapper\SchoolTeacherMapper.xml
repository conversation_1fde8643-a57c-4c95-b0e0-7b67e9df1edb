<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.itmk.web.school_teacher.mapper.SchoolTeacherMapper">
    <!-- 查询所有教师 -->
    <select id="selectList" resultType="com.itmk.web.school_teacher.entity.SchoolTeacher">
        select * from school_teacher
    </select>

    <!-- 根据ID查询教师 -->
    <select id="selectById" resultType="com.itmk.web.school_teacher.entity.SchoolTeacher">
        select * from school_teacher where teacher_id = #{teacherId}
    </select>

    <!-- 插入教师 -->
    <insert id="insertTeacher" parameterType="com.itmk.web.school_teacher.entity.SchoolTeacher">
        insert into school_teacher (teacher_name, sex, phone, teacher_num, password)
        values (#{teacher<PERSON>ame}, #{sex}, #{phone}, #{teacherNum}, #{password})
    </insert>

    <!-- 更新教师 -->
    <update id="updateTeacher" parameterType="com.itmk.web.school_teacher.entity.SchoolTeacher">
        update school_teacher
        set teacher_name = #{teacherName},
            sex = #{sex},
            phone = #{phone},
            teacher_num = #{teacherNum},
            password = #{password}
        where teacher_id = #{teacherId}
    </update>

    <!-- 删除教师 -->
    <delete id="deleteTeacher" parameterType="Long">
        delete from school_teacher where teacher_id = #{teacherId}
    </delete>

    <!-- 分页查询教师 -->
    <select id="getList" resultType="com.itmk.web.school_teacher.entity.SchoolTeacher">
        select * from school_teacher
        where 1=1
        <if test="parm.teacherName != null and parm.teacherName !=''">
            and teacher_name like concat('%',#{parm.teacherName},'%')
        </if>
        order by teacher_id asc
    </select>
</mapper>