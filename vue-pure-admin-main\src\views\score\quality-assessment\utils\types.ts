// 基本素质测评记录类型
export interface QualityAssessmentRecord {
  evaluationId?: number;
  studentId: string;
  studentName: string;
  dormitoryNo: string;
  addScore: number;
  reduceScore: number;
  addScoreRemark: string;
  reduceScoreRemark: string;
  evaluationPeriod: string;
  evaluationPeriodName: string;
  semesterName: string;
  periodScore: number;
  totalScore: number;
  classId?: string;
  className?: string;
  createTime?: string;
  updateTime?: string;
}

// 查询参数类型
export interface QualityAssessmentQuery {
  classId?: string;
  semesterId?: string;
  studentName?: string;
  studentId?: string;
  dormitoryNo?: string;
  currentPage?: number;
  pageSize?: number;
}

// 表单数据类型
export interface QualityAssessmentFormProps {
  title: string;
  evaluationId: number | null;
  studentId: string;
  studentName: string;
  dormitoryNo: string;
  addScore: number;
  reduceScore: number;
  addScoreRemark: string;
  reduceScoreRemark: string;
  evaluationPeriod: string;
  evaluationPeriodName: string;
  periodScore: number;
  totalScore: number;
  classId: string;
  className: string;
}

// 导入表单数据类型
export interface ImportFormProps {
  title: string;
  classCode: string;
  className: string;
  semesterId: string;
  semesterName: string;
  selectedCourseCodes: string[];
  availableSemesters: any[];
  availableCourses: any[];
}

// 导入结果类型
export interface ImportResult {
  success: boolean;
  totalRows: number;
  successRows: number;
  failedRows: number;
  errorMessages?: string[];
}
