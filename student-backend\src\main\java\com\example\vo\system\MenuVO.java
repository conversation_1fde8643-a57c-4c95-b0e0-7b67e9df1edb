package com.example.vo.system;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

import java.util.List;

/**
 * 菜单响应VO
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
public class MenuVO {

    /**
     * 菜单ID
     */
    private Integer id;

    /**
     * 父菜单ID
     */
    private Integer parentId;

    /**
     * 菜单名称
     */
    @NotBlank(message = "菜单名称不能为空")
    private String title;

    /**
     * 路由名称
     */
    @NotBlank(message = "路由名称不能为空")
    private String name;

    /**
     * 路由路径
     */
    @NotBlank(message = "路由路径不能为空")
    private String path;

    /**
     * 组件路径
     */
    private String component;

    /**
     * 重定向路径
     */
    private String redirect;

    /**
     * 菜单类型：0目录、1菜单、2按钮
     */
    private Integer menuType;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 排序
     */
    private Integer rank;

    /**
     * 是否显示链接
     */
    private Boolean showLink;

    /**
     * 是否显示父级
     */
    private Boolean showParent;

    /**
     * 是否缓存
     */
    private Boolean keepAlive;

    /**
     * 是否固定标签
     */
    private Boolean fixedTag;

    /**
     * 内嵌地址
     */
    private String frameSrc;

    /**
     * 是否隐藏菜单
     */
    private Boolean hideMenu;

    /**
     * 权限标识
     */
    private String auths;

    /**
     * 状态：0禁用、1启用
     */
    private Integer status;

    /**
     * 是否为后台路由
     */
    private Boolean backstage = true;

    /**
     * 子菜单列表
     */
    private List<MenuVO> children;

    /**
     * Meta信息（用于前端路由）
     */
    private Meta meta;

    @Data
    public static class Meta {
        private String title;
        private String icon;
        private Integer rank;
        private Boolean showLink;
        private Boolean showParent;
        private Boolean keepAlive;
        private Boolean fixedTag;
        private String frameSrc;
        private Boolean hideMenu;
        private String auths;
        private Boolean backstage = true;
    }
}
