package com.itmk.web.school_teacher.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.itmk.web.school_teacher.entity.SchoolTeacher;
import com.itmk.web.school_teacher.entity.TeacherListParm;
import org.apache.ibatis.annotations.Param;

public interface SchoolTeacherMapper extends BaseMapper<SchoolTeacher> {
    IPage<SchoolTeacher> getList(IPage<SchoolTeacher> page, @Param("parm") TeacherListParm teacherListParm);
}