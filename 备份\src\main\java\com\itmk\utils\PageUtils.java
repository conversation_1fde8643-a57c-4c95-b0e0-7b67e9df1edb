package com.itmk.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 分页工具类
 * 提供分页查询的方法
 * 
 * <AUTHOR>
 * @since 2025-07-19
 */
public class PageUtils {
    
    /**
     * 默认页大小
     */
    private static final long DEFAULT_PAGE_SIZE = 10L;
    
    /**
     * 最大页大小
     */
    private static final long MAX_PAGE_SIZE = 1000L;
    
    /**
     * 创建分页对象
     * 
     * @param current 当前页
     * @param size 页大小
     * @param <T> 实体类型
     * @return 分页对象
     */
    public static <T> IPage<T> createPage(Long current, Long size) {
        return createPage(current, size, true);
    }
    
    /**
     * 创建分页对象
     * 
     * @param current 当前页
     * @param size 页大小
     * @param searchCount 是否查询总数
     * @param <T> 实体类型
     * @return 分页对象
     */
    public static <T> IPage<T> createPage(Long current, Long size, boolean searchCount) {
        // 参数校验和默认值设置
        if (current == null || current < 1) {
            current = 1L;
        }
        
        if (size == null || size < 1) {
            size = DEFAULT_PAGE_SIZE;
        }
        
        // 限制最大页大小
        if (size > MAX_PAGE_SIZE) {
            size = MAX_PAGE_SIZE;
        }
        
        Page<T> page = new Page<>(current, size, searchCount);
        
        // 对于大数据量查询，关闭count查询
        if (size > 100) {
            page.setOptimizeCountSql(false);
        }
        
        return page;
    }
    
    /**
     * 创建不查询总数的分页对象（适用于大数据量场景）
     * 
     * @param current 当前页
     * @param size 页大小
     * @param <T> 实体类型
     * @return 分页对象
     */
    public static <T> IPage<T> createPageWithoutCount(Long current, Long size) {
        return createPage(current, size, false);
    }
    
    /**
     * 获取安全的页大小
     * 
     * @param size 原始页大小
     * @return 安全的页大小
     */
    public static long getSafePageSize(Long size) {
        if (size == null || size < 1) {
            return DEFAULT_PAGE_SIZE;
        }
        return Math.min(size, MAX_PAGE_SIZE);
    }
    
    /**
     * 获取安全的当前页
     * 
     * @param current 原始当前页
     * @return 安全的当前页
     */
    public static long getSafeCurrent(Long current) {
        if (current == null || current < 1) {
            return 1L;
        }
        return current;
    }
}
