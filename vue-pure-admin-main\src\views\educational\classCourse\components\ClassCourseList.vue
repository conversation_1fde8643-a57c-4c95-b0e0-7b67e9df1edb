<template>
  <div class="main">
    <!-- 搜索表单 -->
    <el-form
      ref="formRef"
      :inline="true"
      :model="queryForm"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
    >
      <!-- 返回按钮 -->
      <el-form-item>
        <el-button
          :icon="useRenderIcon(leftLine)"
          @click="$emit('back')"
        >
          返回班级列表
        </el-button>
      </el-form-item>

      <el-form-item label="课程代码：" prop="courseCode">
        <el-input
          v-model="queryForm.courseCode"
          placeholder="请输入课程代码"
          clearable
          class="!w-[200px]"
        />
      </el-form-item>
      <el-form-item label="课程名称：" prop="courseName">
        <el-input
          v-model="queryForm.courseName"
          placeholder="请输入课程名称"
          clearable
          class="!w-[200px]"
        />
      </el-form-item>

      <el-form-item label="学期：" prop="semesterId">
        <el-select
          v-model="queryForm.semesterId"
          placeholder="请选择学期"
          clearable
          class="!w-[200px]"
        >
          <el-option
            v-for="semester in semesterOptions"
            :key="semester.id"
            :label="semester.semesterName"
            :value="semester.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon(Search)"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon(Refresh)" @click="resetForm(formRef)">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <PureTableBar :title="tableTitle" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon(AddFill)"
          @click="openDialog('新增')"
        >
          分配课程
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <!-- 数据表格 -->
        <pure-table
          ref="tableRef"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small'"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @selection-change="handleSelectionChange"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <!-- 课程性质和状态列已在columns.tsx中使用cellRenderer定义 -->

          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              size="small"
              :icon="useRenderIcon(EditPen)"
              @click="openDialog('修改', row)"
            >
              修改
            </el-button>
            <el-popconfirm
              :title="`是否确认删除课程分配：${row.courseName}？`"
              @confirm="onDelete(row)"
            >
              <template #reference>
                <el-button
                  class="reset-margin"
                  link
                  type="danger"
                  size="small"
                  :icon="useRenderIcon(Delete)"
                >
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { useClassCourse } from "../composables/useClassCourse";
import { useColumns } from "../utils/columns";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

import Search from "~icons/ep/search";
import Refresh from "~icons/ep/refresh";
import AddFill from "~icons/ri/add-circle-line";
import EditPen from "~icons/ep/edit-pen";
import Delete from "~icons/ep/delete";
import leftLine from "~icons/ri/arrow-left-line";

// 定义 props
interface Props {
  selectedClass: any;
}

const props = defineProps<Props>();

// 定义事件
defineEmits<{
  back: [];
}>();

const formRef = ref();

// 表格列配置
const { columns } = useColumns();

const {
  dataList,
  semesterOptions,
  tableRef,
  loading,
  selectedNum,
  pagination,
  queryForm,
  onSearch,
  resetForm,
  openDialog,
  onDelete,
  onBatchDelete,
  onExport,
  handleSizeChange,
  handleCurrentChange,
  handleSelectionChange
} = useClassCourse();

// 课程性质类型已在columns.tsx中定义

// 计算表格标题
const tableTitle = computed(() => {
  if (props.selectedClass) {
    return `${props.selectedClass.className} - 课程分配列表`;
  }
  return "课程分配列表";
});

// 监听selectedClass变化，设置查询条件
watch(() => props.selectedClass, (newClass) => {
  if (newClass) {
    queryForm.classCode = newClass.classCode;
    queryForm.collegeCode = newClass.collegeCode;
    queryForm.majorCode = newClass.majorCode;
    pagination.currentPage = 1;
    onSearch();
  }
}, { immediate: true });
</script>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
