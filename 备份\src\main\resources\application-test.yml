#端口号配置
server:
  port: 8080
#数据库连接配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************************
    username: root
    password: 123456
  # 配置热加载
  devtools:
    restart:
      enabled: true  # 设置开启热部署
      additional-paths: src/main/java  # 重启目录
      exclude: WEB-INF/**
    livereload:
      enabled: true  # 页面热部署

#mybatis plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 设置当字段为null时依然更新该字段
    call-setters-on-nulls: true
  global-config:
    db-config:
      #配置mybatis plus 在更新时包括null值字段
      field-strategy: IGNORED

#jwt配置
jwt:
  #颁发者
  issuer: itmk-student-system
  #秘钥 - 建议使用更复杂的密钥
  secret: ${JWT_SECRET:itmk-student-system-2024-secure-key-change-in-production}
  #30分钟过期
  expiration: 30

# Knife4j配置
knife4j:
  enable: true
  setting:
    language: zh_cn

# SpringFox配置 - 禁用原生Swagger UI
springfox:
  documentation:
    swagger-ui:
      enabled: false

#日志配置
logging:
  pattern:
    console: '%d{yyyy-MM-dd} [%thread] %-5level %logger- %msg%n'

