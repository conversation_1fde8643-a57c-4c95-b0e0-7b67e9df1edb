package com.example.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 * 统一管理CORS配置、静态资源配置等Web相关配置
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    /**
     * CORS跨域配置
     */
    @Bean
    public CorsFilter corsFilter() {
        // 创建CORS配置
        CorsConfiguration corsConfiguration = new CorsConfiguration();

        // 允许所有源（开发环境）
        corsConfiguration.addAllowedOriginPattern("*");
        
        // 允许凭证(cookies等)
        corsConfiguration.setAllowCredentials(true);
        
        // 允许所有请求方法
        corsConfiguration.addAllowedMethod("*");
        
        // 允许所有请求头
        corsConfiguration.addAllowedHeader("*");
        
        // 设置预检请求的有效期，单位秒（1小时）
        corsConfiguration.setMaxAge(3600L);
        
        // 添加响应头，允许客户端访问返回的自定义响应头
        corsConfiguration.addExposedHeader("content-disposition");
        corsConfiguration.addExposedHeader("Content-Length");
        corsConfiguration.addExposedHeader("Access-Control-Allow-Origin");
        corsConfiguration.addExposedHeader("Access-Control-Allow-Headers");
        
        // 添加映射路径，拦截一切请求
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", corsConfiguration);
        
        return new CorsFilter(source);
    }

    /**
     * 静态资源处理器配置
     * 确保API请求不被当作静态资源处理
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 静态资源映射
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/");
        
        // Swagger UI 资源映射
        registry.addResourceHandler("/swagger-ui/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/swagger-ui/");
        
        // 确保API路径不被当作静态资源处理
        // 这里不添加 /api/** 的资源映射，让它走控制器路由
    }
}
