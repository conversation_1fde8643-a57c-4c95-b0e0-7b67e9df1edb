import { http } from "@/utils/http";

export interface MajorItem {
  id?: number;
  majorCode: string;
  majorName: string;
  collegeCode: string;
  collegeName?: string;
  description?: string;
  createTime?: string;
  updateTime?: string;
}

export interface MajorQueryDTO {
  majorName?: string;
  majorCode?: string;
  collegeCode?: string;
  current?: number;
  size?: number;
}

export interface MajorPageVO {
  records: MajorItem[];
  total: number;
  current: number;
  size: number;
}

/** 获取专业分页列表 */
export const getMajorList = (data?: MajorQueryDTO) => {
  return http.request<MajorPageVO>("post", "/api/basic/major/list", { data });
};

/** 获取所有专业列表 */
export const getAllMajors = () => {
  return http.request<MajorItem[]>("get", "/api/basic/major/all");
};

/** 根据学院代码获取专业列表 */
export const getMajorsByCollegeCode = (collegeCode: string) => {
  return http.request<MajorItem[]>("get", `/api/basic/major/college/${collegeCode}`);
};

/** 保存专业 */
export const saveMajor = (data: MajorItem) => {
  return http.request("post", "/api/basic/major/save", { data });
};

/** 更新专业 */
export const updateMajor = (data: MajorItem) => {
  return http.request("post", "/api/basic/major/update", { data });
};

/** 删除专业 */
export const deleteMajor = (data: { id: number }) => {
  return http.request("post", "/api/basic/major/delete", { data });
};

/** 批量删除专业 */
export const batchDeleteMajors = (ids: number[]) => {
  return http.request("post", "/api/basic/major/batch-delete", { data: ids });
};