import { reactive } from "vue";
import type { FormRules } from "element-plus";

/** 自定义表单规则校验 */
export const formRules = reactive(<FormRules>{
  collegeCode: [
    { required: true, message: "学院代码为必填项", trigger: "blur" },
    { min: 2, max: 20, message: "学院代码长度应为2-20个字符", trigger: "blur" }
  ],
  collegeName: [
    { required: true, message: "学院名称为必填项", trigger: "blur" },
    { min: 2, max: 100, message: "学院名称长度应为2-100个字符", trigger: "blur" }
  ],
  description: [
    { max: 500, message: "学院描述长度不能超过500个字符", trigger: "blur" }
  ]
});
