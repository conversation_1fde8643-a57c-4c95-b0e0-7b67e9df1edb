package com.itmk.web.school_class.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itmk.utils.PageUtils;
import com.itmk.web.school_class.entity.SchoolClass;
import com.itmk.web.school_class.entity.SchoolClassParm;
import com.itmk.web.school_class.mapper.SchoolClassMapper;
import com.itmk.web.school_class.service.SchoolClassService;
import org.springframework.stereotype.Service;


@Service
public class SchoolClassServiceImpl extends ServiceImpl<SchoolClassMapper, SchoolClass> implements SchoolClassService {
    @Override
    public IPage<SchoolClass> getList(SchoolClassParm parm) {
        //构造分页对象
        IPage<SchoolClass> page = PageUtils.createPage(parm.getCurrentPage(), parm.getPageSize());
        return this.baseMapper.getList(page, parm);
    }
}
