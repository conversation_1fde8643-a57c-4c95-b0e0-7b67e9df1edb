package com.itmk.web.sys_menu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.itmk.web.sys_menu.dto.MenuDTO;
import com.itmk.web.sys_menu.dto.MenuQueryDTO;
import com.itmk.web.sys_menu.dto.RouterDTO;
import com.itmk.web.sys_menu.entity.SysMenu;

import java.util.List;

/**
 * 菜单服务接口
 * 基于Spring Boot最佳实践设计
 */
public interface SysMenuService extends IService<SysMenu> {

    /**
     * 获取菜单列表
     */
    List<MenuDTO> getMenuList();

    /**
     * 根据查询条件获取菜单列表
     */
    List<MenuDTO> getMenuList(MenuQueryDTO queryDTO);

    /**
     * 获取上级菜单列表
     */
    List<MenuDTO> getParentMenuList();

    /**
     * 根据用户ID获取菜单路由
     */
    List<RouterDTO> getMenuByUserId(Long userId);

    /**
     * 根据角色ID获取菜单
     */
    List<MenuDTO> getMenuByRoleId(Long roleId);

    /**
     * 根据学生ID获取菜单路由
     */
    List<RouterDTO> getMenuByStudentId(Long studentId);

    /**
     * 根据教师ID获取菜单路由
     */
    List<RouterDTO> getMenuByTeacherId(Long teacherId);

    /**
     * 创建菜单
     */
    boolean createMenu(MenuDTO menuDTO);

    /**
     * 更新菜单
     */
    boolean updateMenu(MenuDTO menuDTO);

    /**
     * 删除菜单
     */
    boolean deleteMenu(Long menuId);

    /**
     * 强制删除菜单（级联删除相关数据）
     */
    boolean forceDeleteMenu(Long menuId);

    /**
     * 根据用户ID获取原始菜单实体列表（用于角色分配）
     */
    List<SysMenu> getMenuEntitiesByUserId(Long userId);

    /**
     * 根据角色名称获取菜单路由
     */
    List<RouterDTO> getMenuByRoleName(String roleName);

    /**
     * 根据权限代码列表获取菜单路由
     */
    List<RouterDTO> getMenuByPermissions(List<String> permissions);
}
