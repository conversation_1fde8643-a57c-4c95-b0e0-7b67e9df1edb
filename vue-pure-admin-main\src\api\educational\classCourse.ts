import { http } from "@/utils/http";

// 班级课程分配查询参数类型
export interface ClassCourseQueryDTO {
  classCode?: string;
  className?: string;
  courseCode?: string;
  courseName?: string;
  semesterId?: number;

  majorCode?: string;
  collegeCode?: string;
  isActive?: boolean;
  current?: number;
  size?: number;
}

// 班级课程分配信息类型
export interface ClassCourseVO {
  id?: number;
  classCode: string;
  className?: string;
  majorCode?: string;
  majorName?: string;
  collegeCode?: string;
  collegeName?: string;
  courseCode: string;
  courseName?: string;
  credits?: number;
  courseType?: string;
  semesterId: number;
  semesterName?: string;
  academicYear?: string;

  isActive?: boolean;
  sortOrder?: number;
  remark?: string;
  createdBy?: string;
  createdAt?: string;
  updatedBy?: string;
  updatedAt?: string;
}

// 批量分配参数类型
export interface BatchAssignParams {
  classCode: string;
  courseCodes: string[];
  semesterId: number;

}

// 复制学期参数类型
export interface CopySemesterParams {
  sourceClassCode: string;
  sourceSemesterId: number;
  targetClassCode: string;
  targetSemesterId: number;
}

// 分页查询班级课程分配列表
export const getClassCoursePage = (data: ClassCourseQueryDTO) => {
  return http.request<any>("post", "/api/educational/class-course/page", { data });
};

// 根据ID获取班级课程分配详情
export const getClassCourseById = (id: number) => {
  return http.request<ClassCourseVO>("get", `/api/educational/class-course/${id}`);
};

// 新增班级课程分配
export const saveClassCourse = (data: ClassCourseVO) => {
  return http.request<any>("post", "/api/educational/class-course", { data });
};

// 更新班级课程分配
export const updateClassCourse = (data: ClassCourseVO) => {
  return http.request<any>("put", "/api/educational/class-course", { data });
};

// 删除班级课程分配
export const deleteClassCourse = (id: number) => {
  return http.request<any>("delete", `/api/educational/class-course/${id}`);
};

// 批量删除班级课程分配
export const batchDeleteClassCourses = (ids: number[]) => {
  return http.request<any>("delete", "/api/educational/class-course/batch", { data: ids });
};

// 根据班级代码获取课程分配列表
export const getCoursesByClass = (classCode: string) => {
  return http.request<ClassCourseVO[]>("get", `/api/educational/class-course/class/${classCode}`);
};

// 根据班级代码和学期ID获取课程分配列表
export const getCoursesByClassAndSemester = (classCode: string, semesterId: number) => {
  return http.request<ClassCourseVO[]>("get", `/api/educational/class-course/class/${classCode}/semester/${semesterId}`);
};

// 根据课程代码获取分配的班级列表
export const getClassesByCourse = (courseCode: string) => {
  return http.request<ClassCourseVO[]>("get", `/api/educational/class-course/course/${courseCode}`);
};

// 批量分配课程到班级
export const batchAssignCourses = (data: BatchAssignParams) => {
  return http.request<any>("post", "/api/educational/class-course/batch-assign", { data });
};

// 复制学期课程分配
export const copySemesterCourses = (data: CopySemesterParams) => {
  return http.request<any>("post", "/api/educational/class-course/copy-semester", { data });
};

// 根据班级代码统计课程数量
export const countCoursesByClass = (classCode: string) => {
  return http.request<number>("get", `/api/educational/class-course/count/class/${classCode}`);
};

// 根据课程代码统计分配的班级数量
export const countClassesByCourse = (courseCode: string) => {
  return http.request<number>("get", `/api/educational/class-course/count/course/${courseCode}`);
};
