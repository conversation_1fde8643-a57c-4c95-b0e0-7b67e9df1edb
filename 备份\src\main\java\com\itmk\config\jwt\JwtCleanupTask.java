package com.itmk.config.jwt;

import com.itmk.utils.LogUtils;
import com.itmk.utils.TaskExecutionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * JWT清理定时任务
 * 定期清理过期的JWT相关数据
 */
@Component
public class JwtCleanupTask {

    private static final Logger logger = LoggerFactory.getLogger(JwtCleanupTask.class);

    @Autowired
    private JwtSecurityEnhancer jwtSecurityEnhancer;

    /**
     * 每小时清理一次过期数据
     */
    @Scheduled(fixedRate = 3600000) // 1小时 = 3600000毫秒
    public void cleanupExpiredJwtData() {
        TaskExecutionUtils.executeTask(
            "JWT过期数据清理",
            "清理过期的JWT相关数据",
            () -> jwtSecurityEnhancer.cleanupExpiredData(),
            logger
        );
    }
}
