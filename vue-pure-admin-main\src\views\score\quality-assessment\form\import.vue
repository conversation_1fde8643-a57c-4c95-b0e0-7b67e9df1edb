<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { UploadFilled, Download, Upload } from "@element-plus/icons-vue";
import type { UploadFile } from "element-plus";
import ReCol from "@/components/ReCol";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { importFormRules } from "../utils/rule";

// API导入
import { 
  downloadQualityAssessmentTemplate, 
  importQualityAssessment 
} from "@/api/score/quality-assessment";

// Props
interface Props {
  formInline?: {
    title: string;
    classCode: string;
    className: string;
    semesterId: string;
    semesterName: string;
    selectedCourseCodes: string[];
    availableSemesters: any[];
    availableCourses: any[];
  };
}

const props = withDefaults(defineProps<Props>(), {
  formInline: () => ({
    title: "基本素质测评成绩导入",
    classCode: "",
    className: "",
    semesterId: "",
    semesterName: "",
    selectedCourseCodes: [],
    availableSemesters: [],
    availableCourses: []
  })
});

// Emits
const emit = defineEmits<{
  success: [];
}>();

// 表单数据
const formInline = computed(() => props.formInline);

const ruleFormRef = ref();
const downloadLoading = ref(false);
const importLoading = ref(false);
const selectedFile = ref<File | null>(null);
const fileList = ref<UploadFile[]>([]);
const uploadRef = ref();
const importResult = ref<any>(null);

// 文件上传处理
const handleFileChange = (file: UploadFile) => {
  selectedFile.value = file.raw || null;
  fileList.value = [file];
  return false;
};

const handleExceed = () => {
  ElMessage.warning("只能上传一个文件");
};

// 下载模板
const handleDownloadTemplate = async () => {
  if (!formInline.value?.classCode || !formInline.value?.semesterId) {
    ElMessage.error("请先选择班级和学期");
    return;
  }

  downloadLoading.value = true;
  try {
    const response = await downloadQualityAssessmentTemplate({
      classCode: formInline.value.classCode,
      semesterId: formInline.value.semesterId
    });

    const blob = new Blob([response], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = "基本素质测评成绩导入模板.xlsx";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    ElMessage.success("模板下载成功");
  } catch (error) {
    console.error("下载模板失败:", error);
    ElMessage.error("下载模板失败");
  } finally {
    downloadLoading.value = false;
  }
};

// 开始导入
const handleImport = async () => {
  if (!selectedFile.value) {
    ElMessage.error("请选择要导入的文件");
    return;
  }

  if (!formInline.value?.classCode || !formInline.value?.semesterId) {
    ElMessage.error("请先选择班级和学期");
    return;
  }

  try {
    await ElMessageBox.confirm(
      '确定要导入基本素质测评成绩吗？导入过程中请勿关闭页面。',
      '确认导入',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    importLoading.value = true;
    importResult.value = null;

    const formData = new FormData();
    formData.append('file', selectedFile.value);
    formData.append('classCode', formInline.value.classCode);
    formData.append('semesterId', formInline.value.semesterId);

    const result = await importQualityAssessment(formData);

    importResult.value = result.data;

    if (result.data.success) {
      ElMessage.success("导入完成");
      if (result.data.successRows > 0) {
        emit("success");
      }
    } else {
      ElMessage.error("导入失败，请查看错误详情");
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error("导入失败:", error);
      ElMessage.error("导入失败");
    }
  } finally {
    importLoading.value = false;
  }
};

// 重置表单
const resetForm = () => {
  selectedFile.value = null;
  fileList.value = [];
  importResult.value = null;

  if (uploadRef.value) {
    uploadRef.value.clearFiles();
  }
};

function getRef() {
  return ruleFormRef.value;
}

defineExpose({ getRef });
</script>

<template>
  <el-form
    ref="ruleFormRef"
    :model="formInline"
    :rules="importFormRules"
    label-width="100px"
  >
    <el-row :gutter="30">
      <re-col :value="24">
        <el-form-item label="班级信息">
          <el-tag type="info">{{ formInline?.classCode }} - {{ formInline?.className }}</el-tag>
        </el-form-item>
      </re-col>

      <re-col :value="24">
        <el-form-item label="评分学期">
          <el-tag type="success">{{ formInline?.semesterName }}</el-tag>
        </el-form-item>
      </re-col>

      <re-col :value="24">
        <el-form-item label="操作">
          <el-space>
            <el-button
              type="primary"
              :icon="useRenderIcon(Download)"
              @click="handleDownloadTemplate"
              :loading="downloadLoading"
            >
              下载导入模板
            </el-button>
            <el-button
              type="success"
              :icon="useRenderIcon(Upload)"
              @click="handleImport"
              :loading="importLoading"
              :disabled="!selectedFile"
            >
              开始导入
            </el-button>
          </el-space>
        </el-form-item>
      </re-col>

      <re-col :value="24">
        <el-form-item label="选择文件">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :show-file-list="true"
            :limit="1"
            accept=".xlsx,.xls"
            :on-change="handleFileChange"
            :on-exceed="handleExceed"
            :file-list="fileList"
            drag
            style="width: 100%"
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                只能上传 .xlsx/.xls 文件，且不超过 10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </re-col>

      <!-- 导入结果显示 -->
      <re-col v-if="importResult" :value="24">
        <el-form-item label="导入结果">
          <el-alert
            :title="`导入完成：总计${importResult.totalRows}条，成功${importResult.successRows}条，失败${importResult.failedRows}条`"
            :type="importResult.success ? 'success' : 'error'"
            :closable="false"
          >
            <div v-if="importResult.errorMessages && importResult.errorMessages.length > 0">
              <p><strong>错误信息：</strong></p>
              <ul>
                <li v-for="(error, index) in importResult.errorMessages" :key="index">{{ error }}</li>
              </ul>
            </div>
          </el-alert>
        </el-form-item>
      </re-col>
    </el-row>
  </el-form>
</template>

<style scoped>
.el-upload__tip {
  color: var(--el-text-color-secondary);
  font-size: 12px;
}
</style>
