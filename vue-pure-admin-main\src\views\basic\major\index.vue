<template>
  <div class="main-content">
    <el-form
      ref="formRef"
      :inline="true"
      :model="form"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
    >
      <el-form-item label="所属学院：" prop="collegeCode">
        <el-select
          v-model="form.collegeCode"
          placeholder="请选择学院"
          clearable
          class="!w-[150px]"
        >
          <el-option
            v-for="college in collegeOptions"
            :key="college.collegeCode"
            :label="college.collegeName"
            :value="college.collegeCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="专业代码：" prop="majorCode">
        <el-input
          v-model="form.majorCode"
          placeholder="请输入专业代码"
          clearable
          class="!w-[150px]"
        />
      </el-form-item>
      <el-form-item label="专业名称：" prop="majorName">
        <el-input
          v-model="form.majorName"
          placeholder="请输入专业名称"
          clearable
          class="!w-[180px]"
        />
      </el-form-item>
      <el-form-item label="学制年限：" prop="duration">
        <el-select
          v-model="form.duration"
          placeholder="请选择学制"
          clearable
          class="!w-[120px]"
        >
          <el-option label="1年" :value="1" />
          <el-option label="2年" :value="2" />
          <el-option label="3年" :value="3" />
          <el-option label="4年" :value="4" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:search')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon('ep:refresh')" @click="resetForm(formRef)">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <PureTableBar title="专业管理" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon('ri:add-circle-line')"
          @click="openDialog()"
        >
          新增专业
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          align-whole="center"
          showOverflowTooltip
          table-layout="auto"
          :loading="loading"
          :size="size"
          adaptive
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small' ? true : false"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:edit-pen')"
              @click="openDialog('编辑', row)"
            >
              修改
            </el-button>
            <el-popconfirm
              :title="`是否确认删除专业名称为${row.majorName}的这条数据`"
              @confirm="handleDelete(row)"
            >
              <template #reference>
                <el-button
                  class="reset-margin"
                  link
                  type="primary"
                  :size="size"
                  :icon="useRenderIcon('ep:delete')"
                >
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useMajor } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { getAllColleges, type CollegeItem } from "@/api/basic/college";

defineOptions({
  name: "Major"
});

const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  onSearch,
  resetForm,
  openDialog,
  handleDelete,
  handleSizeChange,
  handleCurrentChange
} = useMajor();

const formRef = ref();
const collegeOptions = ref<CollegeItem[]>([]);

// 加载学院选项
const loadCollegeOptions = async () => {
  try {
    const response = await getAllColleges();
    if (response.success && response.data) {
      collegeOptions.value = response.data;
    }
  } catch (error) {
    // 静默处理错误
  }
};

onMounted(() => {
  loadCollegeOptions();
});
</script>

<style lang="scss" scoped>
:deep(.el-table__inner-wrapper::before) {
  height: 0;
}



.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
