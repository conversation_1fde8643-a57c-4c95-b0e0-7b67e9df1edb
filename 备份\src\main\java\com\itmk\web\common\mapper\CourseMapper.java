package com.itmk.web.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.itmk.web.common.entity.Course;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface CourseMapper extends BaseMapper<Course> {
    // 获取课程列表，包含学期信息
    IPage<Course> getListWithSemester(IPage<Course> page, @Param("courseName") String courseName,
                                     @Param("academicYear") String academicYear,
                                     @Param("semesterName") String semesterName);
    
    // 根据学期名称查询学期ID
    @Select("SELECT semester_id FROM semesters WHERE semester_name = #{semesterName}")
    Integer getSemesterIdByName(@Param("semesterName") String semesterName);
}