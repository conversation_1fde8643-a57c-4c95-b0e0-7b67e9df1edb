<template>
  <el-form
    ref="ruleFormRef"
    :model="formInline"
    :rules="formRules"
    label-width="100px"
  >
    <el-form-item label="所属学院" prop="collegeCode">
      <el-select
        v-model="formInline.collegeCode"
        placeholder="请选择所属学院"
        clearable
        style="width: 100%"
      >
        <el-option
          v-for="college in collegeOptions"
          :key="college.collegeCode"
          :label="college.collegeName"
          :value="college.collegeCode"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="专业代码" prop="majorCode">
      <el-input
        v-model="formInline.majorCode"
        clearable
        placeholder="请输入专业代码"
      />
    </el-form-item>
    <el-form-item label="专业名称" prop="majorName">
      <el-input
        v-model="formInline.majorName"
        clearable
        placeholder="请输入专业名称"
      />
    </el-form-item>
    <el-form-item label="学制年限" prop="duration">
      <el-select
        v-model="formInline.duration"
        placeholder="请选择学制年限"
        clearable
        style="width: 100%"
      >
        <el-option label="1年" :value="1" />
        <el-option label="2年" :value="2" />
        <el-option label="3年" :value="3" />
        <el-option label="4年" :value="4" />
      </el-select>
    </el-form-item>
    <el-form-item label="专业描述" prop="description">
      <el-input
        v-model="formInline.description"
        :autosize="{ minRows: 3, maxRows: 5 }"
        type="textarea"
        placeholder="请输入专业描述"
      />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import type { FormInstance } from "element-plus";
import { formRules } from "./utils/rule";
import { FormProps } from "./utils/types";
import { getAllColleges, type CollegeItem } from "@/api/basic/college";

const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({
    id: undefined,
    collegeCode: "",
    majorCode: "",
    majorName: "",
    duration: 4,
    description: "",
    collegeOptions: []
  })
});

const ruleFormRef = ref<FormInstance>();
const collegeOptions = ref<CollegeItem[]>([]);
const formInline = reactive(props.formInline);

// 加载学院选项
const loadCollegeOptions = async () => {
  try {
    // 优先使用从props传递过来的学院选项
    if (props.formInline.collegeOptions && props.formInline.collegeOptions.length > 0) {
      collegeOptions.value = props.formInline.collegeOptions;
      return;
    }

    // 如果props中没有学院选项，则从API加载
    const response = await getAllColleges();

    if (response.success && response.data) {
      collegeOptions.value = response.data;
    }
  } catch (error) {
    // 静默处理错误
  }
};

function getRef() {
  return ruleFormRef.value;
}

onMounted(() => {
  loadCollegeOptions();
});

defineExpose({ getRef });
</script>
