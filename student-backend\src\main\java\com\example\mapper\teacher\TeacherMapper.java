package com.example.mapper.teacher;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.dto.teacher.TeacherQueryDTO;
import com.example.entity.teacher.Teacher;
import com.example.vo.teacher.TeacherVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 教师Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Mapper
public interface TeacherMapper extends BaseMapper<Teacher> {

    /**
     * 分页查询教师列表
     *
     * @param page  分页参数
     * @param query 查询条件
     * @return 教师列表
     */
    IPage<TeacherVO> selectTeacherPage(Page<TeacherVO> page, @Param("query") TeacherQueryDTO query);

    /**
     * 查询所有教师
     *
     * @return 教师列表
     */
    List<TeacherVO> selectAllTeachers();

    /**
     * 根据学院代码查询教师列表
     *
     * @param collegeCode 学院代码
     * @return 教师列表
     */
    List<TeacherVO> selectByCollegeCode(@Param("collegeCode") String collegeCode);

    /**
     * 根据教师工号查询教师
     *
     * @param teacherCode 教师工号
     * @return 教师信息
     */
    TeacherVO selectByTeacherCode(@Param("teacherCode") String teacherCode);

    /**
     * 根据ID查询教师详情
     *
     * @param id 教师ID
     * @return 教师详情
     */
    TeacherVO selectTeacherById(@Param("id") Integer id);

    /**
     * 检查教师工号是否存在
     *
     * @param teacherCode 教师工号
     * @param excludeId   排除的ID（用于更新时检查）
     * @return 存在的数量
     */
    int checkTeacherCodeExists(@Param("teacherCode") String teacherCode, @Param("excludeId") Integer excludeId);
}
