# 成绩录入功能说明

## 功能概述

成绩录入功能允许教师和管理员为学生录入、编辑和管理期末成绩。支持单个录入和批量录入两种方式。

## 后端架构

### 1. 控制器层
- `GradeInputController.java` - 成绩录入控制器
  - 提供成绩录入相关的REST API接口
  - 支持CRUD操作和批量操作

### 2. 服务层
- `GradeInputService.java` - 成绩录入服务接口
- `GradeInputServiceImpl.java` - 成绩录入服务实现
  - 业务逻辑处理
  - 绩点自动计算
  - 数据校验

### 3. 数据访问层
- `GradeInputMapper.java` - 成绩录入Mapper接口
- `GradeInputMapper.xml` - MyBatis映射文件

### 4. 数据传输对象
- `GradeInputDTO.java` - 成绩录入DTO
- `BatchGradeInputDTO.java` - 批量成绩录入DTO
- `GradeInputQueryDTO.java` - 成绩录入查询DTO
- `GradeInputVO.java` - 成绩录入VO

## 前端架构

### 1. 页面结构
```
/views/score/grade-input/
├── index.vue                    # 主页面
└── components/
    ├── CourseSelection.vue      # 课程选择组件
    ├── GradeInputList.vue       # 成绩录入列表
    ├── GradeInputDialog.vue     # 成绩录入弹窗
    └── BatchInputDialog.vue     # 批量录入弹窗
```

### 2. API接口
- `/api/score/grade-input.ts` - 成绩录入相关API

## 主要功能

### 1. 成绩录入流程
1. 选择专业 → 选择班级 → 选择课程 → 录入成绩
2. 支持按学期筛选
3. 显示学生列表和录入状态

### 2. 单个成绩录入
- 录入期末成绩（0-100分）
- 自动计算绩点或手动输入
- 标记是否重修
- 添加备注信息

### 3. 批量成绩录入
- 一次性为整个班级录入成绩
- 支持自动计算所有学生绩点
- 支持清空所有成绩重新录入

### 4. 成绩管理
- 编辑已录入的成绩
- 删除成绩记录
- 查看录入状态

## 绩点计算规则

当前采用的绩点计算规则：
- 95-100分：4.0
- 90-94分：3.7
- 85-89分：3.3
- 80-84分：3.0
- 75-79分：2.7
- 70-74分：2.3
- 65-69分：2.0
- 60-64分：1.7
- 60分以下：0.0

## API接口说明

### 成绩录入相关接口

#### 1. 分页查询成绩录入列表
```
POST /api/score/input/page
```

#### 2. 录入单个成绩
```
POST /api/score/input
```

#### 3. 批量录入成绩
```
POST /api/score/input/batch
```

#### 4. 更新成绩
```
PUT /api/score/input/{id}
```

#### 5. 删除成绩
```
DELETE /api/score/input/{id}
```

#### 6. 获取学生成绩录入列表
```
GET /api/score/input/students?classCode={}&courseCode={}&semesterId={}
```

#### 7. 检查成绩是否存在
```
GET /api/score/input/check?studentId={}&courseCode={}&semesterId={}
```

#### 8. 计算绩点
```
GET /api/score/input/calculate-gpa?finalScore={}
```

## 数据库表结构

使用现有的 `grades` 表：
- `id` - 主键
- `student_id` - 学号
- `course_code` - 课程代码
- `semester_id` - 学期ID
- `final_score` - 期末成绩
- `grade_point` - 绩点
- `is_retake` - 是否重修
- `remarks` - 备注
- `created_by` - 创建人
- `created_at` - 创建时间
- `updated_by` - 更新人
- `updated_at` - 更新时间

## 使用说明

### 教师使用流程
1. 登录系统
2. 进入"成绩录入"模块
3. 选择专业和班级
4. 选择要录入成绩的课程
5. 选择学期
6. 录入学生成绩（单个或批量）
7. 系统自动计算绩点
8. 保存成绩

### 注意事项
1. 成绩范围：0-100分
2. 绩点范围：0-5.0
3. 同一学生同一课程同一学期只能有一条成绩记录
4. 支持重修标记
5. 删除操作需要确认

## 扩展功能

可以根据需要扩展以下功能：
1. 成绩导入（Excel文件）
2. 成绩审核流程
3. 成绩统计分析
4. 成绩变更记录
5. 不同绩点计算规则配置
