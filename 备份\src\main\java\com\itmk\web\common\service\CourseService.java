package com.itmk.web.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.itmk.web.common.entity.CommParm;
import com.itmk.web.common.entity.Course;

public interface CourseService extends IService<Course> {
    IPage<Course> getList(CommParm commParm);
    
    // 获取课程列表，包含学期信息
    IPage<Course> getListWithSemester(CommParm commParm);

    // 根据学期名称查询学期ID
    Integer getSemesterIdByName(String semesterName);
}