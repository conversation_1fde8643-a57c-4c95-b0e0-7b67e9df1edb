package com.example.dto.score;

import lombok.Data;

import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 成绩导入DTO
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class GradeImportRequestDTO {

    /**
     * 学号
     */
    @NotBlank(message = "学号不能为空")
    private String studentId;

    /**
     * 课程代码
     */
    @NotBlank(message = "课程代码不能为空")
    private String courseCode;

    /**
     * 学期ID
     */
    @NotNull(message = "学期ID不能为空")
    private Integer semesterId;

    /**
     * 期末成绩
     */
    @NotNull(message = "期末成绩不能为空")
    @DecimalMin(value = "0", message = "成绩不能小于0")
    @DecimalMax(value = "100", message = "成绩不能大于100")
    private BigDecimal finalScore;

    /**
     * 是否重修
     */
    private Boolean isRetake = false;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 行号（用于错误提示）
     */
    private Integer rowNumber;
}
