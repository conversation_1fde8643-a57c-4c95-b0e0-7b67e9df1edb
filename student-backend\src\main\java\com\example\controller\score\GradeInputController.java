package com.example.controller.score;

import com.example.common.PageResult;
import com.example.common.Result;
import com.example.dto.score.GradeInputDTO;
import com.example.dto.score.GradeInputQueryDTO;

import com.example.dto.score.GradeImportResultDTO;
import com.example.service.score.GradeInputService;
import com.example.utils.FileNameUtils;
import com.example.vo.score.GradeInputVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 成绩录入控制器
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Tag(name = "成绩录入管理", description = "成绩录入相关接口")
@RestController
@RequestMapping("/api/score/input")
@RequiredArgsConstructor
@Slf4j
@Validated
public class GradeInputController {

    private final GradeInputService gradeInputService;

    @Operation(summary = "分页查询成绩录入列表")
    @PostMapping("/page")
    public Result<PageResult<GradeInputVO>> getGradeInputPage(@RequestBody GradeInputQueryDTO params) {
        try {
            PageResult<GradeInputVO> result = gradeInputService.getGradeInputPage(params);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询成绩录入列表失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    @Operation(summary = "录入单个成绩")
    @PostMapping
    public Result<Boolean> inputGrade(@RequestBody @Validated GradeInputDTO gradeInputDTO) {
        try {
            boolean success = gradeInputService.inputGrade(gradeInputDTO);
            return success ? Result.success("录入成功", true) : Result.error("录入失败");
        } catch (Exception e) {
            log.error("录入成绩失败", e);
            return Result.error("录入失败：" + e.getMessage());
        }
    }



    @Operation(summary = "更新成绩")
    @PutMapping("/{id}")
    public Result<Boolean> updateGrade(@PathVariable Integer id, @RequestBody @Validated GradeInputDTO gradeInputDTO) {
        try {
            gradeInputDTO.setId(id);
            boolean success = gradeInputService.updateGrade(gradeInputDTO);
            return success ? Result.success("更新成功", true) : Result.error("更新失败");
        } catch (Exception e) {
            log.error("更新成绩失败", e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }



    @Operation(summary = "根据班级和课程获取学生成绩录入列表")
    @GetMapping("/students")
    public Result<List<GradeInputVO>> getStudentGradeInputList(
            @Parameter(description = "班级代码") @RequestParam String classCode,
            @Parameter(description = "课程代码") @RequestParam String courseCode,
            @Parameter(description = "学期ID") @RequestParam Integer semesterId) {
        try {
            List<GradeInputVO> result = gradeInputService.getStudentGradeInputList(classCode, courseCode, semesterId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取学生成绩录入列表失败", e);
            return Result.error("获取失败：" + e.getMessage());
        }
    }

    @Operation(summary = "根据ID获取成绩详情")
    @GetMapping("/{id}")
    public Result<GradeInputVO> getGradeById(@PathVariable Integer id) {
        try {
            GradeInputVO result = gradeInputService.getGradeById(id);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取成绩详情失败", e);
            return Result.error("获取失败：" + e.getMessage());
        }
    }

    @Operation(summary = "检查成绩是否已存在")
    @GetMapping("/check")
    public Result<Boolean> checkGradeExists(
            @Parameter(description = "学号") @RequestParam String studentId,
            @Parameter(description = "课程代码") @RequestParam String courseCode,
            @Parameter(description = "学期ID") @RequestParam Integer semesterId) {
        try {
            boolean exists = gradeInputService.checkGradeExists(studentId, courseCode, semesterId);
            return Result.success(exists);
        } catch (Exception e) {
            log.error("检查成绩是否存在失败", e);
            return Result.error("检查失败：" + e.getMessage());
        }
    }

    @Operation(summary = "导入成绩")
    @PostMapping("/import")
    public Result<GradeImportResultDTO> importGrades(
            @Parameter(description = "Excel文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "班级代码") @RequestParam String classCode,
            @Parameter(description = "课程代码") @RequestParam String courseCode,
            @Parameter(description = "学期ID") @RequestParam Integer semesterId) {
        try {
            GradeImportResultDTO result = gradeInputService.importGrades(file, classCode, courseCode, semesterId);
            if (result.isSuccess()) {
                return Result.success(result.getSummary(), result);
            } else {
                return Result.result(false, Result.BAD_REQUEST, "导入失败", result);
            }
        } catch (Exception e) {
            log.error("导入成绩失败", e);
            return Result.error("导入失败：" + e.getMessage());
        }
    }

    @Operation(summary = "下载成绩导入模板")
    @GetMapping("/template")
    public ResponseEntity<byte[]> downloadImportTemplate(
            @Parameter(description = "班级代码") @RequestParam String classCode,
            @Parameter(description = "课程代码") @RequestParam String courseCode,
            @Parameter(description = "学期ID") @RequestParam Integer semesterId) {
        try {
            byte[] templateData = gradeInputService.generateImportTemplate(classCode, courseCode, semesterId);

            // 生成文件名：xx学期xx班xx课程成绩导入模板
            String fileName = gradeInputService.generateTemplateFileName(classCode, courseCode, semesterId);

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            // 使用工具类生成正确的Content-Disposition头
            headers.set("Content-Disposition", FileNameUtils.generateContentDisposition(fileName));
            headers.set("Cache-Control", "no-cache");

            return new ResponseEntity<>(templateData, headers, HttpStatus.OK);

        } catch (Exception e) {
            log.error("下载成绩导入模板失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Operation(summary = "下载多课程成绩导入模板")
    @GetMapping("/multi-template")
    public ResponseEntity<byte[]> downloadMultiCourseTemplate(
            @Parameter(description = "班级代码") @RequestParam String classCode,
            @Parameter(description = "课程代码列表，逗号分隔") @RequestParam String courseCodes,
            @Parameter(description = "学期ID，可选") @RequestParam(required = false) Integer semesterId) {
        try {
            String[] courseCodeArray = courseCodes.split(",");
            byte[] templateData = gradeInputService.generateMultiCourseTemplate(classCode, courseCodeArray, semesterId);

            // 生成文件名
            String fileName = gradeInputService.generateMultiCourseTemplateFileName(classCode, courseCodeArray, semesterId);

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.set("Content-Disposition", FileNameUtils.generateContentDisposition(fileName));
            headers.set("Cache-Control", "no-cache");

            return new ResponseEntity<>(templateData, headers, HttpStatus.OK);

        } catch (Exception e) {
            log.error("下载多课程成绩导入模板失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Operation(summary = "导入多课程成绩")
    @PostMapping("/multi-import")
    public Result<GradeImportResultDTO> importMultiCourseGrades(
            @Parameter(description = "Excel文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "班级代码") @RequestParam String classCode,
            @Parameter(description = "课程代码列表，逗号分隔") @RequestParam String courseCodes,
            @Parameter(description = "学期ID，可选") @RequestParam(required = false) Integer semesterId) {
        try {
            String[] courseCodeArray = courseCodes.split(",");
            GradeImportResultDTO result = gradeInputService.importMultiCourseGrades(file, classCode, courseCodeArray, semesterId);

            if (result.isSuccess()) {
                return Result.success(result.getSummary(), result);
            } else {
                return Result.result(false, Result.BAD_REQUEST, "导入失败", result);
            }
        } catch (Exception e) {
            log.error("导入多课程成绩失败", e);
            return Result.error("导入失败：" + e.getMessage());
        }
    }

}
