package com.itmk.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;

/**
 * 缓存配置
 * 基于Spring Boot最佳实践
 */
@Configuration
@EnableCaching
public class CacheConfig {

    /**
     * 缓存管理器
     */
    @Bean
    public CacheManager cacheManager() {
        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager();

        // 设置缓存名称
        cacheManager.setCacheNames(Arrays.asList(
            "menuCache",           // 菜单缓存
            "userMenuCache",       // 用户菜单缓存
            "studentMenuCache",    // 学生菜单缓存
            "teacherMenuCache",    // 教师菜单缓存
            "roleMenuCache"        // 角色菜单缓存
        ));

        // 允许空值
        cacheManager.setAllowNullValues(false);

        return cacheManager;
    }
}
