<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.system.SysDeptMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.entity.system.SysDept">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="parent_id" property="parentId" jdbcType="INTEGER"/>
        <result column="dept_name" property="deptName" jdbcType="VARCHAR"/>
        <result column="dept_code" property="deptCode" jdbcType="VARCHAR"/>
        <result column="leader" property="leader" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 通用SQL片段 -->
    <sql id="Base_Column_List">
        id, parent_id, dept_name, dept_code, leader, phone, email, 
        sort, status, remark, create_time, update_time
    </sql>

    <!-- 查询部门列表 -->
    <select id="selectDeptList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_dept
        <where>
            status != 0
            <if test="query.deptName != null and query.deptName != ''">
                AND dept_name LIKE CONCAT('%', #{query.deptName}, '%')
            </if>
            <if test="query.deptCode != null and query.deptCode != ''">
                AND dept_code LIKE CONCAT('%', #{query.deptCode}, '%')
            </if>
            <if test="query.leader != null and query.leader != ''">
                AND leader LIKE CONCAT('%', #{query.leader}, '%')
            </if>
            <if test="query.status != null">
                AND status = #{query.status}
            </if>
            <if test="query.parentId != null">
                AND parent_id = #{query.parentId}
            </if>
        </where>
        ORDER BY sort ASC, create_time DESC
    </select>

    <!-- 查询所有启用的部门 -->
    <select id="selectAllActive" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_dept
        WHERE status = 1
        ORDER BY sort ASC, create_time DESC
    </select>

    <!-- 根据父级ID查询子部门 -->
    <select id="selectByParentId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_dept 
        WHERE parent_id = #{parentId} 
          AND status = 1 
        ORDER BY sort ASC
    </select>

    <!-- 检查部门名称是否存在 -->
    <select id="existsByDeptName" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM sys_dept
        WHERE dept_name = #{deptName}
          AND status = 1
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查部门编码是否存在 -->
    <select id="existsByDeptCode" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM sys_dept
        WHERE dept_code = #{deptCode}
          AND status = 1
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 根据部门编码查询部门 -->
    <select id="selectByDeptCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_dept
        WHERE dept_code = #{deptCode}
          AND status = 1
    </select>

    <!-- 查询部门详情 -->
    <select id="selectDeptDetail" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_dept
        WHERE id = #{deptId}
          AND status = 1
    </select>

    <!-- 根据父级ID查询子部门数量 -->
    <select id="countByParentId" parameterType="java.lang.Integer" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM sys_dept
        WHERE parent_id = #{parentId}
          AND status = 1
    </select>

    <!-- 获取最大排序值 -->
    <select id="getMaxSort" resultType="java.lang.Integer">
        SELECT COALESCE(MAX(sort), 0)
        FROM sys_dept
        WHERE status = 1
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
    </select>

    <!-- 批量更新部门状态 -->
    <update id="batchUpdateStatus">
        UPDATE sys_dept 
        SET status = #{status},
            update_time = NOW()
        WHERE id IN
        <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </update>

    <!-- 递归查询所有子部门ID -->
    <select id="selectChildrenIds" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        WITH RECURSIVE dept_tree AS (
            SELECT id FROM sys_dept WHERE id = #{deptId} AND status = 1
            UNION ALL
            SELECT d.id FROM sys_dept d
            INNER JOIN dept_tree dt ON d.parent_id = dt.id
            WHERE d.status = 1
        )
        SELECT id FROM dept_tree WHERE id != #{deptId}
    </select>

    <!-- 查询部门树结构 -->
    <select id="selectDeptTree" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_dept
        WHERE status = 1
        ORDER BY sort ASC, create_time DESC
    </select>

    <!-- 根据部门ID查询用户数量 -->
    <select id="countUsersByDeptId" parameterType="java.lang.Integer" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM sys_user
        WHERE dept_id = #{deptId}
          AND status = 1
    </select>

    <!-- 统计部门数量 -->
    <select id="countDepts" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM sys_dept
        WHERE status = 1
    </select>

    <!-- 查询顶级部门 -->
    <select id="selectTopLevelDepts" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_dept
        WHERE parent_id = 0
          AND status = 1
        ORDER BY sort ASC
    </select>

</mapper>
