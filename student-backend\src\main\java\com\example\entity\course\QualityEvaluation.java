package com.example.entity.course;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 基本素质测评实体类
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("quality_evaluation")
public class QualityEvaluation {

    /**
     * 主键ID
     */
    @TableId(value = "evaluation_id", type = IdType.AUTO)
    private Integer evaluationId;

    /**
     * 学号
     */
    @TableField("student_id")
    private String studentId;

    /**
     * 加分项
     */
    @TableField("add_score")
    private BigDecimal addScore;

    /**
     * 扣分项
     */
    @TableField("reduce_score")
    private BigDecimal reduceScore;

    /**
     * 加分说明
     */
    @TableField("add_score_remark")
    private String addScoreRemark;

    /**
     * 扣分说明
     */
    @TableField("reduce_score_remark")
    private String reduceScoreRemark;

    /**
     * 学期ID
     */
    @TableField("semester_id")
    private Integer semesterId;

    /**
     * 周期得分
     */
    @TableField("period_score")
    private BigDecimal periodScore;

    /**
     * 总得分
     */
    @TableField("total_score")
    private BigDecimal totalScore;

    /**
     * 上一学期总分
     */
    @TableField("prev_period_score")
    private BigDecimal prevPeriodScore;

    /**
     * 班级排名
     */
    @TableField("class_ranking")
    private Integer classRanking;

    /**
     * 专业排名
     */
    @TableField("major_ranking")
    private Integer majorRanking;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
