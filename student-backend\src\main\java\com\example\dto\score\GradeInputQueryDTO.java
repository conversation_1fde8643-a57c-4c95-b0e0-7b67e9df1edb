package com.example.dto.score;

import com.example.dto.BaseQueryDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 成绩录入查询DTO
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "成绩录入查询DTO")
public class GradeInputQueryDTO extends BaseQueryDTO {

    @Schema(description = "班级代码")
    private String classCode;

    @Schema(description = "课程代码")
    private String courseCode;

    @Schema(description = "学期ID")
    private Integer semesterId;

    @Schema(description = "学号")
    private String studentId;

    @Schema(description = "学生姓名")
    private String studentName;

    @Schema(description = "是否已录入成绩")
    private Boolean hasGrade;
}
