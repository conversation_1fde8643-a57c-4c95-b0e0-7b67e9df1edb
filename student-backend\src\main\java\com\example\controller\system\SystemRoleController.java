package com.example.controller.system;

import com.example.common.PageResult;
import com.example.common.Result;
import com.example.dto.system.RoleQueryDTO;
import com.example.service.system.RoleService;
import com.example.vo.system.RoleVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 系统角色管理控制器
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@RestController
@RequestMapping("/api/system/role")
@RequiredArgsConstructor
@Validated
@Tag(name = "系统角色管理", description = "系统角色管理相关接口")
public class SystemRoleController {

    private final RoleService roleService;

    /**
     * 获取角色列表
     */
    @PostMapping("/list")
    @Operation(summary = "获取角色列表", description = "分页查询角色列表")
    public Result<PageResult<RoleVO>> getRoleList(@RequestBody(required = false) RoleQueryDTO query) {
        PageResult<RoleVO> roleList = roleService.getRoleList(query);
        return Result.success("查询成功", roleList);
    }

    /**
     * 获取所有角色列表（用于下拉选择）
     */
    @GetMapping("/all")
    @Operation(summary = "获取所有角色", description = "获取所有可用角色列表")
    public Result<List<RoleVO>> getAllRoles() {
        List<RoleVO> roleList = roleService.getAllRoles();
        return Result.success("查询成功", roleList);
    }

    /**
     * 根据ID获取角色详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取角色详情", description = "根据ID获取角色详情")
    public Result<RoleVO> getRoleById(@PathVariable Integer id) {
        if (id == null) {
            return Result.badRequest("角色ID不能为空");
        }
        RoleVO role = roleService.getRoleById(id);
        return Result.success("查询成功", role);
    }

    /**
     * 新增角色
     */
    @PostMapping("/save")
    @Operation(summary = "新增角色", description = "新增系统角色")
    public Result<Void> saveRole(@Valid @RequestBody RoleVO roleVO) {
        roleService.saveRole(roleVO);
        return Result.success("新增角色成功");
    }

    /**
     * 更新角色
     */
    @PostMapping("/update")
    @Operation(summary = "更新角色", description = "更新系统角色")
    public Result<Void> updateRole(@Valid @RequestBody RoleVO roleVO) {
        if (roleVO.getId() == null) {
            return Result.badRequest("角色ID不能为空");
        }
        roleService.updateRole(roleVO);
        return Result.success("修改角色成功");
    }

    /**
     * 删除角色
     */
    @PostMapping("/delete")
    @Operation(summary = "删除角色", description = "删除系统角色")
    public Result<Void> deleteRole(@RequestBody Map<String, Object> params) {
        if (params == null || params.get("id") == null) {
            return Result.badRequest("角色ID不能为空");
        }

        try {
            Integer roleId = Integer.valueOf(params.get("id").toString());
            roleService.deleteRole(roleId);
            return Result.success("删除角色成功");
        } catch (NumberFormatException e) {
            return Result.badRequest("角色ID格式错误");
        }
    }

    /**
     * 获取角色菜单权限
     */
    @PostMapping("/menu-permissions")
    @Operation(summary = "获取角色菜单权限", description = "获取指定角色的菜单权限")
    public Result<List<Integer>> getRoleMenuPermissions(@RequestBody Map<String, Object> params) {
        if (params == null || params.get("roleId") == null) {
            return Result.error(400, "角色ID不能为空");
        }

        try {
            Integer roleId = Integer.valueOf(params.get("roleId").toString());
            List<Integer> menuIds = roleService.getRoleMenuPermissions(roleId);
            return Result.success("查询成功", menuIds);
        } catch (NumberFormatException e) {
            return Result.error(400, "角色ID格式错误");
        }
    }

    /**
     * 分配角色菜单权限
     */
    @PostMapping("/assign-menu-permissions")
    @Operation(summary = "分配菜单权限", description = "为角色分配菜单权限")
    public Result<Void> assignMenuPermissions(@RequestBody Map<String, Object> params) {
        if (params == null || params.get("roleId") == null || params.get("menuIds") == null) {
            return Result.error(400, "角色ID和菜单ID不能为空");
        }

        try {
            Integer roleId = Integer.valueOf(params.get("roleId").toString());
            @SuppressWarnings("unchecked")
            List<Integer> menuIds = (List<Integer>) params.get("menuIds");
            roleService.assignMenuPermissions(roleId, menuIds);
            return Result.success("分配菜单权限成功");
        } catch (Exception e) {
            return Result.error(400, "参数格式错误");
        }
    }

    /**
     * 切换角色状态
     */
    @PostMapping("/toggle-status")
    @Operation(summary = "切换角色状态", description = "启用或禁用角色")
    public Result<Void> toggleRoleStatus(@RequestBody Map<String, Object> params) {
        if (params == null || params.get("roleId") == null || params.get("status") == null) {
            return Result.badRequest("角色ID和状态不能为空");
        }

        try {
            Integer roleId = Integer.valueOf(params.get("roleId").toString());
            Integer status = Integer.valueOf(params.get("status").toString());
            roleService.toggleRoleStatus(roleId, status);
            String statusText = status == 1 ? "启用" : "禁用";
            return Result.success(statusText + "角色成功");
        } catch (NumberFormatException e) {
            return Result.badRequest("参数格式错误");
        }
    }
}
