import { score } from "@/router/enums";

export default {
  path: "/score",
  redirect: "/score/grade-input",
  meta: {
    icon: "ri:file-chart-line",
    title: "成绩管理",
    rank: score
  },
  children: [
    {
      path: "/score/grade-input",
      name: "GradeInput",
      component: () => import("@/views/score/grade-input/index.vue"),
      meta: {
        title: "成绩录入",
        showParent: true
      }
    },
    {
      path: "/score/quality-assessment",
      name: "QualityAssessment",
      component: () => import("@/views/score/quality-assessment/index.vue"),
      meta: {
        title: "基本素质测评",
        showParent: true
      }
    }
  ]
} satisfies RouteConfigsTable;
