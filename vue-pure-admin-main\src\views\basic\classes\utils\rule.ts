import { reactive } from "vue";
import type { FormRules } from "element-plus";

/** 自定义表单规则校验 */
export const formRules = reactive(<FormRules>{
  collegeCode: [
    { required: true, message: "请选择所属学院", trigger: "change" }
  ],
  majorCode: [
    { required: true, message: "请选择所属专业", trigger: "change" }
  ],
  classCode: [
    { required: true, message: "班级代码为必填项", trigger: "blur" },
    { min: 2, max: 20, message: "班级代码长度应为2-20个字符", trigger: "blur" }
  ],
  className: [
    { required: true, message: "班级名称为必填项", trigger: "blur" },
    { min: 2, max: 100, message: "班级名称长度应为2-100个字符", trigger: "blur" }
  ],
  gradeYear: [
    { required: true, message: "请选择入学年份", trigger: "change" },
    {
      validator: (_rule, value, callback) => {
        if (!value) {
          callback();
          return;
        }

        const currentYear = new Date().getFullYear();
        if (value < 2000) {
          callback(new Error("入学年份不能小于2000年"));
        } else if (value > currentYear + 1) {
          callback(new Error("入学年份不能超过明年"));
        } else {
          callback();
        }
      },
      trigger: "change"
    }
  ]
});
