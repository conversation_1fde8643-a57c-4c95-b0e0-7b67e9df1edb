<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import ReCol from "@/components/ReCol";

// 图标导入
import AddFill from "@iconify-icons/ri/add-circle-line";
import Upload from "@iconify-icons/ep/upload";
import Download from "@iconify-icons/ep/download";
import EditPen from "@iconify-icons/ep/edit-pen";
import Delete from "@iconify-icons/ep/delete";

import { useQualityAssessmentHook } from "./utils/hook";

defineOptions({
  name: "QualityAssessment"
});

const {
  // 数据状态
  loading,
  dataList,
  pagination,

  // 搜索相关
  searchForm,
  onSearch,
  resetForm,

  // 表格相关
  columns,

  // 操作相关
  openDialog,
  handleDelete,
  handleExport,
  handleImport,

  // 选择相关
  currentSelection,
  majors,
  classes,
  courses,
  handleMajorChange,
  handleClassChange,
  handleCourseChange,

  // 视图切换
  currentView,
  handleViewChange,

  // 成绩相关
  loadGrades,

  // 导入相关
  openImportDialog,
  handleImportSuccess
} = useQualityAssessmentHook();

onMounted(() => {
  // 组件挂载后的初始化逻辑在hook中处理
});
</script>

<template>
  <div class="main">
    <!-- 导航选择区域 -->
    <div class="selection-container">
      <el-card shadow="never" class="selection-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">基本素质测评成绩登记</span>
          </div>
        </template>

        <el-row :gutter="20" class="selection-row">
          <re-col :value="6" :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="选择专业">
              <el-select
                v-model="currentSelection.major"
                placeholder="请选择专业"
                clearable
                filterable
                style="width: 100%"
                @change="handleMajorChange"
              >
                <el-option
                  v-for="major in majors"
                  :key="major.majorId"
                  :label="major.majorName"
                  :value="major"
                />
              </el-select>
            </el-form-item>
          </re-col>

          <re-col :value="6" :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="选择班级">
              <el-select
                v-model="currentSelection.class"
                placeholder="请选择班级"
                clearable
                filterable
                style="width: 100%"
                :disabled="!currentSelection.major"
                @change="handleClassChange"
              >
                <el-option
                  v-for="cls in classes"
                  :key="cls.classId"
                  :label="cls.className"
                  :value="cls"
                />
              </el-select>
            </el-form-item>
          </re-col>

          <re-col :value="6" :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="选择学期">
              <el-select
                v-model="currentSelection.semester"
                placeholder="请选择学期"
                clearable
                filterable
                style="width: 100%"
                @change="handleCourseChange"
              >
                <el-option
                  v-for="semester in courses"
                  :key="semester.semesterId"
                  :label="semester.semesterName"
                  :value="semester"
                />
              </el-select>
            </el-form-item>
          </re-col>

          <re-col :value="6" :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item>
              <el-button
                type="primary"
                :disabled="!currentSelection.class || !currentSelection.semester"
                @click="loadGrades"
              >
                查询成绩
              </el-button>
            </el-form-item>
          </re-col>
        </el-row>
      </el-card>
    </div>

    <!-- 功能操作区域 -->
    <div v-if="currentSelection.class && currentSelection.semester" class="content-container">
      <PureTableBar
        title="基本素质测评成绩"
        :columns="columns"
        @refresh="loadGrades"
      >
        <template #buttons>
          <el-button
            type="primary"
            :icon="useRenderIcon(AddFill)"
            @click="openDialog()"
          >
            新增记录
          </el-button>

          <el-button
            type="success"
            :icon="useRenderIcon(Upload)"
            @click="openImportDialog"
          >
            成绩导入
          </el-button>

          <el-button
            type="info"
            :icon="useRenderIcon(Download)"
            @click="handleExport"
          >
            导出数据
          </el-button>
        </template>

        <template v-slot="{ size, dynamicColumns }">
          <pure-table
            adaptive
            :adaptiveConfig="{ offsetBottom: 108 }"
            align-whole="center"
            showOverflowTooltip
            table-layout="auto"
            :loading="loading"
            :size="size"
            :data="dataList"
            :columns="dynamicColumns"
            :pagination="pagination"
            :paginationSmall="size === 'small'"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            @selection-change="handleSelectionChange"
            @page-size-change="handleSizeChange"
            @page-current-change="handleCurrentChange"
          >
            <template #operation="{ row }">
              <el-button
                class="reset-margin"
                link
                type="primary"
                :size="size"
                :icon="useRenderIcon(EditPen)"
                @click="openDialog('编辑', row)"
              >
                编辑
              </el-button>
              <el-popconfirm
                :title="`是否确认删除学生${row.studentName}的素质测评记录？`"
                @confirm="handleDelete(row)"
              >
                <template #reference>
                  <el-button
                    class="reset-margin"
                    link
                    type="primary"
                    :size="size"
                    :icon="useRenderIcon(Delete)"
                  >
                    删除
                  </el-button>
                </template>
              </el-popconfirm>
            </template>
          </pure-table>
        </template>
      </PureTableBar>
    </div>
  </div>
</template>

<style scoped>
.main {
  margin: 0;
  padding: 12px;
  background: #f0f2f5;
  min-height: calc(100vh - 86px);
}

.selection-container {
  margin-bottom: 16px;
}

.selection-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.selection-row {
  margin: 0;
}

.content-container {
  background: white;
  border-radius: 8px;
  padding: 16px;
}

:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--el-text-color-regular);
}
</style>
