<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.score.GradeInputMapper">

    <!-- 成绩录入VO结果映射 -->
    <resultMap id="GradeInputVOMap" type="com.example.vo.score.GradeInputVO">
        <id column="id" property="id"/>
        <result column="student_id" property="studentId"/>
        <result column="student_name" property="studentName"/>
        <result column="class_code" property="classCode"/>
        <result column="class_name" property="className"/>
        <result column="course_code" property="courseCode"/>
        <result column="course_name" property="courseName"/>
        <result column="course_type" property="courseType"/>
        <result column="credits" property="credits"/>
        <result column="semester_id" property="semesterId"/>
        <result column="semester_name" property="semesterName"/>
        <result column="final_score" property="finalScore"/>
        <result column="grade_point" property="gradePoint"/>
        <result column="is_retake" property="isRetake"/>
        <result column="remarks" property="remarks"/>
        <result column="has_grade" property="hasGrade"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- 分页查询成绩录入列表 -->
    <select id="selectGradeInputPage" resultMap="GradeInputVOMap">
        SELECT
            g.id,
            g.student_id,
            s.name as student_name,
            s.class_code,
            c.class_name,
            g.course_code,
            co.course_name,
            co.course_type,
            co.credits,
            g.semester_id,
            sem.semester_name,
            g.final_score,
            g.grade_point,
            g.is_retake,
            g.remarks,
            CASE WHEN g.id IS NOT NULL THEN 1 ELSE 0 END as has_grade,
            g.created_at,
            g.updated_at
        FROM grades g
        LEFT JOIN students s ON g.student_id = s.student_id
        LEFT JOIN classes c ON s.class_code = c.class_code
        LEFT JOIN courses co ON g.course_code = co.course_code
        LEFT JOIN semesters sem ON g.semester_id = sem.id
        <where>
            <if test="params.classCode != null and params.classCode != ''">
                AND s.class_code = #{params.classCode}
            </if>
            <if test="params.courseCode != null and params.courseCode != ''">
                AND g.course_code = #{params.courseCode}
            </if>
            <if test="params.semesterId != null">
                AND g.semester_id = #{params.semesterId}
            </if>
            <if test="params.studentId != null and params.studentId != ''">
                AND g.student_id LIKE CONCAT('%', #{params.studentId}, '%')
            </if>
            <if test="params.studentName != null and params.studentName != ''">
                AND s.student_name LIKE CONCAT('%', #{params.studentName}, '%')
            </if>
        </where>
        ORDER BY g.created_at DESC
    </select>

    <!-- 根据班级和课程获取学生成绩录入列表 -->
    <select id="selectStudentGradeInputList" resultMap="GradeInputVOMap">
        SELECT
            g.id,
            s.student_id,
            s.name as student_name,
            s.class_code,
            c.class_name,
            #{courseCode} as course_code,
            co.course_name,
            co.course_type,
            co.credits,
            #{semesterId} as semester_id,
            sem.semester_name,
            g.final_score,
            g.grade_point,
            g.is_retake,
            g.remarks,
            CASE WHEN g.id IS NOT NULL THEN 1 ELSE 0 END as has_grade,
            g.created_at,
            g.updated_at
        FROM students s
        LEFT JOIN classes c ON s.class_code = c.class_code
        LEFT JOIN courses co ON co.course_code = #{courseCode}
        LEFT JOIN semesters sem ON sem.id = #{semesterId}
        LEFT JOIN grades g ON s.student_id = g.student_id
                           AND g.course_code = #{courseCode}
                           AND g.semester_id = #{semesterId}
        WHERE s.class_code = #{classCode}
        ORDER BY s.student_id
    </select>

    <!-- 根据ID获取成绩详情 -->
    <select id="selectGradeInputById" resultMap="GradeInputVOMap">
        SELECT
            g.id,
            g.student_id,
            s.name as student_name,
            s.class_code,
            c.class_name,
            g.course_code,
            co.course_name,
            co.course_type,
            co.credits,
            g.semester_id,
            sem.semester_name,
            g.final_score,
            g.grade_point,
            g.is_retake,
            g.remarks,
            1 as has_grade,
            g.created_at,
            g.updated_at
        FROM grades g
        LEFT JOIN students s ON g.student_id = s.student_id
        LEFT JOIN classes c ON s.class_code = c.class_code
        LEFT JOIN courses co ON g.course_code = co.course_code
        LEFT JOIN semesters sem ON g.semester_id = sem.id
        WHERE g.id = #{id}
    </select>

    <!-- 检查成绩是否已存在 -->
    <select id="checkGradeExists" resultType="int">
        SELECT COUNT(1)
        FROM grades
        WHERE student_id = #{studentId}
          AND course_code = #{courseCode}
          AND semester_id = #{semesterId}
    </select>

    <!-- 根据条件查询成绩 -->
    <select id="selectGradeByCondition" resultType="com.example.entity.score.Grade">
        SELECT *
        FROM grades
        WHERE student_id = #{studentId}
          AND course_code = #{courseCode}
          AND semester_id = #{semesterId}
        LIMIT 1
    </select>

</mapper>
