<template>
  <el-dialog
    v-model="dialogVisible"
    title="批量分配课程"
    width="800px"
    draggable
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="选择班级" prop="classCodes">
        <el-select
          v-model="form.classCodes"
          multiple
          placeholder="请选择要分配课程的班级"
          class="w-full"
          filterable
        >
          <el-option
            v-for="classItem in classOptions"
            :key="classItem.classCode"
            :label="classItem.className"
            :value="classItem.classCode"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="选择课程" prop="courseCodes">
        <el-select
          v-model="form.courseCodes"
          multiple
          placeholder="请选择要分配的课程"
          class="w-full"
          filterable
        >
          <el-option
            v-for="course in courseOptions"
            :key="course.courseCode"
            :label="`${course.courseName} (${course.courseCode})`"
            :value="course.courseCode"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="学期" prop="semesterId">
        <el-select
          v-model="form.semesterId"
          placeholder="请选择学期"
          class="w-full"
        >
          <el-option
            v-for="semester in semesterOptions"
            :key="semester.id"
            :label="semester.semesterName"
            :value="semester.id"
          />
        </el-select>
      </el-form-item>


    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleConfirm"
        >
          确定分配
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { batchAssignCourses, BatchAssignParams } from "@/api/educational/classCourse";
import { getAllClasses } from "@/api/basic/classes";
import { getAllCourses } from "@/api/educational/course";
import { getAllSemesters } from "@/api/basic/semester";

interface Props {
  visible: boolean;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  "update:visible": [value: boolean];
  success: [];
}>();

// 响应式数据
const formRef = ref();
const loading = ref(false);
const dialogVisible = ref(false);

// 表单数据
const form = reactive({
  classCodes: [] as string[],
  courseCodes: [] as string[],
  semesterId: null as number | null
});

// 选项数据
const classOptions = ref([]);
const courseOptions = ref([]);
const semesterOptions = ref([]);

// 表单验证规则
const rules = {
  classCodes: [
    { required: true, message: "请选择班级", trigger: "change" },
    { type: "array", min: 1, message: "至少选择一个班级", trigger: "change" }
  ],
  courseCodes: [
    { required: true, message: "请选择课程", trigger: "change" },
    { type: "array", min: 1, message: "至少选择一门课程", trigger: "change" }
  ],
  semesterId: [
    { required: true, message: "请选择学期", trigger: "change" }
  ],

};

// 监听visible变化
watch(() => props.visible, (val) => {
  dialogVisible.value = val;
  if (val) {
    resetForm();
  }
});

watch(dialogVisible, (val) => {
  emit("update:visible", val);
});

// 重置表单
function resetForm() {
  Object.assign(form, {
    classCodes: [],
    courseCodes: [],
    semesterId: null
  });
  formRef.value?.clearValidate();
}

// 取消
function handleCancel() {
  dialogVisible.value = false;
}

// 确认分配
async function handleConfirm() {
  try {
    const valid = await formRef.value.validate();
    if (!valid) return;

    loading.value = true;

    // 为每个班级调用批量分配API
    const promises = form.classCodes.map(classCode =>
      batchAssignCourses({
        classCode,
        courseCodes: form.courseCodes,
        semesterId: form.semesterId!
      })
    );

    await Promise.all(promises);

    ElMessage.success("批量分配成功");
    emit("success");
    dialogVisible.value = false;
  } catch (error) {
    console.error("批量分配失败:", error);
    ElMessage.error("批量分配失败");
  } finally {
    loading.value = false;
  }
}

// 加载选项数据
async function loadOptions() {
  try {
    const [classRes, courseRes, semesterRes] = await Promise.all([
      getAllClasses(),
      getAllCourses(),
      getAllSemesters()
    ]);

    if (classRes.success) {
      classOptions.value = classRes.data || [];
    }
    if (courseRes.success) {
      courseOptions.value = courseRes.data || [];
    }
    if (semesterRes.success) {
      semesterOptions.value = semesterRes.data || [];
    }
  } catch (error) {
    console.error("加载选项数据失败:", error);
  }
}

onMounted(() => {
  loadOptions();
});
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
