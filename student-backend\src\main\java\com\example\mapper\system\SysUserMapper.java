package com.example.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.entity.system.SysUser;
import com.example.dto.system.UserQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统用户Mapper
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {

    /**
     * 根据用户ID查询角色编码列表
     *
     * @param userId 用户ID
     * @return 角色编码列表
     */
    List<String> selectRoleCodesByUserId(@Param("userId") Integer userId);

    /**
     * 根据用户ID查询权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    List<String> selectPermissionsByUserId(@Param("userId") Integer userId);

    /**
     * 分页查询用户列表
     *
     * @param page 分页参数
     * @param query 查询条件
     * @return 用户列表
     */
    IPage<SysUser> selectUserPage(IPage<SysUser> page, @Param("query") UserQueryDTO query);

    /**
     * 根据用户名查询用户（用于登录验证）
     *
     * @param username 用户名
     * @return 用户信息
     */
    SysUser selectByUsername(@Param("username") String username);

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    Boolean existsByUsername(@Param("username") String username, @Param("excludeId") Integer excludeId);

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    Boolean existsByEmail(@Param("email") String email, @Param("excludeId") Integer excludeId);

    /**
     * 检查手机号是否存在
     *
     * @param phone 手机号
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    Boolean existsByPhone(@Param("phone") String phone, @Param("excludeId") Integer excludeId);

    /**
     * 根据部门ID查询用户数量
     *
     * @param deptId 部门ID
     * @return 用户数量
     */
    Long countByDeptId(@Param("deptId") Integer deptId);

    /**
     * 更新用户最后登录信息
     *
     * @param userId 用户ID
     * @param lastLoginTime 最后登录时间
     * @param lastLoginIp 最后登录IP
     */
    void updateLastLoginInfo(@Param("userId") Integer userId,
                           @Param("lastLoginTime") LocalDateTime lastLoginTime,
                           @Param("lastLoginIp") String lastLoginIp);

    /**
     * 批量更新用户状态
     *
     * @param userIds 用户ID列表
     * @param status 状态
     */
    void batchUpdateStatus(@Param("userIds") List<Integer> userIds, @Param("status") Integer status);

    /**
     * 重置用户密码
     *
     * @param userId 用户ID
     * @param newPassword 新密码
     */
    void resetPassword(@Param("userId") Integer userId, @Param("newPassword") String newPassword);
}
