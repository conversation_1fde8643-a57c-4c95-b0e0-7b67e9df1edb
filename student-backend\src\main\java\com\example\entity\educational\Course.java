package com.example.entity.educational;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 课程实体类
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("courses")
public class Course {

    /**
     * 课程ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 课程代码
     */
    @TableField("course_code")
    private String courseCode;

    /**
     * 课程名称
     */
    @TableField("course_name")
    private String courseName;

    /**
     * 学分
     */
    @TableField("credits")
    private BigDecimal credits;

    /**
     * 课程类型
     */
    @TableField("course_type")
    private String courseType;

    /**
     * 所属学院代码
     */
    @TableField("college_code")
    private String collegeCode;

    /**
     * 课程描述
     */
    @TableField("description")
    private String description;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
