<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.system.SysUserRoleMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.entity.system.SysUserRole">
        <result column="user_id" property="userId" jdbcType="INTEGER"/>
        <result column="role_id" property="roleId" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 通用SQL片段 -->
    <sql id="Base_Column_List">
        user_id, role_id
    </sql>

    <!-- 根据用户ID查询角色ID列表 -->
    <select id="selectRoleIdsByUserId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT role_id
        FROM sys_user_role
        WHERE user_id = #{userId}
        ORDER BY role_id ASC
    </select>

    <!-- 根据角色ID查询用户ID列表 -->
    <select id="selectUserIdsByRoleId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT user_id
        FROM sys_user_role
        WHERE role_id = #{roleId}
        ORDER BY user_id ASC
    </select>

    <!-- 检查用户角色关系是否存在 -->
    <select id="existsUserRole" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM sys_user_role
        WHERE user_id = #{userId}
          AND role_id = #{roleId}
    </select>

    <!-- 根据用户ID删除用户角色关系 -->
    <delete id="deleteByUserId" parameterType="java.lang.Integer">
        DELETE FROM sys_user_role
        WHERE user_id = #{userId}
    </delete>

    <!-- 根据角色ID删除用户角色关系 -->
    <delete id="deleteByRoleId" parameterType="java.lang.Integer">
        DELETE FROM sys_user_role
        WHERE role_id = #{roleId}
    </delete>

    <!-- 删除指定用户角色关系 -->
    <delete id="deleteUserRole">
        DELETE FROM sys_user_role
        WHERE user_id = #{userId}
          AND role_id = #{roleId}
    </delete>

    <!-- 批量插入用户角色关系 -->
    <insert id="batchInsert">
        INSERT INTO sys_user_role (user_id, role_id)
        VALUES
        <foreach collection="userRoles" item="userRole" separator=",">
            (#{userRole.userId}, #{userRole.roleId})
        </foreach>
    </insert>

    <!-- 批量删除用户角色关系 -->
    <delete id="batchDelete">
        DELETE FROM sys_user_role
        WHERE (user_id, role_id) IN
        <foreach collection="userRoles" item="userRole" open="(" separator="," close=")">
            (#{userRole.userId}, #{userRole.roleId})
        </foreach>
    </delete>

    <!-- 根据用户ID列表删除用户角色关系 -->
    <delete id="deleteByUserIds">
        DELETE FROM sys_user_role
        WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <!-- 根据角色ID列表删除用户角色关系 -->
    <delete id="deleteByRoleIds">
        DELETE FROM sys_user_role
        WHERE role_id IN
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </delete>

    <!-- 查询用户角色关系列表 -->
    <select id="selectUserRoleList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_user_role
        <where>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="roleId != null">
                AND role_id = #{roleId}
            </if>
        </where>
        ORDER BY user_id ASC, role_id ASC
    </select>

    <!-- 统计用户角色关系数量 -->
    <select id="countUserRoles" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM sys_user_role
        <where>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="roleId != null">
                AND role_id = #{roleId}
            </if>
        </where>
    </select>

    <!-- 查询拥有指定角色的用户详情 -->
    <select id="selectUsersByRoleId" parameterType="java.lang.Integer" resultType="com.example.entity.system.SysUser">
        SELECT u.id, u.username, u.nickname, u.email, u.phone, u.avatar,
               u.gender, u.dept_id, u.status, u.remark, u.last_login_time,
               u.last_login_ip, u.create_time, u.update_time
        FROM sys_user u
        INNER JOIN sys_user_role ur ON u.id = ur.user_id
        WHERE ur.role_id = #{roleId}
          AND u.status = 1
        ORDER BY u.create_time DESC
    </select>

    <!-- 查询用户拥有的角色详情 -->
    <select id="selectRolesByUserId" parameterType="java.lang.Integer" resultType="com.example.entity.system.SysRole">
        SELECT r.id, r.role_name, r.role_code, r.description, r.sort,
               r.status, r.remark, r.create_time, r.update_time
        FROM sys_role r
        INNER JOIN sys_user_role ur ON r.id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND r.status = 1
        ORDER BY r.sort ASC
    </select>

</mapper>
