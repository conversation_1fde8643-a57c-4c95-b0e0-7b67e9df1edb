package com.example.controller;

import com.example.common.Result;
import com.example.dto.auth.LoginRequest;
import com.example.service.AuthService;
import com.example.service.system.MenuService;
import com.example.vo.auth.LoginResponse;
import com.example.vo.system.MenuVO;
import com.example.utils.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 认证控制器
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
@Tag(name = "认证管理", description = "用户登录、登出、刷新token等认证相关接口")
public class AuthController {

    private final AuthService authService;
    private final MenuService menuService;
    private final JwtUtil jwtUtil;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户名密码登录")
    public Result<LoginResponse> login(@Validated @RequestBody LoginRequest loginRequest) {
        LoginResponse response = authService.login(loginRequest);
        return Result.success("登录成功", response);
    }

    /**
     * 刷新token
     */
    @PostMapping("/refresh-token")
    @Operation(summary = "刷新token", description = "使用刷新token获取新的访问token")
    public Result<LoginResponse> refreshToken(@RequestBody Map<String, String> request) {
        String refreshToken = request.get("refreshToken");
        LoginResponse response = authService.refreshToken(refreshToken);
        return Result.success("刷新token成功", response);
    }

    /**
     * 获取异步路由
     */
    @GetMapping("/get-async-routes")
    @Operation(summary = "获取异步路由", description = "获取用户的异步路由菜单")
    public Result<List<MenuVO>> getAsyncRoutes(HttpServletRequest request) {
        try {
            log.info("收到获取异步路由请求");

            // 从请求头获取token
            String token = request.getHeader("Authorization");
            if (token != null && token.startsWith("Bearer ")) {
                token = token.substring(7);
            }

            log.info("解析到的token: {}", token != null ? "存在" : "不存在");

            // 从token获取用户ID
            Integer userId = jwtUtil.getUserIdFromToken(token);
            if (userId == null) {
                log.error("无法从token获取用户ID");
                return Result.unauthorized("无效的token");
            }

            log.info("获取到用户ID: {}", userId);

            // 获取用户的异步路由
            List<MenuVO> routes = menuService.getAsyncRoutes(userId);
            log.info("获取到{}个路由", routes != null ? routes.size() : 0);

            return Result.success("获取路由成功", routes);

        } catch (Exception e) {
            log.error("获取异步路由失败", e);
            return Result.error("获取路由失败");
        }
    }
}
