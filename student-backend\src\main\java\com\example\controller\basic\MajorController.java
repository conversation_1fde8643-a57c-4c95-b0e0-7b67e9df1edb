package com.example.controller.basic;

import com.example.common.PageResult;
import com.example.common.Result;
import com.example.dto.basic.MajorQueryDTO;
import com.example.service.basic.MajorService;
import com.example.vo.basic.MajorVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 专业管理控制器
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Slf4j
@RestController
@RequestMapping("/api/basic/major")
@RequiredArgsConstructor
@Validated
@Tag(name = "专业管理", description = "专业管理相关接口")
public class MajorController {

    private final MajorService majorService;

    /**
     * 分页查询专业列表
     */
    @PostMapping("/list")
    @Operation(summary = "获取专业列表", description = "分页查询专业列表")
    public Result<PageResult<MajorVO>> getMajorList(@RequestBody(required = false) MajorQueryDTO query) {
        PageResult<MajorVO> majorList = majorService.getMajorList(query);
        return Result.success("查询成功", majorList);
    }

    /**
     * 获取所有专业列表
     */
    @GetMapping("/all")
    @Operation(summary = "获取所有专业", description = "获取所有专业列表，不分页")
    public Result<List<MajorVO>> getAllMajors() {
        List<MajorVO> majorList = majorService.getAllMajors();
        return Result.success("查询成功", majorList);
    }

    /**
     * 根据学院代码获取专业列表
     */
    @GetMapping("/college/{collegeCode}")
    @Operation(summary = "根据学院获取专业", description = "根据学院代码获取专业列表")
    public Result<List<MajorVO>> getMajorsByCollegeCode(
            @Parameter(description = "学院代码") @PathVariable String collegeCode) {
        List<MajorVO> majorList = majorService.getMajorsByCollegeCode(collegeCode);
        return Result.success("查询成功", majorList);
    }

    /**
     * 根据ID获取专业详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取专业详情", description = "根据ID获取专业详情")
    public Result<MajorVO> getMajorById(@Parameter(description = "专业ID") @PathVariable Integer id) {
        MajorVO major = majorService.getMajorById(id);
        return Result.success("查询成功", major);
    }

    /**
     * 新增专业
     */
    @PostMapping("/save")
    @Operation(summary = "新增专业", description = "新增专业信息")
    public Result<Void> saveMajor(@Validated @RequestBody MajorVO majorVO) {
        majorService.saveMajor(majorVO);
        return Result.success("新增专业成功");
    }

    /**
     * 更新专业
     */
    @PostMapping("/update")
    @Operation(summary = "更新专业", description = "更新专业信息")
    public Result<Void> updateMajor(@Validated @RequestBody MajorVO majorVO) {
        majorService.updateMajor(majorVO);
        return Result.success("更新专业成功");
    }

    /**
     * 删除专业
     */
    @PostMapping("/delete")
    @Operation(summary = "删除专业", description = "根据ID删除专业")
    public Result<Void> deleteMajor(@RequestBody @Parameter(description = "包含专业ID的请求体") java.util.Map<String, Integer> request) {
        Integer id = request.get("id");
        majorService.deleteMajor(id);
        return Result.success("删除专业成功");
    }
}
