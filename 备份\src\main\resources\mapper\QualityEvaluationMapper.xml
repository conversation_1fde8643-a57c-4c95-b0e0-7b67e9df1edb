<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.itmk.web.quality_evaluation.mapper.QualityEvaluationMapper">

    <!-- 分页查询基本素质测评成绩 -->
    <select id="getList" resultType="com.itmk.web.quality_evaluation.entity.QualityEvaluation">
        SELECT 
            qe.*,
            stu.name as studentName,
            stu.dormitory as dormitoryNo
            <if test="parm.prevSemesterId != null and parm.prevSemesterId != ''">
                ,(SELECT total_score FROM quality_evaluation
                WHERE student_id = qe.student_id AND evaluation_period = #{parm.prevSemesterId} LIMIT 1) as prevPeriodScore
            </if>
        FROM 
            quality_evaluation qe
        LEFT JOIN 
            students stu ON qe.student_id = stu.student_id
        <where>
            <if test="parm.studentId != null and parm.studentId != ''">
                AND qe.student_id = #{parm.studentId}
            </if>
            <if test="parm.studentName != null and parm.studentName != ''">
                AND stu.name LIKE CONCAT('%', #{parm.studentName}, '%')
            </if>
            <if test="parm.dormitoryNo != null and parm.dormitoryNo != ''">
                AND stu.dormitory = #{parm.dormitoryNo}
            </if>
            <if test="parm.evaluationPeriod != null and parm.evaluationPeriod != ''">
                AND qe.evaluation_period = #{parm.evaluationPeriod}
            </if>
            <if test="parm.evaluationPeriods != null and parm.evaluationPeriods.size() > 0">
                AND qe.evaluation_period IN
                <foreach collection="parm.evaluationPeriods" item="period" open="(" separator="," close=")">
                    #{period}
                </foreach>
            </if>
        </where>
        ORDER BY qe.create_time DESC
    </select>

    <!-- 更新第二学期的prev_period_score字段为上一学期的总分 -->
    <update id="updatePrevPeriodScore">
        UPDATE quality_evaluation qe1
        INNER JOIN quality_evaluation qe2 ON qe1.student_id = qe2.student_id
        SET qe1.prev_period_score = qe2.total_score,
            qe1.update_time = NOW()
        WHERE qe2.total_score IS NOT NULL
        AND qe2.total_score > 0
        AND (
            <!-- 处理形如 '2023-2024-1' 到 '2023-2024-2' 的学期ID对 -->
            (qe1.evaluation_period LIKE '%-2'
             AND qe2.evaluation_period LIKE '%-1'
             AND SUBSTRING_INDEX(qe1.evaluation_period, '-', 2) = SUBSTRING_INDEX(qe2.evaluation_period, '-', 2))
            <!-- 处理包含"第一学期"/"第二学期"关键字的学期ID对 -->
            OR (qe1.evaluation_period LIKE '%第二学期%'
                AND qe2.evaluation_period LIKE '%第一学期%'
                AND SUBSTRING_INDEX(qe1.evaluation_period, '第', 1) = SUBSTRING_INDEX(qe2.evaluation_period, '第', 1))
            <!-- 处理包含"上学期"/"下学期"关键字的学期ID对 -->
            OR (qe1.evaluation_period LIKE '%下学期%'
                AND qe2.evaluation_period LIKE '%上学期%'
                AND SUBSTRING_INDEX(qe1.evaluation_period, '上', 1) = SUBSTRING_INDEX(qe2.evaluation_period, '上', 1))
            <!-- 处理纯数字学期ID，连续的学期对 -->
            OR (qe1.evaluation_period REGEXP '^[0-9]+$'
                AND qe2.evaluation_period REGEXP '^[0-9]+$'
                AND CAST(qe1.evaluation_period AS SIGNED) = CAST(qe2.evaluation_period AS SIGNED) + 1)
        )
        AND (qe1.prev_period_score IS NULL OR qe1.prev_period_score != qe2.total_score)
    </update>
    
    <!-- 更新第二学期的period_score字段为prev_period_score -->
    <update id="updatePeriodScoreFromPrev">
        UPDATE quality_evaluation
        SET period_score = prev_period_score,
            update_time = NOW()
        WHERE prev_period_score IS NOT NULL
        AND prev_period_score > 0
        AND (period_score IS NULL OR period_score != prev_period_score)
        AND (
            evaluation_period LIKE '%-2'
            OR evaluation_period LIKE '%第二学期%'
            OR evaluation_period LIKE '%下学期%'
            OR (evaluation_period REGEXP '^[0-9]+$' AND CAST(evaluation_period AS SIGNED) % 2 = 0)
            OR (evaluation_period REGEXP '^[0-9]+$' AND CAST(evaluation_period AS SIGNED) > 1)
        )
    </update>
    
    <!-- 更新第二学期记录的基础分和总分 -->
    <update id="updateScores">
        UPDATE quality_evaluation
        SET prev_period_score = #{prevPeriodScore},
            period_score = #{periodScore},
            total_score = #{totalScore},
            update_time = NOW()
        WHERE evaluation_id = #{id}
    </update>

    <!-- 批量重新计算第二学期的总分 -->
    <update id="recalculateSecondSemesterTotalScores">
        UPDATE quality_evaluation
        SET total_score = GREATEST(0, period_score + IFNULL(add_score, 0) - IFNULL(reduce_score, 0)),
            update_time = NOW()
        WHERE (
            evaluation_period LIKE '%-2'
            OR evaluation_period LIKE '%第二学期%'
            OR evaluation_period LIKE '%下学期%'
            OR (evaluation_period REGEXP '^[0-9]+$' AND CAST(evaluation_period AS SIGNED) % 2 = 0)
            OR (evaluation_period REGEXP '^[0-9]+$' AND CAST(evaluation_period AS SIGNED) > 1)
        )
        AND period_score IS NOT NULL
    </update>

</mapper>