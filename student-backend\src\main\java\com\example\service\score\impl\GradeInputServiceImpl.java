package com.example.service.score.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.common.PageResult;
import com.example.common.exception.BusinessException;
import com.example.common.excel.ApachePoiExportService;
import com.example.common.excel.ExcelConfigFactory;
import com.example.common.excel.ExcelConfig;
import com.example.dto.score.GradeInputDTO;
import com.example.dto.score.GradeInputQueryDTO;

import com.example.dto.score.GradeImportRequestDTO;
import com.example.dto.score.GradeImportResultDTO;
import com.example.entity.score.Grade;
import com.example.mapper.score.GradeInputMapper;
import com.example.service.score.GradeInputService;
import com.example.service.student.StudentsService;
import com.example.service.educational.CourseService;
import com.example.service.educational.ClassCourseService;
import com.example.service.basic.ClassesService;
import com.example.service.basic.SemesterService;
import com.example.vo.score.GradeInputVO;
import com.example.vo.student.StudentsVO;
import com.example.vo.educational.CourseVO;
import com.example.vo.educational.ClassCourseVO;
import com.example.vo.basic.ClassesVO;
import com.example.vo.basic.SemesterVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 成绩录入服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class GradeInputServiceImpl implements GradeInputService {

    private final GradeInputMapper gradeInputMapper;
    private final ApachePoiExportService apachePoiExportService;
    private final StudentsService studentsService;
    private final CourseService courseService;
    private final ClassCourseService classCourseService;
    private final ClassesService classesService;
    private final SemesterService semesterService;
    private final Validator validator;

    @Override
    public PageResult<GradeInputVO> getGradeInputPage(GradeInputQueryDTO params) {
        // 设置默认分页参数
        if (params.getCurrent() == null || params.getCurrent() <= 0) {
            params.setCurrent(1);
        }
        if (params.getSize() == null || params.getSize() <= 0) {
            params.setSize(10);
        }

        Page<GradeInputVO> page = new Page<>(params.getCurrent(), params.getSize());
        IPage<GradeInputVO> result = gradeInputMapper.selectGradeInputPage(page, params);

        return PageResult.<GradeInputVO>builder()
                .list(result.getRecords())
                .total(result.getTotal())
                .pageNum(Math.toIntExact(result.getCurrent()))
                .pageSize(Math.toIntExact(result.getSize()))
                .totalPages(result.getPages())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean inputGrade(GradeInputDTO gradeInputDTO) {
        // 参数校验
        validateGradeInput(gradeInputDTO);

        // 检查成绩是否已存在
        if (checkGradeExists(gradeInputDTO.getStudentId(), gradeInputDTO.getCourseCode(), gradeInputDTO.getSemesterId())) {
            throw new BusinessException("该学生在此学期的课程成绩已存在，请使用更新功能");
        }

        // 转换为实体
        Grade grade = new Grade();
        BeanUtils.copyProperties(gradeInputDTO, grade);

        // 自动计算绩点（无论是否传入绩点都重新计算）
        if (gradeInputDTO.getFinalScore() != null) {
            Double gradePoint = calculateGradePoint(gradeInputDTO.getFinalScore().doubleValue());
            grade.setGradePoint(BigDecimal.valueOf(gradePoint));
        }

        // 插入数据
        int result = gradeInputMapper.insert(grade);
        log.info("录入成绩成功: studentId={}, courseCode={}, semesterId={}, finalScore={}",
                gradeInputDTO.getStudentId(), gradeInputDTO.getCourseCode(),
                gradeInputDTO.getSemesterId(), gradeInputDTO.getFinalScore());

        return result > 0;
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateGrade(GradeInputDTO gradeInputDTO) {
        if (gradeInputDTO.getId() == null) {
            throw new BusinessException("成绩ID不能为空");
        }

        // 参数校验
        validateGradeInput(gradeInputDTO);

        // 转换为实体
        Grade grade = new Grade();
        BeanUtils.copyProperties(gradeInputDTO, grade);

        // 自动计算绩点（无论是否传入绩点都重新计算）
        if (gradeInputDTO.getFinalScore() != null) {
            Double gradePoint = calculateGradePoint(gradeInputDTO.getFinalScore().doubleValue());
            grade.setGradePoint(BigDecimal.valueOf(gradePoint));
        }

        // 更新数据
        int result = gradeInputMapper.updateById(grade);
        log.info("更新成绩成功: id={}, studentId={}, courseCode={}, finalScore={}",
                gradeInputDTO.getId(), gradeInputDTO.getStudentId(),
                gradeInputDTO.getCourseCode(), gradeInputDTO.getFinalScore());

        return result > 0;
    }



    @Override
    public List<GradeInputVO> getStudentGradeInputList(String classCode, String courseCode, Integer semesterId) {
        if (!StringUtils.hasText(classCode)) {
            throw new BusinessException("班级代码不能为空");
        }
        if (!StringUtils.hasText(courseCode)) {
            throw new BusinessException("课程代码不能为空");
        }
        if (semesterId == null) {
            throw new BusinessException("学期ID不能为空");
        }

        return gradeInputMapper.selectStudentGradeInputList(classCode, courseCode, semesterId);
    }

    @Override
    public GradeInputVO getGradeById(Integer id) {
        if (id == null) {
            throw new BusinessException("成绩ID不能为空");
        }

        return gradeInputMapper.selectGradeInputById(id);
    }

    @Override
    public boolean checkGradeExists(String studentId, String courseCode, Integer semesterId) {
        if (!StringUtils.hasText(studentId) || !StringUtils.hasText(courseCode) || semesterId == null) {
            return false;
        }

        int count = gradeInputMapper.checkGradeExists(studentId, courseCode, semesterId);
        return count > 0;
    }

    /**
     * 计算绩点（私有方法）
     * 规则：
     * - 成绩小于60分：绩点为0.0
     * - 成绩60分及以上：使用公式 (final_score / 10) - 5
     * - 最高绩点为5.0（100分）
     */
    private Double calculateGradePoint(Double finalScore) {
        if (finalScore == null || finalScore < 0 || finalScore > 100) {
            return 0.0;
        }

        // 成绩小于60分，绩点为0
        if (finalScore < 60.0) {
            return 0.0;
        }

        // 成绩60分及以上，使用公式：(final_score / 10) - 5
        double gradePoint = (finalScore / 10.0) - 5.0;

        // 确保绩点不大于5.0（理论上100分对应5.0绩点）
        if (gradePoint > 5.0) {
            gradePoint = 5.0;
        }

        // 保留2位小数
        return BigDecimal.valueOf(gradePoint).setScale(2, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * 校验成绩录入参数
     */
    private void validateGradeInput(GradeInputDTO gradeInputDTO) {
        if (!StringUtils.hasText(gradeInputDTO.getStudentId())) {
            throw new BusinessException("学号不能为空");
        }
        if (!StringUtils.hasText(gradeInputDTO.getCourseCode())) {
            throw new BusinessException("课程代码不能为空");
        }
        if (gradeInputDTO.getSemesterId() == null) {
            throw new BusinessException("学期ID不能为空");
        }
        // 允许期末成绩为空，但如果不为空则需要在有效范围内
        if (gradeInputDTO.getFinalScore() != null) {
            if (gradeInputDTO.getFinalScore().compareTo(BigDecimal.ZERO) < 0 ||
                gradeInputDTO.getFinalScore().compareTo(BigDecimal.valueOf(100)) > 0) {
                throw new BusinessException("期末成绩必须在0-100之间");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public GradeImportResultDTO importGrades(MultipartFile file, String classCode, String courseCode, Integer semesterId) {
        log.info("开始导入成绩: classCode={}, courseCode={}, semesterId={}, fileName={}",
                classCode, courseCode, semesterId, file.getOriginalFilename());

        GradeImportResultDTO result = new GradeImportResultDTO();

        try {
            // 使用流式Excel服务导入数据
            ApachePoiExportService.ImportResult<Grade> importResult =
                apachePoiExportService.importExcel(file, ExcelConfigFactory.getGradeImportConfig());

            if (!importResult.isSuccess()) {
                result.setSuccess(false);
                result.addErrorMessage(importResult.getMessage());
                return result;
            }

            List<Grade> grades = importResult.getSuccessData();
            result.setTotalRows(grades.size() + importResult.getErrorCount());

            // 设置课程和学期信息，并保存成绩
            int successCount = 0;
            for (Grade grade : grades) {
                try {
                    // 设置课程代码和学期ID
                    grade.setCourseCode(courseCode);
                    grade.setSemesterId(semesterId);

                    // 清除classCode字段，因为它不存储在数据库中
                    grade.setClassCode(null);

                    // 自动计算绩点
                    if (grade.getFinalScore() != null) {
                        Double gradePoint = calculateGradePoint(grade.getFinalScore().doubleValue());
                        grade.setGradePoint(BigDecimal.valueOf(gradePoint));
                    }

                    // 检查成绩是否已存在，如果存在则更新，否则插入
                    if (checkGradeExists(grade.getStudentId(), courseCode, semesterId)) {
                        // 获取现有成绩
                        Grade existingGrade = gradeInputMapper.selectGradeByCondition(
                                grade.getStudentId(), courseCode, semesterId);
                        if (existingGrade != null) {
                            // 更新现有成绩
                            grade.setId(existingGrade.getId());
                            int updateResult = gradeInputMapper.updateById(grade);
                            if (updateResult > 0) {
                                successCount++;
                                log.info("更新成绩成功: studentId={}", grade.getStudentId());
                            } else {
                                result.addErrorDetail(0, grade.getStudentId(), "数据库更新失败");
                            }
                        } else {
                            result.addErrorDetail(0, grade.getStudentId(), "查询现有成绩失败");
                        }
                    } else {
                        // 插入新成绩
                        int insertResult = gradeInputMapper.insert(grade);
                        if (insertResult > 0) {
                            successCount++;
                            log.info("插入成绩成功: studentId={}", grade.getStudentId());
                        } else {
                            result.addErrorDetail(0, grade.getStudentId(), "数据库保存失败");
                        }
                    }

                } catch (Exception e) {
                    log.warn("保存成绩失败: studentId={}, error={}", grade.getStudentId(), e.getMessage());
                    result.addErrorDetail(0, grade.getStudentId(), "保存失败: " + e.getMessage());
                }
            }

            // 添加Excel解析错误
            for (ApachePoiExportService.ImportError error : importResult.getErrors()) {
                result.addErrorDetail(error.getRowNumber(), "", error.getErrorMessage());
            }

            result.setSuccessRows(successCount);
            result.setFailedRows(result.getTotalRows() - successCount);
            result.setSuccess(successCount > 0);

            log.info("成绩导入完成: 总数={}, 成功={}, 失败={}",
                    result.getTotalRows(), result.getSuccessRows(), result.getFailedRows());
            return result;

        } catch (Exception e) {
            log.error("导入成绩失败", e);
            result.setSuccess(false);
            result.addErrorMessage("导入失败: " + e.getMessage());
            return result;
        }
    }

    @Override
    public byte[] generateImportTemplate(String classCode, String courseCode, Integer semesterId) {
        log.info("生成成绩导入模板: classCode={}, courseCode={}, semesterId={}", classCode, courseCode, semesterId);

        try {
            // 参数验证
            if (!StringUtils.hasText(classCode)) {
                throw new BusinessException("班级代码不能为空");
            }
            if (!StringUtils.hasText(courseCode)) {
                throw new BusinessException("课程代码不能为空");
            }
            if (semesterId == null) {
                throw new BusinessException("学期ID不能为空");
            }

            // 查询班级学生列表
            List<StudentsVO> students = studentsService.getStudentsByClassCode(classCode);
            if (students.isEmpty()) {
                log.warn("班级 {} 没有找到学生", classCode);
                // 如果没有学生，生成空模板
                return apachePoiExportService.generateTemplate(ExcelConfigFactory.getGradeImportConfig());
            }

            // 查询班级信息
            ClassesVO classInfo = classesService.getClassesByCode(classCode);
            if (classInfo == null) {
                throw new BusinessException("班级不存在: " + classCode);
            }

            // 查询课程信息
            CourseVO course = courseService.getCourseByCode(courseCode);
            if (course == null) {
                throw new BusinessException("课程不存在: " + courseCode);
            }

            // 查询学期信息
            SemesterVO semester = semesterService.getSemesterById(semesterId);
            if (semester == null) {
                throw new BusinessException("学期不存在: " + semesterId);
            }

            // 为每个学生创建预填充的成绩数据
            List<Grade> prefilledGrades = new ArrayList<>();
            for (StudentsVO student : students) {
                Grade grade = new Grade();
                grade.setStudentId(student.getStudentId());
                grade.setClassCode(classCode); // 仅用于Excel模板显示，不存储到数据库
                grade.setCourseCode(courseCode);
                grade.setSemesterId(semesterId);
                // 期末成绩、是否重修、备注留空，由老师填写
                grade.setIsRetake(false); // 默认值
                prefilledGrades.add(grade);
            }

            // 使用预填充数据生成模板
            return generatePrefilledTemplate(prefilledGrades, students, classInfo, course, semester);

        } catch (Exception e) {
            log.error("生成成绩导入模板失败", e);
            throw new BusinessException("生成模板失败: " + e.getMessage());
        }
    }

    /**
     * 生成预填充的Excel模板
     */
    private byte[] generatePrefilledTemplate(List<Grade> grades, List<StudentsVO> students,
                                           ClassesVO classInfo, CourseVO course, SemesterVO semester) {
        try {
            // 构建表头映射
            Map<String, String> headers = new LinkedHashMap<>();
            headers.put("studentId", "学号");
            headers.put("studentName", "学生姓名");
            headers.put("classCode", "班级代码");
            headers.put("className", "班级名称");
            headers.put("courseCode", "课程代码");
            headers.put("courseName", "课程名称");
            headers.put("semesterName", "学期");
            headers.put("finalScore", "期末成绩");
            headers.put("isRetake", "是否重修");
            headers.put("remarks", "备注");

            // 构建字段顺序
            List<String> fieldOrder = new ArrayList<>(headers.keySet());

            // 构建预填充数据
            List<Map<String, Object>> templateData = new ArrayList<>();

            if (!grades.isEmpty()) {
                // 如果有现有成绩数据，使用现有数据
                for (Grade grade : grades) {
                    Map<String, Object> row = new LinkedHashMap<>();
                    row.put("studentId", grade.getStudentId());
                    row.put("studentName", ""); // 需要从学生信息中获取
                    row.put("classCode", grade.getClassCode());
                    row.put("className", classInfo.getClassName());
                    row.put("courseCode", grade.getCourseCode());
                    row.put("courseName", course.getCourseName());
                    row.put("semesterName", semester.getSemesterName());
                    row.put("finalScore", grade.getFinalScore());
                    row.put("isRetake", grade.getIsRetake() != null && grade.getIsRetake() ? "是" : "否");
                    row.put("remarks", grade.getRemarks());
                    templateData.add(row);
                }
            } else {
                // 如果没有现有数据，为每个学生创建空记录
                for (StudentsVO student : students) {
                    Map<String, Object> row = new LinkedHashMap<>();
                    row.put("studentId", student.getStudentId());
                    row.put("studentName", student.getName());
                    row.put("classCode", classInfo.getClassCode());
                    row.put("className", classInfo.getClassName());
                    row.put("courseCode", course.getCourseCode());
                    row.put("courseName", course.getCourseName());
                    row.put("semesterName", semester.getSemesterName());
                    row.put("finalScore", ""); // 期末成绩留空
                    row.put("isRetake", "否");
                    row.put("remarks", ""); // 备注留空
                    templateData.add(row);
                }
            }

            // 使用基于Map的导出方法生成预填充模板
            String sheetName = "成绩导入";
            return apachePoiExportService.exportGradesToExcel(templateData, headers, sheetName);

        } catch (Exception e) {
            log.error("生成预填充模板失败", e);
            throw new BusinessException("生成预填充模板失败: " + e.getMessage());
        }
    }

    @Override
    public String generateTemplateFileName(String classCode, String courseCode, Integer semesterId) {
        // 直接返回固定的文件名
        return "课程成绩导入模板.xlsx";
    }

    @Override
    public Map<String, String> getImportTemplateHeaders() {
        Map<String, String> headers = new LinkedHashMap<>();
        headers.put("studentId", "学号");
        headers.put("courseCode", "课程代码");
        headers.put("semesterId", "学期ID");
        headers.put("finalScore", "期末成绩");
        headers.put("isRetake", "是否重修");
        headers.put("remarks", "备注");
        return headers;
    }

    @Override
    public byte[] generateMultiCourseTemplate(String classCode, String[] courseCodes, Integer semesterId) {
        log.info("生成多课程成绩导入模板: classCode={}, courseCodes={}, semesterId={}",
                classCode, Arrays.toString(courseCodes), semesterId);

        try {
            // 参数验证
            if (!StringUtils.hasText(classCode)) {
                throw new BusinessException("班级代码不能为空");
            }
            if (courseCodes == null || courseCodes.length == 0) {
                throw new BusinessException("课程代码不能为空");
            }

            // 查询班级学生列表
            List<StudentsVO> students = studentsService.getStudentsByClassCode(classCode);
            if (students.isEmpty()) {
                log.warn("班级 {} 没有找到学生", classCode);
            }

            // 查询班级信息
            ClassesVO classInfo = classesService.getClassesByCode(classCode);
            if (classInfo == null) {
                throw new BusinessException("班级不存在: " + classCode);
            }

            // 查询课程信息
            List<CourseVO> courses = new ArrayList<>();
            for (String courseCode : courseCodes) {
                CourseVO course = courseService.getCourseByCode(courseCode);
                if (course != null) {
                    courses.add(course);
                } else {
                    log.warn("课程不存在: {}", courseCode);
                }
            }

            if (courses.isEmpty()) {
                throw new BusinessException("没有找到有效的课程");
            }

            // 生成多课程模板
            return generateMultiCourseExcelTemplate(students, classInfo, courses, semesterId);

        } catch (Exception e) {
            log.error("生成多课程成绩导入模板失败", e);
            throw new BusinessException("生成多课程模板失败: " + e.getMessage());
        }
    }

    @Override
    public String generateMultiCourseTemplateFileName(String classCode, String[] courseCodes, Integer semesterId) {
        // 直接返回固定的文件名
        return "课程成绩导入模板.xlsx";
    }

    @Override
    public GradeImportResultDTO importMultiCourseGrades(MultipartFile file, String classCode, String[] courseCodes, Integer semesterId) {
        log.info("开始导入多课程成绩: classCode={}, courseCodes={}, semesterId={}, fileName={}",
                classCode, Arrays.toString(courseCodes), semesterId, file.getOriginalFilename());

        GradeImportResultDTO result = new GradeImportResultDTO();

        try {
            // 参数验证
            if (!StringUtils.hasText(classCode)) {
                throw new BusinessException("班级代码不能为空");
            }
            if (courseCodes == null || courseCodes.length == 0) {
                throw new BusinessException("课程代码不能为空");
            }

            // 解析Excel文件
            List<Map<String, Object>> excelData = parseMultiCourseExcel(file);
            if (excelData.isEmpty()) {
                result.setSuccess(false);
                result.addErrorMessage("Excel文件中没有有效数据");
                return result;
            }

            // 处理导入数据
            return processMultiCourseImportData(excelData, classCode, courseCodes, semesterId, result);

        } catch (Exception e) {
            log.error("导入多课程成绩失败", e);
            result.setSuccess(false);
            result.addErrorMessage("导入失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 生成多课程Excel模板
     */
    private byte[] generateMultiCourseExcelTemplate(List<StudentsVO> students, ClassesVO classInfo,
                                                   List<CourseVO> courses, Integer semesterId) {
        try {
            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("多课程成绩导入");

            // 创建样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);

            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);

            // 创建表头
            Row headerRow = sheet.createRow(0);
            int cellIndex = 0;

            // 基础列
            headerRow.createCell(cellIndex++).setCellValue("学号*");
            headerRow.createCell(cellIndex++).setCellValue("学生姓名*");
            headerRow.createCell(cellIndex++).setCellValue("学期*");

            // 课程列
            for (CourseVO course : courses) {
                Cell cell = headerRow.createCell(cellIndex++);
                cell.setCellValue(course.getCourseName() + "(" + course.getCourseCode() + ")");
                cell.setCellStyle(headerStyle);
            }

            // 设置表头样式
            for (int i = 0; i < 3; i++) {
                headerRow.getCell(i).setCellStyle(headerStyle);
            }

            // 添加学生数据行
            int rowIndex = 1;
            for (StudentsVO student : students) {
                Row dataRow = sheet.createRow(rowIndex++);
                cellIndex = 0;

                dataRow.createCell(cellIndex++).setCellValue(student.getStudentId());
                dataRow.createCell(cellIndex++).setCellValue(student.getName());

                // 学期列留空，让用户填写
                dataRow.createCell(cellIndex++).setCellValue("");

                // 课程成绩列留空
                for (int i = 0; i < courses.size(); i++) {
                    dataRow.createCell(cellIndex++).setCellValue("");
                }
            }

            // 添加说明行
            Row noteRow = sheet.createRow(rowIndex + 1);
            Cell noteCell = noteRow.createCell(0);
            noteCell.setCellValue("说明：\n" +
                    "1. 标记*的字段为必填项\n" +
                    "2. 学号、姓名必须与系统中的学生信息匹配\n" +
                    "3. 学期格式如：2023-2024-1，表示2023-2024学年第1学期\n" +
                    "4. 可以在学期列中填写多个学期（用逗号分隔）\n" +
                    "5. 成绩为0-100的数值，可以包含小数\n" +
                    "6. 如果某门课程没有成绩，可以留空");

            // 合并说明单元格
            sheet.addMergedRegion(new CellRangeAddress(rowIndex + 1, rowIndex + 6, 0, Math.max(3, courses.size() + 2)));

            // 自动调整列宽
            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                sheet.autoSizeColumn(i);
            }

            // 转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            workbook.close();

            return outputStream.toByteArray();

        } catch (Exception e) {
            log.error("生成多课程Excel模板失败", e);
            throw new BusinessException("生成Excel模板失败: " + e.getMessage());
        }
    }

    /**
     * 解析多课程Excel文件
     */
    private List<Map<String, Object>> parseMultiCourseExcel(MultipartFile file) {
        List<Map<String, Object>> result = new ArrayList<>();

        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = WorkbookFactory.create(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            if (sheet.getLastRowNum() < 1) {
                return result;
            }

            // 读取表头
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                return result;
            }

            // 解析表头，获取课程代码映射
            Map<Integer, String> columnToCourseCode = new HashMap<>();
            for (int colIndex = 3; colIndex < headerRow.getLastCellNum(); colIndex++) {
                Cell headerCell = headerRow.getCell(colIndex);
                if (headerCell != null) {
                    String headerValue = getCellValueAsString(headerCell);
                    // 提取课程代码 (从"课程名称(课程代码)"格式中提取)
                    int startIdx = headerValue.lastIndexOf('(');
                    int endIdx = headerValue.lastIndexOf(')');
                    if (startIdx > 0 && endIdx > startIdx) {
                        String courseCode = headerValue.substring(startIdx + 1, endIdx);
                        columnToCourseCode.put(colIndex, courseCode);
                    }
                }
            }

            // 读取数据行
            for (int rowNum = 1; rowNum <= sheet.getLastRowNum(); rowNum++) {
                Row row = sheet.getRow(rowNum);
                if (row == null) continue;

                Map<String, Object> rowData = new HashMap<>();

                // 读取基础信息
                rowData.put("studentId", getCellValueAsString(row.getCell(0)));
                rowData.put("studentName", getCellValueAsString(row.getCell(1)));
                rowData.put("semesterInfo", getCellValueAsString(row.getCell(2)));
                rowData.put("rowNumber", rowNum + 1);

                // 读取课程成绩
                Map<String, String> courseGrades = new HashMap<>();
                for (Map.Entry<Integer, String> entry : columnToCourseCode.entrySet()) {
                    int colIndex = entry.getKey();
                    String courseCode = entry.getValue();
                    Cell gradeCell = row.getCell(colIndex);
                    String gradeValue = getCellValueAsString(gradeCell);
                    if (StringUtils.hasText(gradeValue)) {
                        courseGrades.put(courseCode, gradeValue);
                    }
                }
                rowData.put("courseGrades", courseGrades);

                result.add(rowData);
            }

        } catch (Exception e) {
            log.error("解析多课程Excel文件失败", e);
            throw new BusinessException("解析Excel文件失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 处理多课程导入数据
     */
    private GradeImportResultDTO processMultiCourseImportData(List<Map<String, Object>> excelData,
                                                             String classCode, String[] courseCodes,
                                                             Integer semesterId, GradeImportResultDTO result) {
        List<Grade> gradesToSave = new ArrayList<>();
        int totalGradeEntries = 0; // 总的成绩条目数

        // 获取课程代码到学期ID的映射
        Map<String, Integer> courseToSemesterMap = getCourseToSemesterMap(classCode, courseCodes);

        for (Map<String, Object> rowData : excelData) {
            try {
                String studentId = (String) rowData.get("studentId");
                String studentName = (String) rowData.get("studentName");
                int rowNumber = (Integer) rowData.get("rowNumber");
                @SuppressWarnings("unchecked")
                Map<String, String> courseGrades = (Map<String, String>) rowData.get("courseGrades");

                // 验证学生信息
                if (!StringUtils.hasText(studentId)) {
                    result.addErrorMessage("第 " + rowNumber + " 行：学号为必填项");
                    continue;
                }

                StudentsVO student = studentsService.getStudentByStudentId(studentId);
                if (student == null) {
                    result.addErrorMessage("第 " + rowNumber + " 行：学号 " + studentId + " 不存在");
                    continue;
                }

                if (!student.getName().equals(studentName)) {
                    result.addErrorMessage("第 " + rowNumber + " 行：学生姓名不匹配，系统中为 " + student.getName());
                    continue;
                }

                // 处理每门课程的成绩
                for (Map.Entry<String, String> gradeEntry : courseGrades.entrySet()) {
                    String courseCode = gradeEntry.getKey();
                    String gradeStr = gradeEntry.getValue();
                    totalGradeEntries++; // 统计总的成绩条目数

                    // 根据课程代码获取对应的学期ID
                    Integer courseSemesterId = courseToSemesterMap.get(courseCode);
                    if (courseSemesterId == null) {
                        result.addErrorMessage("第 " + rowNumber + " 行：课程 " + courseCode + " 未找到对应的学期信息");
                        continue;
                    }

                    try {
                        Double gradeValue = Double.parseDouble(gradeStr);
                        if (gradeValue < 0 || gradeValue > 100) {
                            result.addErrorMessage("第 " + rowNumber + " 行：课程 " + courseCode + " 的成绩应在 0-100 之间");
                            continue;
                        }

                        // 创建成绩记录，使用课程对应的学期ID
                        Grade grade = createGradeRecord(studentId, courseCode, courseSemesterId, gradeValue, classCode);
                        gradesToSave.add(grade);

                    } catch (NumberFormatException e) {
                        result.addErrorMessage("第 " + rowNumber + " 行：课程 " + courseCode + " 的成绩格式不正确: " + gradeStr);
                    }
                }

            } catch (Exception e) {
                result.addErrorMessage("第 " + rowData.get("rowNumber") + " 行：处理失败 - " + e.getMessage());
            }
        }

        // 更新总数为实际的成绩条目数
        result.setTotalRows(totalGradeEntries);

        // 批量保存成绩（支持插入或更新）
        int successCount = 0;
        if (!gradesToSave.isEmpty()) {
            try {
                for (Grade grade : gradesToSave) {
                    // 检查成绩是否已存在
                    int existCount = gradeInputMapper.checkGradeExists(
                        grade.getStudentId(),
                        grade.getCourseCode(),
                        grade.getSemesterId()
                    );

                    if (existCount > 0) {
                        // 如果已存在，则更新
                        Grade existingGrade = gradeInputMapper.selectGradeByCondition(
                            grade.getStudentId(),
                            grade.getCourseCode(),
                            grade.getSemesterId()
                        );
                        if (existingGrade != null) {
                            existingGrade.setFinalScore(grade.getFinalScore());
                            existingGrade.setGradePoint(grade.getGradePoint());
                            existingGrade.setIsRetake(grade.getIsRetake());
                            existingGrade.setUpdatedBy(grade.getUpdatedBy());
                            existingGrade.setUpdatedAt(grade.getUpdatedAt());
                            gradeInputMapper.updateById(existingGrade);
                            successCount++;
                        }
                    } else {
                        // 如果不存在，则插入
                        gradeInputMapper.insert(grade);
                        successCount++;
                    }
                }
                log.info("批量保存成绩成功，数量: {}", successCount);
            } catch (Exception e) {
                log.error("批量保存成绩失败", e);
                result.addErrorMessage("保存成绩到数据库失败: " + e.getMessage());
                successCount = 0;
            }
        }

        result.setSuccessRows(successCount);
        result.setFailedRows(result.getTotalRows() - successCount);
        result.setSuccess(successCount > 0);

        log.info("多课程成绩导入完成: 总数={}, 成功={}, 失败={}",
                result.getTotalRows(), result.getSuccessRows(), result.getFailedRows());

        return result;
    }

    /**
     * 获取课程代码到学期ID的映射
     */
    private Map<String, Integer> getCourseToSemesterMap(String classCode, String[] courseCodes) {
        Map<String, Integer> map = new HashMap<>();
        try {
            // 查询班级的课程分配信息，获取每个课程对应的学期ID
            for (String courseCode : courseCodes) {
                // 通过班级代码和课程代码查询课程分配信息
                List<ClassCourseVO> classCourses = classCourseService.getClassCoursesByClassAndCourse(classCode, courseCode);
                if (!classCourses.isEmpty()) {
                    // 取第一个匹配的课程分配记录的学期ID
                    ClassCourseVO classCourse = classCourses.get(0);
                    map.put(courseCode, classCourse.getSemesterId());
                } else {
                    log.warn("未找到班级 {} 的课程 {} 的分配信息", classCode, courseCode);
                }
            }
        } catch (Exception e) {
            log.warn("获取课程学期映射失败: {}", e.getMessage());
        }
        return map;
    }

    /**
     * 获取学期名称到ID的映射
     */
    private Map<String, Integer> getSemesterNameToIdMap() {
        Map<String, Integer> map = new HashMap<>();
        try {
            List<SemesterVO> semesters = semesterService.getAllSemesters();
            for (SemesterVO semester : semesters) {
                if (semester.getSemesterName() != null && semester.getId() != null) {
                    map.put(semester.getSemesterName(), semester.getId());
                }
            }
        } catch (Exception e) {
            log.warn("获取学期映射失败", e);
        }
        return map;
    }

    /**
     * 确定有效的学期ID
     */
    private Integer determineEffectiveSemesterId(String semesterInfo, Integer defaultSemesterId,
                                               Map<String, Integer> semesterNameToIdMap) {
        // 如果Excel中有学期信息，优先使用
        if (StringUtils.hasText(semesterInfo)) {
            // 处理可能包含多个学期的情况，取第一个
            String[] semesterNames = semesterInfo.split(",");
            if (semesterNames.length > 0) {
                String firstSemesterName = semesterNames[0].trim();
                Integer semesterId = semesterNameToIdMap.get(firstSemesterName);
                if (semesterId != null) {
                    return semesterId;
                }
            }
        }

        // 否则使用默认学期ID
        return defaultSemesterId;
    }

    /**
     * 创建成绩记录
     */
    private Grade createGradeRecord(String studentId, String courseCode, Integer semesterId,
                                  Double gradeValue, String classCode) {
        Grade grade = new Grade();
        grade.setStudentId(studentId);
        grade.setCourseCode(courseCode);
        grade.setSemesterId(semesterId);
        grade.setFinalScore(BigDecimal.valueOf(gradeValue));

        // 自动计算绩点
        Double gradePoint = calculateGradePoint(gradeValue);
        grade.setGradePoint(BigDecimal.valueOf(gradePoint));

        grade.setIsRetake(false);
        grade.setCreatedAt(LocalDateTime.now());
        grade.setUpdatedAt(LocalDateTime.now());
        return grade;
    }

    /**
     * 获取单元格值作为字符串
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 处理数字，避免科学计数法
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return String.valueOf(cell.getNumericCellValue());
                } catch (Exception e) {
                    return cell.getStringCellValue().trim();
                }
            default:
                return "";
        }
    }

    /**
     * 转换Excel数据为导入DTO
     */
    private List<GradeImportRequestDTO> convertToImportDTOs(List<Map<String, Object>> dataList,
                                                           String classCode, String courseCode, Integer semesterId) {
        List<GradeImportRequestDTO> importDTOs = new ArrayList<>();

        for (int i = 0; i < dataList.size(); i++) {
            Map<String, Object> rowData = dataList.get(i);
            GradeImportRequestDTO dto = new GradeImportRequestDTO();
            dto.setRowNumber(i + 2); // Excel行号从2开始（第1行是表头）

            try {
                // 学号
                Object studentIdObj = rowData.get("studentId");
                if (studentIdObj != null) {
                    dto.setStudentId(studentIdObj.toString().trim());
                }

                // 课程代码（优先使用Excel中的值，如果为空则使用参数值）
                Object courseCodeObj = rowData.get("courseCode");
                if (courseCodeObj != null && !courseCodeObj.toString().trim().isEmpty()) {
                    dto.setCourseCode(courseCodeObj.toString().trim());
                } else {
                    dto.setCourseCode(courseCode);
                }

                // 学期ID（优先使用Excel中的值，如果为空则使用参数值）
                Object semesterIdObj = rowData.get("semesterId");
                if (semesterIdObj != null) {
                    if (semesterIdObj instanceof Number) {
                        dto.setSemesterId(((Number) semesterIdObj).intValue());
                    } else {
                        dto.setSemesterId(Integer.parseInt(semesterIdObj.toString().trim()));
                    }
                } else {
                    dto.setSemesterId(semesterId);
                }

                // 期末成绩
                Object finalScoreObj = rowData.get("finalScore");
                if (finalScoreObj != null) {
                    if (finalScoreObj instanceof Number) {
                        dto.setFinalScore(BigDecimal.valueOf(((Number) finalScoreObj).doubleValue()));
                    } else {
                        dto.setFinalScore(new BigDecimal(finalScoreObj.toString().trim()));
                    }
                }

                // 是否重修
                Object isRetakeObj = rowData.get("isRetake");
                if (isRetakeObj != null) {
                    if (isRetakeObj instanceof Boolean) {
                        dto.setIsRetake((Boolean) isRetakeObj);
                    } else {
                        String retakeStr = isRetakeObj.toString().trim().toLowerCase();
                        dto.setIsRetake("true".equals(retakeStr) || "是".equals(retakeStr) || "1".equals(retakeStr));
                    }
                } else {
                    dto.setIsRetake(false);
                }

                // 备注
                Object remarksObj = rowData.get("remarks");
                if (remarksObj != null) {
                    dto.setRemarks(remarksObj.toString().trim());
                }

                importDTOs.add(dto);

            } catch (Exception e) {
                log.warn("转换第{}行数据失败: {}", i + 2, e.getMessage());
                // 仍然添加到列表中，让验证阶段处理错误
                importDTOs.add(dto);
            }
        }

        return importDTOs;
    }

    /**
     * 验证导入数据
     */
    private List<GradeImportRequestDTO> validateImportData(List<GradeImportRequestDTO> importDTOs, GradeImportResultDTO result) {
        List<GradeImportRequestDTO> validDTOs = new ArrayList<>();

        for (GradeImportRequestDTO dto : importDTOs) {
            List<String> errors = new ArrayList<>();

            // 使用Bean Validation验证
            Set<ConstraintViolation<GradeImportRequestDTO>> violations = validator.validate(dto);
            for (ConstraintViolation<GradeImportRequestDTO> violation : violations) {
                errors.add(violation.getMessage());
            }

            // 检查成绩是否已存在
            if (StringUtils.hasText(dto.getStudentId()) && StringUtils.hasText(dto.getCourseCode()) && dto.getSemesterId() != null) {
                if (checkGradeExists(dto.getStudentId(), dto.getCourseCode(), dto.getSemesterId())) {
                    errors.add("该学生在此学期的课程成绩已存在");
                }
            }

            if (errors.isEmpty()) {
                validDTOs.add(dto);
            } else {
                String errorMessage = String.join("; ", errors);
                result.addErrorDetail(dto.getRowNumber(), dto.getStudentId(), errorMessage);
            }
        }

        return validDTOs;
    }

    /**
     * 批量导入有效成绩
     */
    private int batchImportValidGrades(List<GradeImportRequestDTO> validDTOs, GradeImportResultDTO result) {
        int successCount = 0;

        for (GradeImportRequestDTO dto : validDTOs) {
            try {
                // 转换为Grade实体
                Grade grade = new Grade();
                grade.setStudentId(dto.getStudentId());
                grade.setCourseCode(dto.getCourseCode());
                grade.setSemesterId(dto.getSemesterId());
                grade.setFinalScore(dto.getFinalScore());
                grade.setIsRetake(dto.getIsRetake());
                grade.setRemarks(dto.getRemarks());

                // 自动计算绩点
                if (dto.getFinalScore() != null) {
                    Double gradePoint = calculateGradePoint(dto.getFinalScore().doubleValue());
                    grade.setGradePoint(BigDecimal.valueOf(gradePoint));
                }

                // 插入数据
                int insertResult = gradeInputMapper.insert(grade);
                if (insertResult > 0) {
                    successCount++;
                } else {
                    result.addErrorDetail(dto.getRowNumber(), dto.getStudentId(), "数据库插入失败");
                }

            } catch (Exception e) {
                log.error("导入第{}行成绩失败: {}", dto.getRowNumber(), e.getMessage());
                result.addErrorDetail(dto.getRowNumber(), dto.getStudentId(), "导入失败: " + e.getMessage());
            }
        }

        return successCount;
    }
}
