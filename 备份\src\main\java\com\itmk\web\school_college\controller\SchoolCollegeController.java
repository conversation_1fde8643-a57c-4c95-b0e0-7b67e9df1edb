package com.itmk.web.school_college.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.itmk.utils.ResultUtils;
import com.itmk.utils.ResultVo;
import com.itmk.web.school_college.entity.ListParm;
import com.itmk.web.school_college.entity.SchoolCollege;
import com.itmk.web.school_college.service.SchoolCollegeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/college")
public class SchoolCollegeController {
    @Autowired
    private SchoolCollegeService schoolCollegeService;

    //新增
    @PostMapping
    public ResultVo add(@RequestBody SchoolCollege schoolCollege){
        try {
            // 设置创建时间
            schoolCollege.setCreateTime(new java.util.Date());

            boolean save = schoolCollegeService.save(schoolCollege);
            if(save){
                return ResultUtils.success("新增学院成功!");
            }
            return ResultUtils.error("新增学院失败!");
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtils.error("新增学院失败: " + e.getMessage());
        }
    }

    //编辑
    @PutMapping
    public ResultVo edit(@RequestBody SchoolCollege schoolCollege){
        try {
            boolean save = schoolCollegeService.updateById(schoolCollege);
            if(save){
                return ResultUtils.success("编辑学院成功!");
            }
            return ResultUtils.error("编辑学院失败!");
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtils.error("编辑学院失败: " + e.getMessage());
        }
    }

    //删除
    @DeleteMapping("/{collegeId}")
    public ResultVo delete(@PathVariable("collegeId") Long collegeId){
        try {
            boolean save = schoolCollegeService.removeById(collegeId);
            if(save){
                return ResultUtils.success("删除学院成功!");
            }
            return ResultUtils.error("删除学院失败!");
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtils.error("删除学院失败: " + e.getMessage());
        }
    }

    //列表
    @GetMapping("/list")
    public ResultVo getList(ListParm listParm){
        //直接调用service层的getList方法
        IPage<SchoolCollege> list = schoolCollegeService.getList(listParm);
        return ResultUtils.success("查询成功", list);
    }
}