<template>
  <el-form
    ref="ruleFormRef"
    :model="formData"
    :rules="formRules"
    label-width="100px"
  >
    <el-row :gutter="30">
      <!-- 学院选择 -->
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="所属学院" prop="collegeCode">
          <el-select
            v-model="formData.collegeCode"
            placeholder="请选择所属学院"
            clearable
            filterable
            class="w-full"
            @change="handleCollegeChange"
          >
            <el-option
              v-for="college in collegeOptions"
              :key="college.collegeCode"
              :label="college.collegeName"
              :value="college.collegeCode"
            />
          </el-select>
        </el-form-item>
      </re-col>

      <!-- 专业选择 -->
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="所属专业" prop="majorCode">
          <el-select
            v-model="formData.majorCode"
            placeholder="请先选择学院"
            :disabled="!formData.collegeCode"
            clearable
            filterable
            class="w-full"
            @change="handleMajorChange"
          >
            <el-option
              v-for="major in filteredMajorOptions"
              :key="major.majorCode"
              :label="major.majorName"
              :value="major.majorCode"
            />
          </el-select>
        </el-form-item>
      </re-col>

      <!-- 班级选择 -->
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="所属班级" prop="classCode">
          <el-select
            v-model="formData.classCode"
            placeholder="请先选择专业"
            :disabled="!formData.majorCode"
            clearable
            filterable
            class="w-full"
          >
            <el-option
              v-for="classItem in filteredClassOptions"
              :key="classItem.classCode"
              :label="classItem.className"
              :value="classItem.classCode"
            />
          </el-select>
        </el-form-item>
      </re-col>

      <!-- 学号 -->
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="学号" prop="studentId">
          <el-input
            v-model="formData.studentId"
            clearable
            placeholder="请输入学号"
          />
        </el-form-item>
      </re-col>

      <!-- 姓名 -->
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="姓名" prop="name">
          <el-input
            v-model="formData.name"
            clearable
            placeholder="请输入姓名"
          />
        </el-form-item>
      </re-col>

      <!-- 性别 -->
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="性别" prop="gender">
          <el-select
            v-model="formData.gender"
            placeholder="请选择性别"
            clearable
            class="w-full"
          >
            <el-option label="男" value="男" />
            <el-option label="女" value="女" />
          </el-select>
        </el-form-item>
      </re-col>

      <!-- 出生日期 -->
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="出生日期" prop="birthDate">
          <el-date-picker
            v-model="formData.birthDate"
            type="date"
            placeholder="请选择出生日期"
            class="w-full"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </re-col>

      <!-- 身份证号 -->
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="身份证号" prop="idCard">
          <el-input
            v-model="formData.idCard"
            clearable
            placeholder="请输入身份证号"
          />
        </el-form-item>
      </re-col>

      <!-- 电话号码 -->
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="电话号码" prop="phone">
          <el-input
            v-model="formData.phone"
            clearable
            placeholder="请输入电话号码"
          />
        </el-form-item>
      </re-col>

      <!-- 邮箱 -->
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="formData.email"
            clearable
            placeholder="请输入邮箱"
          />
        </el-form-item>
      </re-col>

      <!-- 入学日期 -->
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="入学日期" prop="enrollmentDate">
          <el-date-picker
            v-model="formData.enrollmentDate"
            type="date"
            placeholder="请选择入学日期"
            class="w-full"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </re-col>

      <!-- 学籍状态 -->
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="学籍状态" prop="status">
          <el-select
            v-model="formData.status"
            placeholder="请选择学籍状态"
            clearable
            class="w-full"
          >
            <el-option label="在校" value="在校" />
            <el-option label="毕业" value="毕业" />
            <el-option label="退学" value="退学" />
            <el-option label="休学" value="休学" />
            <el-option label="转学" value="转学" />
          </el-select>
        </el-form-item>
      </re-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import ReCol from "@/components/ReCol";
import { formRules } from "../utils/rule";
import { FormProps } from "../utils/types";

const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({
    id: undefined,
    studentId: "",
    name: "",
    gender: "",
    birthDate: "",
    idCard: "",
    phone: "",
    email: "",
    collegeCode: "",
    majorCode: "",
    classCode: "",
    enrollmentDate: new Date().toISOString().split('T')[0],
    status: "在校",
    collegeOptions: [],
    majorOptions: [],
    classOptions: []
  })
});

const ruleFormRef = ref();
const formData = ref({ ...props.formInline });

// 获取选项数据
const collegeOptions = computed(() => formData.value.collegeOptions || []);
const majorOptions = computed(() => formData.value.majorOptions || []);
const classOptions = computed(() => formData.value.classOptions || []);

// 根据选择的学院筛选专业
const filteredMajorOptions = computed(() => {
  if (!formData.value.collegeCode) return majorOptions.value;
  return majorOptions.value.filter(
    (major: any) => major.collegeCode === formData.value.collegeCode
  );
});

// 根据选择的专业筛选班级
const filteredClassOptions = computed(() => {
  if (!formData.value.majorCode) return classOptions.value;
  return classOptions.value.filter(
    (classItem: any) => classItem.majorCode === formData.value.majorCode
  );
});

// 学院变化处理
const handleCollegeChange = () => {
  if (formData.value.majorCode) {
    const currentMajor = majorOptions.value.find(
      (major: any) => major.majorCode === formData.value.majorCode
    );
    if (!currentMajor || currentMajor.collegeCode !== formData.value.collegeCode) {
      formData.value.majorCode = "";
      formData.value.classCode = "";
    }
  }
};

// 专业变化处理
const handleMajorChange = () => {
  if (formData.value.classCode) {
    const currentClass = classOptions.value.find(
      (classItem: any) => classItem.classCode === formData.value.classCode
    );
    if (!currentClass || currentClass.majorCode !== formData.value.majorCode) {
      formData.value.classCode = "";
    }
  }
};

// 监听props变化
watch(() => props.formInline, (newVal) => {
  if (newVal) {
    formData.value = { ...newVal };
  }
}, { immediate: true, deep: true });

function getRef() {
  return ruleFormRef.value;
}

function getFormData() {
  return formData.value;
}

defineExpose({ getRef, getFormData });
</script>
