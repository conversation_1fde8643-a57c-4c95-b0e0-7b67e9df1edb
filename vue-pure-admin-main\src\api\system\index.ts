import { http } from "@/utils/http";

export interface UserVO {
  id?: number;
  username: string;
  nickname?: string;
  email?: string;
  phone?: string;
  avatar?: string;
  gender?: string;
  status?: string;
  deptId?: number;
  deptName?: string;
  roles?: string[];
  createTime?: string;
  updateTime?: string;
}

export interface RoleVO {
  id?: number;
  roleCode: string;
  roleName: string;
  description?: string;
  status?: string;
  permissions?: string[];
  createTime?: string;
  updateTime?: string;
}

export interface MenuVO {
  id?: number;
  parentId?: number;
  menuName: string;
  menuType: string;
  path?: string;
  component?: string;
  permission?: string;
  icon?: string;
  sort?: number;
  status?: string;
  children?: MenuVO[];
  createTime?: string;
  updateTime?: string;
}

export interface DeptVO {
  id?: number;
  parentId?: number;
  deptName: string;
  deptCode?: string;
  leader?: string;
  phone?: string;
  email?: string;
  sort?: number;
  status?: string;
  children?: DeptVO[];
  createTime?: string;
  updateTime?: string;
}

export interface LoginLogVO {
  id?: number;
  username: string;
  loginTime: string;
  loginIp: string;
  loginLocation?: string;
  browser?: string;
  os?: string;
  status: string;
  message?: string;
}

export interface OperationLogVO {
  id?: number;
  username: string;
  operation: string;
  method: string;
  params?: string;
  time: number;
  ip: string;
  location?: string;
  createTime: string;
}

export interface SystemLogVO {
  id?: number;
  level: string;
  logger: string;
  message: string;
  exception?: string;
  createTime: string;
}

export interface OnlineUserVO {
  sessionId: string;
  username: string;
  nickname?: string;
  loginTime: string;
  loginIp: string;
  browser?: string;
  os?: string;
}

// 用户管理
export const getUserList = (data?: any) => {
  return http.request("post", "/api/system/user/list", { data });
};
export const addUser = (data: UserVO) => {
  return http.request("post", "/api/system/user/save", { data });
};

export const updateUser = (data: UserVO) => {
  return http.request("post", "/api/system/user/update", { data });
};

export const deleteUser = (id: number) => {
  return http.request("post", `/api/system/user/delete/${id}`);
};

export const resetUserPassword = (data: { userId: number; newPassword: string }) => {
  return http.request("post", "/api/system/user/reset-password", { data });
};

export const toggleUserStatus = (data: { userId: number; status: number }) => {
  return http.request("post", "/api/system/user/toggle-status", { data });
};

export const assignUserRoles = (data: { userId: number; roleIds: number[] }) => {
  return http.request("post", "/api/system/user/assign-roles", { data });
};

export const getRoleIds = (params: { userId: number }) => {
  return http.request<number[]>("get", "/api/system/user/role-ids", { params });
};

// 角色管理
export const getRoleList = (data?: any) => {
  return http.request("post", "/api/system/role/list", { data });
};

export const getAllRoleList = () => {
  return http.request("get", "/api/system/role/all");
};

export const addRole = (data: RoleVO) => {
  return http.request("post", "/api/system/role/save", { data });
};

export const updateRole = (data: RoleVO) => {
  return http.request("post", "/api/system/role/update", { data });
};

export const deleteRole = (id: number) => {
  return http.request("post", `/api/system/role/delete/${id}`);
};

export const getRoleMenu = (roleId: number) => {
  return http.request("post", `/api/system/role/menu-permissions`, { data: { roleId } });
};

export const getRoleMenuIds = (data: { roleId: number }) => {
  return http.request("post", `/api/system/role/menu-permissions`, { data });
};

export const assignRoleMenuPermissions = (data: { roleId: number; menuIds: number[] }) => {
  return http.request("post", `/api/system/role/assign-menu-permissions`, { data });
};

export const toggleRoleStatus = (data: { id: number; status: number }) => {
  return http.request("post", `/api/system/role/toggle-status`, { data });
};

// 菜单管理
export const getMenuList = (data?: any) => {
  return http.request<MenuVO[]>("post", "/api/system/menu/list", { data });
};

export const saveMenu = (data: MenuVO) => {
  return http.request("post", "/api/system/menu/save", { data });
};

export const updateMenu = (data: MenuVO) => {
  return http.request("post", "/api/system/menu/update", { data });
};

export const deleteMenu = (id: number) => {
  return http.request("post", `/api/system/menu/delete/${id}`);
};

export const toggleMenuStatus = (id: number, status: string) => {
  return http.request("put", `/api/system/menu/${id}/status`, { data: { status } });
};

// 部门管理
export const getDeptList = (data?: any) => {
  return http.request<DeptVO[]>("post", "/api/system/dept/list", { data });
};

export const getAllDepts = () => {
  return http.request<DeptVO[]>("post", "/api/system/dept/all");
};

export const addDept = (data: DeptVO) => {
  return http.request("post", "/api/system/dept/save", { data });
};

export const updateDept = (data: DeptVO) => {
  return http.request("post", "/api/system/dept/update", { data });
};

export const deleteDept = (id: number) => {
  return http.request("post", `/api/system/dept/delete/${id}`);
};

export const toggleDeptStatus = (data: { id: number; status: number }) => {
  return http.request("post", `/api/system/dept/toggle-status`, { data });
};

// 日志管理
export const getLoginLogsList = (data?: any) => {
  return http.request("post", "/api/monitor/login-logs", { data });
};

export const getOperationLogsList = (data?: any) => {
  return http.request("post", "/api/system/logs/operation", { data });
};

export const getSystemLogsList = (data?: any) => {
  return http.request("post", "/api/system/logs/system", { data });
};

export const getSystemLogsDetail = (id: number) => {
  return http.request("get", `/api/system/logs/system/${id}`);
};

// 在线用户
export const getOnlineLogsList = (data?: any) => {
  return http.request("post", "/api/monitor/online-users", { data });
};