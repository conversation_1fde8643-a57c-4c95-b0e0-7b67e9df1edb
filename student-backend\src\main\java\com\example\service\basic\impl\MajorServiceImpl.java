package com.example.service.basic.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.common.exception.BusinessException;
import com.example.common.PageResult;
import com.example.dto.basic.MajorQueryDTO;
import com.example.entity.basic.Major;
import com.example.entity.basic.College;
import com.example.mapper.basic.MajorMapper;
import com.example.mapper.basic.CollegeMapper;
import com.example.service.basic.MajorService;
import com.example.vo.basic.MajorVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 专业服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MajorServiceImpl implements MajorService {

    private final MajorMapper majorMapper;
    private final CollegeMapper collegeMapper;

    @Override
    @Cacheable(value = "majorList", key = "#query != null ? #query.toString() : 'default'")
    public PageResult<MajorVO> getMajorList(MajorQueryDTO query) {
        if (query == null) {
            query = new MajorQueryDTO();
        }

        // 设置默认分页参数
        if (query.getCurrent() == null || query.getCurrent() <= 0) {
            query.setCurrent(1);
        }
        if (query.getSize() == null || query.getSize() <= 0) {
            query.setSize(10);
        }

        IPage<Major> page = new Page<>(query.getCurrent(), query.getSize());
        IPage<Major> majorPage = majorMapper.selectMajorPage(page, query);

        List<MajorVO> majorVOList = majorPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return PageResult.<MajorVO>builder()
                .list(majorVOList)
                .total(majorPage.getTotal())
                .pageNum(query.getCurrent())
                .pageSize(query.getSize())
                .build();
    }

    @Override
    @Cacheable(value = "allMajors")
    public List<MajorVO> getAllMajors() {
        List<Major> majors = majorMapper.selectAllMajors();
        return majors.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "majorsByCollege", key = "#collegeCode")
    public List<MajorVO> getMajorsByCollegeCode(String collegeCode) {
        if (!StringUtils.hasText(collegeCode)) {
            throw new BusinessException("学院代码不能为空");
        }

        List<Major> majors = majorMapper.selectMajorsByCollegeCode(collegeCode);
        return majors.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "majorDetail", key = "#id")
    public MajorVO getMajorById(Integer id) {
        if (id == null) {
            throw new BusinessException("专业ID不能为空");
        }

        Major major = majorMapper.selectById(id);
        if (major == null) {
            throw new BusinessException("专业不存在");
        }

        return convertToVO(major);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"majorList", "allMajors", "majorsByCollege", "majorDetail"}, allEntries = true)
    public void saveMajor(MajorVO majorVO) {
        if (majorVO == null) {
            throw new BusinessException("专业信息不能为空");
        }

        // 验证专业信息
        validateMajorInfo(majorVO, null);

        try {
            Major major = new Major();
            copyVOToEntity(majorVO, major);

            major.setCreatedAt(LocalDateTime.now());
            major.setUpdatedAt(LocalDateTime.now());

            majorMapper.insert(major);

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("新增专业失败: {}", majorVO.getMajorName(), e);
            throw new BusinessException("新增专业失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"majorList", "allMajors", "majorsByCollege", "majorDetail"}, allEntries = true)
    public void updateMajor(MajorVO majorVO) {
        if (majorVO == null || majorVO.getId() == null) {
            throw new BusinessException("专业信息不能为空");
        }

        // 检查专业是否存在
        Major existingMajor = majorMapper.selectById(majorVO.getId());
        if (existingMajor == null) {
            throw new BusinessException("专业不存在");
        }

        // 验证专业信息
        validateMajorInfo(majorVO, majorVO.getId());

        try {
            Major major = new Major();
            copyVOToEntity(majorVO, major);
            major.setId(majorVO.getId());
            major.setUpdatedAt(LocalDateTime.now());

            majorMapper.updateById(major);

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新专业失败: {}", majorVO.getMajorName(), e);
            throw new BusinessException("更新专业失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"majorList", "allMajors", "majorsByCollege", "majorDetail"}, allEntries = true)
    public void deleteMajor(Integer id) {
        if (id == null) {
            throw new BusinessException("专业ID不能为空");
        }

        Major major = majorMapper.selectById(id);
        if (major == null) {
            throw new BusinessException("专业不存在");
        }

        try {
            majorMapper.deleteById(id);
        } catch (Exception e) {
            log.error("删除专业失败: {}", major.getMajorName(), e);
            throw new BusinessException("删除专业失败，可能存在关联数据");
        }
    }

    /**
     * 验证专业信息
     */
    private void validateMajorInfo(MajorVO majorVO, Integer excludeId) {
        // 检查专业代码是否重复
        if (StringUtils.hasText(majorVO.getMajorCode())) {
            Boolean exists = majorMapper.existsByMajorCode(majorVO.getMajorCode(), excludeId);
            if (Boolean.TRUE.equals(exists)) {
                throw new BusinessException("专业代码已存在");
            }
        }

        // 检查专业名称是否重复
        if (StringUtils.hasText(majorVO.getMajorName())) {
            Boolean exists = majorMapper.existsByMajorName(majorVO.getMajorName(), excludeId);
            if (Boolean.TRUE.equals(exists)) {
                throw new BusinessException("专业名称已存在");
            }
        }

        // 检查学院是否存在
        if (StringUtils.hasText(majorVO.getCollegeCode())) {
            College college = collegeMapper.selectByCollegeCode(majorVO.getCollegeCode());
            if (college == null) {
                throw new BusinessException("所属学院不存在");
            }
        }
    }

    /**
     * 实体转VO
     */
    private MajorVO convertToVO(Major major) {
        if (major == null) {
            return null;
        }

        MajorVO majorVO = new MajorVO();
        BeanUtils.copyProperties(major, majorVO);

        // 获取学院名称
        if (StringUtils.hasText(major.getCollegeCode())) {
            College college = collegeMapper.selectByCollegeCode(major.getCollegeCode());
            if (college != null) {
                majorVO.setCollegeName(college.getCollegeName());
            }
        }

        return majorVO;
    }

    /**
     * VO转实体
     */
    private void copyVOToEntity(MajorVO majorVO, Major major) {
        if (majorVO == null || major == null) {
            return;
        }

        BeanUtils.copyProperties(majorVO, major, "id", "createdAt", "updatedAt");
    }
}
