<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.educational.ClassCourseMapper">

    <!-- 结果映射 -->
    <resultMap id="ClassCourseVOResultMap" type="com.example.vo.educational.ClassCourseVO">
        <id column="id" property="id"/>
        <result column="class_code" property="classCode"/>
        <result column="class_name" property="className"/>
        <result column="major_code" property="majorCode"/>
        <result column="major_name" property="majorName"/>
        <result column="college_code" property="collegeCode"/>
        <result column="college_name" property="collegeName"/>
        <result column="course_code" property="courseCode"/>
        <result column="course_name" property="courseName"/>
        <result column="credits" property="credits"/>
        <result column="course_type" property="courseType"/>
        <result column="semester_id" property="semesterId"/>
        <result column="semester_name" property="semesterName"/>
        <result column="academic_year" property="academicYear"/>

        <result column="is_active" property="isActive"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="remark" property="remark"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- 通用SQL片段 -->
    <sql id="Base_Column_List">
        cc.id, cc.class_code, cc.course_code, cc.semester_id,
        cc.is_active, cc.sort_order, cc.remark, cc.created_by, cc.created_at,
        cc.updated_by, cc.updated_at,
        cl.class_name, cl.major_code,
        m.major_name, m.college_code,
        col.college_name,
        c.course_name, c.credits, c.course_type,
        s.semester_name, s.academic_year
    </sql>

    <!-- 通用连接查询 -->
    <sql id="Base_Join">
        FROM class_courses cc
        LEFT JOIN classes cl ON cc.class_code = cl.class_code
        LEFT JOIN majors m ON cl.major_code = m.major_code
        LEFT JOIN colleges col ON m.college_code = col.college_code
        LEFT JOIN courses c ON cc.course_code = c.course_code
        LEFT JOIN semesters s ON cc.semester_id = s.id
    </sql>

    <!-- 分页查询班级课程分配列表 -->
    <select id="selectClassCoursePage" resultMap="ClassCourseVOResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Base_Join"/>
        <where>
            <if test="query.classCode != null and query.classCode != ''">
                AND cc.class_code = #{query.classCode}
            </if>
            <if test="query.className != null and query.className != ''">
                AND cl.class_name LIKE CONCAT('%', #{query.className}, '%')
            </if>
            <if test="query.courseCode != null and query.courseCode != ''">
                AND cc.course_code LIKE CONCAT('%', #{query.courseCode}, '%')
            </if>
            <if test="query.courseName != null and query.courseName != ''">
                AND c.course_name LIKE CONCAT('%', #{query.courseName}, '%')
            </if>
            <if test="query.semesterId != null">
                AND cc.semester_id = #{query.semesterId}
            </if>

            <if test="query.majorCode != null and query.majorCode != ''">
                AND cl.major_code = #{query.majorCode}
            </if>
            <if test="query.collegeCode != null and query.collegeCode != ''">
                AND m.college_code = #{query.collegeCode}
            </if>
            <if test="query.isActive != null">
                AND cc.is_active = #{query.isActive}
            </if>
        </where>
        ORDER BY cc.class_code, cc.semester_id, cc.sort_order
    </select>

    <!-- 根据ID获取班级课程分配详情 -->
    <select id="selectClassCourseById" resultMap="ClassCourseVOResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Base_Join"/>
        WHERE cc.id = #{id}
    </select>

    <!-- 根据班级代码获取课程分配列表 -->
    <select id="selectCoursesByClass" resultMap="ClassCourseVOResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Base_Join"/>
        WHERE cc.class_code = #{classCode}
        ORDER BY cc.semester_id, cc.sort_order
    </select>

    <!-- 根据班级代码和学期ID获取课程分配列表 -->
    <select id="selectCoursesByClassAndSemester" resultMap="ClassCourseVOResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Base_Join"/>
        WHERE cc.class_code = #{classCode} AND cc.semester_id = #{semesterId}
        ORDER BY cc.sort_order
    </select>

    <!-- 根据课程代码获取分配的班级列表 -->
    <select id="selectClassesByCourse" resultMap="ClassCourseVOResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Base_Join"/>
        WHERE cc.course_code = #{courseCode}
        ORDER BY cc.class_code, cc.semester_id
    </select>

    <!-- 检查班级课程分配是否存在 -->
    <select id="checkClassCourseExists" resultType="int">
        SELECT COUNT(1)
        FROM class_courses
        WHERE class_code = #{classCode}
        AND course_code = #{courseCode}
        AND semester_id = #{semesterId}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 批量删除班级课程分配 -->
    <delete id="batchDeleteClassCourses">
        DELETE FROM class_courses
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据班级代码统计课程数量 -->
    <select id="countCoursesByClass" resultType="int">
        SELECT COUNT(1)
        FROM class_courses
        WHERE class_code = #{classCode}
        AND is_active = 1
    </select>

    <!-- 根据课程代码统计分配的班级数量 -->
    <select id="countClassesByCourse" resultType="int">
        SELECT COUNT(1)
        FROM class_courses
        WHERE course_code = #{courseCode}
        AND is_active = 1
    </select>

    <!-- 根据班级代码和课程代码查询课程分配列表 -->
    <select id="selectClassCoursesByClassAndCourse" resultMap="ClassCourseVOResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Base_Join"/>
        WHERE cc.class_code = #{classCode} AND cc.course_code = #{courseCode}
        ORDER BY cc.semester_id, cc.sort_order
    </select>

</mapper>
