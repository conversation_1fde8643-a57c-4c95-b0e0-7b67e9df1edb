import { ref, reactive, onMounted, h, type Ref } from "vue";
import { ElTag } from "element-plus";
import { message } from "@/utils/message";
import { addDialog } from "@/components/ReDialog";
import { deviceDetection } from "@pureadmin/utils";
import type { PaginationProps, TableColumnList } from "@pureadmin/table";
import type {
  GradeFormItemProps,
  ImportFormItemProps,
  SearchFormProps,
  CourseOption,
  SemesterOption
} from "./types";

// 导入API
import {
  getStudentGradeInputList
} from "@/api/score/grade-input";
import { getAllSemesters } from "@/api/basic/semester";
import { getCoursesByClass } from "@/api/educational/classCourse";

// 导入表单组件
import gradeForm from "../form/grade.vue";
import importForm from "../form/import.vue";

export function useGradeInput(tableRef: Ref) {
  /** 搜索表单 */
  const searchForm = reactive<SearchFormProps>({
    studentName: "",
    studentId: "",
    classCode: "",
    courseCode: "",
    semesterId: undefined
  });

  /** 表格加载状态 */
  const loading = ref(true);

  /** 表格数据 */
  const dataList = ref([]);

  /** 分页配置 */
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  });

  /** 表格列配置 */
  const columns: TableColumnList = [
    {
      label: "学号",
      prop: "studentId",
      minWidth: 120
    },
    {
      label: "学生姓名",
      prop: "studentName",
      minWidth: 100
    },
    {
      label: "课程名称",
      prop: "courseName",
      minWidth: 120
    },
    {
      label: "学期",
      prop: "semesterName",
      minWidth: 120
    },
    {
      label: "期末成绩",
      prop: "finalScore",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <span class={row.finalScore >= 60 ? "text-green-500" : "text-red-500"}>
          {row.finalScore}
        </span>
      )
    },
    {
      label: "绩点",
      prop: "gradePoint",
      minWidth: 80
    },
    {
      label: "是否重修",
      prop: "isRetake",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <el-tag type={row.isRetake ? "warning" : "success"}>
          {row.isRetake ? "是" : "否"}
        </el-tag>
      )
    },
    {
      label: "备注",
      prop: "remarks",
      minWidth: 120
    },
    {
      label: "操作",
      fixed: "right",
      width: 180,
      slot: "operation"
    }
  ];

  /** 获取成绩列表 */
  async function getGradeList() {
    loading.value = true;
    try {
      const { data } = await getStudentGradeInputList({
        ...searchForm,
        page: pagination.currentPage,
        size: pagination.pageSize
      });
      dataList.value = data.records || [];
      pagination.total = data.total || 0;
    } catch (error) {
      console.error("获取成绩列表失败:", error);
      message("获取成绩列表失败", { type: "error" });
    } finally {
      loading.value = false;
    }
  }

  /** 搜索 */
  function onSearch() {
    pagination.currentPage = 1;
    getGradeList();
  }

  /** 重置搜索表单 */
  function resetForm() {
    Object.keys(searchForm).forEach(key => {
      searchForm[key] = key === "semesterId" ? undefined : "";
    });
    onSearch();
  }

  /** 分页大小改变 */
  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    getGradeList();
  }

  /** 当前页改变 */
  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    getGradeList();
  }

  /** 打开成绩录入/编辑弹窗 */
  function openGradeDialog(title = "新增", row?: any) {
    const formInline: GradeFormItemProps = {
      title,
      studentId: row?.studentId ?? "",
      studentName: row?.studentName ?? "",
      classCode: searchForm.classCode ?? "",
      courseCode: row?.courseCode ?? "",
      courseName: row?.courseName ?? "",
      semesterId: row?.semesterId ?? undefined,
      semesterName: row?.semesterName ?? "",
      finalScore: row?.finalScore ?? undefined,
      gradePoint: row?.gradePoint ?? undefined,
      isRetake: row?.isRetake ?? false,
      remarks: row?.remarks ?? ""
    };

    addDialog({
      title: `${title}成绩`,
      props: { formInline },
      width: "46%",
      draggable: true,
      fullscreen: deviceDetection(),
      fullscreenIcon: true,
      closeOnClickModal: false,
      contentRenderer: () => h(gradeForm, { formInline }),
      beforeSure: (done, { options }) => {
        const FormRef = options.contentRenderer().ref;
        const curData = options.props.formInline as GradeFormItemProps;

        FormRef.getRef()?.validate(async (valid: boolean) => {
          if (valid) {
            try {
              // TODO: 实现成绩的新增和修改API
              message(`${title}成功`, { type: "success" });
              done();
              getGradeList();
            } catch (error) {
              console.error(`${title}成绩失败:`, error);
              message(`${title}失败`, { type: "error" });
            }
          }
        });
      }
    });
  }

  /** 打开成绩导入弹窗 */
  function openImportDialog() {
    const formInline: ImportFormItemProps = {
      title: "成绩导入",
      classCode: searchForm.classCode ?? "",
      semesterId: "",
      selectedCourseCodes: [],
      file: undefined
    };

    addDialog({
      title: "成绩导入",
      props: { formInline },
      width: "80%",
      draggable: true,
      fullscreen: deviceDetection(),
      fullscreenIcon: true,
      closeOnClickModal: false,
      contentRenderer: () => h(importForm, { formInline }),
      beforeSure: (done) => {
        // 导入逻辑在表单组件内部处理
        done();
        getGradeList();
      }
    });
  }

  /** 删除成绩 */
  async function handleDelete(row: any) {
    try {
      // TODO: 实现删除成绩API
      message("删除成功", { type: "success" });
      getGradeList();
    } catch (error) {
      console.error("删除成绩失败:", error);
      message("删除失败", { type: "error" });
    }
  }

  /** 组件挂载时获取数据 */
  onMounted(() => {
    getGradeList();
  });

  return {
    searchForm,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    openGradeDialog,
    openImportDialog,
    handleDelete,
    deviceDetection
  };
}
