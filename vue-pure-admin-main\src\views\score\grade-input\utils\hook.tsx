import { ref, reactive, computed, onMounted, h, type Ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { addDialog } from "@/components/ReDialog";
import { usePublicHooks } from "@/views/system/hooks";
import { deviceDetection } from "@pureadmin/utils";
import type { PaginationProps } from "@pureadmin/table";

// 导入API
import { getAllMajors } from "@/api/basic/major";
import { getClassesByMajor } from "@/api/basic/classes";
import { getCoursesByClass } from "@/api/educational/classCourse";
import { getStudentGradeInputList, addGrade, updateGrade, deleteGrade } from "@/api/score/grade-input";

// 导入类型
import type {
  MajorInfo,
  ClassInfo,
  CourseInfo,
  StudentGradeInfo,
  ViewType,
  CurrentSelection,
  SearchFormData,
  BreadcrumbItem
} from "./types";

// 导入表单组件
import editForm from "../form/index.vue";
// import importForm from "../form/import.vue";

export function useGradeInput(tableRef: Ref) {
  const { tagStyle } = usePublicHooks();

  // 响应式数据
  const loading = ref(false);
  const currentView = ref<ViewType>('majors');
  const currentSelection = reactive<CurrentSelection>({});

  // 列表数据
  const majorList = ref<MajorInfo[]>([]);
  const classList = ref<ClassInfo[]>([]);
  const courseList = ref<CourseInfo[]>([]);
  const gradeList = ref<StudentGradeInfo[]>([]);

  // 搜索表单
  const searchForm = reactive<SearchFormData>({
    studentName: "",
    studentId: ""
  });

  // 分页配置
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  });

  // 面包屑导航
  const breadcrumbs = computed<BreadcrumbItem[]>(() => {
    const items: BreadcrumbItem[] = [
      { title: "成绩录入", disabled: currentView.value === 'majors' }
    ];

    if (currentSelection.major) {
      items.push({
        title: currentSelection.major.majorName,
        disabled: currentView.value === 'classes'
      });
    }

    if (currentSelection.class) {
      items.push({
        title: currentSelection.class.className,
        disabled: currentView.value === 'courses'
      });
    }

    if (currentSelection.course) {
      items.push({
        title: currentSelection.course.courseName,
        disabled: currentView.value === 'grades'
      });
    }

    return items;
  });

  // 表格列配置
  const majorColumns = ref([
    {
      label: "专业代码",
      prop: "majorCode",
      minWidth: 120
    },
    {
      label: "专业名称",
      prop: "majorName",
      minWidth: 200
    },
    {
      label: "操作",
      fixed: "right",
      width: 120,
      slot: "operation"
    }
  ]);

  const classColumns = ref([
    {
      label: "班级代码",
      prop: "classCode",
      minWidth: 120
    },
    {
      label: "班级名称",
      prop: "className",
      minWidth: 200
    },
    {
      label: "操作",
      fixed: "right",
      width: 120,
      slot: "operation"
    }
  ]);

  const courseColumns = ref([
    {
      label: "课程代码",
      prop: "courseCode",
      minWidth: 120
    },
    {
      label: "课程名称",
      prop: "courseName",
      minWidth: 200
    },
    {
      label: "学期",
      prop: "semesterName",
      minWidth: 120
    },
    {
      label: "学分",
      prop: "credits",
      minWidth: 80
    },
    {
      label: "操作",
      fixed: "right",
      width: 180,
      slot: "operation"
    }
  ]);

  const gradeColumns = ref([
    {
      label: "学号",
      prop: "studentId",
      minWidth: 120
    },
    {
      label: "学生姓名",
      prop: "studentName",
      minWidth: 120
    },
    {
      label: "期末成绩",
      prop: "finalScore",
      minWidth: 100
    },
    {
      label: "绩点",
      prop: "gradePoint",
      minWidth: 80
    },
    {
      label: "是否重修",
      prop: "isRetake",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <el-tag size="small" type={row.isRetake ? "warning" : "success"}>
          {row.isRetake ? "是" : "否"}
        </el-tag>
      )
    },
    {
      label: "备注",
      prop: "remarks",
      minWidth: 150
    },
    {
      label: "操作",
      fixed: "right",
      width: 180,
      slot: "operation"
    }
  ]);

  // 获取当前列配置
  const currentColumns = computed(() => {
    switch (currentView.value) {
      case 'majors':
        return majorColumns.value;
      case 'classes':
        return classColumns.value;
      case 'courses':
        return courseColumns.value;
      case 'grades':
        return gradeColumns.value;
      default:
        return [];
    }
  });

  // 获取当前数据列表
  const currentDataList = computed(() => {
    switch (currentView.value) {
      case 'majors':
        return majorList.value;
      case 'classes':
        return classList.value;
      case 'courses':
        return courseList.value;
      case 'grades':
        return gradeList.value;
      default:
        return [];
    }
  });

  // 加载专业列表
  const loadMajors = async () => {
    loading.value = true;
    try {
      const response = await getAllMajors();
      majorList.value = response.data || [];
    } catch (error) {
      console.error("加载专业列表失败:", error);
      ElMessage.error("加载专业列表失败");
    } finally {
      loading.value = false;
    }
  };

  // 加载班级列表
  const loadClasses = async (majorCode: string) => {
    loading.value = true;
    try {
      const response = await getClassesByMajor(majorCode);
      classList.value = response.data || [];
    } catch (error) {
      console.error("加载班级列表失败:", error);
      ElMessage.error("加载班级列表失败");
    } finally {
      loading.value = false;
    }
  };

  // 加载课程列表
  const loadCourses = async (classCode: string) => {
    loading.value = true;
    try {
      const response = await getCoursesByClass(classCode);
      courseList.value = response.data || [];
    } catch (error) {
      console.error("加载课程列表失败:", error);
      ElMessage.error("加载课程列表失败");
    } finally {
      loading.value = false;
    }
  };

  // 加载成绩列表
  const loadGrades = async () => {
    if (!currentSelection.class?.classCode || !currentSelection.course?.courseCode) {
      return;
    }

    loading.value = true;
    try {
      const response = await getStudentGradeInputList({
        classCode: currentSelection.class.classCode,
        courseCode: currentSelection.course.courseCode,
        semesterId: currentSelection.course.semesterId,
        studentName: searchForm.studentName,
        studentId: searchForm.studentId,
        page: pagination.currentPage,
        size: pagination.pageSize
      });

      gradeList.value = response.data?.records || [];
      pagination.total = response.data?.total || 0;
    } catch (error) {
      console.error("加载成绩列表失败:", error);
      ElMessage.error("加载成绩列表失败");
    } finally {
      loading.value = false;
    }
  };

  // 选择专业
  const selectMajor = (major: MajorInfo) => {
    currentSelection.major = major;
    currentView.value = 'classes';
    loadClasses(major.majorCode);
  };

  // 选择班级
  const selectClass = (classInfo: ClassInfo) => {
    currentSelection.class = classInfo;
    currentView.value = 'courses';
    loadCourses(classInfo.classCode);
  };

  // 选择课程
  const selectCourse = (course: CourseInfo) => {
    currentSelection.course = course;
    currentView.value = 'grades';
    loadGrades();
  };

  // 返回上一级
  const goBack = () => {
    switch (currentView.value) {
      case 'classes':
        currentView.value = 'majors';
        currentSelection.major = undefined;
        break;
      case 'courses':
        currentView.value = 'classes';
        currentSelection.class = undefined;
        break;
      case 'grades':
        currentView.value = 'courses';
        currentSelection.course = undefined;
        break;
    }
  };

  // 搜索
  const onSearch = () => {
    if (currentView.value === 'grades') {
      pagination.currentPage = 1;
      loadGrades();
    }
  };

  // 重置搜索
  const resetSearch = () => {
    searchForm.studentName = "";
    searchForm.studentId = "";
    onSearch();
  };

  // 打开编辑对话框
  const openDialog = (title = "新增", row?: StudentGradeInfo) => {
    addDialog({
      title: `${title}成绩`,
      props: {
        formInline: {
          title,
          id: row?.id ?? undefined,
          studentId: row?.studentId ?? "",
          studentName: row?.studentName ?? "",
          courseCode: currentSelection.course?.courseCode ?? "",
          courseName: currentSelection.course?.courseName ?? "",
          semesterId: currentSelection.course?.semesterId ?? 0,
          semesterName: currentSelection.course?.semesterName ?? "",
          finalScore: row?.finalScore ?? undefined,
          gradePoint: row?.gradePoint ?? undefined,
          isRetake: row?.isRetake ?? false,
          remarks: row?.remarks ?? ""
        }
      },
      width: "46%",
      draggable: true,
      fullscreen: deviceDetection(),
      fullscreenIcon: true,
      closeOnClickModal: false,
      contentRenderer: () => h(editForm, { ref: formRef }),
      beforeSure: (done, { options }) => {
        const FormRef = formRef.value.getRef();
        const curData = options.props.formInline as StudentGradeInfo;

        FormRef.validate(async (valid: boolean) => {
          if (valid) {
            try {
              const submitData = {
                ...curData,
                classCode: currentSelection.class?.classCode
              };

              if (title === "新增") {
                await addGrade(submitData);
                ElMessage.success("新增成功");
              } else {
                await updateGrade(submitData);
                ElMessage.success("修改成功");
              }

              done();
              loadGrades();
            } catch (error) {
              console.error("操作失败:", error);
              ElMessage.error("操作失败");
            }
          }
        });
      }
    });
  };

  // 打开导入对话框
  const openImportDialog = () => {
    ElMessage.info("导入功能正在开发中...");
    // TODO: 实现导入功能
    /*
    addDialog({
      title: "成绩导入",
      props: {
        formInline: {
          title: "成绩导入",
          classCode: currentSelection.class?.classCode ?? "",
          className: currentSelection.class?.className ?? "",
          selectedSemesterId: "",
          selectedCourseCodes: [],
          availableSemesters: [],
          availableCourses: []
        }
      },
      width: "80%",
      draggable: true,
      fullscreen: deviceDetection(),
      fullscreenIcon: true,
      closeOnClickModal: false,
      contentRenderer: () => h(importForm, { ref: importFormRef }),
      beforeSure: (done) => {
        ElMessage.success("导入完成");
        done();
        if (currentView.value === 'grades') {
          loadGrades();
        }
      }
    });
    */
  };

  // 删除成绩
  const handleDelete = (row: StudentGradeInfo) => {
    ElMessageBox.confirm(
      `确认删除学生"${row.studentName}"的成绩吗？`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    ).then(async () => {
      try {
        await deleteGrade(row.id!);
        ElMessage.success("删除成功");
        loadGrades();
      } catch (error) {
        console.error("删除失败:", error);
        ElMessage.error("删除失败");
      }
    });
  };

  // 分页处理
  const handleSizeChange = (val: number) => {
    pagination.pageSize = val;
    loadGrades();
  };

  const handleCurrentChange = (val: number) => {
    pagination.currentPage = val;
    loadGrades();
  };

  // 表单引用
  const formRef = ref();
  // const importFormRef = ref();

  // 组件挂载时加载专业列表
  onMounted(() => {
    loadMajors();
  });

  return {
    loading,
    currentView,
    currentSelection,
    searchForm,
    pagination,
    breadcrumbs,
    currentColumns,
    currentDataList,
    majorList,
    classList,
    courseList,
    gradeList,
    selectMajor,
    selectClass,
    selectCourse,
    goBack,
    onSearch,
    resetSearch,
    openDialog,
    openImportDialog,
    handleDelete,
    handleSizeChange,
    handleCurrentChange,
    deviceDetection
  };
}
