package com.example.service.system.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.common.PageResult;
import com.example.dto.system.UserQueryDTO;
import com.example.entity.system.SysDept;
import com.example.entity.system.SysRole;
import com.example.entity.system.SysUser;
import com.example.entity.system.SysUserRole;
import com.example.common.exception.BusinessException;
import com.example.mapper.system.SysDeptMapper;
import com.example.mapper.system.SysUserMapper;
import com.example.mapper.system.SysUserRoleMapper;
import com.example.service.system.UserService;
import com.example.mapper.system.SysRoleMapper;
import com.example.vo.system.UserVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用户服务实现
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class UserServiceImpl implements UserService {

    private final SysUserMapper userMapper;
    private final SysUserRoleMapper userRoleMapper;
    private final SysDeptMapper deptMapper;
    private final SysRoleMapper roleMapper;
    private final PasswordEncoder passwordEncoder;

    @Override
    @Cacheable(value = "userList", key = "#query != null ? #query.hashCode() : 'null'")
    public PageResult<UserVO> getUserList(UserQueryDTO query) {
        if (query == null) {
            query = new UserQueryDTO();
        }

        try {
            // 构建查询条件
            LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();

            if (StringUtils.hasText(query.getUsername())) {
                queryWrapper.like(SysUser::getUsername, query.getUsername());
            }
            if (StringUtils.hasText(query.getNickname())) {
                queryWrapper.like(SysUser::getNickname, query.getNickname());
            }
            if (StringUtils.hasText(query.getEmail())) {
                queryWrapper.like(SysUser::getEmail, query.getEmail());
            }
            if (StringUtils.hasText(query.getPhone())) {
                queryWrapper.like(SysUser::getPhone, query.getPhone());
            }
            if (query.getStatus() != null) {
                queryWrapper.eq(SysUser::getStatus, query.getStatus());
            }
            if (query.getDeptId() != null) {
                queryWrapper.eq(SysUser::getDeptId, query.getDeptId());
            }

            // 排序
            if ("asc".equalsIgnoreCase(query.getOrderDirection())) {
                queryWrapper.orderByAsc(SysUser::getCreateTime);
            } else {
                queryWrapper.orderByDesc(SysUser::getCreateTime);
            }

            // 分页查询
            Page<SysUser> page = new Page<>(query.getPage(), query.getSize());
            Page<SysUser> result = userMapper.selectPage(page, queryWrapper);

            // 转换为VO
            List<UserVO> userVOList = result.getRecords().stream()
                    .map(this::convertToUserVO)
                    .collect(Collectors.toList());

            // 返回分页结果
            return PageResult.of(result, userVOList);

        } catch (Exception e) {
            log.error("查询用户列表失败", e);
            throw new BusinessException("查询用户列表失败");
        }
    }

    @Override
    @Cacheable(value = "allUsers", key = "'all'")
    public List<UserVO> getAllUsers() {
        try {
            LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysUser::getStatus, 1)
                       .orderByDesc(SysUser::getCreateTime);

            List<SysUser> users = userMapper.selectList(queryWrapper);
            return users.stream()
                    .map(this::convertToUserVO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询所有用户失败", e);
            throw new BusinessException("查询所有用户失败");
        }
    }

    @Override
    @Cacheable(value = "userDetail", key = "#id")
    public UserVO getUserById(Integer id) {
        if (id == null) {
            throw new BusinessException("用户ID不能为空");
        }

        try {
            SysUser user = userMapper.selectById(id);
            if (user == null || user.getStatus() == 0) {
                throw new BusinessException("用户不存在或已被删除");
            }

            UserVO userVO = convertToUserVO(user);
            // 获取用户角色
            userVO.setRoleIds(getUserRoleIds(id));
            return userVO;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询用户详情失败: {}", id, e);
            throw new BusinessException("查询用户详情失败");
        }
    }

    @Override
    @CacheEvict(value = {"userList", "allUsers", "userDetail", "userMenuList", "asyncRoutes", "userRoles"}, allEntries = true)
    public void saveUser(UserVO userVO) {
        if (userVO == null) {
            throw new BusinessException("用户信息不能为空");
        }

        // 验证用户信息
        validateUserInfo(userVO, null);

        try {
            SysUser user = new SysUser();
            copyUserVOToEntity(userVO, user);

            // 设置默认值
            user.setStatus(1);
            user.setCreateTime(LocalDateTime.now());
            user.setUpdateTime(LocalDateTime.now());

            // 加密密码
            if (StringUtils.hasText(userVO.getPassword())) {
                user.setPassword(passwordEncoder.encode(userVO.getPassword()));
            } else {
                user.setPassword(passwordEncoder.encode("123456")); // 默认密码
            }

            userMapper.insert(user);

            // 分配角色
            if (userVO.getRoleIds() != null && !userVO.getRoleIds().isEmpty()) {
                assignRoles(user.getId(), userVO.getRoleIds());
            }

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("新增用户失败: {}", userVO.getUsername(), e);
            throw new BusinessException("新增用户失败");
        }
    }

    @Override
    @CacheEvict(value = {"userList", "allUsers", "userDetail", "userMenuList", "asyncRoutes", "userRoles"}, allEntries = true)
    public void updateUser(UserVO userVO) {
        if (userVO == null || userVO.getId() == null) {
            throw new BusinessException("用户ID不能为空");
        }

        // 检查用户是否存在
        SysUser existingUser = userMapper.selectById(userVO.getId());
        if (existingUser == null || existingUser.getStatus() == 0) {
            throw new BusinessException("用户不存在或已被删除");
        }

        // 验证用户信息
        validateUserInfo(userVO, userVO.getId());

        try {
            SysUser user = new SysUser();
            copyUserVOToEntity(userVO, user);
            user.setUpdateTime(LocalDateTime.now());

            // 不更新密码，密码单独重置
            user.setPassword(null);

            int updated = userMapper.updateById(user);
            if (updated == 0) {
                throw new BusinessException("更新用户失败，用户可能已被删除");
            }

            // 更新角色
            if (userVO.getRoleIds() != null) {
                assignRoles(userVO.getId(), userVO.getRoleIds());
            }

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新用户失败: {}", userVO.getId(), e);
            throw new BusinessException("更新用户失败");
        }
    }

    @Override
    @Caching(evict = {
        @CacheEvict(value = {"userList", "allUsers", "userDetail", "userMenuList", "asyncRoutes", "userRoles"}, allEntries = true),
        @CacheEvict(value = "userRoleIds", key = "#userId")
    })
    public void deleteUser(Integer userId) {
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }

        // 检查用户是否存在
        SysUser existingUser = userMapper.selectById(userId);
        if (existingUser == null || existingUser.getStatus() == 0) {
            throw new BusinessException("用户不存在或已被删除");
        }

        // 检查是否为管理员
        if ("admin".equals(existingUser.getUsername())) {
            throw new BusinessException("不能删除管理员用户");
        }

        try {
            // 软删除用户
            SysUser user = new SysUser();
            user.setId(userId);
            user.setStatus(0);
            user.setUpdateTime(LocalDateTime.now());

            int deleted = userMapper.updateById(user);
            if (deleted == 0) {
                throw new BusinessException("删除用户失败");
            }

            // 删除用户角色关联
            LambdaQueryWrapper<SysUserRole> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SysUserRole::getUserId, userId);
            userRoleMapper.delete(wrapper);

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除用户失败: {}", userId, e);
            throw new BusinessException("删除用户失败");
        }
    }

    @Override
    @CacheEvict(value = {"userDetail"}, key = "#userId")
    public void resetPassword(Integer userId, String newPassword) {
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }

        if (!StringUtils.hasText(newPassword)) {
            newPassword = "123456"; // 默认密码
        }

        try {
            SysUser user = new SysUser();
            user.setId(userId);
            user.setPassword(passwordEncoder.encode(newPassword));
            user.setUpdateTime(LocalDateTime.now());

            int updated = userMapper.updateById(user);
            if (updated == 0) {
                throw new BusinessException("重置密码失败，用户可能不存在");
            }

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("重置用户密码失败: {}", userId, e);
            throw new BusinessException("重置密码失败");
        }
    }

    @Override
    @Caching(evict = {
        @CacheEvict(value = {"userList", "allUsers", "userDetail", "userMenuList", "asyncRoutes", "userRoles"}, allEntries = true),
        @CacheEvict(value = "userRoleIds", key = "#userId")
    })
    public void assignRoles(Integer userId, List<Integer> roleIds) {
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }

        try {
            // 删除原有角色关联
            LambdaQueryWrapper<SysUserRole> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(SysUserRole::getUserId, userId);
            userRoleMapper.delete(deleteWrapper);

            // 添加新的角色关联
            if (roleIds != null && !roleIds.isEmpty()) {
                List<SysUserRole> userRoles = roleIds.stream()
                        .map(roleId -> {
                            SysUserRole userRole = new SysUserRole();
                            userRole.setUserId(userId);
                            userRole.setRoleId(roleId);
                            return userRole;
                        })
                        .collect(Collectors.toList());

                for (SysUserRole userRole : userRoles) {
                    userRoleMapper.insert(userRole);
                }
            }

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("分配用户角色失败: userId={}, roleIds={}", userId, roleIds, e);
            throw new BusinessException("分配用户角色失败");
        }
    }

    @Override
    @Cacheable(value = "userRoleIds", key = "#userId")
    public List<Integer> getUserRoleIds(Integer userId) {
        if (userId == null) {
            return new ArrayList<>();
        }

        try {
            LambdaQueryWrapper<SysUserRole> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SysUserRole::getUserId, userId);

            List<SysUserRole> userRoles = userRoleMapper.selectList(wrapper);
            return userRoles.stream()
                    .map(SysUserRole::getRoleId)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询用户角色失败: {}", userId, e);
            return new ArrayList<>();
        }
    }

    @Override
    @CacheEvict(value = {"userList", "allUsers", "userDetail", "userMenuList", "asyncRoutes", "userRoles"}, allEntries = true)
    public void toggleUserStatus(Integer userId, Integer status) {
        if (userId == null || status == null) {
            throw new BusinessException("用户ID和状态不能为空");
        }

        if (status != 0 && status != 1) {
            throw new BusinessException("状态值无效");
        }

        try {
            SysUser user = new SysUser();
            user.setId(userId);
            user.setStatus(status);
            user.setUpdateTime(LocalDateTime.now());

            int updated = userMapper.updateById(user);
            if (updated == 0) {
                throw new BusinessException("更新用户状态失败，用户可能不存在");
            }

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("切换用户状态失败: userId={}, status={}", userId, status, e);
            throw new BusinessException("切换用户状态失败");
        }
    }

    @Override
    @Cacheable(value = "userByUsername", key = "#username")
    public UserVO getUserByUsername(String username) {
        if (!StringUtils.hasText(username)) {
            return null;
        }

        try {
            LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SysUser::getUsername, username)
                   .eq(SysUser::getStatus, 1);

            SysUser user = userMapper.selectOne(wrapper);
            return user != null ? convertToUserVO(user) : null;
        } catch (Exception e) {
            log.error("根据用户名查询用户失败: {}", username, e);
            return null;
        }
    }

    @Override
    public boolean isUsernameExists(String username, Integer excludeId) {
        if (!StringUtils.hasText(username)) {
            return false;
        }

        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUser::getUsername, username)
               .eq(SysUser::getStatus, 1);

        if (excludeId != null) {
            wrapper.ne(SysUser::getId, excludeId);
        }

        return userMapper.selectCount(wrapper) > 0;
    }

    @Override
    public boolean isEmailExists(String email, Integer excludeId) {
        if (!StringUtils.hasText(email)) {
            return false;
        }

        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUser::getEmail, email)
               .eq(SysUser::getStatus, 1);

        if (excludeId != null) {
            wrapper.ne(SysUser::getId, excludeId);
        }

        return userMapper.selectCount(wrapper) > 0;
    }

    @Override
    public boolean isPhoneExists(String phone, Integer excludeId) {
        if (!StringUtils.hasText(phone)) {
            return false;
        }

        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUser::getPhone, phone)
               .eq(SysUser::getStatus, 1);

        if (excludeId != null) {
            wrapper.ne(SysUser::getId, excludeId);
        }

        return userMapper.selectCount(wrapper) > 0;
    }

    /**
     * 验证用户信息
     */
    private void validateUserInfo(UserVO userVO, Integer excludeId) {
        // 检查用户名是否重复
        if (isUsernameExists(userVO.getUsername(), excludeId)) {
            throw new BusinessException("用户名已存在");
        }

        // 检查邮箱是否重复
        if (StringUtils.hasText(userVO.getEmail()) && isEmailExists(userVO.getEmail(), excludeId)) {
            throw new BusinessException("邮箱已存在");
        }

        // 检查手机号是否重复
        if (StringUtils.hasText(userVO.getPhone()) && isPhoneExists(userVO.getPhone(), excludeId)) {
            throw new BusinessException("手机号已存在");
        }
    }

    /**
     * 转换为UserVO
     */
    private UserVO convertToUserVO(SysUser user) {
        UserVO userVO = new UserVO();
        BeanUtils.copyProperties(user, userVO);

        // 获取部门名称
        if (user.getDeptId() != null) {
            try {
                SysDept dept = deptMapper.selectById(user.getDeptId());
                if (dept != null) {
                    userVO.setDeptName(dept.getDeptName());
                }
            } catch (Exception e) {
                log.error("获取部门信息失败，部门ID: {}", user.getDeptId(), e);
            }
        }

        // 获取角色信息
        try {
            List<Integer> roleIds = getUserRoleIds(user.getId());
            userVO.setRoleIds(roleIds);

            // 获取角色名称列表
            if (roleIds != null && !roleIds.isEmpty()) {
                List<String> roleNames = roleIds.stream()
                    .map(roleId -> {
                        try {
                            // 这里需要查询角色名称，先简单实现
                            LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>();
                            wrapper.eq(SysRole::getId, roleId)
                                   .eq(SysRole::getStatus, 1);
                            SysRole role = roleMapper.selectOne(wrapper);
                            return role != null ? role.getRoleName() : null;
                        } catch (Exception e) {
                            log.error("查询角色名称失败，角色ID: {}", roleId, e);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
                userVO.setRoleNames(roleNames);
            }
        } catch (Exception e) {
            log.error("获取用户角色信息失败，用户ID: {}", user.getId(), e);
        }

        return userVO;
    }

    /**
     * 复制UserVO到实体
     */
    private void copyUserVOToEntity(UserVO userVO, SysUser user) {
        BeanUtils.copyProperties(userVO, user);
        // 不复制密码，密码单独处理
        user.setPassword(null);
    }
}
