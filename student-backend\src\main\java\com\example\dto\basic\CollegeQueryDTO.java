package com.example.dto.basic;

import com.example.dto.BaseQueryDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 学院查询DTO
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "学院查询条件")
public class CollegeQueryDTO extends BaseQueryDTO {

    @Schema(description = "学院代码")
    private String collegeCode;

    @Schema(description = "学院名称")
    private String collegeName;

    @Schema(description = "排序字段")
    private String sortField;

    @Schema(description = "排序方向")
    private String sortOrder;
}
