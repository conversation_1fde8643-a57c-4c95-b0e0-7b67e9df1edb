package com.itmk.config.jwt;

import com.auth0.jwt.interfaces.DecodedJWT;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;

/**
 * JWT拦截器，用于解析token并设置用户上下文
 */
@Component
public class JwtInterceptor implements HandlerInterceptor {

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private JwtSecurityEnhancer jwtSecurityEnhancer;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 检查是否是排除路径
        String requestURI = request.getRequestURI();
        if (isExcludedPath(requestURI)) {
            UserContext.clear(); // 清除上下文，确保不会有残留信息
            return true; // 直接放行
        }

        // 获取token - 支持多种格式
        String token = extractToken(request);

        // 如果没有token，直接拒绝访问
        if (StringUtils.isBlank(token)) {
            UserContext.clear();
            handleTokenError(response, "访问被拒绝：缺少认证token");
            return false;
        }

        // 如果token存在，解析并设置用户上下文
        try {
            // 使用安全增强器验证token
            if (jwtSecurityEnhancer.validateTokenSecurity(token, request)) {
                // 解析token
                DecodedJWT decodedJWT = jwtUtils.jwtDecode(token);
                String userId = decodedJWT.getClaim("userId").asString();
                String username = decodedJWT.getClaim("username").asString();
                String userType = decodedJWT.getClaim("userType").asString();

                // 设置用户上下文
                UserContext userContext = new UserContext();
                userContext.setUserId(Long.valueOf(userId));
                userContext.setUsername(username);
                userContext.setUserType(userType);

                UserContext.setCurrentUser(userContext);

                // 检查token是否即将过期（可选：实现token刷新机制）
                checkTokenExpiry(decodedJWT, response);

                return true; // token验证成功，允许访问
            } else {
                // token验证失败
                UserContext.clear();
                handleTokenError(response, "访问被拒绝：token验证失败");
                return false;
            }
        } catch (Exception e) {
            // token解析失败，清除用户上下文并返回401
            UserContext.clear();
            handleTokenError(response, "访问被拒绝：token解析失败 - " + e.getMessage());
            return false;
        }
    }

    /**
     * 检查是否是排除路径
     */
    private boolean isExcludedPath(String requestURI) {
        // 定义排除路径列表，与WebConfig中的配置保持一致
        String[] excludedPaths = {
            "/api/login/",           // 原登录接口
            "/api/auth/",            // 现代化认证接口
            "/doc.html",             // Knife4j文档
            "/webjars/",             // webjars资源
            "/swagger-resources/",   // swagger资源
            "/v3/api-docs/",         // OpenAPI文档
            "/favicon.ico",          // 图标
            "/actuator/"             // 健康检查等监控接口
        };

        for (String excludedPath : excludedPaths) {
            if (requestURI.startsWith(excludedPath)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 从请求中提取token，支持多种格式
     */
    private String extractToken(HttpServletRequest request) {
        // 1. 从Authorization头获取 Bearer token
        String authHeader = request.getHeader("Authorization");
        if (StringUtils.isNotBlank(authHeader) && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }

        // 2. 从token头获取
        String token = request.getHeader("token");
        if (StringUtils.isNotBlank(token)) {
            return token;
        }

        // 3. 从X-Token头获取（兼容性）
        token = request.getHeader("X-Token");
        if (StringUtils.isNotBlank(token)) {
            return token;
        }

        return null;
    }

    /**
     * 检查token过期时间
     */
    private void checkTokenExpiry(DecodedJWT decodedJWT, HttpServletResponse response) {
        Date expiresAt = decodedJWT.getExpiresAt();
        if (expiresAt != null) {
            long timeUntilExpiry = expiresAt.getTime() - System.currentTimeMillis();
            // 如果token在5分钟内过期，添加响应头提示前端刷新
            if (timeUntilExpiry < 5 * 60 * 1000) {
                response.setHeader("X-Token-Expiring", "true");
            }
        }
    }

    /**
     * 处理token错误
     */
    private void handleTokenError(HttpServletResponse response, String errorMessage) throws Exception {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write("{\"code\":600,\"msg\":\"" + errorMessage + "\"}");
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 请求完成后清除用户上下文
        UserContext.clear();
    }
}
