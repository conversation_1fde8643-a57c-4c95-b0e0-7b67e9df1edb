import { reactive } from "vue";
import type { FormRules } from "element-plus";

/** 基本素质测评表单校验 */
const formRules = reactive(<FormRules>{
  studentId: [
    { required: true, message: "请选择学生学号", trigger: "change" }
  ],
  evaluationPeriod: [
    { required: true, message: "请选择评分学期", trigger: "change" }
  ],
  periodScore: [
    { required: true, message: "请输入基础分", trigger: "blur" },
    { type: "number", min: 0, max: 200, message: "基础分必须在0-200之间", trigger: "blur" }
  ],
  addScore: [
    { type: "number", min: 0, max: 100, message: "加分必须在0-100之间", trigger: "blur" }
  ],
  reduceScore: [
    { type: "number", min: 0, max: 100, message: "扣分必须在0-100之间", trigger: "blur" }
  ]
});

/** 导入表单校验 */
const importFormRules = reactive(<FormRules>{
  // 导入表单暂时不需要特殊校验规则
});

export { formRules, importFormRules };
