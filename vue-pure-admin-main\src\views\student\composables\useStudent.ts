import { ref, onMounted } from "vue";
import { useTable } from "./useTable";
import { useCrud } from "./useCrud";
import {
  getStudentsPage,
  saveStudents,
  updateStudents,
  deleteStudents,
  batchDeleteStudents
} from "@/api/student/students";
import { getAllColleges } from "@/api/basic/college";
import { getAllMajors } from "@/api/basic/major";
import { getAllClasses } from "@/api/basic/classes";
import editForm from "../components/StudentForm.vue";
import { FormItemProps, QueryFormProps } from "../utils/types";

export function useStudent() {
  // 基础数据选项
  const collegeOptions = ref([]);
  const majorOptions = ref([]);
  const classOptions = ref([]);

  // 表格管理
  const {
    queryForm,
    dataList,
    loading,
    selectedNum,
    tableRef,
    pagination,
    onSearch,
    resetForm,
    onSizeChange,
    onCurrentChange,
    handleSelectionChange,
    onSelectionCancel
  } = useTable<FormItemProps, QueryFormProps>(getStudentsPage, {
    studentId: "",
    name: "",
    gender: "",
    collegeCode: "",
    majorCode: "",
    classCode: "",
    status: "",
    enrollmentYear: null,
    current: 1,
    size: 10
  });

  // CRUD操作
  const { openDialog, handleDelete, onBatchDelete } = useCrud(editForm, {
    save: saveStudents,
    update: updateStudents,
    delete: deleteStudents,
    batchDelete: batchDeleteStudents
  }, {
    entityName: "学生",
    onSuccess: onSearch
  });

  // 表格列定义
  const columns = [
    {
      label: "勾选列",
      type: "selection",
      width: 55,
      align: "left",
      hide: false
    },
    {
      label: "序号",
      type: "index",
      width: 70,
      hide: false
    },
    {
      label: "学号",
      prop: "studentId",
      minWidth: 120,
      hide: false
    },
    {
      label: "姓名",
      prop: "name",
      minWidth: 100,
      hide: false
    },
    {
      label: "性别",
      prop: "gender",
      minWidth: 80,
      hide: false
    },
    {
      label: "学院",
      prop: "collegeName",
      minWidth: 120,
      hide: false
    },
    {
      label: "专业",
      prop: "majorName",
      minWidth: 120,
      hide: false
    },
    {
      label: "班级",
      prop: "className",
      minWidth: 120,
      hide: false
    },
    {
      label: "学籍状态",
      prop: "status",
      minWidth: 100,
      hide: false
    },
    {
      label: "入学日期",
      prop: "enrollmentDate",
      minWidth: 120,
      hide: false
    },
    {
      label: "操作",
      fixed: "right",
      width: 180,
      slot: "operation",
      hide: false
    }
  ];

  /** 加载基础数据 */
  async function loadBaseData() {
    try {
      const [collegeRes, majorRes, classRes] = await Promise.all([
        getAllColleges(),
        getAllMajors(),
        getAllClasses()
      ]);

      if (collegeRes.success) collegeOptions.value = collegeRes.data;
      if (majorRes.success) majorOptions.value = majorRes.data;
      if (classRes.success) classOptions.value = classRes.data;
    } catch (error) {
      console.error("加载基础数据失败:", error);
    }
  }

  /** 打开学生对话框 */
  function openStudentDialog(title = "新增", row?: FormItemProps) {
    // 如果是新增模式且当前在班级列表页面，自动填充班级信息
    let defaultData = row || {};
    if (title === "新增" && queryForm.classCode) {
      defaultData = {
        ...defaultData,
        classCode: queryForm.classCode,
        collegeCode: queryForm.collegeCode,
        majorCode: queryForm.majorCode
      };
    }

    openDialog(title, defaultData, {
      collegeOptions: collegeOptions.value,
      majorOptions: majorOptions.value,
      classOptions: classOptions.value
    });
  }

  /** 批量删除学生 */
  function onBatchDeleteStudents() {
    onBatchDelete(tableRef);
  }

  onMounted(() => {
    loadBaseData();
    onSearch();
  });

  return {
    queryForm,
    loading,
    columns,
    dataList,
    pagination,
    selectedNum,
    collegeOptions,
    majorOptions,
    classOptions,
    tableRef,
    onSearch,
    resetForm,
    onBatchDeleteStudents,
    openStudentDialog,
    handleDelete,
    onSizeChange,
    onCurrentChange,
    onSelectionCancel,
    handleSelectionChange
  };
}
