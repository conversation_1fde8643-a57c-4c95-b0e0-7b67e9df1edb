<script setup lang="ts">
import { ref, onMounted } from "vue";
import { getAllColleges, type CollegeItem } from "@/api/basic/college";
import type { TeacherFormProps } from "./utils/types";
import { teacherFormRules } from "./utils/rule";
import {
  GENDER_OPTIONS,
  TITLE_OPTIONS,
  STATUS_OPTIONS
} from "./utils/types";

const props = withDefaults(defineProps<TeacherFormProps>(), {
  formInline: () => ({
    teacherCode: "",
    name: "",
    gender: "",
    birthDate: "",
    phone: "",
    email: "",
    collegeCode: "",
    title: "",
    hireDate: "",
    status: "在职"
  })
});

const ruleFormRef = ref();
const colleges = ref<CollegeItem[]>([]);

// 获取学院列表
const loadColleges = async () => {
  try {
    const res = await getAllColleges();
    if (res.success) {
      colleges.value = res.data;
    }
  } catch (error) {
    console.error("获取学院列表失败:", error);
  }
};

onMounted(() => {
  loadColleges();
});

function getRef() {
  return ruleFormRef.value;
}

defineExpose({ getRef });
</script>

<template>
  <el-form
    ref="ruleFormRef"
    :model="formInline"
    :rules="teacherFormRules"
    label-width="100px"
  >
    <el-row :gutter="30">
      <el-col :xs="24" :sm="12">
        <el-form-item label="教师工号" prop="teacherCode">
          <el-input
            v-model="formInline.teacherCode"
            clearable
            placeholder="请输入教师工号"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item label="姓名" prop="name">
          <el-input
            v-model="formInline.name"
            clearable
            placeholder="请输入姓名"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item label="性别" prop="gender">
          <el-select
            v-model="formInline.gender"
            placeholder="请选择性别"
            class="w-full"
          >
            <el-option
              v-for="option in GENDER_OPTIONS"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item label="出生日期" prop="birthDate">
          <el-date-picker
            v-model="formInline.birthDate"
            type="date"
            placeholder="请选择出生日期"
            class="w-full"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item label="电话号码" prop="phone">
          <el-input
            v-model="formInline.phone"
            clearable
            placeholder="请输入电话号码"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="formInline.email"
            clearable
            placeholder="请输入邮箱"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item label="所属学院" prop="collegeCode">
          <el-select
            v-model="formInline.collegeCode"
            placeholder="请选择所属学院"
            class="w-full"
          >
            <el-option
              v-for="college in colleges"
              :key="college.collegeCode"
              :label="college.collegeName"
              :value="college.collegeCode"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item label="职称" prop="title">
          <el-select
            v-model="formInline.title"
            placeholder="请选择职称"
            class="w-full"
          >
            <el-option
              v-for="option in TITLE_OPTIONS"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item label="入职日期" prop="hireDate">
          <el-date-picker
            v-model="formInline.hireDate"
            type="date"
            placeholder="请选择入职日期"
            class="w-full"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="formInline.status"
            placeholder="请选择状态"
            class="w-full"
          >
            <el-option
              v-for="option in STATUS_OPTIONS"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
