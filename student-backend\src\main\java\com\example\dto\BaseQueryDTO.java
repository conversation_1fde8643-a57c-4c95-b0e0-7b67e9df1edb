package com.example.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 基础分页DTO
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Data
@Schema(description = "基础分页参数")
public class BaseQueryDTO {

    @Schema(description = "当前页码", example = "1")
    private Integer current = 1;

    @Schema(description = "每页大小", example = "10")
    private Integer size = 10;

    @Schema(description = "排序字段")
    private String sortField;

    @Schema(description = "排序方向：asc、desc")
    private String sortOrder = "desc";
}
