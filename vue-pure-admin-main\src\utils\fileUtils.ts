/**
 * 文件相关工具函数
 */

/**
 * 从HTTP响应的Content-Disposition头中提取文件名
 * @param response HTTP响应对象
 * @returns 提取的文件名，如果提取失败返回null
 */
export const getFileNameFromContentDisposition = (response: any): string | null => {
  try {
    // 检查响应对象是否存在且有headers属性
    if (!response || !response.headers) {
      console.warn('响应对象无效或缺少headers属性');
      return null;
    }

    const contentDisposition = response.headers['content-disposition'] || response.headers['Content-Disposition'];

    if (!contentDisposition) {
      console.warn('响应头中未找到Content-Disposition');
      return null;
    }

    // 匹配 filename*=UTF-8''encoded_filename 格式（RFC 5987标准）
    const utf8Match = contentDisposition.match(/filename\*=UTF-8''(.+)/);
    if (utf8Match) {
      return decodeURIComponent(utf8Match[1]);
    }

    // 匹配 filename="filename" 格式（传统格式）
    const fallbackMatch = contentDisposition.match(/filename="?([^"]+)"?/);
    if (fallbackMatch) {
      return fallbackMatch[1];
    }

    console.warn('无法从Content-Disposition中解析文件名:', contentDisposition);
    return null;

  } catch (error) {
    console.error('解析Content-Disposition失败:', error);
    return null;
  }
};

/**
 * 创建并触发文件下载
 * @param blob 文件Blob对象
 * @param fileName 文件名
 */
export const downloadFile = (blob: Blob, fileName: string): void => {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName;

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};

/**
 * 生成带时间戳的默认文件名
 * @param prefix 文件名前缀
 * @param extension 文件扩展名（包含点号，如'.xlsx'）
 * @returns 生成的文件名
 */
export const generateDefaultFileName = (prefix: string = '导出文件', extension: string = '.xlsx'): string => {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
  return `${prefix}_${timestamp}${extension}`;
};

/**
 * 处理文件下载的完整流程
 * @param response HTTP响应对象
 * @param fallbackFileName 当无法从响应头获取文件名时的备用文件名
 * @param mimeType 文件MIME类型
 */
export const handleFileDownload = (
  response: any,
  fallbackFileName?: string,
  mimeType: string = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
): void => {
  const blob = new Blob([response.data], { type: mimeType });

  // 尝试从响应头获取文件名
  const fileName = getFileNameFromContentDisposition(response);

  if (fileName) {
    downloadFile(blob, fileName);
  } else if (fallbackFileName) {
    downloadFile(blob, fallbackFileName);
    console.warn('使用备用文件名:', fallbackFileName);
  } else {
    const defaultFileName = generateDefaultFileName();
    downloadFile(blob, defaultFileName);
    console.warn('使用默认文件名:', defaultFileName);
  }
};
