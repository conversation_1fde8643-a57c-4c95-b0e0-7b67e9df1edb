package com.itmk.utils;

import com.itmk.web.sys_menu.dto.MenuDTO;
import com.itmk.web.sys_menu.dto.RouterDTO;
import com.itmk.web.sys_menu.entity.SysMenu;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 树形结构构建工具类
 * 基于Spring Boot最佳实践设计，支持泛型和高性能构建
 */
public class TreeBuilder {

    /**
     * 构建菜单树（从SysMenu实体）
     */
    public static List<MenuDTO> buildMenuTree(List<SysMenu> menuList, Long rootId) {
        if (CollectionUtils.isEmpty(menuList)) {
            return new ArrayList<>();
        }

        // 转换为DTO并建立索引
        Map<Long, MenuDTO> menuMap = menuList.stream()
            .map(TreeBuilder::convertToMenuDTO)
            .collect(Collectors.toMap(MenuDTO::getMenuId, Function.identity()));

        return buildTreeFromMap(menuMap, rootId);
    }

    /**
     * 构建菜单树（从MenuDTO）
     */
    public static List<MenuDTO> buildMenuTreeFromDTO(List<MenuDTO> menuList, Long rootId) {
        if (CollectionUtils.isEmpty(menuList)) {
            return new ArrayList<>();
        }

        // 建立索引
        Map<Long, MenuDTO> menuMap = menuList.stream()
            .collect(Collectors.toMap(MenuDTO::getMenuId, Function.identity()));

        return buildTreeFromMap(menuMap, rootId);
    }

    /**
     * 从Map构建树形结构（高性能实现）
     */
    private static List<MenuDTO> buildTreeFromMap(Map<Long, MenuDTO> menuMap, Long rootId) {
        List<MenuDTO> rootNodes = new ArrayList<>();

        for (MenuDTO menu : menuMap.values()) {
            if (Objects.equals(menu.getParentId(), rootId)) {
                // 这是根节点
                rootNodes.add(menu);
            } else {
                // 这是子节点，找到父节点并添加
                MenuDTO parent = menuMap.get(menu.getParentId());
                if (parent != null) {
                    parent.addChild(menu);
                }
            }
        }

        // 按排序号排序
        sortMenuTree(rootNodes);
        return rootNodes;
    }

    /**
     * 构建路由树
     */
    public static List<RouterDTO> buildRouterTree(List<SysMenu> menuList, Long rootId) {
        if (CollectionUtils.isEmpty(menuList)) {
            return new ArrayList<>();
        }

        // 过滤掉按钮类型的菜单
        List<SysMenu> filteredMenus = menuList.stream()
            .filter(menu -> !"2".equals(menu.getType()))
            .collect(Collectors.toList());

        return buildRouterTreeRecursive(filteredMenus, rootId);
    }

    /**
     * 递归构建路由树
     */
    private static List<RouterDTO> buildRouterTreeRecursive(List<SysMenu> menuList, Long parentId) {
        return menuList.stream()
            .filter(menu -> Objects.equals(menu.getParentId(), parentId))
            .sorted(Comparator.comparing(SysMenu::getOrderNum, Comparator.nullsLast(Comparator.naturalOrder())))
            .map(menu -> {
                RouterDTO router = convertToRouterDTO(menu);

                // 递归构建子路由
                List<RouterDTO> children = buildRouterTreeRecursive(menuList, menu.getMenuId());
                router.setChildren(children);

                // 设置alwaysShow
                if (Objects.equals(menu.getParentId(), 0L) || router.hasChildren()) {
                    router.setAlwaysShow(true);
                }

                return router;
            })
            .collect(Collectors.toList());
    }

    /**
     * 转换SysMenu为MenuDTO
     */
    private static MenuDTO convertToMenuDTO(SysMenu sysMenu) {
        MenuDTO menuDTO = new MenuDTO();
        BeanUtils.copyProperties(sysMenu, menuDTO);
        return menuDTO;
    }

    /**
     * 转换SysMenu为RouterDTO
     */
    private static RouterDTO convertToRouterDTO(SysMenu menu) {
        RouterDTO router = new RouterDTO();
        router.setName(menu.getName());
        router.setPath(menu.getPath());

        // 设置组件
        if (Objects.equals(menu.getParentId(), 0L)) {
            router.setComponent("Layout");
        } else {
            router.setComponent(menu.getUrl());
        }

        // 设置Meta信息（移除权限字段）
        RouterDTO.Meta meta = RouterDTO.createMeta(
            menu.getTitle(),
            menu.getIcon(),
            new String[0],
            new String[0]
        );
        router.setMeta(meta);

        return router;
    }

    /**
     * 递归排序菜单树
     */
    private static void sortMenuTree(List<MenuDTO> menuList) {
        if (CollectionUtils.isEmpty(menuList)) {
            return;
        }

        // 排序当前级别
        menuList.sort(Comparator.comparing(MenuDTO::getOrderNum,
            Comparator.nullsLast(Comparator.naturalOrder())));

        // 递归排序子菜单
        for (MenuDTO menu : menuList) {
            if (menu.hasChildren()) {
                sortMenuTree(menu.getChildren());
            }
        }
    }

    /**
     * 获取所有叶子节点
     */
    public static List<MenuDTO> getLeafNodes(List<MenuDTO> menuTree) {
        List<MenuDTO> leafNodes = new ArrayList<>();
        collectLeafNodes(menuTree, leafNodes);
        return leafNodes;
    }

    /**
     * 递归收集叶子节点
     */
    private static void collectLeafNodes(List<MenuDTO> menuList, List<MenuDTO> leafNodes) {
        if (CollectionUtils.isEmpty(menuList)) {
            return;
        }

        for (MenuDTO menu : menuList) {
            if (!menu.hasChildren()) {
                leafNodes.add(menu);
            } else {
                collectLeafNodes(menu.getChildren(), leafNodes);
            }
        }
    }

    /**
     * 扁平化树形结构
     */
    public static List<MenuDTO> flattenTree(List<MenuDTO> menuTree) {
        List<MenuDTO> flatList = new ArrayList<>();
        flattenTreeRecursive(menuTree, flatList);
        return flatList;
    }

    /**
     * 递归扁平化
     */
    private static void flattenTreeRecursive(List<MenuDTO> menuList, List<MenuDTO> flatList) {
        if (CollectionUtils.isEmpty(menuList)) {
            return;
        }

        for (MenuDTO menu : menuList) {
            flatList.add(menu);
            if (menu.hasChildren()) {
                flattenTreeRecursive(menu.getChildren(), flatList);
            }
        }
    }
}
